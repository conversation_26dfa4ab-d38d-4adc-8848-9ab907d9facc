#!/usr/bin/env python
"""
清除数据库中的所有需求文档、需求、测试用例、知识库及其关联关系的脚本
支持连接到远程数据库
"""
import asyncio
import logging
import os
import sys
import argparse

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tortoise import Tortoise
from app.settings.config import TORTOISE_ORM

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 自定义数据库配置
def get_db_config(host=None, port=None, user=None, password=None, database=None):
    """根据参数生成数据库配置"""
    # 如果没有提供参数，使用默认配置
    if not any([host, port, user, password, database]):
        return TORTOISE_ORM

    # 复制原始配置
    import copy
    config = copy.deepcopy(TORTOISE_ORM)

    # 更新数据库连接信息
    if host:
        config["connections"]["default"]["credentials"]["host"] = host
    if port:
        config["connections"]["default"]["credentials"]["port"] = int(port)
    if user:
        config["connections"]["default"]["credentials"]["user"] = user
    if password:
        config["connections"]["default"]["credentials"]["password"] = password
    if database:
        config["connections"]["default"]["credentials"]["database"] = database

    return config

async def connect_db(host=None, port=None, user=None, password=None, database=None):
    """连接到数据库"""
    logger.info("正在连接到数据库...")
    # 获取数据库配置
    db_config = get_db_config(host, port, user, password, database)
    # 打印连接信息（隐藏密码）
    conn_info = db_config["connections"]["default"]["credentials"].copy()
    if "password" in conn_info:
        conn_info["password"] = "******"  # 隐藏密码
    logger.info(f"数据库连接信息: {conn_info}")

    await Tortoise.init(config=db_config)
    logger.info("数据库连接成功！")
    return Tortoise.get_connection("default")

async def close_db():
    """关闭数据库连接"""
    logger.info("正在关闭数据库连接...")
    await Tortoise.close_connections()
    logger.info("数据库连接已关闭")

async def clear_requirement_knowledge_relations(db_params=None):
    """清除需求与知识点的关联关系"""
    conn = await connect_db(**db_params if db_params else {})
    try:
        logger.info("开始清除需求与知识点的关联关系...")
        # 清除需求与知识点的关联关系表
        sql = "DELETE FROM requirement_knowledge_relation"
        await conn.execute_query(sql)
        # 重置自增ID
        sql = "ALTER SEQUENCE requirement_knowledge_relation_id_seq RESTART WITH 1"
        await conn.execute_query(sql)
        logger.info("需求与知识点的关联关系已清除")
    except Exception as e:
        logger.error(f"清除需求与知识点的关联关系时出错：{str(e)}")
    finally:
        await close_db()

async def clear_knowledge_items(db_params=None):
    """清除知识库条目"""
    conn = await connect_db(**db_params if db_params else {})
    try:
        logger.info("开始清除知识库条目...")
        # 清除知识库条目表
        sql = "DELETE FROM knowledge_items"
        await conn.execute_query(sql)
        # 重置自增ID
        sql = "ALTER SEQUENCE knowledge_items_id_seq RESTART WITH 1"
        await conn.execute_query(sql)
        logger.info("知识库条目已清除")
    except Exception as e:
        logger.error(f"清除知识库条目时出错：{str(e)}")
    finally:
        await close_db()

async def clear_test_cases(db_params=None):
    """清除测试用例及相关数据"""
    conn = await connect_db(**db_params if db_params else {})
    try:
        logger.info("开始清除测试用例及相关数据...")

        # 1. 清除测试用例与测试步骤的关联关系
        sql = "DELETE FROM test_cases_test_steps"
        await conn.execute_query(sql)
        logger.info("测试用例与测试步骤的关联关系已清除")

        # 2. 清除测试步骤
        sql = "DELETE FROM test_steps"
        await conn.execute_query(sql)
        logger.info("测试步骤已清除")

        # 3. 清除测试用例
        sql = "DELETE FROM test_cases"
        await conn.execute_query(sql)
        logger.info("测试用例已清除")

        # 4. 重置自增ID
        sql = "ALTER SEQUENCE test_steps_id_seq RESTART WITH 1"
        await conn.execute_query(sql)
        sql = "ALTER SEQUENCE test_cases_id_seq RESTART WITH 1"
        await conn.execute_query(sql)
        logger.info("测试用例相关表的自增ID已重置")
    except Exception as e:
        logger.error(f"清除测试用例及相关数据时出错：{str(e)}")
    finally:
        await close_db()

async def clear_requirements(db_params=None):
    """清除需求"""
    conn = await connect_db(**db_params if db_params else {})
    try:
        logger.info("开始清除需求...")
        # 清除需求表
        sql = "DELETE FROM requirement"
        await conn.execute_query(sql)
        # 重置自增ID
        sql = "ALTER SEQUENCE requirement_id_seq RESTART WITH 1"
        await conn.execute_query(sql)
        logger.info("需求已清除")
    except Exception as e:
        logger.error(f"清除需求时出错：{str(e)}")
    finally:
        await close_db()

async def clear_requirement_docs(db_params=None):
    """清除需求文档"""
    conn = await connect_db(**db_params if db_params else {})
    try:
        logger.info("开始清除需求文档...")
        # 清除需求文档表
        sql = "DELETE FROM requirement_doc"
        await conn.execute_query(sql)
        # 重置自增ID
        sql = "ALTER SEQUENCE requirement_doc_id_seq RESTART WITH 1"
        await conn.execute_query(sql)
        logger.info("需求文档已清除")
    except Exception as e:
        logger.error(f"清除需求文档时出错：{str(e)}")
    finally:
        await close_db()

async def count_records(db_params=None):
    """统计各表中的记录数量"""
    conn = await connect_db(**db_params if db_params else {})
    try:
        tables = [
            "requirement_knowledge_relation",
            "knowledge_items",
            "test_cases_test_steps",
            "test_steps",
            "test_cases",
            "requirement",
            "requirement_doc"
        ]

        logger.info("当前数据库记录统计：")
        for table in tables:
            try:
                sql = f"SELECT COUNT(*) FROM {table}"
                result = await conn.execute_query(sql)
                count = result[1][0][0] if result and result[1] and result[1][0] else 0
                logger.info(f"表 {table} 中有 {count} 条记录")
            except Exception as e:
                logger.error(f"统计表 {table} 记录数时出错：{str(e)}")
    except Exception as e:
        logger.error(f"统计记录数时出错：{str(e)}")
    finally:
        await close_db()

async def clear_all(db_params=None):
    """清除所有相关数据"""
    logger.info("开始清除所有数据...")

    # 统计清除前的记录数
    await count_records(db_params)

    # 按照依赖关系顺序清除数据
    # 1. 先清除关联关系
    await clear_requirement_knowledge_relations(db_params)

    # 2. 清除测试用例及相关数据
    await clear_test_cases(db_params)

    # 3. 清除知识库条目
    await clear_knowledge_items(db_params)

    # 4. 清除需求
    await clear_requirements(db_params)

    # 5. 清除需求文档
    await clear_requirement_docs(db_params)

    # 统计清除后的记录数
    logger.info("清除后的数据库记录统计：")
    await count_records(db_params)

    logger.info("所有数据已清除完毕！")

def main():
    """主函数"""
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description="清除数据库中的所有需求文档、需求、测试用例、知识库及其关联关系")

    # 添加数据库连接参数
    parser.add_argument("--host", help="数据库主机地址，例如：localhost 或 *************")
    parser.add_argument("--port", help="数据库端口，例如：5432")
    parser.add_argument("--user", help="数据库用户名，例如：admin")
    parser.add_argument("--password", help="数据库密码")
    parser.add_argument("--database", help="数据库名称，例如：agent_testing")
    parser.add_argument("--force", action="store_true", help="强制执行，跳过确认")

    # 解析命令行参数
    args = parser.parse_args()

    # 构建数据库参数字典
    db_params = {}
    if args.host:
        db_params["host"] = args.host
    if args.port:
        db_params["port"] = args.port
    if args.user:
        db_params["user"] = args.user
    if args.password:
        db_params["password"] = args.password
    if args.database:
        db_params["database"] = args.database

    # 打印连接信息（隐藏密码）
    if db_params:
        conn_info = db_params.copy()
        if "password" in conn_info:
            conn_info["password"] = "******"  # 隐藏密码
        print(f"将连接到远程数据库: {conn_info}")

    print("此脚本将清除数据库中的所有需求文档、需求、测试用例、知识库及其关联关系。")

    # 强制执行模式
    if args.force:
        print("强制执行模式，跳过确认...")
        try:
            asyncio.run(clear_all(db_params))
            print("数据清除完成！")
        except Exception as e:
            print(f"清除数据时出错：{str(e)}")
            logger.error(f"清除数据时出错：{str(e)}", exc_info=True)
        return

    # 交互式确认
    confirm = input("确定要继续吗？此操作不可撤销！(y/n): ")

    if confirm.lower() == 'y':
        try:
            asyncio.run(clear_all(db_params))
            print("数据清除完成！")
        except Exception as e:
            print(f"清除数据时出错：{str(e)}")
            logger.error(f"清除数据时出错：{str(e)}", exc_info=True)
    else:
        print("操作已取消。")

if __name__ == "__main__":
    main()
