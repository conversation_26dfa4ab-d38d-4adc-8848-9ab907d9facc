#!/usr/bin/env python3
"""
WebSocket连接测试脚本
用于测试AI测试平台的WebSocket连接是否正常工作
"""

import asyncio
import websockets
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_websocket_connection():
    """测试WebSocket连接"""
    
    # WebSocket服务器地址
    ws_url = "ws://localhost:9999/api/v1/agent/ws/generate"
    
    try:
        logger.info(f"🔗 尝试连接到WebSocket服务器: {ws_url}")
        
        # 连接到WebSocket服务器
        async with websockets.connect(ws_url) as websocket:
            logger.info("✅ WebSocket连接成功建立")
            
            # 发送测试消息
            test_message = {
                "id": 999,
                "name": "连接测试",
                "description": "这是一个WebSocket连接测试",
                "task": "测试WebSocket连接",
                "scenario": "连接测试场景",
                "project_id": 1,
                "tapd_url": "http://test.com",
                "reviewer": "测试用户"
            }
            
            logger.info("📤 发送测试消息...")
            await websocket.send(json.dumps(test_message))
            logger.info("✅ 测试消息发送成功")
            
            # 等待响应
            logger.info("📥 等待服务器响应...")
            try:
                # 设置超时时间
                response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                logger.info(f"✅ 收到服务器响应: {response}")
                
                # 尝试解析JSON响应
                try:
                    response_data = json.loads(response)
                    logger.info(f"📋 响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                except json.JSONDecodeError:
                    logger.info(f"📋 响应内容（非JSON）: {response}")
                
            except asyncio.TimeoutError:
                logger.warning("⏰ 等待响应超时（10秒）")
            
            logger.info("🔚 测试完成，关闭连接")
            
    except websockets.exceptions.ConnectionRefused:
        logger.error("❌ WebSocket连接被拒绝 - 请检查服务器是否正在运行")
        logger.error("   确保后端服务器在端口9999上运行")
        return False
    except websockets.exceptions.InvalidURI:
        logger.error(f"❌ WebSocket URI无效: {ws_url}")
        return False
    except Exception as e:
        logger.error(f"❌ WebSocket连接测试失败: {str(e)}")
        return False
    
    return True

async def test_http_status():
    """测试HTTP状态端点"""
    import aiohttp
    
    try:
        logger.info("🔗 测试HTTP状态端点...")
        
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:9999/api/v1/agent/ws/status") as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"✅ HTTP状态检查成功: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    return True
                else:
                    logger.error(f"❌ HTTP状态检查失败: {response.status}")
                    return False
                    
    except aiohttp.ClientConnectorError:
        logger.error("❌ HTTP连接失败 - 请检查服务器是否正在运行")
        return False
    except Exception as e:
        logger.error(f"❌ HTTP状态检查失败: {str(e)}")
        return False

async def main():
    """主函数"""
    logger.info("🚀 开始WebSocket连接测试")
    logger.info("=" * 50)
    
    # 测试HTTP状态端点
    http_success = await test_http_status()
    logger.info("=" * 50)
    
    # 测试WebSocket连接
    ws_success = await test_websocket_connection()
    logger.info("=" * 50)
    
    # 总结测试结果
    logger.info("📊 测试结果总结:")
    logger.info(f"   HTTP状态端点: {'✅ 成功' if http_success else '❌ 失败'}")
    logger.info(f"   WebSocket连接: {'✅ 成功' if ws_success else '❌ 失败'}")
    
    if http_success and ws_success:
        logger.info("🎉 所有测试通过！WebSocket服务正常运行")
        return True
    else:
        logger.error("💥 测试失败！请检查后端服务器状态")
        logger.error("   建议检查项目:")
        logger.error("   1. 后端服务器是否在端口9999上运行")
        logger.error("   2. WebSocket路由是否正确注册")
        logger.error("   3. 防火墙是否阻止了连接")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 测试被用户中断")
        exit(1)
