import logging
import os
import uvicorn

# 创建日志目录
os.makedirs("logs", exist_ok=True)

if __name__ == "__main__":
    # 配置uvicorn日志
    log_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "()": "uvicorn.logging.DefaultFormatter",
                "fmt": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
        },
        "handlers": {
            "default": {
                "formatter": "default",
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stderr",
            },
            "file": {
                "formatter": "default",
                "class": "logging.handlers.RotatingFileHandler",
                "filename": "logs/uvicorn.log",
                "maxBytes": 10*1024*1024,  # 10MB
                "backupCount": 3,
                "encoding": "utf-8",
            },
        },
        "loggers": {
            "uvicorn": {"handlers": ["default", "file"], "level": "INFO"},
            "uvicorn.error": {"level": "INFO"},
            "uvicorn.access": {"handlers": ["default", "file"], "level": "INFO", "propagate": False},
        },
    }

    # 从环境变量获取端口，默认为9999
    port = int(os.environ.get("API_PORT", "9999"))

    # 获取日志级别，默认为INFO
    log_level_str = os.environ.get("LOG_LEVEL", "INFO").upper()
    # 确保日志级别是有效的
    log_level = getattr(logging, log_level_str, logging.INFO)

    # 启动服务器
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=port,
        reload=True,
        log_config=log_config,
        log_level=log_level_str.lower()  # 使用小写的日志级别字符串
    )
