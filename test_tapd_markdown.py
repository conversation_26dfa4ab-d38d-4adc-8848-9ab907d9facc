"""
测试TAPD内容转换为Markdown并优化格式
"""
import os
import sys
import logging
import traceback

# 添加当前目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 配置日志输出到控制台
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    handlers=[logging.StreamHandler()])

# 导入需要测试的函数
from app.api.v1.reqAgent.tapdAgent import html_to_markdown

# 设置环境变量，启用图片分析
os.environ["ENABLE_IMAGE_ANALYSIS"] = "true"

# 设置 OpenAI API 密钥 - 请替换为您自己的API密钥
# os.environ["LLM_API_KEY"] = "sk-your-openai-api-key-here"

# 测试HTML内容
html_content = """
<div class="tapd-detail-content">
    <h1>需求标题：用户管理功能</h1>
    <div class="description">
        <p>本需求主要实现系统的用户管理功能</p>
        <p>包含用户的增删改查等基本操作</p>
    </div>
    <div class="requirement-details">
        <h2>功能点</h2>
        <ul>
            <li>用户注册</li>
            <li>用户登录</li>
            <li>用户信息修改</li>
            <li>用户权限管理</li>
        </ul>
        <h2>界面设计</h2>
        <p>界面需要简洁大方符合整体风格</p>
        <img src="https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7" alt="用户界面示例">
        <p>上图为用户界面参考设计</p>
        <h2>技术要求</h2>
        <p>前端使用Vue框架开发</p>
        <p>后端使用Spring Boot</p>
        <p>数据库使用MySQL</p>
    </div>
</div>
"""

def run_test():
    """运行测试"""
    print("\n" + "="*50)
    print("TAPD内容转Markdown测试")
    print("="*50)

    try:
        # 转换HTML为Markdown
        print("原始HTML内容:")
        print("-"*50)
        print(html_content)
        print("-"*50)

        # 使用优化后的html_to_markdown函数
        markdown_result = html_to_markdown(html_content)

        print("\n转换后的Markdown内容:")
        print("-"*50)
        print(markdown_result)
        print("-"*50)

        print("\n测试完成！")

    except Exception as e:
        print(f"测试失败: {str(e)}")
        print("错误详情:")
        traceback.print_exc()

    print("="*50)

if __name__ == "__main__":
    run_test()
