"""
测试图片解析功能
"""
import os
from app.api.v1.reqAgent.image_markdown_converter import html_to_markdown_with_image_analysis

# 设置环境变量，启用图片分析
os.environ["ENABLE_IMAGE_ANALYSIS"] = "true"

# 设置 OpenAI API 密钥
# 注意：请替换为您自己的 API 密钥
os.environ["LLM_API_KEY"] = "sk-your-openai-api-key-here"

# 测试HTML内容，包含图片
html_content = """
<div>
    <h1>测试文档</h1>
    <p>这是一个包含图片的测试文档</p>
    <img src="https://www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png" alt="百度Logo">
    <p>这是图片下方的文字</p>
</div>
"""

# 转换为Markdown并分析图片
markdown_result = html_to_markdown_with_image_analysis(html_content)

# 打印结果
print("转换结果:")
print("-" * 50)
print(markdown_result)
print("-" * 50)
