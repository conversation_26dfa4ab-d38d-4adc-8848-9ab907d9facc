from markdownify import markdownify

# 测试基本的图片标签
html = '<img src="test.jpg" alt="Test Image">'
print("基本图片标签:")
print(markdownify(html))
print("\n")

# 测试带有其他属性的图片标签
html2 = '<img src="test.jpg" alt="Test Image" width="100" height="100">'
print("带属性的图片标签:")
print(markdownify(html2))
print("\n")

# 测试带有样式的图片标签
html3 = '<img src="test.jpg" alt="Test Image" style="width:100px;height:100px;">'
print("带样式的图片标签:")
print(markdownify(html3))
