import asyncio
import json
from tortoise import Tortoise, connections
from app.settings.config import TORTOISE_ORM
from pydantic import BaseModel
from typing import List, Optional
import uuid

class RequirementItem(BaseModel):
    name: str
    description: str
    category: str = "功能"
    parent: Optional[str] = None
    module: Optional[str] = None
    level: str = "高"
    reviewer: Optional[str] = None
    estimate: int = 8
    criteria: Optional[str] = None
    remark: Optional[str] = "[功能模块: 测试模块]"
    keywords: Optional[str] = None
    project_id: int = 1
    tapd_url: Optional[str] = None

async def test_sql_insert():
    # 初始化Tortoise ORM
    await Tortoise.init(config=TORTOISE_ORM)
    
    try:
        # 创建测试需求
        unique_name = f"测试需求SQL-{uuid.uuid4().hex[:8]}"
        
        # 获取数据库连接
        conn = connections.get("default")
        
        # 构建需求字典
        requirement_dict = {
            "name": unique_name,
            "description": "这是一个测试需求描述",
            "category": "功能",
            "level": "高",
            "estimate": 8,
            "project_id": 1,
            "remark": "这是一个测试备注 [功能模块: 测试模块]"
        }
        
        print(f"原始需求字典: {requirement_dict}")
        
        # 构建SQL插入语句
        fields = []
        values = []
        params = []
        
        for field_name, field_value in requirement_dict.items():
            if field_value is not None:
                fields.append(field_name)
                values.append(f"${len(params) + 1}")
                params.append(field_value)
        
        # 特别检查remark字段是否已添加
        if 'remark' in requirement_dict:
            print(f"\nremark字段值: {requirement_dict['remark']}")
        else:
            print("\n警告: 需求字典中不包含remark字段!")
        
        # 添加created_at和updated_at字段
        fields.extend(["created_at", "updated_at"])
        values.extend(["NOW()", "NOW()"])
        
        # 构建完整的SQL语句
        sql = f"INSERT INTO requirement ({', '.join(fields)}) VALUES ({', '.join(values)}) RETURNING id"
        
        # 执行SQL语句
        print(f"执行SQL: {sql}")
        print(f"参数: {params}")
        result = await conn.execute_query(sql, params)
        requirement_id = result[1][0][0]
        print(f"需求保存成功! ID: {requirement_id}")
        
        # 查询刚刚创建的记录
        result = await conn.execute_query(
            "SELECT id, name, remark FROM requirement WHERE id = $1",
            [requirement_id]
        )
        
        print(f"查询结果: {result}")
        
    except Exception as e:
        print(f"错误: {str(e)}")
    
    # 关闭连接
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(test_sql_insert())
