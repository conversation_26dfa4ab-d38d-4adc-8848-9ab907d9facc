# PostgreSQL时区问题彻底解决方案

本文档说明了如何彻底解决PostgreSQL数据库中的时区问题，特别是"can't subtract offset-naive and offset-aware datetimes"错误。

## 问题描述

在使用PostgreSQL数据库时，如果混合使用了带时区信息(offset-aware)和不带时区信息(offset-naive)的datetime对象，会出现以下错误：

```
需求入库过程出错: invalid input for query argument $1: datetime.datetime(2025, 4, 26, 17, 5, 37... (can't subtract offset-naive and offset-aware datetimes)
```

## 解决方案

我们采用了以下综合方案来彻底解决这个问题：

### 1. 统一时区配置

将PostgreSQL和应用代码都统一使用UTC时区：

```python
# 在app/settings/config.py中
"server_settings": {
    "timezone": "UTC",  # 统一使用UTC时区
    "application_name": "agent_testing"
}

# 在TORTOISE_ORM配置中
"use_tz": True,  # 是否使用时区感知的时间，启用
"timezone": "UTC",  # 统一使用UTC时区
```

### 2. 中心化时区处理函数

在app/models/base.py中定义了统一的时区处理函数：

```python
def get_now_with_timezone():
    """获取带UTC时区的当前时间"""
    return datetime.now(pytz.UTC)

def ensure_timezone(dt):
    """确保datetime对象有时区信息"""
    if dt is None:
        return None
    if dt.tzinfo is None:
        return dt.replace(tzinfo=pytz.UTC)
    return dt

def ensure_all_datetimes_have_timezone(obj_dict):
    """确保字典中所有datetime对象都有时区信息"""
    # 实现详见代码
```

### 3. 绕过ORM直接使用SQL

为了彻底解决时区问题，我们修改了RequirementDatabaseAgent类，使用原始SQL语句直接插入数据，完全避开ORM的时区处理：

```python
# 使用原始SQL插入，完全避免ORM的时区处理
conn = connections.get("default")

# 构建SQL插入语句
fields = []
values = []
params = []

for field_name, field_value in minimal_dict.items():
    if field_value is not None:
        fields.append(field_name)
        values.append(f"${len(params) + 1}")
        params.append(field_value)

# 添加created_at和updated_at字段，使用数据库的NOW()函数
fields.extend(["created_at", "updated_at"])
values.extend(["NOW() AT TIME ZONE 'UTC'", "NOW() AT TIME ZONE 'UTC'"])

# 构建完整的SQL语句
sql = f"INSERT INTO requirement ({', '.join(fields)}) VALUES ({', '.join(values)}) RETURNING id"

# 执行SQL语句
result = await conn.execute_query(sql, params)
requirement_id = result[1][0][0]
```

### 4. 修复现有数据

创建了数据库迁移脚本(fix_timezone_direct.py)来修复现有数据的时区问题：

```python
# 修复created_at字段
await conn.execute_query("""
UPDATE requirement 
SET created_at = created_at AT TIME ZONE 'UTC' 
WHERE created_at IS NOT NULL AND extract(timezone from created_at) = 0
""")

# 修复updated_at字段
await conn.execute_query("""
UPDATE requirement 
SET updated_at = updated_at AT TIME ZONE 'UTC' 
WHERE updated_at IS NOT NULL AND extract(timezone from updated_at) = 0
""")
```

### 5. 提供直接插入测试脚本

创建了direct_insert_requirement.py脚本，用于测试直接SQL插入是否能解决时区问题：

```python
# 构建SQL插入语句
fields = []
values = []
params = []

for field_name, field_value in minimal_dict.items():
    if field_value is not None:
        fields.append(field_name)
        values.append(f"${len(params) + 1}")
        params.append(field_value)

# 添加created_at和updated_at字段，使用数据库的NOW()函数
fields.extend(["created_at", "updated_at"])
values.extend(["NOW() AT TIME ZONE 'UTC'", "NOW() AT TIME ZONE 'UTC'"])

# 构建完整的SQL语句
sql = f"INSERT INTO requirement ({', '.join(fields)}) VALUES ({', '.join(values)}) RETURNING id"
```

## 如何应用修复

1. 首先运行数据库迁移脚本修复现有数据：

```bash
python fix_timezone_direct.py
```

2. 测试直接SQL插入是否能解决问题：

```bash
python direct_insert_requirement.py
```

3. 重启应用服务器

## 注意事项

- 所有新创建的datetime对象都应该使用`get_now_with_timezone()`函数
- 对于关键的数据库操作，考虑使用直接SQL插入而不是ORM
- 如果遇到类似的时区问题，请检查是否有新的代码没有使用统一的时区处理函数
- 确保PostgreSQL数据库的时区设置为UTC
