#!/usr/bin/env python
import asyncio
import sys
from datetime import datetime
from tortoise import Tortoise
from app.settings.config import settings

async def init():
    await Tortoise.init(config=settings.TORTOISE_ORM)

async def export_schema_and_data():
    # 初始化连接
    conn = Tortoise.get_connection("default")
    
    # 获取所有表名
    result = await conn.execute_query("SELECT name FROM sqlite_master WHERE type='table';")
    tables = [row[0] for row in result[1]]
    # 过滤掉系统表
    tables = [t for t in tables if not t.startswith('sqlite_')]

    # 创建输出文件
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'db_export_{timestamp}.sql'
    
    with open(filename, 'w', encoding='utf-8') as f:
        # 写入文件头
        f.write('-- Database export from SQLite to PostgreSQL\n')
        f.write(f'-- Generated at {datetime.now()}\n\n')
        
        # 对每个表进行处理
        for table in tables:
            # 获取表结构
            schema_result = await conn.execute_query(f"PRAGMA table_info({table});")
            columns = schema_result[1]
            
            # 写入建表语句
            f.write(f'\n-- Table: {table}\n')
            f.write(f'DROP TABLE IF EXISTS "{table}" CASCADE;\n')
            f.write(f'CREATE TABLE "{table}" (\n')
            
            # 处理列定义
            col_defs = []
            for col in columns:
                name = col[1]
                type_ = col[2].upper()
                nullable = 'NOT NULL' if col[3] else ''
                pk = 'PRIMARY KEY' if col[5] else ''
                
                # SQLite到PostgreSQL类型转换
                if type_ == 'INTEGER':
                    type_ = 'BIGINT'
                elif type_ == 'REAL':
                    type_ = 'DOUBLE PRECISION'
                elif type_ == 'TEXT':
                    type_ = 'TEXT'
                elif type_ == 'BLOB':
                    type_ = 'BYTEA'
                
                col_defs.append(f'    "{name}" {type_} {nullable} {pk}'.strip())
            
            f.write(',\n'.join(col_defs))
            f.write('\n);\n\n')
            
            # 导出数据
            data_result = await conn.execute_query(f'SELECT * FROM "{table}";')
            if data_result[1]:  # 如果有数据
                f.write(f'-- Data for table: {table}\n')
                for row in data_result[1]:
                    # 处理数据中的特殊字符
                    values = []
                    for val in row:
                        if val is None:
                            values.append('NULL')
                        elif isinstance(val, (int, float)):
                            values.append(str(val))
                        else:
                            # 转义字符串中的单引号
                            val_str = str(val).replace("'", "''")
                            values.append(f"'{val_str}'")
                    
                    f.write(f'INSERT INTO "{table}" VALUES ({", ".join(values)});\n')
                f.write('\n')

    print(f"导出完成，文件保存为: {filename}")

async def main():
    await init()
    await export_schema_and_data()
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(main())