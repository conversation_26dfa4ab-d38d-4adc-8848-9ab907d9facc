#!/bin/bash

# 创建dist目录
mkdir -p dist

# 打包原始插件
echo "打包原始插件..."
cd tapd_extractor
zip -r ../dist/tapd_extractor.zip * -x "*.git*" -x "*.DS_Store"
cd ..

# 打包修复版插件
echo "打包修复版插件..."
cd tapd_extractor_fixed
zip -r ../dist/tapd_extractor_fixed.zip * -x "*.git*" -x "*.DS_Store"
cd ..

echo "插件已打包完成:"
echo "1. 原始版本: dist/tapd_extractor.zip"
echo "2. 修复版本: dist/tapd_extractor_fixed.zip"
echo ""
echo "安装说明:"
echo "1. 打开Chrome浏览器"
echo "2. 在地址栏输入 chrome://extensions/"
echo "3. 打开右上角的'开发者模式'"
echo "4. 将zip文件直接拖放到浏览器窗口中安装"
echo "或者"
echo "4. 解压zip文件，点击'加载已解压的扩展程序'，选择解压后的目录"
