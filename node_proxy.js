const http = require('http');
const httpProxy = require('http-proxy');

// 创建代理服务器
const proxy = httpProxy.createProxyServer({});

// 处理代理错误
proxy.on('error', function(err, req, res) {
  console.error('代理错误:', err);
  res.writeHead(500, {
    'Content-Type': 'text/plain'
  });
  res.end('代理错误');
});

// 创建 HTTP 服务器
const server = http.createServer(function(req, res) {
  // 设置 CORS 头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization');
  
  // 处理 OPTIONS 请求
  if (req.method === 'OPTIONS') {
    res.writeHead(204);
    res.end();
    return;
  }
  
  // 检查是否是重复的 API 路径
  if (req.url.startsWith('/api/v1/api/v1/')) {
    // 修正 URL
    req.url = req.url.replace('/api/v1/api/v1/', '/api/v1/');
    console.log('修正后的 URL:', req.url);
  }
  
  // 转发请求到目标服务器
  proxy.web(req, res, {
    target: 'http://localhost:9999'
  });
});

// 监听 8080 端口
server.listen(8080, function() {
  console.log('代理服务器运行在 http://localhost:8080');
});
