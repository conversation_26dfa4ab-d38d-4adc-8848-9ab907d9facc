import asyncio
from tortoise import Tortoise, connections
from app.settings.config import TORTOISE_ORM
from app.models.admin import Requirement
import uuid

async def test_save_remark():
    # 初始化Tortoise ORM
    await Tortoise.init(config=TORTOISE_ORM)
    
    # 生成唯一名称，避免冲突
    unique_name = f"测试需求-{uuid.uuid4().hex[:8]}"
    
    try:
        # 方法1：使用ORM模型直接创建
        print("方法1：使用ORM模型直接创建")
        requirement = Requirement(
            name=unique_name,
            description="测试描述",
            category="功能",
            level="高",
            estimate=8,
            project_id=1,
            remark="这是一个测试备注 [功能模块: 测试模块]"
        )
        await requirement.save()
        print(f"需求已保存，ID: {requirement.id}")
        
        # 查询刚刚创建的记录
        saved_req = await Requirement.filter(name=unique_name).first()
        print(f"查询结果: ID={saved_req.id}, 名称={saved_req.name}, 备注={saved_req.remark}")
        
        # 方法2：使用原始SQL插入
        print("\n方法2：使用原始SQL插入")
        unique_name2 = f"测试需求SQL-{uuid.uuid4().hex[:8]}"
        conn = connections.get("default")
        
        # 构建SQL插入语句
        sql = """
        INSERT INTO requirement (name, description, category, level, estimate, project_id, remark, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
        RETURNING id
        """
        
        result = await conn.execute_query(
            sql, 
            [unique_name2, "SQL测试描述", "功能", "高", 8, 1, "这是SQL插入的备注 [功能模块: SQL模块]"]
        )
        
        req_id = result[1][0][0]
        print(f"SQL插入成功，ID: {req_id}")
        
        # 查询SQL插入的记录
        result = await conn.execute_query(
            "SELECT id, name, remark FROM requirement WHERE id = $1",
            [req_id]
        )
        
        print(f"SQL查询结果: {result}")
        
    except Exception as e:
        print(f"错误: {str(e)}")
    
    # 关闭连接
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(test_save_remark())
