# PostgreSQL 15 在华为云CCE上的部署指南

本文档提供了在华为云CCE（Cloud Container Engine）上部署PostgreSQL 15数据库的详细步骤和说明。

## 文件说明

`postgres-deployment.yaml`文件包含以下Kubernetes资源：

1. **PersistentVolumeClaim (PVC)**: 用于数据持久化存储
2. **ConfigMap**: 存储PostgreSQL的非敏感配置
3. **Secret**: 存储数据库密码等敏感信息
4. **Deployment**: 定义PostgreSQL容器的部署规范
5. **Service (ClusterIP)**: 用于集群内部访问PostgreSQL
6. **Service (NodePort)**: 可选，用于从集群外部访问PostgreSQL

## 部署前准备

1. 确保已创建华为云CCE集群并配置好kubectl工具
2. 确认集群中有可用的存储类(StorageClass)
3. 根据需要调整资源配置（CPU、内存、存储空间等）

## 部署步骤

### 1. 检查并调整配置

在应用YAML文件之前，请根据实际需求调整以下配置：

- **存储类名称**: 修改`storageClassName`字段为华为云CCE集群中可用的存储类名称
- **存储空间大小**: 根据数据量调整`storage`字段
- **数据库名称、用户名和密码**: 修改ConfigMap和Secret中的相关字段
- **资源限制**: 根据需要调整CPU和内存的请求和限制
- **PostgreSQL配置参数**: 根据工作负载特点调整ConfigMap中的postgresql.conf

### 2. 应用YAML文件

```bash
kubectl apply -f postgres-deployment.yaml
```

### 3. 验证部署

```bash
# 检查PVC状态
kubectl get pvc postgres-pvc

# 检查Pod状态
kubectl get pods -l app=postgres

# 检查服务状态
kubectl get svc -l app=postgres
```

### 4. 连接到PostgreSQL

#### 集群内部连接

应用可以使用以下连接信息连接到PostgreSQL：

- **主机**: `postgres`
- **端口**: `5432`
- **数据库**: `agent_testing`
- **用户名**: `admin`
- **密码**: `admin123`

示例连接字符串：
```
*****************************************/agent_testing
```

#### 集群外部连接（如果启用了NodePort服务）

1. 获取CCE集群中任一节点的IP地址
2. 使用节点IP和NodePort（30432）连接

示例连接字符串：
```
********************************************/agent_testing
```

## 数据持久化

PostgreSQL的数据存储在持久卷中，即使Pod重启或重新调度，数据也不会丢失。

## 安全注意事项

1. **密码管理**: 生产环境中应使用更强的密码，并考虑使用华为云密钥管理服务
2. **网络访问**: 评估是否真的需要NodePort服务暴露数据库
3. **资源隔离**: 考虑使用Kubernetes命名空间隔离不同环境的数据库

## 备份策略

建议实施定期备份策略：

```bash
# 创建备份
kubectl exec -it $(kubectl get pods -l app=postgres -o jsonpath='{.items[0].metadata.name}') -- \
  pg_dump -U admin agent_testing > backup_$(date +%Y%m%d).sql

# 恢复备份
cat backup_file.sql | kubectl exec -i $(kubectl get pods -l app=postgres -o jsonpath='{.items[0].metadata.name}') -- \
  psql -U admin -d agent_testing
```

## 监控

考虑部署PostgreSQL Exporter和Prometheus来监控数据库性能和健康状况。

## 高可用性考虑

本部署为单实例PostgreSQL。对于生产环境，建议考虑：

1. 使用PostgreSQL的复制功能
2. 部署PostgreSQL集群解决方案，如Patroni或Crunchy PostgreSQL Operator
3. 配置定期备份和灾难恢复策略
