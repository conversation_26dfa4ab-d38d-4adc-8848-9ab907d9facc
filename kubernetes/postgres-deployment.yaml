---
# PostgreSQL持久卷声明(PVC)
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  labels:
    app: postgres
spec:
  accessModes:
    - ReadWriteOnce  # 单节点读写模式
  storageClassName: csi-disk  # 华为云CCE的存储类名称，可能需要根据实际情况调整
  resources:
    requests:
      storage: 10Gi  # 请求10GB存储空间，可根据需要调整

---
# PostgreSQL ConfigMap，用于存储非敏感配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-config
  labels:
    app: postgres
data:
  POSTGRES_DB: "agent_testing"
  POSTGRES_USER: "admin"
  POSTGRES_PORT: "5432"
  PGDATA: "/var/lib/postgresql/data/pgdata"
  # PostgreSQL配置参数
  postgresql.conf: |
    listen_addresses = '*'
    max_connections = 100
    shared_buffers = 128MB
    dynamic_shared_memory_type = posix
    max_wal_size = 1GB
    min_wal_size = 80MB
    log_timezone = 'UTC'
    datestyle = 'iso, mdy'
    timezone = 'UTC'
    lc_messages = 'en_US.utf8'
    lc_monetary = 'en_US.utf8'
    lc_numeric = 'en_US.utf8'
    lc_time = 'en_US.utf8'
    default_text_search_config = 'pg_catalog.english'

---
# PostgreSQL Secret，用于存储敏感信息
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secret
  labels:
    app: postgres
type: Opaque
stringData:
  POSTGRES_PASSWORD: "admin123"  # 数据库密码，生产环境中应使用更强的密码

---
# PostgreSQL Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  labels:
    app: postgres
spec:
  replicas: 1  # PostgreSQL单实例部署
  selector:
    matchLabels:
      app: postgres
  strategy:
    type: Recreate  # 使用Recreate策略，确保在更新时先停止旧Pod再创建新Pod
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15  # 使用PostgreSQL 15镜像
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 5432
          name: postgres
        envFrom:
        - configMapRef:
            name: postgres-config
        - secretRef:
            name: postgres-secret
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
        - name: postgres-config-volume
          mountPath: /etc/postgresql/postgresql.conf
          subPath: postgresql.conf
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - admin
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - admin
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          successThreshold: 1
          failureThreshold: 3
      volumes:
      - name: postgres-data
        persistentVolumeClaim:
          claimName: postgres-pvc
      - name: postgres-config-volume
        configMap:
          name: postgres-config
          items:
          - key: postgresql.conf
            path: postgresql.conf

---
# PostgreSQL Service - ClusterIP (内部访问)
apiVersion: v1
kind: Service
metadata:
  name: postgres
  labels:
    app: postgres
spec:
  type: ClusterIP
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
    name: postgres
  selector:
    app: postgres

---
# PostgreSQL Service - NodePort (可选，用于外部访问)
# 注意：生产环境中通常不建议直接暴露数据库服务
apiVersion: v1
kind: Service
metadata:
  name: postgres-external
  labels:
    app: postgres
spec:
  type: NodePort
  ports:
  - port: 5432
    targetPort: 5432
    nodePort: 30432  # 节点端口，范围通常为30000-32767
    protocol: TCP
    name: postgres
  selector:
    app: postgres
