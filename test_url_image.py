#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import logging
from app.api.v1.reqAgent.image_markdown_converter import ImageMarkdownConverter
from app.api.v1.reqAgent.autogen_image_analyzer import analyze_image_with_autogen

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_url_image")

def test_url_image_analysis():
    """测试URL图片分析功能"""
    # 测试URL图片
    test_image_url = "https://file.tapd.cn//tfl/captures/2025-02/tapd_43702532_base64_1739767514_518.png"
    
    print(f"开始测试URL图片分析功能，图片URL: {test_image_url}")
    
    # 准备提示词
    prompt = "请详细描述这张图片的内容。包括图片中的文字、图表、界面元素等。"
    
    # 直接使用analyze_image_with_autogen函数
    try:
        print("开始分析URL图片...")
        api_key = os.environ.get("LLM_API_KEY", "sk-85477c3eb0424bb89d5421d2b28d2051")
        api_base = os.environ.get("LLM_API_BASE", "https://dashscope.aliyuncs.com/compatible-mode/v1")
        model = os.environ.get("DEFAULT_LLM_MODEL", "qwen-vl-plus-latest")
        
        result = analyze_image_with_autogen(test_image_url, prompt, api_key, api_base, model)
        
        # 检查结果
        if result:
            print(f"图片分析成功，结果长度: {len(result)}")
            print(f"分析结果: {result[:200]}...")
            return True
        else:
            print("图片分析失败")
            return False
    except Exception as e:
        print(f"分析过程中发生错误: {str(e)}")
        import traceback
        print(f"异常堆栈: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_url_image_analysis()
    sys.exit(0 if success else 1)
