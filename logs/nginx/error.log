2025/05/02 16:47:18 [warn] 10#10: *44 a client request body is buffered to a temporary file /var/lib/nginx/body/0000000001, client: ************, server: localhost, request: "POST /api/v1/reqAgent/tapd/parse HTTP/1.1", host: "localhost:9999"
2025/05/02 23:09:05 [error] 8#8: *33 recv() failed (104: Connection reset by peer) while proxying upgraded connection, client: ************, server: localhost, request: "GET /api/v1/agent/ws/generate HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/agent/ws/generate", host: "localhost:9999"
2025/05/02 23:09:05 [error] 8#8: *33 recv() failed (104: Connection reset by peer) while proxying upgraded connection, client: ************, server: localhost, request: "GET /api/v1/agent/ws/generate HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/agent/ws/generate", host: "localhost:9999"
2025/05/03 01:50:12 [error] 9#9: *1 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "GET /api/v1/base/userinfo HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/base/userinfo", host: "localhost:9999", referrer: "http://localhost:9999/testing/generator"
2025/05/03 01:50:12 [error] 9#9: *1 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "GET /api/v1/base/userinfo HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/base/userinfo", host: "localhost:9999", referrer: "http://localhost:9999/testing/generator"
2025/05/03 01:50:13 [error] 9#9: *6 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "GET /api/v1/base/usermenu HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/base/usermenu", host: "localhost:9999", referrer: "http://localhost:9999/testing/generator"
2025/05/03 01:50:13 [error] 9#9: *6 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "GET /api/v1/base/usermenu HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/base/usermenu", host: "localhost:9999", referrer: "http://localhost:9999/testing/generator"
2025/05/03 01:50:23 [error] 9#9: *11 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "POST /api/v1/auth/login HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/auth/login", host: "localhost:9999", referrer: "http://localhost:9999/login?redirect=/testing/generator"
2025/05/03 01:50:23 [error] 9#9: *11 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "POST /api/v1/auth/login HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/auth/login", host: "localhost:9999", referrer: "http://localhost:9999/login?redirect=/testing/generator"
2025/05/03 01:51:38 [error] 9#9: *19 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "POST /api/v1/auth/login HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/auth/login", host: "localhost:9999", referrer: "http://localhost:9999/login"
2025/05/03 01:51:38 [error] 9#9: *19 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "POST /api/v1/auth/login HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/auth/login", host: "localhost:9999", referrer: "http://localhost:9999/login"
2025/05/03 01:51:54 [error] 9#9: *21 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "POST /api/v1/auth/login HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/auth/login", host: "localhost:9999", referrer: "http://localhost:9999/login"
2025/05/03 01:51:54 [error] 9#9: *21 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "POST /api/v1/auth/login HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/auth/login", host: "localhost:9999", referrer: "http://localhost:9999/login"
2025/05/03 01:56:04 [error] 9#9: *5 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "POST /api/v1/auth/login HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/auth/login", host: "localhost:9999", referrer: "http://localhost:9999/login"
2025/05/03 01:56:04 [error] 9#9: *5 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "POST /api/v1/auth/login HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/auth/login", host: "localhost:9999", referrer: "http://localhost:9999/login"
2025/05/03 03:04:40 [error] 9#9: *5 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "POST /api/v1/auth/login HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/auth/login", host: "localhost:9999", referrer: "http://localhost:9999/login"
2025/05/03 03:04:40 [error] 9#9: *5 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "POST /api/v1/auth/login HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/auth/login", host: "localhost:9999", referrer: "http://localhost:9999/login"
2025/05/03 18:17:33 [error] 9#9: *4 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "GET /api/v1/base/userinfo HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/base/userinfo", host: "localhost:9999", referrer: "http://localhost:9999/workbench"
2025/05/03 18:17:33 [error] 9#9: *4 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "GET /api/v1/base/userinfo HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/base/userinfo", host: "localhost:9999", referrer: "http://localhost:9999/workbench"
2025/05/03 18:17:34 [error] 9#9: *4 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "GET /api/v1/base/usermenu HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/base/usermenu", host: "localhost:9999", referrer: "http://localhost:9999/workbench"
2025/05/03 18:17:34 [error] 9#9: *4 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "GET /api/v1/base/usermenu HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/base/usermenu", host: "localhost:9999", referrer: "http://localhost:9999/workbench"
2025/05/03 18:17:44 [error] 9#9: *7 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "POST /api/v1/auth/login HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/auth/login", host: "localhost:9999", referrer: "http://localhost:9999/login?redirect=/workbench"
2025/05/03 18:17:44 [error] 9#9: *7 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "POST /api/v1/auth/login HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/auth/login", host: "localhost:9999", referrer: "http://localhost:9999/login?redirect=/workbench"
2025/05/03 18:31:49 [notice] 26#26: signal process started
2025/05/03 18:32:08 [error] 32#32: *3 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "POST /api/v1/auth/login HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/auth/login", host: "localhost:9999", referrer: "http://localhost:9999/login?redirect=/workbench"
2025/05/03 18:32:08 [error] 32#32: *3 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "POST /api/v1/auth/login HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/auth/login", host: "localhost:9999", referrer: "http://localhost:9999/login?redirect=/workbench"
2025/05/03 18:32:34 [error] 32#32: *6 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "POST /api/v1/auth/login HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/auth/login", host: "localhost:9999", referrer: "http://localhost:9999/login?redirect=/workbench"
2025/05/03 18:32:34 [error] 32#32: *6 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: localhost, request: "POST /api/v1/auth/login HTTP/1.1", upstream: "http://127.0.0.1:9999/api/v1/auth/login", host: "localhost:9999", referrer: "http://localhost:9999/login?redirect=/workbench"
