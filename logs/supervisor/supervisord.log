2025-05-02 16:45:49,891 INFO Set uid to user 0 succeeded
2025-05-02 16:45:49,892 INFO supervisord started with pid 1
2025-05-02 16:45:50,906 INFO spawned: 'fastapi' with pid 7
2025-05-02 16:45:50,912 INFO spawned: 'nginx' with pid 8
2025-05-02 16:45:52,060 INFO success: fastapi entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-02 16:45:52,061 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-02 18:44:15,753 INFO Set uid to user 0 succeeded
2025-05-02 18:44:15,755 INFO supervisord started with pid 1
2025-05-02 18:44:16,759 INFO spawned: 'fastapi' with pid 7
2025-05-02 18:44:16,761 INFO spawned: 'nginx' with pid 8
2025-05-02 18:44:17,914 INFO success: fastapi entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-02 18:44:17,914 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-02 18:44:47,210 WARN received SIGTERM indicating exit request
2025-05-02 18:44:47,211 INFO waiting for fastapi, nginx to die
2025-05-02 18:44:47,232 INFO stopped: nginx (exit status 0)
2025-05-02 18:44:47,464 INFO stopped: fastapi (exit status 0)
2025-05-02 18:44:47,464 INFO reaped unknown pid 17 (exit status 0)
2025-05-02 21:03:53,868 INFO Set uid to user 0 succeeded
2025-05-02 21:03:53,869 INFO supervisord started with pid 1
2025-05-02 21:03:54,881 INFO spawned: 'fastapi' with pid 7
2025-05-02 21:03:54,885 INFO spawned: 'nginx' with pid 8
2025-05-02 21:03:56,104 INFO success: fastapi entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-02 21:03:56,105 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-02 21:04:12,612 WARN received SIGTERM indicating exit request
2025-05-02 21:04:12,612 INFO waiting for fastapi, nginx to die
2025-05-02 21:04:12,619 INFO stopped: nginx (exit status 0)
2025-05-02 21:04:12,831 INFO stopped: fastapi (exit status 0)
2025-05-02 21:04:12,832 INFO reaped unknown pid 17 (exit status 0)
2025-05-02 21:06:38,089 INFO Set uid to user 0 succeeded
2025-05-02 21:06:38,090 INFO supervisord started with pid 1
2025-05-02 21:06:39,095 INFO spawned: 'fastapi' with pid 7
2025-05-02 21:06:39,099 INFO spawned: 'nginx' with pid 8
2025-05-02 21:06:40,236 INFO success: fastapi entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-02 21:06:40,237 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-02 21:27:11,874 WARN received SIGTERM indicating exit request
2025-05-02 21:27:11,892 INFO waiting for fastapi, nginx to die
2025-05-02 21:27:12,991 INFO stopped: nginx (exit status 0)
2025-05-02 21:27:13,410 INFO stopped: fastapi (exit status 0)
2025-05-02 21:27:13,410 INFO reaped unknown pid 17 (exit status 0)
2025-05-02 21:27:21,216 INFO Set uid to user 0 succeeded
2025-05-02 21:27:21,218 INFO supervisord started with pid 1
2025-05-02 21:27:22,225 INFO spawned: 'fastapi' with pid 7
2025-05-02 21:27:22,228 INFO spawned: 'nginx' with pid 8
2025-05-02 21:27:23,483 INFO success: fastapi entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-02 21:27:23,483 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-02 21:28:55,413 WARN received SIGTERM indicating exit request
2025-05-02 21:28:55,415 INFO waiting for fastapi, nginx to die
2025-05-02 21:28:55,432 INFO stopped: nginx (exit status 0)
2025-05-02 21:28:55,701 INFO stopped: fastapi (exit status 0)
2025-05-02 21:28:55,701 INFO reaped unknown pid 17 (exit status 0)
2025-05-02 21:29:16,094 INFO Set uid to user 0 succeeded
2025-05-02 21:29:16,096 INFO supervisord started with pid 1
2025-05-02 21:29:17,106 INFO spawned: 'fastapi' with pid 7
2025-05-02 21:29:17,109 INFO spawned: 'nginx' with pid 8
2025-05-02 21:29:18,234 INFO success: fastapi entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-02 21:29:18,234 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-02 21:41:14,670 WARN received SIGTERM indicating exit request
2025-05-02 21:41:14,674 INFO waiting for fastapi, nginx to die
2025-05-02 21:41:14,704 INFO stopped: nginx (exit status 0)
2025-05-02 21:41:15,029 INFO stopped: fastapi (exit status 0)
2025-05-02 21:41:15,030 INFO reaped unknown pid 17 (exit status 0)
2025-05-02 21:44:38,220 INFO Set uid to user 0 succeeded
2025-05-02 21:44:38,222 INFO supervisord started with pid 1
2025-05-02 21:44:39,230 INFO spawned: 'fastapi' with pid 7
2025-05-02 21:44:39,232 INFO spawned: 'nginx' with pid 8
2025-05-02 21:44:40,445 INFO success: fastapi entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-02 21:44:40,445 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-02 21:52:05,292 WARN received SIGTERM indicating exit request
2025-05-02 21:52:05,304 INFO waiting for fastapi, nginx to die
2025-05-02 21:52:06,357 INFO stopped: nginx (exit status 0)
2025-05-02 21:52:06,805 INFO stopped: fastapi (exit status 0)
2025-05-02 21:52:06,805 INFO reaped unknown pid 17 (exit status 0)
2025-05-02 21:55:13,786 INFO Set uid to user 0 succeeded
2025-05-02 21:55:13,787 INFO supervisord started with pid 1
2025-05-02 21:55:14,790 INFO spawned: 'fastapi' with pid 7
2025-05-02 21:55:14,793 INFO spawned: 'nginx' with pid 8
2025-05-02 21:55:16,059 INFO success: fastapi entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-02 21:55:16,060 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-02 22:41:30,507 WARN received SIGTERM indicating exit request
2025-05-02 22:41:30,529 INFO waiting for fastapi, nginx to die
2025-05-02 22:41:31,636 INFO stopped: nginx (exit status 0)
2025-05-02 22:41:32,729 INFO stopped: fastapi (exit status 0)
2025-05-02 22:41:32,730 INFO reaped unknown pid 17 (exit status 0)
2025-05-02 22:45:46,743 INFO Set uid to user 0 succeeded
2025-05-02 22:45:46,744 INFO supervisord started with pid 1
2025-05-02 22:45:47,748 INFO spawned: 'fastapi' with pid 6
2025-05-02 22:45:47,751 INFO spawned: 'nginx' with pid 7
2025-05-02 22:45:48,883 INFO success: fastapi entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-02 22:45:48,884 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-02 23:10:56,899 WARN received SIGTERM indicating exit request
2025-05-02 23:10:56,907 INFO waiting for fastapi, nginx to die
2025-05-02 23:10:57,032 INFO stopped: nginx (exit status 0)
2025-05-02 23:10:57,302 INFO stopped: fastapi (exit status 0)
2025-05-02 23:10:57,302 INFO reaped unknown pid 16 (exit status 0)
2025-05-02 23:10:58,116 INFO Set uid to user 0 succeeded
2025-05-02 23:10:58,118 INFO supervisord started with pid 1
2025-05-02 23:10:59,125 INFO spawned: 'fastapi' with pid 13
2025-05-02 23:10:59,142 INFO spawned: 'nginx' with pid 14
2025-05-02 23:11:00,365 INFO success: fastapi entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-02 23:11:00,365 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-03 00:29:48,986 WARN received SIGTERM indicating exit request
2025-05-03 00:29:49,001 INFO waiting for fastapi, nginx to die
2025-05-03 00:29:49,049 INFO stopped: nginx (exit status 0)
2025-05-03 00:29:49,537 INFO stopped: fastapi (exit status 0)
2025-05-03 00:29:49,537 INFO reaped unknown pid 23 (exit status 0)
2025-05-03 01:50:05,016 INFO Set uid to user 0 succeeded
2025-05-03 01:50:05,018 INFO supervisord started with pid 1
2025-05-03 01:50:06,023 INFO spawned: 'fastapi' with pid 7
2025-05-03 01:50:06,028 INFO spawned: 'nginx' with pid 8
2025-05-03 01:50:07,561 INFO success: fastapi entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-03 01:50:07,561 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-03 01:54:51,157 WARN received SIGTERM indicating exit request
2025-05-03 01:54:51,159 INFO waiting for fastapi, nginx to die
2025-05-03 01:54:52,181 INFO stopped: nginx (exit status 0)
2025-05-03 01:54:52,261 INFO stopped: fastapi (exit status 0)
2025-05-03 01:54:52,262 INFO reaped unknown pid 17 (exit status 0)
2025-05-03 01:55:16,114 INFO Set uid to user 0 succeeded
2025-05-03 01:55:16,116 INFO supervisord started with pid 1
2025-05-03 01:55:17,126 INFO spawned: 'fastapi' with pid 7
2025-05-03 01:55:17,129 INFO spawned: 'nginx' with pid 8
2025-05-03 01:55:18,135 INFO success: fastapi entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-03 01:55:18,135 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-03 01:55:18,723 INFO exited: fastapi (exit status 1; not expected)
2025-05-03 01:55:19,734 INFO spawned: 'fastapi' with pid 17
2025-05-03 01:55:20,045 INFO exited: fastapi (exit status 1; not expected)
2025-05-03 01:55:21,056 INFO spawned: 'fastapi' with pid 18
2025-05-03 01:55:21,607 INFO exited: fastapi (exit status 1; not expected)
2025-05-03 01:55:23,617 INFO spawned: 'fastapi' with pid 19
2025-05-03 01:55:24,009 INFO exited: fastapi (exit status 1; not expected)
2025-05-03 01:55:27,023 INFO spawned: 'fastapi' with pid 20
2025-05-03 01:55:27,419 INFO exited: fastapi (exit status 1; not expected)
2025-05-03 01:55:28,425 INFO gave up: fastapi entered FATAL state, too many start retries too quickly
2025-05-03 02:04:32,620 WARN received SIGTERM indicating exit request
2025-05-03 02:04:32,626 INFO waiting for nginx to die
2025-05-03 02:04:33,653 INFO stopped: nginx (exit status 0)
2025-05-03 02:05:05,936 INFO Set uid to user 0 succeeded
2025-05-03 02:05:05,938 INFO supervisord started with pid 1
2025-05-03 02:05:06,962 INFO spawned: 'fastapi' with pid 7
2025-05-03 02:05:06,967 INFO spawned: 'nginx' with pid 8
2025-05-03 02:05:07,975 INFO success: fastapi entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-03 02:05:07,975 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-03 02:11:17,613 WARN received SIGTERM indicating exit request
2025-05-03 02:11:17,617 INFO waiting for fastapi, nginx to die
2025-05-03 02:11:18,636 INFO stopped: nginx (exit status 0)
2025-05-03 02:11:19,802 INFO stopped: fastapi (terminated by SIGTERM)
2025-05-03 02:11:40,761 INFO Set uid to user 0 succeeded
2025-05-03 02:11:40,763 INFO supervisord started with pid 1
2025-05-03 02:11:41,778 INFO spawned: 'fastapi' with pid 7
2025-05-03 02:11:41,782 INFO spawned: 'nginx' with pid 8
2025-05-03 02:11:42,787 INFO success: fastapi entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-03 02:11:42,787 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-03 02:15:54,070 WARN received SIGTERM indicating exit request
2025-05-03 02:15:54,077 INFO waiting for fastapi, nginx to die
2025-05-03 02:15:54,093 INFO stopped: nginx (exit status 0)
2025-05-03 02:15:55,212 INFO stopped: fastapi (terminated by SIGTERM)
2025-05-03 02:16:10,172 INFO Set uid to user 0 succeeded
2025-05-03 02:16:10,175 INFO supervisord started with pid 1
2025-05-03 02:16:11,181 INFO spawned: 'fastapi' with pid 6
2025-05-03 02:16:11,185 INFO spawned: 'nginx' with pid 7
2025-05-03 02:16:12,190 INFO success: fastapi entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-03 02:16:12,191 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-03 02:22:42,983 WARN received SIGTERM indicating exit request
2025-05-03 02:22:42,986 INFO waiting for fastapi, nginx to die
2025-05-03 02:22:44,013 INFO stopped: nginx (exit status 0)
2025-05-03 02:22:45,214 INFO stopped: fastapi (terminated by SIGTERM)
2025-05-03 02:22:58,255 INFO Set uid to user 0 succeeded
2025-05-03 02:22:58,258 INFO supervisord started with pid 1
2025-05-03 02:22:59,266 INFO spawned: 'fastapi' with pid 7
2025-05-03 02:22:59,273 INFO spawned: 'nginx' with pid 8
2025-05-03 02:23:00,277 INFO success: fastapi entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-03 02:23:00,277 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-03 02:44:52,461 WARN received SIGTERM indicating exit request
2025-05-03 02:44:52,470 INFO waiting for fastapi, nginx to die
2025-05-03 02:44:53,602 INFO stopped: nginx (exit status 0)
2025-05-03 02:44:54,870 INFO stopped: fastapi (terminated by SIGTERM)
2025-05-03 02:53:10,434 INFO Set uid to user 0 succeeded
2025-05-03 02:53:10,435 INFO supervisord started with pid 1
2025-05-03 02:53:11,449 INFO spawned: 'fastapi' with pid 6
2025-05-03 02:53:11,454 INFO spawned: 'nginx' with pid 7
2025-05-03 02:53:13,211 INFO success: fastapi entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-03 02:53:13,211 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-03 03:02:23,017 WARN received SIGTERM indicating exit request
2025-05-03 03:02:23,020 INFO waiting for fastapi, nginx to die
2025-05-03 03:02:24,059 INFO stopped: nginx (exit status 0)
2025-05-03 03:02:24,413 INFO stopped: fastapi (exit status 0)
2025-05-03 03:02:24,414 INFO reaped unknown pid 16 (exit status 0)
2025-05-03 03:04:22,947 INFO Set uid to user 0 succeeded
2025-05-03 03:04:22,952 INFO supervisord started with pid 1
2025-05-03 03:04:23,963 INFO spawned: 'fastapi' with pid 7
2025-05-03 03:04:23,969 INFO spawned: 'nginx' with pid 8
2025-05-03 03:04:25,499 INFO success: fastapi entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-03 03:04:25,499 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-03 03:10:51,769 WARN received SIGTERM indicating exit request
2025-05-03 03:10:51,777 INFO waiting for fastapi, nginx to die
2025-05-03 03:10:52,827 INFO stopped: nginx (exit status 0)
2025-05-03 03:10:53,010 INFO stopped: fastapi (exit status 0)
2025-05-03 03:10:53,011 INFO reaped unknown pid 17 (exit status 0)
2025-05-03 03:11:11,394 INFO Set uid to user 0 succeeded
2025-05-03 03:11:11,396 INFO supervisord started with pid 1
2025-05-03 03:11:12,413 INFO spawned: 'fastapi' with pid 7
2025-05-03 03:11:12,419 INFO spawned: 'nginx' with pid 8
2025-05-03 03:11:14,193 INFO success: fastapi entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-03 03:11:14,194 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-03 17:52:53,372 WARN received SIGTERM indicating exit request
2025-05-03 17:52:53,396 INFO waiting for fastapi, nginx to die
2025-05-03 17:52:54,509 INFO stopped: nginx (exit status 0)
2025-05-03 17:52:55,292 INFO stopped: fastapi (exit status 0)
2025-05-03 17:52:55,294 INFO reaped unknown pid 17 (exit status 0)
2025-05-03 18:17:19,991 INFO Set uid to user 0 succeeded
2025-05-03 18:17:19,993 INFO supervisord started with pid 1
2025-05-03 18:17:20,999 INFO spawned: 'fastapi' with pid 7
2025-05-03 18:17:21,003 INFO spawned: 'nginx' with pid 8
2025-05-03 18:17:21,724 INFO exited: fastapi (exit status 1; not expected)
2025-05-03 18:17:22,749 INFO spawned: 'fastapi' with pid 17
2025-05-03 18:17:22,751 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-03 18:17:22,884 INFO exited: fastapi (exit status 1; not expected)
2025-05-03 18:17:24,901 INFO spawned: 'fastapi' with pid 18
2025-05-03 18:17:25,033 INFO exited: fastapi (exit status 1; not expected)
2025-05-03 18:17:28,044 INFO spawned: 'fastapi' with pid 19
2025-05-03 18:17:28,129 INFO exited: fastapi (exit status 1; not expected)
2025-05-03 18:17:29,135 INFO gave up: fastapi entered FATAL state, too many start retries too quickly
2025-05-03 18:31:37,690 WARN received SIGTERM indicating exit request
2025-05-03 18:31:37,703 INFO waiting for nginx to die
2025-05-03 18:31:38,785 INFO stopped: nginx (exit status 0)
2025-05-03 18:31:40,372 INFO Set uid to user 0 succeeded
2025-05-03 18:31:40,375 INFO supervisord started with pid 1
2025-05-03 18:31:41,383 INFO spawned: 'fastapi' with pid 7
2025-05-03 18:31:41,388 INFO spawned: 'nginx' with pid 8
2025-05-03 18:31:42,150 INFO exited: fastapi (exit status 1; not expected)
2025-05-03 18:31:43,161 INFO spawned: 'fastapi' with pid 17
2025-05-03 18:31:43,162 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-03 18:31:43,336 INFO exited: fastapi (exit status 1; not expected)
2025-05-03 18:31:45,353 INFO spawned: 'fastapi' with pid 18
2025-05-03 18:31:45,491 INFO exited: fastapi (exit status 1; not expected)
2025-05-03 18:31:48,507 INFO spawned: 'fastapi' with pid 19
2025-05-03 18:31:48,635 INFO exited: fastapi (exit status 1; not expected)
2025-05-03 18:31:49,636 INFO gave up: fastapi entered FATAL state, too many start retries too quickly
2025-05-03 18:32:46,017 WARN received SIGTERM indicating exit request
2025-05-03 18:32:46,023 INFO waiting for nginx to die
2025-05-03 18:32:47,051 INFO stopped: nginx (exit status 0)
2025-05-03 18:32:50,687 INFO Set uid to user 0 succeeded
2025-05-03 18:32:50,691 INFO supervisord started with pid 1
2025-05-03 18:32:51,697 INFO spawned: 'fastapi' with pid 7
2025-05-03 18:32:51,701 INFO spawned: 'nginx' with pid 8
2025-05-03 18:32:52,956 INFO success: fastapi entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-03 18:32:52,957 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-06 11:28:38,244 INFO Set uid to user 0 succeeded
2025-05-06 11:28:38,246 INFO supervisord started with pid 1
2025-05-06 11:28:39,252 INFO spawned: 'fastapi' with pid 7
2025-05-06 11:28:39,254 INFO spawned: 'nginx' with pid 8
2025-05-06 11:28:40,559 INFO success: fastapi entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-06 11:28:40,562 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-05-06 11:28:49,279 WARN received SIGTERM indicating exit request
2025-05-06 11:28:49,280 INFO waiting for fastapi, nginx to die
2025-05-06 11:28:50,302 INFO stopped: nginx (exit status 0)
2025-05-06 11:28:50,592 INFO stopped: fastapi (exit status 0)
2025-05-06 11:28:50,593 INFO reaped unknown pid 17 (exit status 0)
