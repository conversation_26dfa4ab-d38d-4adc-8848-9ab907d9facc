import asyncio
import asyncpg

async def run():
    # 直接使用asyncpg连接数据库
    conn = await asyncpg.connect(
        user='admin',
        password='admin123',
        database='agent_testing',
        host='host.docker.internal',
        port=5432
    )
    
    try:
        # 执行SQL修改tags列类型
        await conn.execute('ALTER TABLE test_cases ALTER COLUMN tags TYPE VARCHAR(20);')
        print('表结构已更新：tags列类型已修改为VARCHAR(20)')
    except Exception as e:
        print(f'更新失败: {str(e)}')
    finally:
        # 关闭连接
        await conn.close()

if __name__ == "__main__":
    asyncio.run(run())
