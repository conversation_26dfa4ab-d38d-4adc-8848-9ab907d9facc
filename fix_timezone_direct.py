#!/usr/bin/env python
import asyncio
import sys
import pytz
from datetime import datetime
from tortoise import Tortoise, connections
from app.settings.config import settings

# 获取带时区的当前时间
def get_now_with_timezone():
    """获取带UTC时区的当前时间"""
    return datetime.now(pytz.UTC)

async def init():
    # 初始化Tortoise ORM
    await Tortoise.init(config=settings.TORTOISE_ORM)
    print("数据库连接初始化完成")

async def fix_timezone_direct():
    """直接修复数据库中的时区问题"""
    # 获取数据库连接
    conn = connections.get("default")

    # 1. 修改PostgreSQL会话时区设置
    await conn.execute_query("SET timezone TO 'UTC';")
    print("已将当前会话时区设置为UTC")

    # 2. 修复menu表中的时区问题
    try:
        # 检查表结构，确定created_at和updated_at的数据类型
        result = await conn.execute_query("""
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_name = 'menu' AND column_name IN ('created_at', 'updated_at');
        """)

        column_info = {}
        for row in result[1]:
            column_info[row[0]] = {"type": row[1], "nullable": row[2]}

        print(f"menu表字段信息: {column_info}")

        # 根据字段类型选择不同的修复方法
        for column, info in column_info.items():
            if "timestamp" in info["type"].lower():
                if "without time zone" in info["type"].lower():
                    # 对于timestamp without time zone类型，直接转换为timestamptz
                    print(f"将{column}从timestamp without time zone转换为timestamp with time zone")
                    await conn.execute_query(f"""
                    ALTER TABLE menu
                    ALTER COLUMN {column} TYPE timestamp with time zone
                    USING {column} AT TIME ZONE 'UTC';
                    """)
                    print(f"已修改menu表的{column}字段类型")
                else:
                    print(f"{column}已经是timestamp with time zone类型，无需修改")

        # 更新所有记录的时区信息 - 使用不带时区的时间
        now = datetime.utcnow()
        await conn.execute_query("""
        UPDATE menu
        SET updated_at = $1
        """, [now])
        print(f"已更新menu表的updated_at为 {now}")

    except Exception as e:
        print(f"修复menu表时出错: {e}")

    # 3. 修复requirement表中的时区问题
    try:
        # 检查表结构，确定created_at和updated_at的数据类型
        result = await conn.execute_query("""
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_name = 'requirement' AND column_name IN ('created_at', 'updated_at');
        """)

        column_info = {}
        for row in result[1]:
            column_info[row[0]] = {"type": row[1], "nullable": row[2]}

        print(f"requirement表字段信息: {column_info}")

        # 根据字段类型选择不同的修复方法
        for column, info in column_info.items():
            if "timestamp" in info["type"].lower():
                if "without time zone" in info["type"].lower():
                    # 对于timestamp without time zone类型，直接转换为timestamptz
                    print(f"将{column}从timestamp without time zone转换为timestamp with time zone")
                    await conn.execute_query(f"""
                    ALTER TABLE requirement
                    ALTER COLUMN {column} TYPE timestamp with time zone
                    USING {column} AT TIME ZONE 'UTC';
                    """)
                    print(f"已修改requirement表的{column}字段类型")
                else:
                    print(f"{column}已经是timestamp with time zone类型，无需修改")

        # 更新所有记录的时区信息
        await conn.execute_query("""
        UPDATE requirement
        SET updated_at = $1
        WHERE created_at IS NOT NULL
        """, [now])
        print("已更新requirement表的时区信息")

    except Exception as e:
        print(f"修复requirement表时出错: {e}")

    # 4. 修复requirement_doc表中的时区问题
    try:
        # 检查表结构，确定created_at和updated_at的数据类型
        result = await conn.execute_query("""
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_name = 'requirement_doc' AND column_name IN ('created_at', 'updated_at');
        """)

        column_info = {}
        for row in result[1]:
            column_info[row[0]] = {"type": row[1], "nullable": row[2]}

        print(f"requirement_doc表字段信息: {column_info}")

        # 根据字段类型选择不同的修复方法
        for column, info in column_info.items():
            if "timestamp" in info["type"].lower():
                if "without time zone" in info["type"].lower():
                    # 对于timestamp without time zone类型，直接转换为timestamptz
                    print(f"将{column}从timestamp without time zone转换为timestamp with time zone")
                    await conn.execute_query(f"""
                    ALTER TABLE requirement_doc
                    ALTER COLUMN {column} TYPE timestamp with time zone
                    USING {column} AT TIME ZONE 'UTC';
                    """)
                    print(f"已修改requirement_doc表的{column}字段类型")
                else:
                    print(f"{column}已经是timestamp with time zone类型，无需修改")

        # 更新所有记录的时区信息
        await conn.execute_query("""
        UPDATE requirement_doc
        SET updated_at = $1
        WHERE created_at IS NOT NULL
        """, [now])
        print("已更新requirement_doc表的时区信息")

    except Exception as e:
        print(f"修复requirement_doc表时出错: {e}")

    # 4. 修改数据库默认时区设置
    try:
        # 获取当前数据库名称
        result = await conn.execute_query("SELECT current_database();")
        db_name = result[1][0][0]
        print(f"当前数据库名称: {db_name}")

        # 修改数据库默认时区设置
        await conn.execute_query(f"ALTER DATABASE {db_name} SET timezone TO 'UTC';")
        print("已将数据库默认时区设置为UTC")
    except Exception as e:
        print(f"修改数据库默认时区设置时出错: {e}")

    print("时区修复完成")

async def main():
    try:
        await init()
        await fix_timezone_direct()
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(main())
