import asyncio
from tortoise import Tortoise, connections
from app.settings.config import TORTOISE_ORM
from app.schemas.testcases import CaseCreate, Priority, Status, TestCaseTag, TestStepCreate
from app.controllers.testcase import testcase_controller

async def test_create_testcase_with_large_requirement_id():
    # 初始化Tortoise ORM
    await Tortoise.init(config=TORTOISE_ORM)

    # 创建一个测试用例，使用大数值作为requirement_id
    large_requirement_id = 1143702532001004810  # 这是之前报错的ID

    # 创建测试步骤
    test_steps = [
        TestStepCreate(
            description="进入患者聊天窗口",
            expected_result="显示推送的商品卡片"
        ),
        TestStepCreate(
            description="检查商品卡片订单状态显示",
            expected_result="显示订单A的'付款成功'状态"
        )
    ]

    # 创建测试用例
    test_case = CaseCreate(
        title="验证存在付款成功订单时的状态判定",
        desc="当用户存在多个订单且包含付款成功的父订单时，系统应正确识别第一次付款成功的父订单状态",
        priority=Priority.HIGH,
        status=Status.NOT_STARTED,
        preconditions="用户已收到照护师推送的多个商品\n存在3个父订单：\n  - 订单A：付款成功(最早提交)\n  - 订单B：待付款\n  - 订单C：已取消",
        postconditions="商品卡片显示订单A的状态",
        tags=TestCaseTag.FUNCTIONAL_TEST,
        requirement_id=large_requirement_id,
        project_id=43702532,
        creator="朱海华",
        tapd_url="https://www.tapd.cn/tapd_fe/43702532/story/detail/1143702532001004810",
        steps=test_steps
    )

    try:
        # 创建测试用例
        print(f"开始创建测试用例，requirement_id = {large_requirement_id}")
        result = await testcase_controller.create_TestCase(test_case)
        print(f"测试用例创建成功，ID = {result}")

        # 验证创建的测试用例
        conn = connections.get("default")
        query_result = await conn.execute_query(
            "SELECT id, test_case_id, requirement_id FROM test_cases WHERE requirement_id = $1",
            [large_requirement_id]
        )

        if query_result[1]:
            print("\n验证结果:")
            for row in query_result[1]:
                print(f"ID: {row[0]}, test_case_id: {row[1]}, requirement_id: {row[2]}")
        else:
            print("\n未找到创建的测试用例")
    except Exception as e:
        print(f"测试用例创建失败: {str(e)}")

    # 关闭连接
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(test_create_testcase_with_large_requirement_id())
