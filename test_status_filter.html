<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试状态筛选功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        pre {
            background-color: #f1f1f1;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试用例状态筛选功能测试</h1>
        
        <div class="form-group">
            <label for="statusSelect">选择用例状态（多选）:</label>
            <select id="statusSelect" multiple size="5">
                <option value="未开始">未开始</option>
                <option value="进行中">进行中</option>
                <option value="通过">通过</option>
                <option value="失败">失败</option>
                <option value="阻塞">阻塞</option>
            </select>
            <small>按住 Ctrl (Windows) 或 Cmd (Mac) 键可以选择多个选项</small>
        </div>
        
        <div class="form-group">
            <button onclick="testAPI()">测试API调用</button>
            <button onclick="clearResults()">清除结果</button>
        </div>
        
        <div id="result" class="result" style="display: none;">
            <h3>测试结果:</h3>
            <div id="resultContent"></div>
        </div>
    </div>

    <script>
        function getSelectedStatuses() {
            const select = document.getElementById('statusSelect');
            const selected = [];
            for (let option of select.options) {
                if (option.selected) {
                    selected.push(option.value);
                }
            }
            return selected;
        }

        function showResult(content, type = 'info') {
            const resultDiv = document.getElementById('result');
            const contentDiv = document.getElementById('resultContent');
            
            resultDiv.style.display = 'block';
            resultDiv.className = `result ${type}`;
            contentDiv.innerHTML = content;
        }

        function clearResults() {
            document.getElementById('result').style.display = 'none';
        }

        async function testAPI() {
            const selectedStatuses = getSelectedStatuses();
            
            showResult('正在测试API调用...', 'info');
            
            try {
                // 构建查询参数
                let queryParams = 'page=1&page_size=10';
                
                // 添加状态参数
                if (selectedStatuses.length > 0) {
                    selectedStatuses.forEach(status => {
                        queryParams += `&status=${encodeURIComponent(status)}`;
                    });
                }
                
                console.log('查询参数:', queryParams);
                console.log('选中的状态:', selectedStatuses);
                
                // 发送请求
                const response = await fetch(`http://localhost:9999/api/v1/testcase/list?${queryParams}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwiaXNfc3VwZXJ1c2VyIjpmYWxzZX0.TxCU67MEoNpJHfcoveZnxwP8Q15iP97YC4jJlUBQgvM'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                
                const data = await response.json();
                
                let resultHtml = `
                    <h4>API调用成功!</h4>
                    <p><strong>选中的状态:</strong> ${selectedStatuses.length > 0 ? selectedStatuses.join(', ') : '无'}</p>
                    <p><strong>查询参数:</strong> ${queryParams}</p>
                    <p><strong>返回数据总数:</strong> ${data.total || 0}</p>
                    <p><strong>返回的测试用例数量:</strong> ${data.data ? data.data.length : 0}</p>
                `;
                
                if (data.data && data.data.length > 0) {
                    resultHtml += '<h4>返回的测试用例状态分布:</h4>';
                    const statusCount = {};
                    data.data.forEach(item => {
                        const status = item.status || '未知';
                        statusCount[status] = (statusCount[status] || 0) + 1;
                    });
                    
                    resultHtml += '<ul>';
                    for (const [status, count] of Object.entries(statusCount)) {
                        resultHtml += `<li>${status}: ${count} 个</li>`;
                    }
                    resultHtml += '</ul>';
                }
                
                resultHtml += `
                    <h4>完整响应数据:</h4>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
                showResult(resultHtml, 'success');
                
            } catch (error) {
                console.error('API调用失败:', error);
                showResult(`
                    <h4>API调用失败!</h4>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                    <p><strong>选中的状态:</strong> ${selectedStatuses.join(', ')}</p>
                    <p>请确保后端服务正在运行在 http://localhost:9999</p>
                `, 'error');
            }
        }
    </script>
</body>
</html>
