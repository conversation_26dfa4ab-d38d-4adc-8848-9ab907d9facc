// 插件安装或更新时执行
chrome.runtime.onInstalled.addListener(function() {
  console.log('TAPD Content Extractor 已安装/更新');
});

// 监听来自弹出窗口或内容脚本的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  // 处理获取Cookie的请求
  if (request.action === 'getTapdCookies') {
    chrome.cookies.getAll({domain: 'tapd.cn'}, function(cookies) {
      sendResponse({cookies: cookies});
    });
    return true; // 保持消息通道开放以支持异步响应
  }

  // 处理获取图片的请求
  if (request.action === 'fetchImage') {
    fetchImageWithCookies(request.url)
      .then(base64 => sendResponse({success: true, data: base64}))
      .catch(error => {
        console.error('Background: 获取图片失败', error);
        sendResponse({success: false, error: error.message});
      });
    return true; // 保持消息通道开放以支持异步响应
  }

  // 处理提取内容的请求
  if (request.action === 'extractContent') {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      if (tabs.length === 0) {
        sendResponse({error: '没有活动标签页'});
        return;
      }

      const activeTab = tabs[0];
      chrome.tabs.sendMessage(activeTab.id, {action: 'extractContent'}, function(response) {
        sendResponse(response);
      });
    });
    return true; // 保持消息通道开放以支持异步响应
  }
});

// 使用Cookie获取图片并转换为Base64
async function fetchImageWithCookies(url) {
  console.log('Background: 开始获取图片', url);

  try {
    // 处理TAPD特殊图片URL
    let processedUrl = url;

    // 处理compress图片URL，将其转换为原始图片URL
    if (url.includes('compress/compress_img') && url.includes('?src=')) {
      const srcMatch = url.match(/\?src=([^&]+)/);
      if (srcMatch && srcMatch[1]) {
        processedUrl = decodeURIComponent(srcMatch[1]);
        console.log(`Background: 处理compress图片URL，原始URL: ${processedUrl}`);
      }
    }

    // 获取所有TAPD相关的Cookie
    const cookies = await new Promise(resolve => {
      chrome.cookies.getAll({domain: 'tapd.cn'}, resolve);
    });

    // 构建Cookie字符串
    const cookieString = cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');
    console.log('Background: 获取到Cookie数量', cookies.length);

    // 检查是否有必要的cookie
    const hasTapdSession = cookieString.includes('tapdsession');
    const hasTU = cookieString.includes('t_u');
    console.log(`Background: 是否包含tapdsession: ${hasTapdSession}, 是否包含t_u: ${hasTU}`);

    if (!hasTapdSession || !hasTU) {
      console.warn('Background: 缺少必要的cookie，可能无法正确获取图片');
    }

    // 发送请求
    const response = await fetch(processedUrl, {
      method: 'GET',
      headers: {
        'Cookie': cookieString,
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
        'Referer': 'https://www.tapd.cn/',
        'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
        'Origin': 'https://www.tapd.cn',
        'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'Sec-Fetch-Dest': 'image',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site'
      },
      credentials: 'include',
      redirect: 'follow' // 自动跟随重定向
    });

    if (!response.ok) {
      console.warn(`Background: HTTP响应不成功: ${response.status}`);
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const blob = await response.blob();

    // 检查blob类型
    if (!blob.type.startsWith('image/')) {
      console.warn(`Background: 获取的内容不是图片，而是 ${blob.type}，大小: ${blob.size} 字节`);
      throw new Error(`获取的内容不是图片，而是 ${blob.type}`);
    }

    console.log(`Background: 成功获取图片，类型: ${blob.type}，大小: ${blob.size} 字节`);

    // 转换为Base64
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.error('Background: 获取图片失败', error);
    throw error;
  }
}

// 当标签页更新时，检查是否是TAPD页面
chrome.tabs.onUpdated.addListener(function(tabId, changeInfo, tab) {
  if (changeInfo.status === 'complete' && tab.url && tab.url.includes('tapd.cn')) {
    // 更新图标状态，表示当前页面是TAPD
    chrome.action.setBadgeText({
      text: 'TAPD',
      tabId: tabId
    });
    chrome.action.setBadgeBackgroundColor({
      color: '#4285F4',
      tabId: tabId
    });
  } else if (changeInfo.status === 'complete') {
    // 清除图标状态
    chrome.action.setBadgeText({
      text: '',
      tabId: tabId
    });
  }
});
