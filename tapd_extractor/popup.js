// 当弹出窗口加载完成时执行
document.addEventListener('DOMContentLoaded', function() {
  // 获取DOM元素
  const statusMessage = document.getElementById('statusMessage');
  const extractButton = document.getElementById('extractButton');
  const appUrlInput = document.getElementById('appUrlInput');
  const saveUrlButton = document.getElementById('saveUrlButton');
  const tokenInput = document.getElementById('tokenInput');
  const saveTokenButton = document.getElementById('saveTokenButton');

  // 从存储中加载应用URL、令牌和Base64编码设置
  chrome.storage.sync.get(['appUrl', 'token', 'enableBase64'], function(result) {
    if (result.appUrl) {
      appUrlInput.value = result.appUrl;
    }
    if (result.token) {
      tokenInput.value = result.token;
    } else {
      tokenInput.value = 'dev'; // 默认值
    }

    // 设置Base64编码选项
    const enableBase64Checkbox = document.getElementById('enableBase64');
    if (enableBase64Checkbox) {
      enableBase64Checkbox.checked = result.enableBase64 !== false; // 默认启用

      // 添加变更事件
      enableBase64Checkbox.addEventListener('change', function() {
        chrome.storage.sync.set({enableBase64: enableBase64Checkbox.checked});
        updateStatus('success', `图片Base64编码已${enableBase64Checkbox.checked ? '启用' : '禁用'}`);
      });
    }
  });

  // 检查当前标签页是否是TAPD页面
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    const currentUrl = tabs[0].url;
    if (!currentUrl.includes('tapd.cn')) {
      updateStatus('warning', '当前页面不是TAPD页面，请在TAPD页面使用此插件');
      extractButton.disabled = true;
    }
  });

  // 提取按钮点击事件
  extractButton.addEventListener('click', function() {
    // 获取应用URL
    const appUrl = appUrlInput.value.trim();
    if (!appUrl) {
      updateStatus('error', '请先设置应用接收URL');
      return;
    }

    // 获取当前标签页
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      const currentTab = tabs[0];

      // 更新状态
      updateStatus('', '正在提取内容...');

      // 获取TAPD的Cookie
      chrome.cookies.getAll({domain: 'tapd.cn'}, function(cookies) {
        // 构建Cookie字符串
        const cookieString = cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');

        // 获取Base64编码设置
        const enableBase64Checkbox = document.getElementById('enableBase64');
        const enableBase64 = enableBase64Checkbox ? enableBase64Checkbox.checked : true;

        // 先执行脚本注入，然后发送消息
        chrome.scripting.executeScript({
          target: {tabId: currentTab.id},
          files: ['content.js']
        }, function() {
          // 脚本注入后，发送消息请求提取内容
          setTimeout(() => { // 添加小延迟，确保脚本已加载
            addDebugInfo(`开始提取内容，启用Base64编码: ${enableBase64}`);

            chrome.tabs.sendMessage(currentTab.id, {
              action: 'extractContent',
              settings: {
                enableBase64: enableBase64
              }
            }, function(response) {
              // 检查是否有错误
              if (chrome.runtime.lastError) {
                const errorMsg = '内容提取失败: ' + chrome.runtime.lastError.message;
                updateStatus('error', errorMsg);
                addDebugInfo(errorMsg, 'error');
                return;
              }

              // 检查响应
              if (!response) {
                const errorMsg = '内容提取失败: 无响应';
                updateStatus('error', errorMsg);
                addDebugInfo(errorMsg, 'error');
                return;
              }

              if (response.error) {
                const errorMsg = '内容提取失败: ' + response.error;
                updateStatus('error', errorMsg);
                addDebugInfo(errorMsg, 'error');
                return;
              }

              // 检查是否成功
              if (!response.success) {
                const errorMsg = '内容提取失败: 未标记为成功';
                updateStatus('error', errorMsg);
                addDebugInfo(errorMsg, 'error');
                return;
              }

              // 检查是否有内容
              if (!response.content) {
                const errorMsg = '内容提取失败: 无内容';
                updateStatus('error', errorMsg);
                addDebugInfo(errorMsg, 'error');
                return;
              }

              // 检查是否有Base64图片
              const hasBase64Images = response.content.includes('data:image');
              addDebugInfo(`提取内容成功，${hasBase64Images ? '包含' : '不包含'}Base64图片`);

              // 获取提取的内容
              const content = response;

              // 准备发送的数据
              const data = {
                url: currentTab.url,
                title: content.title,
                content: content.content,
                cookies: cookieString,
                user_id: 1,  // 默认用户ID
                project_id: 2  // 默认项目ID
              };

              // 发送数据到应用
              sendToApp(appUrl, data);
            });
          }, 100); // 100毫秒的延迟
        });
      });
    });
  });

  // 保存URL按钮点击事件
  saveUrlButton.addEventListener('click', function() {
    const appUrl = appUrlInput.value.trim();
    if (!appUrl) {
      updateStatus('error', '请输入有效的应用URL');
      return;
    }

    // 保存到存储
    chrome.storage.sync.set({appUrl: appUrl}, function() {
      updateStatus('success', '应用URL已保存');
    });
  });

  // 保存令牌按钮点击事件
  saveTokenButton.addEventListener('click', function() {
    const token = tokenInput.value.trim();
    if (!token) {
      updateStatus('error', '请输入有效的认证令牌');
      return;
    }

    // 保存到存储
    chrome.storage.sync.set({token: token}, function() {
      updateStatus('success', '认证令牌已保存');
    });
  });

  // 更新状态消息
  function updateStatus(type, message) {
    statusMessage.className = 'status ' + type;
    statusMessage.textContent = message;
  }

  // 发送数据到应用
  function sendToApp(url, data) {
    // 尝试从存储中获取令牌，但不在头部中使用它，避免CORS问题
    chrome.storage.sync.get(['token'], function(result) {
      // 将token添加到请求体中，而不是头部
      data.token = result.token || 'dev';

      fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
          // 移除token头部，避免CORS问题
        },
        body: JSON.stringify(data)
      })
      .then(response => {
        if (response.ok) {
          return response.json();
        }
        throw new Error('请求失败，状态码: ' + response.status);
      })
      .then(data => {
        updateStatus('success', '内容已成功发送到应用');
        console.log('应用响应:', data);
      })
      .catch(error => {
        updateStatus('error', '发送失败: ' + error.message);
        console.error('发送错误:', error);
      });
    });
  }
});

// 这些函数已经移动到content.js文件中
// 添加调试信息函数
function addDebugInfo(message, type = 'info') {
  const debugContainer = document.getElementById('debug-info');
  if (!debugContainer) {
    const container = document.createElement('div');
    container.id = 'debug-info';
    container.style.marginTop = '10px';
    container.style.padding = '10px';
    container.style.border = '1px solid #ccc';
    container.style.borderRadius = '4px';
    container.style.maxHeight = '200px';
    container.style.overflow = 'auto';
    container.style.fontSize = '12px';
    container.style.fontFamily = 'monospace';
    document.body.appendChild(container);
  }

  const debugContainer2 = document.getElementById('debug-info');
  const msgElement = document.createElement('div');
  msgElement.style.margin = '2px 0';
  msgElement.style.color = type === 'error' ? 'red' : (type === 'warning' ? 'orange' : 'green');
  msgElement.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
  debugContainer2.appendChild(msgElement);
  debugContainer2.scrollTop = debugContainer2.scrollHeight;
}
