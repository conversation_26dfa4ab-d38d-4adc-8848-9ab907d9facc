# Build frontend
FROM node:18.12.0-alpine3.16 AS web-builder
WORKDIR /app

# Copy package files first
COPY web/package.json web/pnpm-lock.yaml ./

# Install dependencies
RUN npm install -g pnpm && \
    pnpm install

# Copy only necessary web files
COPY web .

# Build the application
RUN pnpm build

# Build backend
FROM python:3.11-slim-bullseye AS builder
WORKDIR /app

# Install build dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        gcc \
        python3-dev && \
    rm -rf /var/lib/apt/lists/*

# Copy requirements file
COPY requirements.txt .

# Install dependencies into a virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"
RUN pip install --no-cache-dir -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

# Final image
FROM python:3.11-slim-bullseye
WORKDIR /app

# Set timezone
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

# Install only runtime dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        nginx \
        supervisor && \
    rm -rf /var/lib/apt/lists/*

# Copy virtual environment from builder
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy frontend build
COPY --from=web-builder /app/dist /app/web/dist

# Copy only necessary backend files
COPY app /app/app
COPY run.py /app/
COPY deploy/web.conf /etc/nginx/conf.d/default.conf
COPY deploy/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Remove default nginx site
RUN rm -f /etc/nginx/sites-enabled/default

# Set environment variables
ENV LANG=zh_CN.UTF-8
ENV DB_HOST=host.docker.internal
ENV DB_PORT=5432
ENV DB_USER=admin
ENV DB_PASSWORD=admin123
ENV DB_NAME=agent_testing
ENV POSTGRES_HOST=host.docker.internal
ENV POSTGRES_PORT=5432
ENV POSTGRES_USER=admin
ENV POSTGRES_PASSWORD=admin123
ENV POSTGRES_DB=agent_testing
ENV API_PORT=9999

# Create necessary directories and set permissions
RUN mkdir -p /run/nginx \
    && mkdir -p /var/log/nginx \
    && mkdir -p /var/log/supervisor \
    && mkdir -p /var/log/app \
    && mkdir -p /app/logs \
    && chown -R www-data:www-data /app/web/dist \
    && chown -R www-data:www-data /var/log/nginx \
    && chown -R www-data:www-data /var/log/app \
    && chown -R www-data:www-data /app/logs \
    && chown -R www-data:www-data /var/log/supervisor \
    && chown -R www-data:www-data /run/nginx

EXPOSE 80

# Start supervisor (which will start both nginx and fastapi)
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
