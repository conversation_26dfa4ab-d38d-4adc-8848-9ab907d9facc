-- 查找测试模块的父菜单ID
DO $$
DECLARE
    testing_parent_id INT;
    max_order INT;
    knowledge_menu_exists INT;
BEGIN
    -- 查找测试模块的父菜单ID
    SELECT id INTO testing_parent_id FROM menu WHERE name = '测试管理' AND parent_id = 0 LIMIT 1;
    
    -- 如果测试管理菜单不存在，则创建它
    IF testing_parent_id IS NULL THEN
        -- 查找最大的排序值
        SELECT COALESCE(MAX(order), 0) INTO max_order FROM menu WHERE parent_id = 0;
        
        -- 创建测试管理父菜单
        INSERT INTO menu (
            name, menu_type, icon, path, order, parent_id, is_hidden, component, keepalive, redirect, created_at, updated_at
        ) VALUES (
            '测试管理', 'catalog', 'carbon:test-tool', '/testing', max_order + 1, 0, false, 'Layout', false, '/testing/projects', NOW(), NOW()
        ) RETURNING id INTO testing_parent_id;
        
        RAISE NOTICE '创建了测试管理父菜单，ID: %', testing_parent_id;
    ELSE
        RAISE NOTICE '找到测试管理父菜单，ID: %', testing_parent_id;
    END IF;
    
    -- 检查知识库菜单是否已存在
    SELECT COUNT(*) INTO knowledge_menu_exists FROM menu WHERE name = '项目知识库' AND parent_id = testing_parent_id;
    
    -- 如果知识库菜单不存在，则创建它
    IF knowledge_menu_exists = 0 THEN
        -- 查找测试管理下最大的排序值
        SELECT COALESCE(MAX(order), 0) INTO max_order FROM menu WHERE parent_id = testing_parent_id;
        
        -- 创建知识库菜单
        INSERT INTO menu (
            name, menu_type, icon, path, order, parent_id, is_hidden, component, keepalive, redirect, created_at, updated_at
        ) VALUES (
            '项目知识库', 'menu', 'mdi:book-open-page-variant', 'knowledge', max_order + 1, testing_parent_id, false, '/testing/knowledge', true, NULL, NOW(), NOW()
        );
        
        RAISE NOTICE '创建了项目知识库菜单';
    ELSE
        RAISE NOTICE '项目知识库菜单已存在';
    END IF;
END $$;
