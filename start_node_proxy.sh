#!/bin/bash

# 检查是否安装了 Node.js
if ! command -v node &> /dev/null; then
    echo "Node.js 未安装，请先安装 Node.js"
    echo "在 macOS 上，可以使用 brew install node 安装"
    echo "在 Ubuntu 上，可以使用 sudo apt-get install nodejs 安装"
    exit 1
fi

# 检查是否安装了 http-proxy 模块
if ! node -e "require('http-proxy')" &> /dev/null; then
    echo "安装 http-proxy 模块..."
    npm install http-proxy
fi

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# 启动 Node.js 代理服务器
echo "启动 Node.js 代理服务器..."
node "$SCRIPT_DIR/node_proxy.js"
