# 使用多阶段构建和本地依赖缓存的优化版Dockerfile

# 前端构建阶段
FROM node:20-alpine AS web-builder
WORKDIR /app

# 复制前端代码
COPY web .

# 使用本地缓存的依赖（如果存在）
COPY dependency_cache/npm/store /root/.local/share/pnpm/store
COPY web/node_modules ./node_modules

# 配置npm和pnpm
RUN npm config set registry https://registry.npmmirror.com/ && \
    npm install -g pnpm@latest && \
    pnpm config set registry https://registry.npmmirror.com/

# 构建前端应用
RUN NODE_OPTIONS="--max-old-space-size=4096" pnpm build

# 后端构建阶段
FROM python:3.11-slim-bullseye AS builder
WORKDIR /app

# 使用中国的Debian镜像源
RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list && \
    sed -i 's/security.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list && \
    apt-get update && \
    apt-get install -y --no-install-recommends \
        gcc \
        python3-dev && \
    rm -rf /var/lib/apt/lists/*

# 创建虚拟环境
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 复制requirements文件
COPY requirements.txt .

# 使用本地缓存的pip包（如果存在）
COPY dependency_cache/pip/packages /pip-packages

# 从本地安装依赖
RUN if [ -d "/pip-packages" ] && [ "$(ls -A /pip-packages)" ]; then \
        pip install --no-index --find-links=/pip-packages -r requirements.txt; \
    else \
        pip install --no-cache-dir -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple --timeout 300; \
    fi

# 创建缓存目录
RUN mkdir -p /root/.cache/torch/sentence_transformers
RUN mkdir -p /root/.cache/huggingface

# 最终镜像
FROM python:3.11-slim-bullseye
WORKDIR /app

# 设置时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

# 使用中国的Debian镜像源
RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list && \
    sed -i 's/security.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list && \
    apt-get update && \
    apt-get install -y --no-install-recommends \
        nginx \
        supervisor && \
    rm -rf /var/lib/apt/lists/*

# 复制虚拟环境
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 设置环境变量
ENV HF_ENDPOINT=https://hf-mirror.com
ENV SENTENCE_TRANSFORMERS_HOME=/root/.cache/torch/sentence_transformers
ENV TRANSFORMERS_CACHE=/root/.cache/huggingface

# 复制前端构建
COPY --from=web-builder /app/dist /app/web/dist

# 复制后端文件
COPY app /app/app
COPY run.py /app/
COPY deploy/web.conf /etc/nginx/conf.d/default.conf
COPY deploy/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# 删除默认nginx站点
RUN rm -f /etc/nginx/sites-enabled/default

# 设置环境变量
ENV LANG=zh_CN.UTF-8
# 数据库配置
ENV DB_HOST=host.docker.internal
ENV DB_PORT=5432
ENV DB_USER=admin
ENV DB_PASSWORD=admin123
ENV DB_NAME=agent_testing
# 兼容性PostgreSQL环境变量
ENV POSTGRES_HOST=${DB_HOST}
ENV POSTGRES_PORT=${DB_PORT}
ENV POSTGRES_USER=${DB_USER}
ENV POSTGRES_PASSWORD=${DB_PASSWORD}
ENV POSTGRES_DB=${DB_NAME}
# API配置
ENV API_PORT=9999
ENV LOG_LEVEL=INFO
# JWT配置
ENV SECRET_KEY=3488a63e1765035d386f05409663f55c83bfae3b3c61a932744b20ad14244dcf
ENV JWT_ALGORITHM=HS256
ENV JWT_EXPIRE_MINUTES=10080
# 应用名称
ENV APP_NAME=agent_testing
# LLM配置 - DeepSeek
ENV DEFAULT_LLM_MODEL=deepseek-chat
ENV LLM_API_BASE=https://api.deepseek.com/v1
ENV LLM_API_KEY=***********************************
ENV LLM_TIMEOUT_SECONDS=120
ENV LLM_MAX_RETRIES=3
# LLM配置 - DashScope (阿里云)
ENV DASHSCOPE_API_BASE=https://dashscope.aliyuncs.com/compatible-mode/v1
ENV DASHSCOPE_API_KEY=""
ENV DASHSCOPE_MODEL=deepseek-v3
# LLM配置 - Qwen VL (阿里云视觉模型)
ENV QWEN_VL_MODEL=qwen-vl-plus-latest
ENV QWEN_VL_API_BASE=https://dashscope.aliyuncs.com/compatible-mode/v1
ENV QWEN_VL_API_KEY=sk-85477c3eb0424bb89d5421d2b28d2051

# 创建必要的目录并设置权限
RUN mkdir -p /run/nginx \
    && mkdir -p /var/log/nginx \
    && mkdir -p /var/log/supervisor \
    && mkdir -p /var/log/app \
    && mkdir -p /app/logs \
    && chown -R www-data:www-data /app/web/dist \
    && chown -R www-data:www-data /var/log/nginx \
    && chown -R www-data:www-data /var/log/app \
    && chown -R www-data:www-data /app/logs \
    && chown -R www-data:www-data /var/log/supervisor \
    && chown -R www-data:www-data /run/nginx

EXPOSE 80

# 启动supervisor
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
