#!/usr/bin/env python
"""
清空目标数据库中的所有表和序列

此脚本用于在迁移数据库之前，清空目标数据库中的所有表和序列。
"""

import sys
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

def connect_db(host, port, user, password, dbname):
    """连接到PostgreSQL数据库"""
    conn_params = {
        'host': host,
        'port': port,
        'user': user,
        'password': password,
        'dbname': dbname
    }
    
    print(f"尝试连接到数据库: {host}:{port}/{dbname}")
    
    try:
        conn = psycopg2.connect(**conn_params)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        print(f"数据库连接成功: {host}:{port}/{dbname}")
        return conn
    except Exception as e:
        print(f"数据库连接错误: {str(e)}")
        import traceback
        print(traceback.format_exc())
        sys.exit(1)

def clean_database(conn):
    """清空数据库中的所有表和序列"""
    try:
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("""
            SELECT tablename FROM pg_tables 
            WHERE schemaname = 'public';
        """)
        tables = [row[0] for row in cursor.fetchall()]
        
        if not tables:
            print("数据库中没有表，无需清空")
            return
        
        print(f"找到 {len(tables)} 个表: {', '.join(tables)}")
        
        # 禁用外键约束
        cursor.execute("SET CONSTRAINTS ALL DEFERRED;")
        
        # 删除所有表
        for table in tables:
            print(f"删除表: {table}")
            cursor.execute(f'DROP TABLE IF EXISTS "{table}" CASCADE;')
        
        # 获取所有序列
        cursor.execute("""
            SELECT sequence_name FROM information_schema.sequences 
            WHERE sequence_schema = 'public';
        """)
        sequences = [row[0] for row in cursor.fetchall()]
        
        if sequences:
            print(f"找到 {len(sequences)} 个序列: {', '.join(sequences)}")
            
            # 删除所有序列
            for sequence in sequences:
                print(f"删除序列: {sequence}")
                cursor.execute(f'DROP SEQUENCE IF EXISTS "{sequence}" CASCADE;')
        
        cursor.close()
        print("数据库清空完成")
    except Exception as e:
        print(f"清空数据库错误: {str(e)}")
        import traceback
        print(traceback.format_exc())
        sys.exit(1)

def main():
    """主函数"""
    if len(sys.argv) < 6:
        print("用法: python clean_target_db.py <host> <port> <user> <password> <dbname>")
        sys.exit(1)
    
    host = sys.argv[1]
    port = int(sys.argv[2])
    user = sys.argv[3]
    password = sys.argv[4]
    dbname = sys.argv[5]
    
    # 连接到数据库
    conn = connect_db(host, port, user, password, dbname)
    
    # 清空数据库
    clean_database(conn)
    
    # 关闭连接
    conn.close()
    print("操作完成")

if __name__ == "__main__":
    main()
