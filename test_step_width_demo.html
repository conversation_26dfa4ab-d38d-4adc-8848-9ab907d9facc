<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试步骤输入框宽度优化演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 24px;
        }

        .demo-title {
            font-size: 24px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 24px;
            text-align: center;
        }

        .demo-section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #6366f1;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e5e7eb;
        }

        /* 模拟原始样式 */
        .original-style .test-step-row {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            padding: 16px 0;
            border-bottom: 1px solid #f3f4f6;
            margin-bottom: 16px;
        }

        .original-style .step-number {
            width: 40px;
            font-weight: 600;
            color: #6366f1;
            flex-shrink: 0;
            text-align: center;
            padding-top: 8px;
            font-size: 16px;
        }

        .original-style .step-operation {
            flex: 1;
            min-width: 0;
        }

        .original-style .step-connector {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6366f1;
            width: 40px;
            flex-shrink: 0;
            padding-top: 8px;
        }

        .original-style .step-expected {
            flex: 1;
            min-width: 0;
        }

        .original-style .step-actions {
            width: 40px;
            display: flex;
            align-items: flex-start;
            justify-content: center;
            flex-shrink: 0;
            padding-top: 8px;
        }

        /* 优化后的样式 */
        .optimized-style .test-step-row {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            padding: 16px 0;
            border-bottom: 1px solid #f3f4f6;
            margin-bottom: 16px;
        }

        .optimized-style .step-number {
            width: 40px;
            font-weight: 600;
            color: #6366f1;
            flex-shrink: 0;
            text-align: center;
            padding-top: 8px;
            font-size: 16px;
        }

        .optimized-style .step-operation {
            width: 33.33%;
            min-width: 330px;
            flex-shrink: 0;
        }

        .optimized-style .step-connector {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6366f1;
            width: 40px;
            flex-shrink: 0;
            padding-top: 8px;
        }

        .optimized-style .step-expected {
            width: 33.33%;
            min-width: 330px;
            flex-shrink: 0;
        }

        .optimized-style .step-actions {
            width: 40px;
            display: flex;
            align-items: flex-start;
            justify-content: center;
            flex-shrink: 0;
            padding-top: 8px;
        }

        /* 通用输入框样式 */
        .form-textarea {
            width: 100%;
            min-height: 60px;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            line-height: 1.5;
            resize: vertical;
            font-family: inherit;
            transition: border-color 0.2s;
        }

        .form-textarea:focus {
            outline: none;
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .delete-btn {
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 4px;
            width: 24px;
            height: 24px;
            cursor: pointer;
            font-size: 12px;
        }

        .delete-btn:hover {
            background: #dc2626;
        }

        .comparison-note {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 16px;
            color: #0c4a6e;
        }

        .highlight {
            background: #fef3c7;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 600;
        }

        /* 响应式设计演示 */
        @media (max-width: 1200px) {
            .optimized-style .step-operation,
            .optimized-style .step-expected {
                width: auto;
                flex: 1;
                min-width: 250px;
            }
        }

        @media (max-width: 1400px) {
            .optimized-style .test-step-row {
                flex-direction: column;
                gap: 12px;
            }

            .optimized-style .step-number,
            .optimized-style .step-connector,
            .optimized-style .step-actions {
                width: 100%;
                text-align: left;
                padding-top: 0;
            }

            .optimized-style .step-operation,
            .optimized-style .step-expected {
                width: 100%;
                min-width: 0;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">测试步骤输入框宽度优化对比演示</h1>
        
        <div class="demo-section original-style">
            <h2 class="section-title">修改前：弹性布局（flex: 1）</h2>
            <div class="comparison-note">
                原始样式中，操作步骤和预期效果输入框使用 <span class="highlight">flex: 1</span>，平均分配剩余空间，在宽屏幕上可能过宽，影响用户体验。
            </div>
            
            <div class="test-step-row">
                <div class="step-number">1.</div>
                <div class="step-operation">
                    <textarea class="form-textarea" placeholder="请输入操作步骤">打开浏览器，访问系统登录页面</textarea>
                </div>
                <div class="step-connector">→</div>
                <div class="step-expected">
                    <textarea class="form-textarea" placeholder="请输入预期结果">页面正常加载，显示登录表单，包含用户名输入框、密码输入框和登录按钮</textarea>
                </div>
                <div class="step-actions">
                    <button class="delete-btn">×</button>
                </div>
            </div>
            
            <div class="test-step-row">
                <div class="step-number">2.</div>
                <div class="step-operation">
                    <textarea class="form-textarea" placeholder="请输入操作步骤">在用户名输入框中输入有效的用户名</textarea>
                </div>
                <div class="step-connector">→</div>
                <div class="step-expected">
                    <textarea class="form-textarea" placeholder="请输入预期结果">用户名正确显示在输入框中，无格式错误提示</textarea>
                </div>
                <div class="step-actions">
                    <button class="delete-btn">×</button>
                </div>
            </div>
        </div>

        <div class="demo-section optimized-style">
            <h2 class="section-title">修改后：固定宽度（33.33%）</h2>
            <div class="comparison-note">
                优化后的样式中，操作步骤和预期效果输入框使用 <span class="highlight">width: 33.33%</span> 和 <span class="highlight">min-width: 330px</span>，确保输入框有适中的宽度，提升用户体验。
            </div>
            
            <div class="test-step-row">
                <div class="step-number">1.</div>
                <div class="step-operation">
                    <textarea class="form-textarea" placeholder="请输入操作步骤">打开浏览器，访问系统登录页面</textarea>
                </div>
                <div class="step-connector">→</div>
                <div class="step-expected">
                    <textarea class="form-textarea" placeholder="请输入预期结果">页面正常加载，显示登录表单，包含用户名输入框、密码输入框和登录按钮</textarea>
                </div>
                <div class="step-actions">
                    <button class="delete-btn">×</button>
                </div>
            </div>
            
            <div class="test-step-row">
                <div class="step-number">2.</div>
                <div class="step-operation">
                    <textarea class="form-textarea" placeholder="请输入操作步骤">在用户名输入框中输入有效的用户名</textarea>
                </div>
                <div class="step-connector">→</div>
                <div class="step-expected">
                    <textarea class="form-textarea" placeholder="请输入预期结果">用户名正确显示在输入框中，无格式错误提示</textarea>
                </div>
                <div class="step-actions">
                    <button class="delete-btn">×</button>
                </div>
            </div>
        </div>

        <div style="background: #f9fafb; padding: 16px; border-radius: 6px; margin-top: 24px;">
            <h3 style="margin-top: 0; color: #374151;">优化要点总结：</h3>
            <ul style="color: #6b7280; line-height: 1.6;">
                <li><strong>固定宽度：</strong>将输入框宽度设置为模态窗口宽度的1/3（33.33%）</li>
                <li><strong>最小宽度：</strong>设置 min-width: 330px 确保在较小屏幕上仍有足够空间</li>
                <li><strong>响应式设计：</strong>在1200px以下屏幕改为弹性布局，在1400px以下改为垂直布局</li>
                <li><strong>用户体验：</strong>避免输入框过宽或过窄，提供适中的编辑空间</li>
            </ul>
        </div>
    </div>
</body>
</html>
