#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import logging
import requests
import base64
from bs4 import BeautifulSoup
from app.api.v1.reqAgent.image_markdown_converter import ImageMarkdownConverter

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_tapd_image")

def download_image(image_url):
    """下载图片并转换为Base64编码"""
    try:
        # 添加User-Agent和Referer头，模拟浏览器请求
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://www.tapd.cn/'
        }
        
        # 使用更长的超时时间
        response = requests.get(image_url, headers=headers, timeout=30)
        response.raise_for_status()
        image_data = response.content
        
        # 检查下载的内容是否为图片
        if len(image_data) < 100:  # 一个正常的图片通常至少有几KB
            logger.warning(f"下载的内容太小，可能不是图片: {len(image_data)} 字节")
            return None
        
        logger.info(f"成功下载图片，大小: {len(image_data)} 字节")
        
        # 转换为Base64编码
        base64_data = base64.b64encode(image_data).decode('utf-8')
        return f"data:image/jpeg;base64,{base64_data}"
    except Exception as e:
        logger.error(f"下载图片失败: {str(e)}")
        return None

def main():
    """测试TAPD图片处理功能"""
    # 检查是否提供了图片URL
    if len(sys.argv) < 2:
        print("用法: python test_tapd_image.py <image_url>")
        print("例如: python test_tapd_image.py https://file.tapd.cn/tfl/captures/2025-02/tapd_43702532_base64_1739761131_343.png")
        return
    
    # 获取图片URL
    image_url = sys.argv[1]
    
    # 下载图片并转换为Base64编码
    base64_image = download_image(image_url)
    if not base64_image:
        print("下载图片失败")
        return
    
    print(f"成功下载图片并转换为Base64编码，长度: {len(base64_image)}")
    
    # 创建ImageMarkdownConverter实例
    converter = ImageMarkdownConverter(
        image_analysis_enabled=True,
        strip=['script', 'style'],
        convert=['a', 'b', 'i', 'strong', 'em', 'code', 'pre', 'p', 'br', 'hr', 'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'img', 'table', 'thead', 'tbody', 'tr', 'th', 'td']
    )
    
    # 创建一个模拟的BeautifulSoup元素
    soup = BeautifulSoup(f'<img src="{base64_image}" alt="需求文档图片" />', 'html.parser')
    img = soup.find('img')
    
    # 使用analyze_image方法直接分析图片
    print("\n使用analyze_image方法直接分析图片:")
    result = converter.analyze_image(base64_image, "需求文档图片")
    if result:
        print("-" * 50)
        print(result)
        print("-" * 50)
    else:
        print("图片分析失败")
    
    # 使用convert_img方法处理图片
    print("\n使用convert_img方法处理图片:")
    result = converter.convert_img(img, '', [])
    if result:
        print("-" * 50)
        print(result)
        print("-" * 50)
    else:
        print("图片处理失败")
    
    # 将结果保存到文件
    output_file = "tapd_image_analysis.txt"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(result if result else "图片处理失败")
    
    print(f"\n分析结果已保存到 {output_file}")

if __name__ == "__main__":
    main()
