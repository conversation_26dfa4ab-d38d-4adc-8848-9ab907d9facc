"""
使用模拟数据测试图片解析功能
"""
import os
import sys
import traceback
from unittest.mock import patch

# 添加当前目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 模拟的图片解析结果
MOCK_IMAGE_ANALYSIS = """
<!-- 图片分析 -->
这是一张展示多个应用程序图标组合在一起的画面，背景为深色，并且有反射效果。图标包括相机、Twitter、Spotify、TikTok、Discord和Netflix等流行应用的标志。整体构图简洁明了地展现了现代互联网生活中常见的社交媒体及娱乐类软件集合场景。
"""

try:
    # 导入需要的函数
    from app.api.v1.reqAgent.tapdAgent import html_to_markdown
    from app.api.v1.reqAgent.image_markdown_converter import html_to_markdown_with_image_analysis
    
    # 测试HTML内容
    html_content = """
    <div class="tapd-detail-content">
        <h1>需求标题：用户管理功能</h1>
        <div class="description">
            <p>本需求主要实现系统的用户管理功能</p>
            <p>包含用户的增删改查等基本操作</p>
        </div>
        <div class="requirement-details">
            <h2>功能点</h2>
            <ul>
                <li>用户注册</li>
                <li>用户登录</li>
                <li>用户信息修改</li>
                <li>用户权限管理</li>
            </ul>
            <h2>界面设计</h2>
            <p>界面需要简洁大方符合整体风格</p>
            <img src="https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7" alt="用户界面示例">
            <p>上图为用户界面参考设计</p>
            <h2>技术要求</h2>
            <p>前端使用Vue框架开发</p>
            <p>后端使用Spring Boot</p>
            <p>数据库使用MySQL</p>
        </div>
    </div>
    """
    
    # 模拟的Markdown转换结果
    expected_markdown = f"""# 需求标题：用户管理功能

本需求主要实现系统的用户管理功能。

包含用户的增删改查等基本操作。

## 功能点

- 用户注册
- 用户登录
- 用户信息修改
- 用户权限管理

## 界面设计

界面需要简洁大方符合整体风格。

![用户界面示例](https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7)
{MOCK_IMAGE_ANALYSIS}

上图为用户界面参考设计。

## 技术要求

前端使用Vue框架开发。

后端使用Spring Boot。

数据库使用MySQL。"""
    
    print("="*50)
    print("使用模拟数据测试图片解析功能")
    print("="*50)
    
    # 使用模拟的html_to_markdown_with_image_analysis函数
    def mock_html_to_markdown_with_image_analysis(*args, **kwargs):
        # 调用原始函数获取基本的Markdown
        markdown = html_to_markdown_with_image_analysis(*args, **kwargs)
        
        # 在图片标记后添加模拟的图片分析结果
        if "![" in markdown and "](" in markdown:
            parts = markdown.split("![")
            for i in range(1, len(parts)):
                if "](" in parts[i]:
                    img_end = parts[i].find(")") + 1
                    parts[i] = parts[i][:img_end] + MOCK_IMAGE_ANALYSIS + parts[i][img_end:]
            
            markdown = "![".join(parts)
        
        return markdown
    
    # 使用模拟函数进行测试
    with patch('app.api.v1.reqAgent.image_markdown_converter.html_to_markdown_with_image_analysis', 
               side_effect=mock_html_to_markdown_with_image_analysis):
        
        # 测试html_to_markdown函数
        print("\n1. 测试html_to_markdown函数（使用模拟数据）")
        markdown_result = html_to_markdown(html_content)
        print("\nMarkdown转换结果:")
        print("-"*50)
        print(markdown_result)
        print("-"*50)
        
        # 检查是否包含图片解析内容
        if "<!-- 图片分析 -->" in markdown_result:
            print("\n✅ 成功: Markdown结果包含图片分析内容")
        else:
            print("\n❌ 失败: Markdown结果不包含图片分析内容")
        
        # 检查结果是否符合预期
        if expected_markdown.strip() in markdown_result.strip():
            print("\n✅ 成功: Markdown结果符合预期格式")
        else:
            print("\n❌ 失败: Markdown结果不符合预期格式")
            print("\n预期结果:")
            print(expected_markdown)
    
    print("\n"+"="*50)
    print("测试完成")
    print("="*50)
    
except Exception as e:
    print(f"\n❌ 测试失败: {str(e)}")
    traceback.print_exc()
