#!/usr/bin/env python
# -*- coding: utf-8 -*-

import asyncio
import logging
import os
from tortoise import Tortoise

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 远程数据库配置
REMOTE_DB_CONFIG = {
    "connections": {
        "default": {
            "engine": "tortoise.backends.asyncpg",
            "credentials": {
                "host": "************",
                "port": 5432,
                "user": "postgres",
                "password": "admin123",  # 使用正确的密码
                "database": "agent_testing",
            }
        }
    },
    "apps": {
        "models": {
            "models": ["app.models"],
            "default_connection": "default",
        }
    },
    "use_tz": False,
    "timezone": "Asia/Shanghai"
}

async def migrate_remote_knowledge_fields():
    """为远程数据库的knowledge_items表添加新字段"""
    try:
        # 初始化远程数据库连接
        logger.info("正在连接远程数据库...")
        logger.info(f"数据库地址: {REMOTE_DB_CONFIG['connections']['default']['credentials']['host']}")
        logger.info(f"数据库名称: {REMOTE_DB_CONFIG['connections']['default']['credentials']['database']}")

        await Tortoise.init(config=REMOTE_DB_CONFIG)
        logger.info("远程数据库连接成功")

        # 获取数据库连接
        conn = Tortoise.get_connection("default")

        # 检查字段是否已存在
        logger.info("检查字段是否已存在...")
        check_sql = """
            SELECT column_name
            FROM information_schema.columns
            WHERE table_schema = 'public'
            AND table_name = 'knowledge_items'
            AND column_name IN ('source_url', 'source_reference_id');
        """

        result = await conn.execute_query(check_sql)
        existing_columns = [row[0] for row in result[1]] if result[1] else []
        logger.info(f"已存在的字段: {existing_columns}")

        # 需要添加的字段
        fields_to_add = []
        if 'source_url' not in existing_columns:
            fields_to_add.append('source_url')
        if 'source_reference_id' not in existing_columns:
            fields_to_add.append('source_reference_id')

        if not fields_to_add:
            logger.info("所有字段都已存在，无需添加")
            return

        logger.info(f"需要添加的字段: {fields_to_add}")

        # 执行字段添加
        migration_sqls = []

        if 'source_url' in fields_to_add:
            migration_sqls.append("ALTER TABLE knowledge_items ADD COLUMN source_url VARCHAR(500);")
            logger.info("准备添加 source_url 字段")

        if 'source_reference_id' in fields_to_add:
            migration_sqls.append("ALTER TABLE knowledge_items ADD COLUMN source_reference_id VARCHAR(100);")
            migration_sqls.append("CREATE INDEX IF NOT EXISTS idx_knowledge_items_source_reference_id ON knowledge_items(source_reference_id);")
            logger.info("准备添加 source_reference_id 字段和索引")

        # 执行迁移SQL
        for sql in migration_sqls:
            logger.info(f"执行SQL: {sql}")
            await conn.execute_query(sql)
            logger.info("SQL执行成功")

        # 验证字段添加结果
        logger.info("验证字段添加结果...")
        verify_sql = """
            SELECT column_name, data_type, character_maximum_length, is_nullable
            FROM information_schema.columns
            WHERE table_schema = 'public'
            AND table_name = 'knowledge_items'
            AND column_name IN ('source_url', 'source_reference_id')
            ORDER BY column_name;
        """

        result = await conn.execute_query(verify_sql)
        if result and len(result) > 1:
            logger.info("字段验证结果:")
            for row in result[1]:
                logger.info(f"  列名: {row[0]}, 数据类型: {row[1]}, 最大长度: {row[2]}, 可为空: {row[3]}")

        # 检查索引是否创建成功
        logger.info("检查索引创建情况...")
        index_sql = """
            SELECT indexname, tablename
            FROM pg_indexes
            WHERE tablename = 'knowledge_items'
            AND indexname LIKE '%source%';
        """

        result = await conn.execute_query(index_sql)
        if result and len(result) > 1:
            logger.info("索引验证结果:")
            for row in result[1]:
                logger.info(f"  索引名: {row[0]}, 表名: {row[1]}")

        logger.info("远程数据库迁移完成！")

    except Exception as e:
        logger.error(f"远程数据库迁移失败: {str(e)}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        raise
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()
        logger.info("数据库连接已关闭")

async def test_remote_connection():
    """测试远程数据库连接"""
    try:
        logger.info("测试远程数据库连接...")
        await Tortoise.init(config=REMOTE_DB_CONFIG)

        conn = Tortoise.get_connection("default")
        result = await conn.execute_query("SELECT version();")

        if result and len(result) > 1:
            logger.info(f"数据库版本: {result[1][0][0]}")

        # 检查knowledge_items表是否存在
        table_check = await conn.execute_query("""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_name = 'knowledge_items';
        """)

        if table_check and len(table_check) > 1:
            logger.info("knowledge_items表存在")
        else:
            logger.warning("knowledge_items表不存在")

        logger.info("远程数据库连接测试成功")

    except Exception as e:
        logger.error(f"远程数据库连接测试失败: {str(e)}")
        raise
    finally:
        await Tortoise.close_connections()

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # 测试连接
        asyncio.run(test_remote_connection())
    else:
        # 执行迁移
        asyncio.run(migrate_remote_knowledge_fields())
