#!/bin/bash

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# 停止可能正在运行的 Nginx
echo "停止可能正在运行的 Nginx..."
pkill nginx 2>/dev/null || true
sleep 1

# 启动 Nginx 使用自定义配置
echo "启动 Nginx 代理..."
nginx -c "$SCRIPT_DIR/simple_proxy.conf" -p "$SCRIPT_DIR"

# 检查 Nginx 是否成功启动
if pgrep nginx > /dev/null; then
    echo "Nginx 代理已启动，监听端口 8080"
    echo "请使用 http://localhost:8080 访问应用"
    echo "要停止代理，请运行 pkill nginx"
else
    echo "Nginx 启动失败"
fi
