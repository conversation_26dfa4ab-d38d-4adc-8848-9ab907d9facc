pipeline {
    agent any

    environment {
        REPO = 'AItestPlatform'
        DOCKERFILE = 'Dockerfile.fast'
        NPM_TOKEN = credentials('npm_token')
    }

    parameters {
        string(name: 'BRANCH', defaultValue: 'main', description: '要构建的分支')
        choice(name: 'BUILD_ENV', choices: ['dev', 'test', 'prod'], description: '构建环境')
        booleanParam(name: 'PUSH_IMAGE', defaultValue: true, description: '是否推送镜像')
    }

    stages {
        stage('拉取代码') {
            steps {
                // 简化的git步骤
                git url: "https://gitee.com/iHealthTestGroup/aitest.git", branch: "${params.BRANCH}", credentialsId: 'gitee-8835'

                // 简单显示构建信息
                echo "构建分支: ${params.BRANCH}"
                echo "构建环境: ${params.BUILD_ENV}"
            }
        }

        stage('安装依赖') {
            steps {
                // 前端依赖
                dir("${WORKSPACE}/web") {
                    sh 'echo "安装前端依赖..."'
                    sh 'echo registry=https://registry.npmmirror.com/ > .npmrc'
                    sh 'echo //registry.npmmirror.com/:_authToken=${NPM_TOKEN} >> .npmrc'
                    sh 'npm install pnpm@7 --no-save --legacy-peer-deps'
                    sh 'npx pnpm install'
                }

                // 后端依赖
                sh '''#!/bin/bash
                echo "安装后端依赖..."

                # 检查 Conda 是否已安装
                if ! command -v conda &> /dev/null; then
                    echo "Conda 未安装，正在安装 Miniconda..."
                    # 下载 Miniconda 安装脚本
                    wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O miniconda.sh
                    # 安装 Miniconda（安装到用户目录，不需要 root 权限）
                    bash miniconda.sh -b -p $HOME/miniconda
                    # 添加到路径
                    export PATH="$HOME/miniconda/bin:$PATH"
                else
                    echo "Conda 已安装"
                fi

                # 确保使用正确的 Conda
                if [ -f "$HOME/miniconda/bin/conda" ]; then
                    export PATH="$HOME/miniconda/bin:$PATH"
                elif [ -f "/opt/conda/bin/conda" ]; then
                    export PATH="/opt/conda/bin:$PATH"
                fi

                # 设置 Conda 环境
                source $(conda info --base)/etc/profile.d/conda.sh

                # 检查环境是否存在
                if conda env list | grep -q aitest_env; then
                    echo "环境 aitest_env 已存在，将更新依赖"
                    conda activate aitest_env
                else
                    echo "创建新环境 aitest_env"
                    conda create -y -n aitest_env python=3.11
                    conda activate aitest_env
                fi

                # 安装/更新依赖
                pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple
                pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
                '''
            }
        }


        stage('生成镜像') {
            steps {
                // 准备环境
                sh 'echo ${WORKSPACE}'
                sh 'echo ${BRANCH}'
                sh 'echo registry=https://registry.ihealthcn.com/ > .npmrc'
                sh 'echo //registry.ihealthcn.com/:_authToken=${NPM_TOKEN} >> .npmrc'

                // 调用build-image作业
                build job: "Utils/build-image", parameters: [
                    string(name: 'ws', value: "${WORKSPACE}"),
                    string(name: 'dockerfilepath', value: "${DOCKERFILE}"),
                    string(name: 'reponame', value: "${REPO}"),
                    string(name: 'branch', value: "${params.BRANCH}"),
                    string(name: 'buildargs', value: "ENV=${params.BUILD_ENV}")
                ]
            }
        }

        stage('推送到华为镜像仓库') {
            when {
                expression { return params.PUSH_IMAGE == true }
            }
            steps {
                // 调用push-image作业
                build job: "Utils/push-image", parameters: [
                    string(name: 'imagename', value: "${REPO.toLowerCase()}:${params.BRANCH.toLowerCase()}")
                ]
            }
        }
    }

    post {
        always {
            cleanWs()
        }
        success {
            echo "构建成功!"
        }
        failure {
            echo "构建失败!"
        }
    }
}
