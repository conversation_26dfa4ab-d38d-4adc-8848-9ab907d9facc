import asyncio
from tortoise import Tortoise
from app.settings.config import TORTOISE_ORM

async def check_test_cases():
    # Initialize Tortoise ORM
    await Tortoise.init(config=TORTOISE_ORM)
    
    # Get connection
    conn = Tortoise.get_connection("default")
    
    # 查询test_cases表结构
    try:
        result = await conn.execute_query("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'test_cases';
        """)
        print("test_cases表结构:")
        for row in result[1]:
            print(f"- {row[0]}: {row[1]}")
            
        # 查询数据
        result = await conn.execute_query("SELECT COUNT(*) FROM test_cases;")
        count = result[1][0][0]
        print(f"\n表中有 {count} 条记录")
        
        if count > 0:
            result = await conn.execute_query("SELECT id, test_case_id, title, requirement_id, project_id FROM test_cases LIMIT 5;")
            print("\n示例数据:")
            for row in result[1]:
                print(row)
    except Exception as e:
        print(f"查询test_cases表失败: {e}")
    
    # Close connections
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(check_test_cases())
