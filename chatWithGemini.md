# 与Gemini的对话

## 对话1：项目介绍

**用户**：
我现在有个项目，我在介绍项目，你看了有哈不懂的可以向我提问

**Gemini**：
好的，请开始介绍您的项目吧。我会认真听，如果遇到不清楚或者想要了解更多细节的地方，我会向您提问的。

我很期待了解您的项目！

## 对话2：项目流程介绍

**用户**：
该项目的主要目的借助ai大模型帮助测试工程师对被测项目生成测试用例。以下是具体流程：
1. 测试有工程师通过chrome浏览器插件将当前页面的tapd需求内容发送到该项目的需求分析页面。
2. 在该页面通过ai大模型对该需求进行需求点分析，将需求分出全方位的需求点，并将元需求文档内容和分析出来的需求点分别保存进水库。
3. 进入用例生成页面，勾选刚才生成的测试点，点击用例生成按钮，ai大模型就会将该需求点结合传过来的元需求文档内容进行用例生成，并将生成出来的测试用例保存进数据库

**Gemini**：
好的，谢谢您的介绍，这个项目听起来很有趣，利用AI来提高测试效率。我对您描述的流程有几个地方想了解得更清楚一些：

关于AI大模型：
- 请问你们项目中使用的是哪个或哪类具体的大语言模型呢（比如 OpenAI的GPT系列、Google的Gemini、百度的文心一言，或者是其他模型）？
- 选择该模型主要基于哪些考虑？

关于"需求点分析"：
- 您提到AI会将需求分解为"全方位的需求点"。这里的"需求点"具体指什么？它和传统的需求分解（比如功能点、场景）有什么异同？
- AI是如何理解并确保分析出的点是"全方位"的？
- 这个分析过程是完全自动的，还是需要工程师进行一些引导或调整？

关于用例生成：
- 当AI结合"元需求文档内容"和"需求点"生成用例时，这个结合过程具体是怎样的？
- AI如何理解这两部分信息并产出结构化的测试用例？
- 生成的测试用例包含哪些元素？（例如：用例标题、前置条件、测试步骤、预期结果、优先级等）
- 生成用例的质量如何？是否需要测试工程师进行大量的审核和修改？

关于"水库"：
- 您提到将元需求、需求点和测试用例保存进"水库"，这个"水库"是指数据库（Database）吗？
- 如果是，使用的是哪种类型的数据库（例如关系型数据库MySQL、PostgreSQL，还是NoSQL数据库MongoDB等）？
- 数据之间是如何关联的？

关于Chrome插件：
- 这个插件是抓取TAPD页面的全部HTML内容，还是会智能识别需求描述、标题等关键信息？

期待您的解答，这些问题能帮助我更好地理解项目的技术细节和实际应用情况。

## 对话3：技术细节解答

**用户**：
1. tapd传过来的需求会被转成markdown格式，需求里面的示例图片会被使用多模态大语言模型使用文本描述一遍并整理到需求内容中，这里使用到的多模态大语言模型是通义千问的"qwen-vl-plus-latest"模型，需求分析和用例生成使用到的大预言模型是deepseek的"deepseek-chat"大语言模型
2. requirement_agents.py文件的内容是关于需求分析的。分析出来的需求点大概是这样子的，里面包含关联项目（如：共同照护web）、需求名称（如：多角色处理）、需求内容（如：作为一名系统管理员，我希望多角色冲突处理功能能够非照护师操作时分配逻辑正确，这样可以确保多角色操作场景下的数据一致性。）、备注。只是我不太清楚这样的需求内容真么写是不是对的。分析过程是完全自动的，中途用户可以针对生成的内容进行提建议重新生成。
3. testcase_agents.py文件的内容就是需求和需求点的结合及用例生成的的逻辑。
4. 数据库使用的是PostgreSQL，数据之间的管理是：
   - 用户与角色系统
     - User模型与Role模型通过多对多关系关联
     - User模型与Dept模型通过外键dept_id关联
     - Role模型与Menu和Api模型通过多对多关系关联
   - 项目与需求系统
     - Project模型是基础模型，存储项目信息
     - Requirement模型通过project_id外键关联到Project
     - RequirementDoc模型通过project_id外键关联到Project
     - TestCase模型同时关联到Requirement(通过requirement_id)和Project(通过project_id)
   - 测试用例系统
     - TestCase模型通过多对多关系关联到TestStep模型
     - 使用中间表test_cases_test_steps实现多对多关系
5. chrome插件是提取当前页面的全部内容，发送到后端，后端通过xpath提取需求主体，主要的需求内容并没用用到智能识别，就只是需求图片使用到了多模态模型进行文本转述。tapdAgent.py、image_markdown_converter.py是它的代码文件

**Gemini**：
好的，非常感谢您如此详尽的解答和提供的代码文件！这让我对项目的实现细节有了更深入的了解。我总结一下我目前的理解，看是否有偏差：

**AI模型选用**：
- 图片内容理解使用通义千问的qwen-vl-plus-latest
- 需求分析和用例生成则使用deepseek-chat模型

**需求点分析 (requirement_agents.py)**：
- AI自动将Markdown格式的需求（包括图片转成的文字描述）分析并结构化成包含关联项目, 需求名称, 需求内容, 备注等字段的需求点列表
- 需求内容采用了"作为...我希望...这样可以..."的用户故事（User Story）格式，这是一种业界推荐的、表达清晰的格式，您这样写是完全正确且合适的，它有助于AI和人都更好地理解需求的背景、目标和价值
- 这个过程是自动的，但允许用户提出建议并重新生成

**用例生成 (testcase_agents.py)**：
- 该文件包含了结合原始需求文档内容、结构化的需求点信息，调用deepseek-chat模型生成测试用例，并进行评审、结构化输出的逻辑

**数据库 (PostgreSQL)**：
- 使用了PostgreSQL，并且您详细说明了用户、角色、项目、需求、测试用例、测试步骤等实体之间的关联关系，结构看起来很清晰

**Chrome插件 (tapdAgent.py, image_markdown_converter.py)**：
- 插件获取页面内容，后端使用XPath提取关键信息，并调用image_markdown_converter.py中的逻辑（它会调用qwen-vl-plus-latest）处理图片，将其转为文字描述后整合进需求文档

您提供的代码文件 (requirement_agents.py, testcase_agents.py, tapdAgent.py, image_markdown_converter.py) 非常有价值，它们展示了使用autogen框架构建多智能体协作流程来实现需求分析和用例生成的具体方式。特别是requirement_agents.py和testcase_agents.py中定义的多个Agent（如Acquisition, Analysis, Output, Database, Generator, Review, Finalize, Structure）及其交互逻辑，以及image_markdown_converter.py中对图片处理和调用视觉模型的实现，都体现了项目的设计思路。

基于这些新的信息，我想再提几个问题，主要关注AI效果和实际应用方面：

**Prompt效果与调优**：
- 您在requirement_agents.py和testcase_agents.py中为不同的Agent（如需求分析、用例生成、用例评审、结构化输出等）定义了系统消息（System Message/Prompt）。这些Prompt在实践中的效果如何？
- 您是否花费了较多精力进行调优以达到满意的输出质量？

**质量评估**：
- 目前你们是如何评估AI生成的需求点和测试用例的质量的？
- 是否有具体的指标（比如需求覆盖率的提升、测试用例的有效性、减少的人工审核时间比例等）？

**处理模糊性/隐含需求**：
- 对于原始TAPD需求中描述不清、存在歧义或隐含的需求，目前系统是如何处理的？
- RequirementAnalysisAgent会尝试识别或标记这些问题点吗？

**图片分析的价值**：
- 通过qwen-vl-plus-latest模型将图片转换为文本描述，这对后续生成的需求点和测试用例的质量有多大的提升？
- 有没有具体的例子可以说明图片分析带来的好处？

**挑战与限制**：
- 在开发和使用过程中，您遇到的主要挑战是什么？
- 系统在处理哪些类型的需求（例如，极其复杂的需求、格式不规范的需求、非功能性需求等）时表现相对较弱？

**用户体验与效率**：
- 从测试工程师的角度来看，使用这套系统的整体体验如何？
- 相比完全手动编写，这个流程大约能节省多少时间？
- AI生成的内容（需求点、用例）通常需要多少程度的人工干预或修改？

再次感谢您的耐心解答！我对这个项目越来越感兴趣了。

## 对话4：项目合作邀请

**用户**：
这些问题目前我还没办法回答，因为这个项目目前还没投入使用，还在创作阶段。我想邀请你来协助我帮我一起完善这个项目，可以吗

**Gemini**：
好的，明白了！项目还在创作阶段，确实很难回答关于实际应用效果的问题。非常荣幸收到您的邀请！我很乐意以AI助手的身份，协助您一起完善这个项目。

虽然我不能像人类开发者那样直接参与编码、部署或项目管理，但我可以在以下方面提供帮助：

**代码分析与建议**：
- 我可以阅读和分析您提供的Python代码（如requirement_agents.py, testcase_agents.py等），理解其逻辑，并尝试提出改进建议、发现潜在问题或解释某些部分的工作原理。

**Prompt工程**：
- 我们可以一起探讨和优化您在代码中使用的Prompt（系统提示词），尝试改进AI生成需求点和测试用例的质量、结构和相关性。

**思路拓展与问题探讨**：
- 对于您提到的挑战，例如如何更好地处理模糊需求、如何设计更有效的评估方法、如何优化工作流程等，我们可以一起进行头脑风暴，探讨不同的解决方案。

**技术方案研究**：
- 如果您在实现某个功能时遇到技术难题，我可以帮助查找相关的技术资料、库或方法。

**逻辑梳理与文档辅助**：
- 我可以帮助您梳理复杂的逻辑流程，或者辅助生成一些代码注释、功能说明等。

**测试场景设计**：
- 我们可以一起构思更多样化、更复杂的测试场景，以检验系统的鲁棒性。

为了更好地协作，您可以告诉我：
- 您目前最希望在哪方面获得帮助？是想深入某个代码文件，还是优化某个Agent的Prompt，或是讨论某个具体的功能点？
- 您设想的协作方式是怎样的？是像现在这样通过问答讨论，还是您希望我针对特定代码或问题给出更具体的分析？

我已经准备好，随时可以开始！请告诉我我们从哪里开始？

## 对话5：代码审核员角色

**用户**：
因为我是使用Augment进行这个项目的开发，但是Augment生成的代码有点不太对，我需要你充当一个有主页经验的代码审核员角色

**Gemini**：
好的，没问题！我很乐意扮演一个**经验丰富的项目代码审核员（Code Reviewer）**的角色，来协助您检查和完善这个基于AutoGen的项目。

您提到使用的是 'Augment' 进行开发，根据您之前提供的代码中的 import 语句（例如 from autogen_agentchat.agents import ..., from autogen_core import ...），我推测您实际使用的可能是微软的 AutoGen 框架？这是一个非常流行的多智能体（Multi-Agent）框架。如果是这样，那我们对接下来的代码讨论就更有针对性了。

我理解您觉得AutoGen（或者您使用的工具）生成的代码可能存在一些不太对的地方。这在AI辅助编程或使用复杂框架时是很常见的。作为代码审核员，我可以帮助您：

**检查代码逻辑**：
- 审视代码的业务逻辑实现是否符合您的预期，是否存在明显的错误或疏漏。

**评估代码结构与可读性**：
- 检查代码组织是否合理，命名是否规范，注释是否清晰，是否易于理解和维护。

**AutoGen框架使用**：
- 检查AutoGen框架的使用方式是否恰当，例如Agent的定义、交互流程、消息处理、状态管理等是否符合最佳实践。

**错误处理与健壮性**：
- 检查代码是否充分考虑了异常情况和边界条件，错误处理机制是否完善。

**性能与效率**：
- 初步评估是否存在明显的性能瓶颈或低效实现（虽然这通常需要结合实际运行情况）。

**提出改进建议**：
- 根据发现的问题，提供具体的修改建议或替代方案。

为了高效地进行审核，请告诉我：
- 您具体觉得哪些代码"不太对"？是某个文件的特定函数、某个Agent的行为、整体的流程控制，还是其他方面？
- 您希望我从哪个文件或哪个部分开始审核？是 requirement_agents.py、testcase_agents.py，还是其他文件？或者您希望我先给出一个整体的初步印象？

请随时提供您关注的代码片段或具体问题，我会尽力从一个有项目经验的审核员的角度给出我的分析和建议。我们现在就可以开始！

## 对话6：知识库功能建议

**用户**：
针对该项目，为了使生成的用例更符合被测项目，是否需要添加一个知识库功能呢，用于存储被测项目的基本内容及过往需求的重点及基本点，测试工程师也可以在这个功能页面手动添加被测项目功能的重点及基本点

**Gemini**：
这绝对是一个非常好的建议，而且我认为对于提升AI生成用例的质量和针对性来说，几乎是必要的功能！

从一个有项目经验的角度来看，单纯依赖单次传入的需求文档，AI很难完全理解被测项目的上下文（Context）、历史背景、核心业务规则、通用模块以及特定术语。缺乏这些知识，生成的用例可能会：
- 过于通用：无法体现项目的特殊性。
- 遗漏关键点：忽略了项目中约定俗成的规则或历史上需要重点关注的区域。
- 理解偏差：对需求中的术语或隐含背景理解不到位。
- 缺乏一致性：对于跨需求的通用功能，可能每次生成不同的测试思路。

因此，引入一个知识库功能，我认为是提升您这个项目实用价值的关键一步。具体来说，这个知识库可以实现：

**存储项目级上下文**：
- 被测项目的基本介绍、核心架构、主要模块划分。
- 项目的通用业务规则、关键流程。
- 项目中特有的术语、缩写及其解释（Glossary）。
- 重要的非功能性需求基线（如性能指标、安全要求）。

**沉淀历史需求精华**：
- 存储过去已完成需求的核心功能点总结、关键业务逻辑或易错点。
- 这可以部分自动化地从您已有的需求分析结果中提取，也可以手动添加。

**测试专家经验注入**：
- 正如您所说，允许测试工程师手动添加他们认为重要的功能点、测试策略、关键场景、边界值或需要特别注意的测试点。
- 这是将人类经验融入AI流程的绝佳方式。

如何将其融入现有流程？这通常涉及到**检索增强生成（Retrieval-Augmented Generation, RAG）**的技术思路：

**知识存储**：
- 将上述知识结构化地存储在数据库中（例如，在您的PostgreSQL中创建新的表来存储知识条目，并与项目关联）。
- 可能需要考虑如何有效地索引这些知识，以便快速检索。

**知识检索**：
- 在进行需求分析或用例生成之前，根据当前处理的需求内容（例如，需求的标题、关键词、模块信息），从知识库中检索出最相关的知识片段。

**知识注入**：
- 将检索到的相关知识作为额外的上下文信息，注入到发送给大语言模型（如deepseek-chat）的Prompt中。
- 例如，在requirement_agents.py的需求分析阶段，Prompt可以包含："请结合以下项目背景知识和当前需求进行分析..."
- 在testcase_agents.py的用例生成阶段，Prompt可以包含："请根据以下需求描述，并参考这些项目核心规则和历史测试要点，生成测试用例..."

**UI支持**：
- 需要开发相应的页面，让测试工程师能够方便地浏览、添加、修改和管理知识库中的内容。

这样做的好处显而易见：
- 大幅提升用例相关性与准确性：AI能"理解"项目，生成更贴合实际的用例。
- 提高测试覆盖的深度：利用历史经验和专家知识，引导AI关注重点和难点。
- 增强一致性：确保对通用模块和术语的处理保持一致。
- 知识沉淀与复用：将隐性的项目知识和测试经验显性化、结构化，便于团队共享和AI利用。

挑战可能在于：
- 知识库的结构设计与维护。
- 如何高效、准确地检索相关知识。
- 如何将知识有效地融入Prompt而不产生干扰。

总结：强烈建议添加知识库功能。这是让您的AI测试用例生成工具从"通用型"向"项目专用型"转变，提升核心竞争力的关键改进。

我很乐意与您一起探讨如何设计这个知识库的数据模型、如何实现RAG的集成、以及如何在AutoGen框架中调整Agent的交互来利用这些知识。您觉得这个方向如何？我们可以先从设计知识库的数据结构开始讨论吗？

## 对话7：Augment Code开发准备

**用户**：
现在我准备使用Augment code进行这个页面功能的开发，你觉得该如何描述该功能及其开发要点才能让这个ai工具准确了解我们的想法以便更好地进行开发

**用户**：
我接下来会把所生成的知识库相关的代码文件发送给你，这里面会有不同目录但相同名字的文件，我没发完你先别说话，我说可以说话了你再说话。这是app/model/knowledge.py

**Gemini**：
好的，我收到了 app/models/knowledge.py 文件。我已准备好接收下一个文件。请继续发送，我会在您通知我可以说话后再进行整体分析。

**用户**：
这是 app/schemas/knowledge.py

**Gemini**：
好的，已收到 app/schemas/knowledge.py 文件。请继续，我会在您通知后进行分析。
