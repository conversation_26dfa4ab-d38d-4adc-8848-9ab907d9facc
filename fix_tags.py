import asyncio
from tortoise import Tortoise

async def run():
    # 初始化Tortoise ORM
    await Tortoise.init(
        db_url='postgres://admin:<EMAIL>:5432/agent_testing',
        modules={'models': ['app.models.admin']}
    )
    
    try:
        # 获取数据库连接
        conn = Tortoise.get_connection('default')
        
        # 执行SQL修改tags列类型
        await conn.execute_script('ALTER TABLE test_cases ALTER COLUMN tags TYPE VARCHAR(20);')
        
        print('表结构已更新：tags列类型已修改为VARCHAR(20)')
    except Exception as e:
        print(f'更新失败: {str(e)}')
    finally:
        # 关闭连接
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(run())
