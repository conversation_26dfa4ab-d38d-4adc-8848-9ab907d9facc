#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import logging
import base64
import requests
import json

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_api_call")

# 默认API配置
DEFAULT_MODEL = os.environ.get("DEFAULT_LLM_MODEL", "qwen-vl-plus-latest")
DEFAULT_API_BASE = os.environ.get("LLM_API_BASE", "https://dashscope.aliyuncs.com/compatible-mode/v1")
DEFAULT_API_KEY = os.environ.get("LLM_API_KEY", "sk-85477c3eb0424bb89d5421d2b28d2051")

def call_vision_api(image_path, prompt, model=DEFAULT_MODEL, api_base=DEFAULT_API_BASE, api_key=DEFAULT_API_KEY):
    """
    直接调用大模型的视觉API
    
    Args:
        image_path: 图片路径
        prompt: 提示词
        model: 模型名称
        api_base: API基础URL
        api_key: API密钥
        
    Returns:
        str: 大模型的回复
    """
    logger.info(f"开始调用大模型API，模型: {model}")
    
    # 读取图片并转换为Base64
    try:
        with open(image_path, 'rb') as f:
            image_data = f.read()
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        logger.info(f"成功读取图片，大小: {len(image_data)} 字节，Base64长度: {len(image_base64)}")
    except Exception as e:
        logger.error(f"读取图片失败: {str(e)}")
        return None
    
    # 使用OpenAI的API格式
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # 构建请求体
    image_url = f"data:image/jpeg;base64,{image_base64}"
    
    payload = {
        "model": model,
        "messages": [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": image_url
                        }
                    }
                ]
            }
        ],
        "max_tokens": 1000
    }
    
    # 如果是OpenAI的模型，添加特定参数
    if "gpt-4" in model:
        # 对于GPT-4 Vision模型，可以设置图像细节级别
        payload["messages"][0]["content"][1]["image_url"]["detail"] = "auto"
    
    # 根据不同的模型使用不同的端点
    endpoint = "/chat/completions"
    if "qwen" in model.lower():
        # 通义千问模型使用的端点
        endpoint = "/chat/completions"
        logger.info(f"使用通义千问模型端点: {endpoint}")
    elif "gpt-4" in model.lower() or "gpt-3.5" in model.lower():
        # OpenAI模型使用的端点
        endpoint = "/chat/completions"
        logger.info(f"使用OpenAI模型端点: {endpoint}")
    else:
        # 默认端点
        logger.info(f"使用默认端点: {endpoint}")
    
    # 打印完整的API请求URL
    api_url = f"{api_base}{endpoint}"
    logger.info(f"发送API请求到: {api_url}")
    
    # 发送请求
    try:
        response = requests.post(
            api_url,
            headers=headers,
            json=payload,
            timeout=60  # 增加超时时间
        )
        
        # 打印响应状态码
        logger.info(f"响应状态码: {response.status_code}")
        
        # 如果响应不成功，打印响应内容
        if response.status_code != 200:
            logger.error(f"响应内容: {response.text}")
            return None
        
        result = response.json()
        
        # 打印响应结果（不包含敏感信息）
        logger.info(f"响应结果类型: {type(result)}")
        if isinstance(result, dict):
            logger.info(f"响应结果键: {result.keys()}")
        
        # 提取回复内容
        if "choices" in result and len(result["choices"]) > 0:
            if "message" in result["choices"][0] and "content" in result["choices"][0]["message"]:
                content = result["choices"][0]["message"]["content"]
                logger.info(f"成功提取响应内容，长度: {len(content)}")
                return content
            else:
                logger.error(f"响应格式异常，无法提取content: {result['choices'][0]}")
        else:
            logger.error(f"API返回格式异常: {result}")
        return None
    except Exception as e:
        logger.error(f"API请求失败: {str(e)}")
        # 打印异常堆栈
        import traceback
        logger.error(f"异常堆栈: {traceback.format_exc()}")
        return None

def main():
    """测试大模型API调用"""
    # 检查是否提供了图片路径
    if len(sys.argv) < 2:
        print("用法: python test_api_call.py <image_path>")
        print("例如: python test_api_call.py test.jpg")
        return
    
    # 获取图片路径
    image_path = sys.argv[1]
    
    # 检查文件是否存在
    if not os.path.exists(image_path):
        print(f"错误: 文件 {image_path} 不存在")
        return
    
    # 提示词
    prompt = "请详细描述这张图片的内容。包括图片中的文字、图表、界面元素等。"
    
    # 调用大模型API
    response = call_vision_api(image_path, prompt)
    
    if response:
        print("\n大模型响应:")
        print("-" * 50)
        print(response)
        print("-" * 50)
        
        # 将结果保存到文件
        output_file = f"{os.path.splitext(image_path)[0]}_analysis.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(response)
        
        print(f"\n分析结果已保存到 {output_file}")
    else:
        print("\n调用大模型API失败")

if __name__ == "__main__":
    main()
