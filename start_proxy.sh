#!/bin/bash

# 检查是否安装了 Node.js
if ! command -v node &> /dev/null; then
    echo "Node.js 未安装，请先安装 Node.js"
    echo "在 macOS 上，可以使用 brew install node 安装"
    echo "在 Ubuntu 上，可以使用 sudo apt-get install nodejs 安装"
    exit 1
fi

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# 停止可能正在运行的代理服务器
echo "停止可能正在运行的代理服务器..."
pkill -f "node cors_proxy.js" 2>/dev/null || true

# 等待一会儿确保进程完全停止
sleep 2

# 启动 Node.js 代理服务器
echo "启动 CORS 代理服务器..."
node "$SCRIPT_DIR/cors_proxy.js" &

# 检查代理服务器是否成功启动
sleep 2
if pgrep -f "node cors_proxy.js" > /dev/null; then
    echo "CORS 代理服务器已启动，监听端口 8888"
    echo "请使用 http://localhost:8888 访问代理"
    echo "要停止代理，请运行 pkill -f 'node cors_proxy.js'"
else
    echo "代理服务器启动失败，请检查错误"
fi
