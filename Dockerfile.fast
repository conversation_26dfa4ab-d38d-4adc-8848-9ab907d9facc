# Build frontend
FROM node:20-alpine AS web-builder
WORKDIR /app

# Copy package files first
COPY web/package.json web/pnpm-lock.yaml ./

# 配置npm和pnpm使用淘宝镜像源
RUN npm config set registry https://registry.npmmirror.com/ && \
    # 安装特定版本的pnpm (7.x)，与项目锁文件兼容
    npm install -g pnpm@7 && \
    pnpm config set registry https://registry.npmmirror.com/ && \
    pnpm config set fetch-timeout 300000 && \
    # 使用--no-frozen-lockfile允许更新锁文件
    pnpm install

# Copy only necessary web files
COPY web .

# Build the application
RUN NODE_OPTIONS="--max-old-space-size=4096" pnpm build

# Build backend
FROM python:3.11-slim-bullseye AS builder
WORKDIR /app

# 使用中国的Debian镜像源并安装最小依赖
RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list && \
    sed -i 's/security.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list && \
    apt-get update && \
    apt-get install -y --no-install-recommends \
        gcc \
        python3-dev && \
    rm -rf /var/lib/apt/lists/*

# Copy requirements file
COPY requirements.txt .

# Install dependencies into a virtual environment with pip缓存
RUN python -m venv /opt/venv && \
    . /opt/venv/bin/activate && \
    pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple --timeout 300 -r requirements.txt

# 跳过模型预下载，改为在启动时下载（可选）
# 如果模型下载是构建时间长的主要原因，可以考虑注释掉这部分
# 或者使用预构建的包含模型的基础镜像
ENV HF_ENDPOINT=https://hf-mirror.com
ENV TRANSFORMERS_OFFLINE=0

# Final image
FROM python:3.11-slim-bullseye
WORKDIR /app

# Set timezone
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

# 使用中国的Debian镜像源并安装运行时依赖
RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list && \
    sed -i 's/security.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list && \
    apt-get update && \
    apt-get install -y --no-install-recommends \
        nginx \
        supervisor && \
    rm -rf /var/lib/apt/lists/*

# Copy virtual environment from builder
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 设置环境变量，确保使用国内镜像源
ENV HF_ENDPOINT=https://hf-mirror.com

# Copy frontend build
COPY --from=web-builder /app/dist /app/web/dist

# Copy only necessary backend files
COPY app /app/app
COPY run.py /app/
COPY deploy/web.conf /etc/nginx/conf.d/default.conf
COPY deploy/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Remove default nginx site
RUN rm -f /etc/nginx/sites-enabled/default

# Set environment variables with defaults
# These can be overridden when running the container
ENV LANG=zh_CN.UTF-8
# Database configuration
ENV DB_HOST=host.docker.internal
ENV DB_PORT=5432
ENV DB_USER=admin
ENV DB_PASSWORD=admin123
ENV DB_NAME=agent_testing
# Legacy PostgreSQL environment variables (kept for compatibility)
ENV POSTGRES_HOST=${DB_HOST}
ENV POSTGRES_PORT=${DB_PORT}
ENV POSTGRES_USER=${DB_USER}
ENV POSTGRES_PASSWORD=${DB_PASSWORD}
ENV POSTGRES_DB=${DB_NAME}
# API configuration
ENV API_PORT=9999
ENV LOG_LEVEL=INFO
# JWT configuration
ENV SECRET_KEY=3488a63e1765035d386f05409663f55c83bfae3b3c61a932744b20ad14244dcf
ENV JWT_ALGORITHM=HS256
ENV JWT_EXPIRE_MINUTES=10080
# Application name
ENV APP_NAME=agent_testing
# LLM configuration - DeepSeek
ENV DEFAULT_LLM_MODEL=deepseek-chat
ENV LLM_API_BASE=https://api.deepseek.com/v1
ENV LLM_API_KEY=***********************************
ENV LLM_TIMEOUT_SECONDS=120
ENV LLM_MAX_RETRIES=3
# LLM configuration - DashScope (Alibaba Cloud)
ENV DASHSCOPE_API_BASE=https://dashscope.aliyuncs.com/compatible-mode/v1
ENV DASHSCOPE_API_KEY=""
ENV DASHSCOPE_MODEL=deepseek-v3
# LLM configuration - Qwen VL (Alibaba Cloud Vision Model)
ENV QWEN_VL_MODEL=qwen-vl-plus-latest
ENV QWEN_VL_API_BASE=https://dashscope.aliyuncs.com/compatible-mode/v1
ENV QWEN_VL_API_KEY=sk-85477c3eb0424bb89d5421d2b28d2051

# Create necessary directories and set permissions
RUN mkdir -p /run/nginx \
    && mkdir -p /var/log/nginx \
    && mkdir -p /var/log/supervisor \
    && mkdir -p /var/log/app \
    && mkdir -p /app/logs \
    && chown -R www-data:www-data /app/web/dist \
    && chown -R www-data:www-data /var/log/nginx \
    && chown -R www-data:www-data /var/log/app \
    && chown -R www-data:www-data /app/logs \
    && chown -R www-data:www-data /var/log/supervisor \
    && chown -R www-data:www-data /run/nginx

EXPOSE 80

# Start supervisor (which will start both nginx and fastapi)
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
