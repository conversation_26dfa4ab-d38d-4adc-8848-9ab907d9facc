/**
 * TAPD内容提取器 - 内容脚本
 * 负责从TAPD页面提取内容，并将图片转换为Base64编码
 */

// 配置
const CONFIG = {
  // 是否启用图片转换为Base64
  enableImageToBase64: true,
  // 图片大小限制（字节）
  maxImageSize: 1024 * 1024 * 2, // 2MB
  // 日志级别: 'debug', 'info', 'warn', 'error'
  logLevel: 'info'
};

// 日志函数
const logger = {
  debug: (message) => CONFIG.logLevel === 'debug' && console.debug(`[TAPD Plugin] ${message}`),
  info: (message) => ['debug', 'info'].includes(CONFIG.logLevel) && console.info(`[TAPD Plugin] ${message}`),
  warn: (message) => ['debug', 'info', 'warn'].includes(CONFIG.logLevel) && console.warn(`[TAPD Plugin] ${message}`),
  error: (message) => console.error(`[TAPD Plugin] ${message}`)
};

/**
 * 将图片转换为Base64编码
 * @param {HTMLImageElement} imgElement - 图片元素
 * @returns {Promise<string>} - Base64编码的图片
 */
async function convertImageToBase64(imgElement) {
  return new Promise((resolve, reject) => {
    try {
      // 检查图片是否已经加载
      if (!imgElement.complete) {
        logger.info(`图片未加载完成，等待加载: ${imgElement.src}`);
        imgElement.onload = () => processImage();
        imgElement.onerror = () => {
          logger.warn(`图片加载失败: ${imgElement.src}`);
          resolve(imgElement.src); // 返回原始src
        };
      } else {
        processImage();
      }

      function processImage() {
        try {
          // 创建Canvas元素
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          
          // 设置Canvas大小
          canvas.width = imgElement.naturalWidth || imgElement.width;
          canvas.height = imgElement.naturalHeight || imgElement.height;
          
          // 检查图片尺寸
          if (canvas.width === 0 || canvas.height === 0) {
            logger.warn(`图片尺寸为0: ${imgElement.src}`);
            resolve(imgElement.src);
            return;
          }
          
          // 在Canvas上绘制图片
          ctx.drawImage(imgElement, 0, 0, canvas.width, canvas.height);
          
          // 将Canvas内容转换为Base64
          const dataURL = canvas.toDataURL('image/jpeg', 0.8); // 使用JPEG格式，质量0.8
          
          // 检查大小
          if (dataURL.length > CONFIG.maxImageSize) {
            logger.warn(`图片太大，跳过转换: ${imgElement.src}, 大小: ${dataURL.length} 字节`);
            resolve(imgElement.src);
            return;
          }
          
          logger.info(`成功转换图片为Base64: ${imgElement.src}, 大小: ${dataURL.length} 字节`);
          resolve(dataURL);
        } catch (error) {
          logger.error(`处理图片时出错: ${error.message}`);
          resolve(imgElement.src); // 出错时返回原始src
        }
      }
    } catch (error) {
      logger.error(`转换图片失败: ${error.message}`);
      resolve(imgElement.src); // 出错时返回原始src
    }
  });
}

/**
 * 处理HTML内容中的所有图片
 * @param {string} htmlContent - HTML内容
 * @returns {Promise<string>} - 处理后的HTML内容
 */
async function processImages(htmlContent) {
  if (!CONFIG.enableImageToBase64) {
    logger.info('图片Base64转换已禁用，跳过处理');
    return htmlContent;
  }
  
  logger.info('开始处理HTML内容中的图片');
  
  try {
    // 创建一个临时的DOM元素来解析HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    
    // 获取所有图片元素
    const images = tempDiv.querySelectorAll('img');
    logger.info(`找到 ${images.length} 张图片`);
    
    // 转换每个图片
    const promises = [];
    for (let i = 0; i < images.length; i++) {
      const img = images[i];
      promises.push(
        (async () => {
          try {
            // 跳过已经是Base64的图片
            if (img.src.startsWith('data:image')) {
              logger.info(`图片已经是Base64格式，跳过: ${img.src.substring(0, 30)}...`);
              return;
            }
            
            // 转换图片
            const base64Data = await convertImageToBase64(img);
            
            // 替换原始src为Base64编码
            img.src = base64Data;
            logger.debug(`图片 ${i+1}/${images.length} 处理完成`);
          } catch (error) {
            logger.error(`处理图片 ${i+1}/${images.length} 失败: ${error.message}`);
          }
        })()
      );
    }
    
    // 等待所有图片处理完成
    await Promise.all(promises);
    
    logger.info('所有图片处理完成');
    return tempDiv.innerHTML;
  } catch (error) {
    logger.error(`处理HTML内容中的图片失败: ${error.message}`);
    return htmlContent; // 出错时返回原始内容
  }
}

/**
 * 提取TAPD页面内容
 * @returns {Promise<Object>} - 提取的内容
 */
async function extractTapdContent() {
  logger.info('开始提取TAPD页面内容');
  
  try {
    // 获取页面URL
    const url = window.location.href;
    logger.info(`页面URL: ${url}`);
    
    // 获取页面标题
    const title = document.title;
    logger.info(`页面标题: ${title}`);
    
    // 获取需求内容
    let contentElement = document.querySelector('.tapd-detail-content');
    if (!contentElement) {
      contentElement = document.querySelector('.story-content');
    }
    if (!contentElement) {
      contentElement = document.querySelector('.requirement-content');
    }
    
    if (!contentElement) {
      throw new Error('未找到内容元素');
    }
    
    // 获取原始HTML内容
    const originalHtml = contentElement.outerHTML;
    logger.info(`获取到原始HTML内容，长度: ${originalHtml.length}`);
    
    // 处理图片，转换为Base64
    const processedHtml = await processImages(originalHtml);
    logger.info(`处理后的HTML内容，长度: ${processedHtml.length}`);
    
    // 获取用户ID和项目ID
    const userId = document.querySelector('meta[name="current-userid"]')?.content || '';
    const projectId = window.location.pathname.split('/')[1] || '';
    
    return {
      url,
      title,
      content: processedHtml,
      user_id: parseInt(userId) || 1,
      project_id: parseInt(projectId) || 1
    };
  } catch (error) {
    logger.error(`提取TAPD内容失败: ${error.message}`);
    throw error;
  }
}

// 监听来自后台脚本的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'extractContent') {
    logger.info('收到提取内容请求');
    
    extractTapdContent()
      .then(content => {
        logger.info('内容提取成功，发送回响应');
        sendResponse({ success: true, data: content });
      })
      .catch(error => {
        logger.error(`内容提取失败: ${error.message}`);
        sendResponse({ success: false, error: error.message });
      });
    
    // 返回true表示将异步发送响应
    return true;
  }
});

// 自动检测是否在TAPD页面
if (window.location.hostname.includes('tapd.cn')) {
  logger.info('检测到TAPD页面，准备提取内容');
  
  // 等待页面完全加载
  if (document.readyState === 'complete') {
    logger.info('页面已加载完成');
  } else {
    logger.info('等待页面加载完成');
    window.addEventListener('load', () => {
      logger.info('页面加载完成');
    });
  }
}
