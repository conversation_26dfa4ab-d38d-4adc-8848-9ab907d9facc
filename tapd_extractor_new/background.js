/**
 * TAPD内容提取器 - 后台脚本
 * 负责与后端API通信，发送提取的内容
 */

// 配置
const CONFIG = {
  // 后端API地址
  apiEndpoint: 'http://localhost:8000/api/v1/reqAgent/tapd/parse',
  // 日志级别: 'debug', 'info', 'warn', 'error'
  logLevel: 'info'
};

// 日志函数
const logger = {
  debug: (message) => CONFIG.logLevel === 'debug' && console.debug(`[TAPD Plugin BG] ${message}`),
  info: (message) => ['debug', 'info'].includes(CONFIG.logLevel) && console.info(`[TAPD Plugin BG] ${message}`),
  warn: (message) => ['debug', 'info', 'warn'].includes(CONFIG.logLevel) && console.warn(`[TAPD Plugin BG] ${message}`),
  error: (message) => console.error(`[TAPD Plugin BG] ${message}`)
};

/**
 * 发送内容到后端API
 * @param {Object} data - 要发送的数据
 * @returns {Promise<Object>} - 后端响应
 */
async function sendToBackend(data) {
  logger.info('开始发送数据到后端');
  logger.debug(`发送数据: ${JSON.stringify(data).substring(0, 100)}...`);
  
  try {
    const response = await fetch(CONFIG.apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    
    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status}`);
    }
    
    const result = await response.json();
    logger.info('数据发送成功');
    logger.debug(`后端响应: ${JSON.stringify(result).substring(0, 100)}...`);
    return result;
  } catch (error) {
    logger.error(`发送数据到后端失败: ${error.message}`);
    throw error;
  }
}

/**
 * 从当前标签页提取内容
 * @param {number} tabId - 标签页ID
 * @returns {Promise<Object>} - 提取的内容
 */
async function extractContentFromTab(tabId) {
  logger.info(`从标签页 ${tabId} 提取内容`);
  
  return new Promise((resolve, reject) => {
    chrome.tabs.sendMessage(tabId, { action: 'extractContent' }, response => {
      if (chrome.runtime.lastError) {
        logger.error(`发送消息失败: ${chrome.runtime.lastError.message}`);
        reject(new Error(chrome.runtime.lastError.message));
        return;
      }
      
      if (!response) {
        logger.error('未收到响应');
        reject(new Error('未收到响应'));
        return;
      }
      
      if (!response.success) {
        logger.error(`内容提取失败: ${response.error}`);
        reject(new Error(response.error));
        return;
      }
      
      logger.info('内容提取成功');
      resolve(response.data);
    });
  });
}

/**
 * 处理扩展图标点击事件
 */
chrome.action.onClicked.addListener(async (tab) => {
  logger.info(`扩展图标被点击，标签页: ${tab.id}`);
  
  try {
    // 检查是否是TAPD页面
    if (!tab.url.includes('tapd.cn')) {
      logger.warn('不是TAPD页面，跳过处理');
      return;
    }
    
    // 提取内容
    const content = await extractContentFromTab(tab.id);
    
    // 发送到后端
    const result = await sendToBackend(content);
    
    // 显示成功通知
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'images/icon128.png',
      title: 'TAPD内容提取成功',
      message: '内容已成功提取并发送到后端'
    });
    
    logger.info('处理完成');
  } catch (error) {
    logger.error(`处理失败: ${error.message}`);
    
    // 显示错误通知
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'images/icon128.png',
      title: 'TAPD内容提取失败',
      message: `错误: ${error.message}`
    });
  }
});

// 监听安装事件
chrome.runtime.onInstalled.addListener(() => {
  logger.info('扩展已安装');
});

logger.info('后台脚本已加载');
