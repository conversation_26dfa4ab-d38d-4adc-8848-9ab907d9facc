# TAPD内容提取器

这个Chrome扩展用于从TAPD页面提取内容，并将图片转换为Base64编码，以便在后端进行图片解析。

## 功能特点

1. 自动提取TAPD页面内容
2. 将页面中的图片转换为Base64编码
3. 支持图片大小限制，避免过大的图片导致性能问题
4. 详细的日志记录，方便调试
5. 自动发送内容到后端API

## 安装方法

### 开发模式安装

1. 打开Chrome浏览器，进入扩展管理页面（chrome://extensions/）
2. 开启右上角的"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择本扩展所在的文件夹

### 打包安装

1. 使用`package_extension.sh`脚本打包扩展
2. 将生成的`tapd_extractor.zip`文件拖放到Chrome扩展页面进行安装

## 使用方法

1. 在TAPD页面上，点击扩展图标打开弹出窗口
2. 点击"提取当前页面内容"按钮
3. 扩展会自动提取页面内容，将图片转换为Base64编码，并发送到后端API

## 配置选项

在弹出窗口中可以设置以下选项：

- **启用图片Base64编码**：是否将图片转换为Base64编码
- **自动提取内容**：页面加载完成后自动提取内容

## 开发说明

### 文件结构

- `manifest.json`：扩展配置文件
- `content.js`：内容脚本，负责从页面提取内容
- `background.js`：后台脚本，负责与后端API通信
- `popup.html`：弹出窗口HTML
- `popup.js`：弹出窗口脚本
- `images/`：图标文件夹

### 修改API地址

在`background.js`和`popup.js`中修改`apiEndpoint`变量：

```javascript
const apiEndpoint = 'http://your-api-server.com/api/v1/reqAgent/tapd/parse';
```

### 调试方法

1. 在Chrome扩展管理页面中点击扩展的"背景页"链接，打开后台脚本调试窗口
2. 在TAPD页面上右键点击，选择"检查"，然后切换到"Console"标签，可以看到内容脚本的日志

## 注意事项

1. 由于浏览器安全限制，跨域图片可能无法转换为Base64编码
2. 大型图片可能会导致性能问题，默认限制图片大小为2MB
3. 在生产环境中使用时，请确保API地址配置正确
