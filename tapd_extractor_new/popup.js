/**
 * TAPD内容提取器 - 弹出窗口脚本
 */

// 获取DOM元素
const extractBtn = document.getElementById('extractBtn');
const statusDiv = document.getElementById('status');
const enableBase64Checkbox = document.getElementById('enableBase64');
const autoExtractCheckbox = document.getElementById('autoExtract');

// 设置状态显示
function setStatus(message, type = 'info') {
  statusDiv.textContent = message;
  statusDiv.className = `status ${type}`;
}

// 从存储中加载设置
function loadSettings() {
  chrome.storage.sync.get({
    enableBase64: true,
    autoExtract: false
  }, (items) => {
    enableBase64Checkbox.checked = items.enableBase64;
    autoExtractCheckbox.checked = items.autoExtract;
  });
}

// 保存设置到存储
function saveSettings() {
  chrome.storage.sync.set({
    enableBase64: enableBase64Checkbox.checked,
    autoExtract: autoExtractCheckbox.checked
  });
}

// 提取内容
async function extractContent() {
  try {
    setStatus('正在提取内容...', 'info');
    extractBtn.disabled = true;
    
    // 获取当前标签页
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    
    // 检查是否是TAPD页面
    if (!tab.url.includes('tapd.cn')) {
      setStatus('当前页面不是TAPD页面', 'error');
      extractBtn.disabled = false;
      return;
    }
    
    // 向内容脚本发送消息
    chrome.tabs.sendMessage(tab.id, { 
      action: 'extractContent',
      settings: {
        enableBase64: enableBase64Checkbox.checked
      }
    }, async (response) => {
      if (chrome.runtime.lastError) {
        setStatus(`错误: ${chrome.runtime.lastError.message}`, 'error');
        extractBtn.disabled = false;
        return;
      }
      
      if (!response || !response.success) {
        setStatus(`提取失败: ${response?.error || '未知错误'}`, 'error');
        extractBtn.disabled = false;
        return;
      }
      
      // 发送到后端
      try {
        const apiEndpoint = 'http://localhost:8000/api/v1/reqAgent/tapd/parse';
        const result = await fetch(apiEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(response.data)
        });
        
        if (!result.ok) {
          throw new Error(`HTTP错误: ${result.status}`);
        }
        
        const data = await result.json();
        setStatus('内容提取成功并发送到后端', 'success');
      } catch (error) {
        setStatus(`发送到后端失败: ${error.message}`, 'error');
      }
      
      extractBtn.disabled = false;
    });
  } catch (error) {
    setStatus(`错误: ${error.message}`, 'error');
    extractBtn.disabled = false;
  }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
  // 加载设置
  loadSettings();
  
  // 提取按钮点击事件
  extractBtn.addEventListener('click', extractContent);
  
  // 设置变更事件
  enableBase64Checkbox.addEventListener('change', saveSettings);
  autoExtractCheckbox.addEventListener('change', saveSettings);
});
