<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TAPD内容提取器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            width: 300px;
            padding: 10px;
        }
        h1 {
            font-size: 18px;
            margin-top: 0;
            color: #333;
        }
        .status {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 8px 12px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .settings {
            margin-top: 15px;
            border-top: 1px solid #eee;
            padding-top: 10px;
        }
        .settings label {
            display: block;
            margin: 5px 0;
        }
        .footer {
            margin-top: 15px;
            font-size: 12px;
            color: #666;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>TAPD内容提取器</h1>
    
    <div id="status" class="status info">
        准备就绪，点击下方按钮提取内容
    </div>
    
    <button id="extractBtn">提取当前页面内容</button>
    
    <div class="settings">
        <label>
            <input type="checkbox" id="enableBase64" checked>
            启用图片Base64编码
        </label>
        <label>
            <input type="checkbox" id="autoExtract">
            自动提取内容（页面加载完成后）
        </label>
    </div>
    
    <div class="footer">
        版本 1.0 | 用于AItest管理系统
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
