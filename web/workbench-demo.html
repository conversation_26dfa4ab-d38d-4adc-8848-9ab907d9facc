<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI测试平台工作台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .workbench-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .test-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 2px solid #10b981;
        }
        
        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 30px;
            color: white;
            margin-bottom: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .welcome-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .user-info h2 {
            font-size: 28px;
            margin-bottom: 8px;
        }
        
        .user-info p {
            opacity: 0.9;
            margin-bottom: 4px;
        }
        
        .quick-stats {
            display: flex;
            gap: 40px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .metric-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border: 1px solid rgba(109, 40, 217, 0.1);
        }
        
        .metric-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        
        .metric-content {
            display: flex;
            align-items: center;
        }
        
        .metric-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 20px;
        }
        
        .metric-info {
            flex: 1;
        }
        
        .metric-value {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .metric-label {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 8px;
        }
        
        .metric-trend {
            font-size: 12px;
            font-weight: 500;
        }
        
        .metric-trend.positive {
            color: #10b981;
        }
        
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .chart-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(109, 40, 217, 0.1);
        }
        
        .chart-header {
            padding: 20px 24px 0;
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            border-bottom: 1px solid rgba(109, 40, 217, 0.08);
            margin-bottom: 20px;
        }
        
        .chart-container {
            height: 280px;
            padding: 0 24px 24px;
            position: relative;
        }
        
        .chart-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #64748b;
            background: #f8fafc;
            border-radius: 8px;
            border: 2px dashed #e2e8f0;
        }
        
        .chart-placeholder p {
            margin: 8px 0;
            font-size: 14px;
        }
        
        .chart-placeholder p:first-child {
            font-size: 16px;
            font-weight: 600;
        }
        
        .activity-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(109, 40, 217, 0.1);
        }
        
        .activity-list {
            padding: 24px;
        }
        
        .activity-item {
            display: flex;
            align-items: flex-start;
            padding: 16px 0;
            border-bottom: 1px solid rgba(109, 40, 217, 0.08);
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            flex-shrink: 0;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .activity-desc {
            font-size: 13px;
            color: #64748b;
            margin-bottom: 4px;
        }
        
        .activity-time {
            font-size: 12px;
            color: #94a3b8;
        }
        
        @media (max-width: 768px) {
            .workbench-container {
                padding: 16px;
            }
            
            .welcome-content {
                flex-direction: column;
                text-align: center;
                gap: 20px;
            }
            
            .quick-stats {
                gap: 16px;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .charts-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="workbench-container">
        <!-- 测试内容 -->
        <div class="test-card">
            <h3>🎉 工作台页面测试成功！</h3>
            <p>如果您能看到这个内容，说明页面基本结构正常</p>
            <p>当前时间：<span id="current-time"></span></p>
            <p>这是一个简化版的工作台页面，展示了基本的布局和样式</p>
        </div>

        <!-- 欢迎区域 -->
        <div class="welcome-card">
            <div class="welcome-content">
                <div class="user-info">
                    <h2>您好，管理员！</h2>
                    <p>欢迎使用AI测试平台工作台</p>
                    <p id="welcome-time"></p>
                </div>
                <div class="quick-stats">
                    <div class="stat-item">
                        <div class="stat-value">8</div>
                        <div class="stat-label">今日新增</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">23</div>
                        <div class="stat-label">本周完成</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">15</div>
                        <div class="stat-label">待处理</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 核心指标卡片 -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-content">
                    <div class="metric-icon" style="background-color: rgba(99, 102, 241, 0.2); color: #6366f1;">
                        📁
                    </div>
                    <div class="metric-info">
                        <div class="metric-value">5</div>
                        <div class="metric-label">项目总数</div>
                        <div class="metric-trend positive">↗ 12%</div>
                    </div>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-content">
                    <div class="metric-icon" style="background-color: rgba(16, 185, 129, 0.2); color: #10b981;">
                        📄
                    </div>
                    <div class="metric-info">
                        <div class="metric-value">45</div>
                        <div class="metric-label">需求总数</div>
                        <div class="metric-trend positive">↗ 8%</div>
                    </div>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-content">
                    <div class="metric-icon" style="background-color: rgba(245, 158, 11, 0.2); color: #f59e0b;">
                        🧪
                    </div>
                    <div class="metric-info">
                        <div class="metric-value">128</div>
                        <div class="metric-label">测试用例</div>
                        <div class="metric-trend positive">↗ 15%</div>
                    </div>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-content">
                    <div class="metric-icon" style="background-color: rgba(239, 68, 68, 0.2); color: #ef4444;">
                        🧠
                    </div>
                    <div class="metric-info">
                        <div class="metric-value">32</div>
                        <div class="metric-label">知识条目</div>
                        <div class="metric-trend positive">↗ 6%</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-grid">
            <div class="chart-card">
                <div class="chart-header">项目分布</div>
                <div class="chart-container">
                    <div class="chart-placeholder">
                        <p>📊 项目分布图表</p>
                        <p>在Vue应用中，这里会显示ECharts图表</p>
                    </div>
                </div>
            </div>
            
            <div class="chart-card">
                <div class="chart-header">测试用例统计</div>
                <div class="chart-container">
                    <div class="chart-placeholder">
                        <p>📈 测试用例统计图表</p>
                        <p>在Vue应用中，这里会显示ECharts图表</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近活动 -->
        <div class="activity-card">
            <div class="chart-header">最近活动</div>
            <div class="activity-list">
                <div class="activity-item">
                    <div class="activity-icon" style="background-color: rgba(16, 185, 129, 0.2);">
                        <span style="color: #10b981;">➕</span>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">新增测试用例</div>
                        <div class="activity-desc">用户张三创建了5个新的测试用例</div>
                        <div class="activity-time">2分钟前</div>
                    </div>
                </div>
                
                <div class="activity-item">
                    <div class="activity-icon" style="background-color: rgba(99, 102, 241, 0.2);">
                        <span style="color: #6366f1;">📝</span>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">需求更新</div>
                        <div class="activity-desc">需求"用户登录功能"已更新</div>
                        <div class="activity-time">15分钟前</div>
                    </div>
                </div>
                
                <div class="activity-item">
                    <div class="activity-icon" style="background-color: rgba(245, 158, 11, 0.2);">
                        <span style="color: #f59e0b;">🧠</span>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">知识库更新</div>
                        <div class="activity-desc">新增了3条业务规则知识点</div>
                        <div class="activity-time">1小时前</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 更新时间
        function updateTime() {
            const now = new Date();
            const timeStr = now.getFullYear() + '年' + 
                          (now.getMonth() + 1).toString().padStart(2, '0') + '月' + 
                          now.getDate().toString().padStart(2, '0') + '日 ' +
                          now.getHours().toString().padStart(2, '0') + ':' + 
                          now.getMinutes().toString().padStart(2, '0') + ':' + 
                          now.getSeconds().toString().padStart(2, '0');
            
            document.getElementById('current-time').textContent = timeStr;
            document.getElementById('welcome-time').textContent = timeStr;
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            updateTime();
            setInterval(updateTime, 1000);
        });
    </script>
</body>
</html>
