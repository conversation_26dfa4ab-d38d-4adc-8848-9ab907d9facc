## 快速开始

进入前端目录

```sh
cd web
```

安装依赖(建议使用pnpm: https://pnpm.io/zh/installation)

```sh
npm i -g pnpm # 已安装可忽略
pnpm i # 或者 npm i
```

启动

```sh
pnpm dev
```


1.构建镜像
docker build -t vue-fastapi-admin-frontend .

2.运行容器（指定后端地址）
docker run -d -p 80:80 \
  -e BACKEND_URL=http://your-backend-host:9999 \
  -e API_PREFIX=/api/v1 \
  --name frontend vue-fastapi-admin-frontend


3.运行在生产环境
docker run -d -p 80:80 -e BACKEND_URL=http://api.example.com --name frontend vue-fastapi-admin-frontend

docker run -d -p 80:80 -e BACKEND_URL=http://localhost:9999 --name frontend vue-fastapi-admin-frontend

