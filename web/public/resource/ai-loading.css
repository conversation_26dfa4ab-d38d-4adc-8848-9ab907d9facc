/* AI测试加载样式 */
.ai-loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.ai-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.ai-loading-icon {
  width: 120px;
  height: 120px;
}

.ai-loading-text {
  margin-top: 16px;
  font-size: 16px;
  color: var(--primary-color, #0EA5E9);
  font-weight: 500;
}

/* 动画效果 */
.brain {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    stroke-width: 2;
    opacity: 0.8;
  }
  50% {
    stroke-width: 3;
    opacity: 1;
  }
  100% {
    stroke-width: 2;
    opacity: 0.8;
  }
}

/* 神经节点动画 */
.node {
  animation: nodeGlow 2s infinite;
}

.node1 {
  animation-delay: 0s;
}

.node2 {
  animation-delay: 0.3s;
}

.node3 {
  animation-delay: 0.6s;
}

@keyframes nodeGlow {
  0% {
    r: 4;
    opacity: 0.7;
  }
  50% {
    r: 5;
    opacity: 1;
  }
  100% {
    r: 4;
    opacity: 0.7;
  }
}

/* 测试元素动画 */
.test-case {
  animation: testCasePulse 3s infinite;
}

@keyframes testCasePulse {
  0% {
    stroke-width: 1.5;
  }
  50% {
    stroke-width: 2;
  }
  100% {
    stroke-width: 1.5;
  }
}

.checkmark {
  animation: drawCheckmark 3s infinite;
  stroke-dasharray: 30;
  stroke-dashoffset: 30;
}

@keyframes drawCheckmark {
  0% {
    stroke-dashoffset: 30;
  }
  50% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

.testing {
  animation: testingPulse 1s infinite;
}

@keyframes testingPulse {
  0% {
    r: 3;
    opacity: 0.7;
  }
  50% {
    r: 4;
    opacity: 1;
  }
  100% {
    r: 3;
    opacity: 0.7;
  }
}

.error {
  animation: errorFlash 2s infinite;
}

@keyframes errorFlash {
  0% {
    stroke-width: 2;
    opacity: 0.7;
  }
  50% {
    stroke-width: 2.5;
    opacity: 1;
  }
  100% {
    stroke-width: 2;
    opacity: 0.7;
  }
}

/* 数据流动动画 */
.data-point {
  animation: moveAlongPath 4s infinite linear;
}

.dp1 {
  animation-delay: 0s;
}

.dp2 {
  animation-delay: 1.3s;
}

.dp3 {
  animation-delay: 2.6s;
}

@keyframes moveAlongPath {
  0% {
    transform: translate(50px, 35px);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  30% {
    transform: translate(35px, 40px);
  }
  50% {
    transform: translate(25px, 60px);
  }
  70% {
    transform: translate(25px, 75px);
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translate(25px, 85px);
    opacity: 0;
  }
}

/* 设置默认主题色变量，以防CSS变量未定义 */
:root {
  --primary-color: #0EA5E9;
  --success-color: #10B981;
  --warning-color: #F59E0B;
  --error-color: #EF4444;
  --info-color: #2563EB;
}
