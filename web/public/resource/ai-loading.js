// 设置主题色变量
function addThemeColorCssVars() {
  const key = '__THEME_COLOR__';
  const defaultColor = '#0EA5E9'; // 默认主题色
  const themeColor = window.localStorage.getItem(key) || defaultColor;
  
  // 设置CSS变量
  document.documentElement.style.setProperty('--primary-color', themeColor);
  document.documentElement.style.setProperty('--info-color', '#2563EB');
  document.documentElement.style.setProperty('--success-color', '#10B981');
  document.documentElement.style.setProperty('--warning-color', '#F59E0B');
  document.documentElement.style.setProperty('--error-color', '#EF4444');
}

// 初始化主题色
addThemeColorCssVars();

// 在Vue应用加载完成后隐藏初始加载动画
window.addEventListener('load', function() {
  // 给加载容器添加淡出动画
  const loadingContainer = document.querySelector('.ai-loading-container');
  if (loadingContainer) {
    loadingContainer.style.transition = 'opacity 0.5s ease';
    loadingContainer.style.opacity = '0';
    
    // 动画结束后移除加载容器
    setTimeout(function() {
      loadingContainer.style.display = 'none';
    }, 500);
  }
});
