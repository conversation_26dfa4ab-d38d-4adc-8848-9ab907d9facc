#!/bin/sh

# 确保环境变量正确设置
# 如果BACKEND_URL为空，则使用空字符串，让前端自动使用当前访问的服务器地址
export BACKEND_URL=${BACKEND_URL:-""}

# 如果 BACKEND_URL 已经包含 /api/v1，则设置 API_PREFIX 为空字符串
if [ -n "$BACKEND_URL" ] && echo "$BACKEND_URL" | grep -q "/api/v1"; then
  export API_PREFIX=""
else
  export API_PREFIX=${API_PREFIX:-"/api/v1"}
fi

# 打印环境变量，便于调试
echo "BACKEND_URL: $BACKEND_URL"
echo "API_PREFIX: $API_PREFIX"

# 替换前端构建文件中的环境变量
find /usr/share/nginx/html -type f -name "*.js" -exec sed -i "s|VITE_BASE_API:\".*\"|VITE_BASE_API:\"${BACKEND_URL}\"|g" {} \;
find /usr/share/nginx/html -type f -name "*.js" -exec sed -i "s|VITE_API_PREFIX:\".*\"|VITE_API_PREFIX:\"${API_PREFIX}\"|g" {} \;

# 替换 nginx 配置中的环境变量
envsubst '$BACKEND_URL $API_PREFIX' < /etc/nginx/conf.d/default.conf.template > /etc/nginx/conf.d/default.conf

# 启动 nginx
nginx -g 'daemon off;'
