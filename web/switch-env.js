#!/usr/bin/env node

/**
 * 环境切换脚本
 * 用于快速切换不同的开发环境配置
 */

const fs = require('fs');
const path = require('path');

const configs = {
  local: {
    name: '本地开发',
    content: `# 本地开发环境配置
# 是否启用代理
VITE_USE_PROXY=true

# base api - 使用空字符串让前端动态获取当前服务器地址
VITE_BASE_API=''
VITE_API_PREFIX='/api/v1'

# 代理目标服务器 - 本地后端
VITE_PROXY_TARGET='http://localhost:9999'

# WebSocket base api - 使用相对路径，由前端代码动态构建
VITE_WS_BASE_API=''

# 网站标题
VITE_TITLE='AItest管理系统'`
  },
  remote: {
    name: '远程开发',
    content: `# 远程开发环境配置
# 是否启用代理
VITE_USE_PROXY=true

# base api - 使用空字符串让前端动态获取当前服务器地址
VITE_BASE_API=''
VITE_API_PREFIX='/api/v1'

# 代理目标服务器 - 远程后端
VITE_PROXY_TARGET='http://************:9999'

# WebSocket base api - 使用相对路径，由前端代码动态构建
VITE_WS_BASE_API=''

# 网站标题
VITE_TITLE='AItest管理系统'`
  },
  dynamic: {
    name: '动态获取',
    content: `# 动态获取环境配置
# 是否启用代理
VITE_USE_PROXY=false

# base api - 使用空字符串让前端动态获取当前服务器地址
VITE_BASE_API=''
VITE_API_PREFIX='/api/v1'

# WebSocket base api - 使用相对路径，由前端代码动态构建
VITE_WS_BASE_API=''

# 网站标题
VITE_TITLE='AItest管理系统'`
  }
};

function showUsage() {
  console.log('🔧 环境切换脚本');
  console.log('\n用法: node switch-env.js <环境名称>');
  console.log('\n可用环境:');
  Object.keys(configs).forEach(key => {
    console.log(`  ${key} - ${configs[key].name}`);
  });
  console.log('\n示例:');
  console.log('  node switch-env.js local   # 切换到本地开发环境');
  console.log('  node switch-env.js remote  # 切换到远程开发环境');
  console.log('  node switch-env.js dynamic # 切换到动态获取环境');
}

function switchEnv(envName) {
  const config = configs[envName];
  if (!config) {
    console.error(`❌ 未知环境: ${envName}`);
    showUsage();
    process.exit(1);
  }

  const envLocalPath = path.join(__dirname, '.env.local');
  
  try {
    fs.writeFileSync(envLocalPath, config.content);
    console.log(`✅ 已切换到 ${config.name} 环境`);
    console.log(`📝 配置已写入 .env.local`);
    console.log('\n⚠️  请重启开发服务器以使配置生效');
  } catch (error) {
    console.error(`❌ 写入配置文件失败:`, error.message);
    process.exit(1);
  }
}

// 主程序
const args = process.argv.slice(2);

if (args.length === 0) {
  showUsage();
  process.exit(0);
}

const envName = args[0];
switchEnv(envName);
