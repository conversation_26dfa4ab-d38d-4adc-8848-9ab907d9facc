{"name": "vue-fastapi-admin-web", "version": "0.0.0", "private": true, "scripts": {"dev": "vite --port 3100", "build": "vite build", "preview": "vite preview", "lint": "eslint --ext .js,.vue .", "lint:fix": "eslint --fix --ext .js,.vue .", "lint:staged": "lint-staged", "prettier": "npx prettier --write ."}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@iconify/json": "^2.2.324", "@iconify/vue": "^4.3.0", "@unocss/eslint-config": "^0.55.7", "@vicons/ionicons5": "^0.13.0", "@vueuse/core": "^10.11.1", "@zclzone/eslint-config": "^0.0.4", "ant-design-vue": "^4.2.6", "axios": "^1.8.4", "dayjs": "^1.11.13", "dompurify": "^3.2.5", "dotenv": "^16.4.7", "echarts": "^5.6.0", "element-plus": "^2.9.7", "eslint": "^8.57.1", "highlight.js": "^11.11.1", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "marked": "^15.0.7", "mind-elixir": "^4.5.0", "naive-ui": "^2.41.0", "pinia": "^2.3.1", "rollup-plugin-visualizer": "^5.14.0", "sass": "^1.77.8", "typescript": "^5.8.3", "unocss": "^0.55.7", "unplugin-auto-import": "^0.16.7", "unplugin-icons": "^0.16.6", "unplugin-vue-components": "^0.25.2", "uuid": "^11.1.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.2", "vite-plugin-svg-icons": "2.0.1", "vue": "3.3.4", "vue-fastapi-admin-web": "file:", "vue-i18n": "^9.14.4", "vue-router": "^4.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.6.2", "@vitejs/plugin-vue-jsx": "^4.1.2", "vite": "^4.5.13"}, "lint-staged": {"*.{js,vue}": ["eslint --ext .js,.vue ."]}, "eslintConfig": {"extends": ["@zclzone", "@unocss", ".eslint-global-variables.json"]}, "pnpm": {"overrides": {}}}