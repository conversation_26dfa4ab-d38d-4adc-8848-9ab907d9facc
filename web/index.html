<!doctype html>
<html lang="cn">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="Expires" content="0" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Cache-control" content="no-cache" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" href="/favicon.svg" />
    <link rel="stylesheet" href="/resource/ai-loading.css" />

    <title><%= title %></title>
    <!-- 添加Monaco编辑器CDN -->
    <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.43.0/min/vs/loader.js"></script>
    <script>
      require.config({
        paths: {
          'vs': 'https://cdn.jsdelivr.net/npm/monaco-editor@0.43.0/min/vs'
        }
      });
      window.MonacoEnvironment = {
        getWorkerUrl: function (workerId, label) {
          return `data:text/javascript;charset=utf-8,${encodeURIComponent(`
            self.MonacoEnvironment = {
              baseUrl: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.43.0/min/'
            };
            importScripts('https://cdn.jsdelivr.net/npm/monaco-editor@0.43.0/min/vs/base/worker/workerMain.js');
          `)}`;
        }
      };
    </script>
    <!-- 字体加载已移除，因为字体文件不存在 -->
  </head>

  <body>
    <div id="app">
      <!-- 白屏时的AI测试loading效果 -->
      <div class="ai-loading-container">
        <div class="ai-loading-content">
          <svg class="ai-loading-icon" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <!-- 大脑图标（代表AI） -->
            <path class="brain" d="M50 15 C60 15, 70 25, 70 35 C70 45, 60 55, 50 55 C40 55, 30 45, 30 35 C30 25, 40 15, 50 15" fill="none" stroke="var(--primary-color)" stroke-width="2" />

            <!-- 神经网络连接线 -->
            <g class="neural-network">
              <line x1="35" y1="40" x2="25" y2="60" stroke="var(--primary-color)" stroke-width="1.5" />
              <line x1="50" y1="55" x2="50" y2="70" stroke="var(--primary-color)" stroke-width="1.5" />
              <line x1="65" y1="40" x2="75" y2="60" stroke="var(--primary-color)" stroke-width="1.5" />

              <!-- 神经节点 -->
              <circle class="node node1" cx="25" cy="60" r="4" fill="var(--primary-color)" />
              <circle class="node node2" cx="50" cy="70" r="4" fill="var(--primary-color)" />
              <circle class="node node3" cx="75" cy="60" r="4" fill="var(--primary-color)" />
            </g>

            <!-- 测试元素 -->
            <g class="test-elements">
              <rect class="test-case" x="15" y="75" width="20" height="10" rx="2" fill="none" stroke="var(--primary-color)" stroke-width="1.5" />
              <rect class="test-case" x="40" y="75" width="20" height="10" rx="2" fill="none" stroke="var(--primary-color)" stroke-width="1.5" />
              <rect class="test-case" x="65" y="75" width="20" height="10" rx="2" fill="none" stroke="var(--primary-color)" stroke-width="1.5" />

              <!-- 对勾标记（代表测试通过） -->
              <path class="checkmark" d="M20 80 L25 85 L35 75" fill="none" stroke="var(--success-color)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />

              <!-- 测试中标记（代表测试进行中） -->
              <circle class="testing" cx="50" cy="80" r="3" fill="var(--warning-color)" />

              <!-- 错误标记（代表测试失败） -->
              <path class="error" d="M70 75 L80 85 M80 75 L70 85" fill="none" stroke="var(--error-color)" stroke-width="2" stroke-linecap="round" />
            </g>

            <!-- 数据流动效果 -->
            <g class="data-flow">
              <circle class="data-point dp1" cx="0" cy="0" r="1.5" fill="var(--primary-color)" />
              <circle class="data-point dp2" cx="0" cy="0" r="1.5" fill="var(--primary-color)" />
              <circle class="data-point dp3" cx="0" cy="0" r="1.5" fill="var(--primary-color)" />
            </g>
          </svg>
          <div class="ai-loading-text"><%= title %></div>
        </div>
      </div>
    </div>
    <script src="/resource/ai-loading.js"></script>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
