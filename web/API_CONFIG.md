# 前端API配置说明

## 问题描述

之前前端登录时访问的是硬编码的远程IP地址 `http://************:9999`，导致无法灵活切换不同的后端服务器。

## 解决方案

现在前端支持动态获取API地址，可以根据不同的部署环境自动使用正确的服务器地址。

## 配置方式

### 1. 开发环境配置

#### 方式一：使用动态获取（推荐）
在 `web/.env.development` 中设置：
```bash
VITE_BASE_API=''  # 空字符串，自动使用当前访问的服务器地址
```

#### 方式二：使用本地配置文件
1. 复制 `web/.env.local.example` 为 `web/.env.local`
2. 根据需要修改配置：

```bash
# 本地开发
VITE_PROXY_TARGET='http://localhost:9999'

# 或者远程服务器开发
VITE_PROXY_TARGET='http://************:9999'
```

### 2. 生产环境配置

生产环境会自动使用当前访问的服务器地址，无需额外配置。

## 工作原理

1. **动态获取**：当 `VITE_BASE_API` 为空时，前端会使用 `window.location.origin` 获取当前访问的服务器地址
2. **环境变量优先**：如果设置了 `VITE_BASE_API`，则优先使用该配置
3. **代理配置**：开发环境下，Vite会将API请求代理到 `VITE_PROXY_TARGET` 指定的服务器

## 使用场景

### 场景1：本地开发，后端也在本地
```bash
# .env.local
VITE_PROXY_TARGET='http://localhost:9999'
```

### 场景2：本地开发，后端在远程服务器
```bash
# .env.local
VITE_PROXY_TARGET='http://************:9999'
```

### 场景3：部署到服务器，前后端在同一服务器
无需配置，自动使用当前服务器地址

### 场景4：部署到服务器，前后端在不同服务器
通过Docker环境变量或Nginx配置处理

## 快速切换工具

为了方便在不同环境之间切换，提供了环境切换脚本：

```bash
# 切换到本地开发环境
node switch-env.js local

# 切换到远程开发环境
node switch-env.js remote

# 切换到动态获取环境（生产模式）
node switch-env.js dynamic
```

## 注意事项

1. `.env.local` 文件已添加到 `.gitignore`，不会被提交到版本控制
2. 修改配置后需要重启开发服务器
3. 生产环境构建时会自动处理API地址配置
4. 使用切换脚本后，`.env.local` 会覆盖 `.env.development` 中的相同配置项
