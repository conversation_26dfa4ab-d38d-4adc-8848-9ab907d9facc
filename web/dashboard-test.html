<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作台测试页面</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 30px;
            color: white;
            margin-bottom: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .welcome-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .user-info h2 {
            font-size: 28px;
            margin-bottom: 8px;
        }
        
        .user-info p {
            opacity: 0.9;
            margin-bottom: 4px;
        }
        
        .quick-stats {
            display: flex;
            gap: 40px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .metric-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border: 1px solid rgba(109, 40, 217, 0.1);
        }
        
        .metric-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        
        .metric-content {
            display: flex;
            align-items: center;
        }
        
        .metric-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 24px;
        }
        
        .metric-info {
            flex: 1;
        }
        
        .metric-value {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .metric-label {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 8px;
        }
        
        .metric-trend {
            font-size: 12px;
            font-weight: 500;
        }
        
        .metric-trend.positive {
            color: #10b981;
        }
        
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
        
        .chart-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(109, 40, 217, 0.1);
        }
        
        .chart-header {
            padding: 20px 24px 0;
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            border-bottom: 1px solid rgba(109, 40, 217, 0.08);
            margin-bottom: 20px;
        }
        
        .chart-container {
            height: 300px;
            padding: 0 24px 24px;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- 欢迎区域 -->
        <div class="welcome-card">
            <div class="welcome-content">
                <div class="user-info">
                    <h2>您好，管理员！</h2>
                    <p>欢迎使用AI测试平台工作台</p>
                    <p id="current-time"></p>
                </div>
                <div class="quick-stats">
                    <div class="stat-item">
                        <div class="stat-value">8</div>
                        <div class="stat-label">今日新增</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">23</div>
                        <div class="stat-label">本周完成</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">15</div>
                        <div class="stat-label">待处理</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 核心指标卡片 -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-content">
                    <div class="metric-icon" style="background-color: rgba(99, 102, 241, 0.2); color: #6366f1;">
                        📁
                    </div>
                    <div class="metric-info">
                        <div class="metric-value">5</div>
                        <div class="metric-label">项目总数</div>
                        <div class="metric-trend positive">↗ 12%</div>
                    </div>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-content">
                    <div class="metric-icon" style="background-color: rgba(16, 185, 129, 0.2); color: #10b981;">
                        📄
                    </div>
                    <div class="metric-info">
                        <div class="metric-value">45</div>
                        <div class="metric-label">需求总数</div>
                        <div class="metric-trend positive">↗ 8%</div>
                    </div>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-content">
                    <div class="metric-icon" style="background-color: rgba(245, 158, 11, 0.2); color: #f59e0b;">
                        🧪
                    </div>
                    <div class="metric-info">
                        <div class="metric-value">128</div>
                        <div class="metric-label">测试用例</div>
                        <div class="metric-trend positive">↗ 15%</div>
                    </div>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-content">
                    <div class="metric-icon" style="background-color: rgba(239, 68, 68, 0.2); color: #ef4444;">
                        🧠
                    </div>
                    <div class="metric-info">
                        <div class="metric-value">32</div>
                        <div class="metric-label">知识条目</div>
                        <div class="metric-trend positive">↗ 6%</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-grid">
            <div class="chart-card">
                <div class="chart-header">项目分布</div>
                <div class="chart-container" id="projectChart"></div>
            </div>
            
            <div class="chart-card">
                <div class="chart-header">测试用例趋势</div>
                <div class="chart-container" id="testCaseChart"></div>
            </div>
        </div>
    </div>

    <script>
        // 更新时间
        function updateTime() {
            const now = new Date();
            const timeStr = now.getFullYear() + '年' + 
                          (now.getMonth() + 1).toString().padStart(2, '0') + '月' + 
                          now.getDate().toString().padStart(2, '0') + '日 ' +
                          now.getHours().toString().padStart(2, '0') + ':' + 
                          now.getMinutes().toString().padStart(2, '0') + ':' + 
                          now.getSeconds().toString().padStart(2, '0');
            document.getElementById('current-time').textContent = timeStr;
        }
        
        // 初始化项目分布图表
        function initProjectChart() {
            const chart = echarts.init(document.getElementById('projectChart'));
            const option = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 'left'
                },
                series: [
                    {
                        name: '项目分布',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        data: [
                            { value: 5, name: '进行中', itemStyle: { color: '#6366f1' } },
                            { value: 3, name: '已完成', itemStyle: { color: '#10b981' } },
                            { value: 1, name: '暂停', itemStyle: { color: '#f59e0b' } },
                            { value: 2, name: '计划中', itemStyle: { color: '#8b5cf6' } }
                        ]
                    }
                ]
            };
            chart.setOption(option);
        }
        
        // 初始化测试用例趋势图表
        function initTestCaseChart() {
            const chart = echarts.init(document.getElementById('testCaseChart'));
            const option = {
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['新增用例', '执行用例', '通过用例']
                },
                xAxis: {
                    type: 'category',
                    data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '新增用例',
                        type: 'line',
                        data: [12, 13, 10, 13, 9, 23, 21],
                        itemStyle: { color: '#6366f1' },
                        areaStyle: { opacity: 0.3 }
                    },
                    {
                        name: '执行用例',
                        type: 'line',
                        data: [22, 18, 19, 23, 29, 33, 31],
                        itemStyle: { color: '#10b981' },
                        areaStyle: { opacity: 0.3 }
                    },
                    {
                        name: '通过用例',
                        type: 'line',
                        data: [15, 23, 20, 15, 19, 33, 31],
                        itemStyle: { color: '#f59e0b' },
                        areaStyle: { opacity: 0.3 }
                    }
                ]
            };
            chart.setOption(option);
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            updateTime();
            setInterval(updateTime, 1000);
            
            setTimeout(() => {
                initProjectChart();
                initTestCaseChart();
            }, 100);
        });
        
        // 窗口大小变化时重新调整图表
        window.addEventListener('resize', function() {
            const projectChart = echarts.getInstanceByDom(document.getElementById('projectChart'));
            const testCaseChart = echarts.getInstanceByDom(document.getElementById('testCaseChart'));
            
            if (projectChart) projectChart.resize();
            if (testCaseChart) testCaseChart.resize();
        });
    </script>
</body>
</html>
