<template>
  <div class="workbench-container">
    <!-- 顶部欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <div class="welcome-left">
          <h1 class="welcome-title">🎉 AI测试平台工作台</h1>
          <p class="welcome-subtitle">欢迎回来！这里是您的数据中心和控制面板</p>
          <p class="current-time">{{ currentTime }}</p>
        </div>
        <div class="welcome-right">
          <div class="quick-actions">
            <button @click="refreshAllData" class="action-btn primary">
              <span class="btn-icon">🔄</span>
              刷新数据
            </button>
            <button @click="exportReport" class="action-btn secondary">
              <span class="btn-icon">📊</span>
              导出报告
            </button>
            <button @click="openSettings" class="action-btn tertiary">
              <span class="btn-icon">⚙️</span>
              设置
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 核心统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card primary" @click="navigateToProjects">
        <div class="stat-header">
          <div class="stat-icon">📁</div>
          <div class="stat-trend positive">
            <span class="trend-icon">↗</span>
            <span class="trend-value">+{{ stats.todayNew || 0 }}</span>
          </div>
        </div>
        <div class="stat-value">{{ stats.projects }}</div>
        <div class="stat-label">项目总数</div>
        <div class="stat-desc">今日新增 {{ stats.todayNew || 0 }} 个</div>
      </div>

      <div class="stat-card success" @click="navigateToRequirements">
        <div class="stat-header">
          <div class="stat-icon">📄</div>
          <div class="stat-trend positive">
            <span class="trend-icon">↗</span>
            <span class="trend-value">+{{ stats.weekCompleted || 0 }}</span>
          </div>
        </div>
        <div class="stat-value">{{ stats.requirements }}</div>
        <div class="stat-label">需求总数</div>
        <div class="stat-desc">本周完成 {{ stats.weekCompleted || 0 }} 个</div>
      </div>

      <div class="stat-card warning" @click="navigateToTestCases">
        <div class="stat-header">
          <div class="stat-icon">🧪</div>
          <div class="stat-trend positive">
            <span class="trend-icon">↗</span>
            <span class="trend-value">{{ testCasePassRate }}%</span>
          </div>
        </div>
        <div class="stat-value">{{ stats.testcases }}</div>
        <div class="stat-label">测试用例</div>
        <div class="stat-desc">通过率 {{ testCasePassRate }}%</div>
      </div>

      <div class="stat-card danger" @click="navigateToKnowledge">
        <div class="stat-header">
          <div class="stat-icon">🧠</div>
          <div class="stat-trend positive">
            <span class="trend-icon">↗</span>
            <span class="trend-value">+{{ stats.knowledge || 0 }}</span>
          </div>
        </div>
        <div class="stat-value">{{ stats.knowledge }}</div>
        <div class="stat-label">知识条目</div>
        <div class="stat-desc">智能推荐系统</div>
      </div>
    </div>

    <!-- 主要图表区域 -->
    <div class="main-charts">
      <div class="chart-row">
        <div class="chart-card large">
          <div class="chart-header">
            <h3 class="chart-title">📈 测试用例执行趋势</h3>
            <div class="chart-controls">
              <select v-model="trendDays" @change="fetchTestCaseTrend" class="chart-select">
                <option value="7">最近7天</option>
                <option value="14">最近14天</option>
                <option value="30">最近30天</option>
              </select>
            </div>
          </div>
          <div ref="testCaseChartRef" class="chart-container large"></div>
        </div>

        <div class="chart-card medium">
          <div class="chart-header">
            <h3 class="chart-title">📊 项目状态分布</h3>
            <div class="chart-legend">
              <div class="legend-item">
                <span class="legend-dot active"></span>
                <span class="legend-text">进行中</span>
              </div>
              <div class="legend-item">
                <span class="legend-dot completed"></span>
                <span class="legend-text">已完成</span>
              </div>
            </div>
          </div>
          <div ref="projectChartRef" class="chart-container medium"></div>
        </div>
      </div>

      <div class="chart-row">
        <div class="chart-card medium">
          <div class="chart-header">
            <h3 class="chart-title">🎯 测试覆盖率分析</h3>
            <div class="coverage-summary">
              <span class="coverage-value">{{ testCoverage }}%</span>
            </div>
          </div>
          <div ref="coverageChartRef" class="chart-container medium"></div>
        </div>

        <div class="chart-card medium">
          <div class="chart-header">
            <h3 class="chart-title">⚡ 平台活跃度</h3>
            <div class="activity-summary">
              <span class="activity-value">{{ platformActivity }}</span>
            </div>
          </div>
          <div ref="activityChartRef" class="chart-container medium"></div>
        </div>
      </div>
    </div>

    <!-- 底部信息面板 -->
    <div class="info-panels">
      <div class="panel-row">
        <!-- 最近活动 -->
        <div class="info-panel">
          <div class="panel-header">
            <h3 class="panel-title">📋 最近活动</h3>
            <button @click="refreshActivities" class="refresh-btn">
              <span class="refresh-icon">🔄</span>
            </button>
          </div>
          <div class="activity-list">
            <div v-for="(activity, index) in activities" :key="index" class="activity-item">
              <div class="activity-icon" :class="activity.type">
                <span>{{ activity.icon }}</span>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-desc">{{ activity.description }}</div>
                <div class="activity-time">{{ activity.time }}</div>
              </div>
              <div class="activity-status" :class="activity.status">
                {{ activity.statusText }}
              </div>
            </div>
          </div>
        </div>

        <!-- 快速操作 -->
        <div class="info-panel">
          <div class="panel-header">
            <h3 class="panel-title">⚡ 快速操作</h3>
          </div>
          <div class="quick-operations">
            <div class="operation-item" @click="createNewProject">
              <div class="operation-icon">➕</div>
              <div class="operation-content">
                <div class="operation-title">新建项目</div>
                <div class="operation-desc">创建新的测试项目</div>
              </div>
            </div>
            <div class="operation-item" @click="generateTestCase">
              <div class="operation-icon">🧪</div>
              <div class="operation-content">
                <div class="operation-title">生成测试用例</div>
                <div class="operation-desc">AI智能生成测试用例</div>
              </div>
            </div>
            <div class="operation-item" @click="analyzeRequirement">
              <div class="operation-icon">📊</div>
              <div class="operation-content">
                <div class="operation-title">需求分析</div>
                <div class="operation-desc">智能分析需求文档</div>
              </div>
            </div>
            <div class="operation-item" @click="viewReports">
              <div class="operation-icon">📈</div>
              <div class="operation-content">
                <div class="operation-title">查看报告</div>
                <div class="operation-desc">测试执行报告</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 系统状态 -->
        <div class="info-panel">
          <div class="panel-header">
            <h3 class="panel-title">🔧 系统状态</h3>
            <div class="system-status" :class="systemStatus.overall">
              {{ systemStatus.text }}
            </div>
          </div>
          <div class="status-list">
            <div class="status-item">
              <div class="status-label">数据库连接</div>
              <div class="status-indicator" :class="systemStatus.database">
                <span class="status-dot"></span>
                <span class="status-text">{{ systemStatus.databaseText }}</span>
              </div>
            </div>
            <div class="status-item">
              <div class="status-label">AI服务</div>
              <div class="status-indicator" :class="systemStatus.ai">
                <span class="status-dot"></span>
                <span class="status-text">{{ systemStatus.aiText }}</span>
              </div>
            </div>
            <div class="status-item">
              <div class="status-label">WebSocket</div>
              <div class="status-indicator" :class="systemStatus.websocket">
                <span class="status-dot"></span>
                <span class="status-text">{{ systemStatus.websocketText }}</span>
              </div>
            </div>
            <div class="status-item">
              <div class="status-label">存储空间</div>
              <div class="status-indicator" :class="systemStatus.storage">
                <span class="status-dot"></span>
                <span class="status-text">{{ systemStatus.storageText }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 性能指标 -->
      <div class="performance-panel">
        <div class="panel-header">
          <h3 class="panel-title">⚡ 性能指标</h3>
          <div class="performance-summary">
            <span class="performance-score" :class="performanceScore.level">
              {{ performanceScore.value }}
            </span>
          </div>
        </div>
        <div class="performance-metrics">
          <div class="metric-item">
            <div class="metric-label">响应时间</div>
            <div class="metric-value">{{ performanceMetrics.responseTime }}ms</div>
            <div class="metric-bar">
              <div class="metric-fill" :style="{ width: performanceMetrics.responseTimePercent + '%' }"></div>
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-label">CPU使用率</div>
            <div class="metric-value">{{ performanceMetrics.cpuUsage }}%</div>
            <div class="metric-bar">
              <div class="metric-fill" :style="{ width: performanceMetrics.cpuUsage + '%' }"></div>
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-label">内存使用率</div>
            <div class="metric-value">{{ performanceMetrics.memoryUsage }}%</div>
            <div class="metric-bar">
              <div class="metric-fill" :style="{ width: performanceMetrics.memoryUsage + '%' }"></div>
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-label">并发用户</div>
            <div class="metric-value">{{ performanceMetrics.concurrentUsers }}</div>
            <div class="metric-bar">
              <div class="metric-fill" :style="{ width: (performanceMetrics.concurrentUsers / 100) * 100 + '%' }"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 回到顶部按钮 -->
    <div
      v-show="showBackToTop"
      @click="scrollToTop"
      class="back-to-top"
      title="回到顶部"
    >
      <span class="back-to-top-icon">↑</span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import * as echarts from 'echarts'
import { request } from '@/utils'
import { useRouter } from 'vue-router'

const router = useRouter()
const currentTime = ref('')

// 回到顶部相关
const showBackToTop = ref(false)

// 路由映射表 - 确保所有路由都存在
const routeMap = {
  projects: '/testing/projects',
  requirements: '/testing/requirements',
  testcases: '/testing/generator',
  knowledge: '/testing/knowledge',
  analysisReq: '/testing/analysisReq',
  testcasesList: '/testing/testcases',
  systemUser: '/system/user'
}

// 图表引用
const projectChartRef = ref(null)
const testCaseChartRef = ref(null)
const coverageChartRef = ref(null)
const activityChartRef = ref(null)

// 图表实例
let projectChart = null
let testCaseChart = null
let coverageChart = null
let activityChart = null

// 控制参数
const trendDays = ref(7)

// 统计数据
const stats = ref({
  projects: 0,
  requirements: 0,
  testcases: 0,
  knowledge: 0,
  todayNew: 0,
  weekCompleted: 0
})

// 项目分布数据
const projectDistribution = ref([])

// 测试用例趋势数据
const testCaseTrend = ref({
  dates: [],
  newCases: [],
  executedCases: [],
  passedCases: []
})

// 活动数据
const activities = ref([
  {
    icon: '🧪',
    title: '测试用例生成完成',
    description: '为"用户登录模块"生成了15个测试用例',
    time: '2分钟前',
    type: 'success',
    status: 'completed',
    statusText: '已完成'
  },
  {
    icon: '📊',
    title: '需求分析报告',
    description: '完成了"支付系统"的需求分析',
    time: '10分钟前',
    type: 'info',
    status: 'completed',
    statusText: '已完成'
  },
  {
    icon: '🔄',
    title: '数据同步',
    description: '正在同步TAPD项目数据...',
    time: '15分钟前',
    type: 'warning',
    status: 'processing',
    statusText: '进行中'
  },
  {
    icon: '✅',
    title: '测试执行',
    description: '批量执行了32个测试用例',
    time: '30分钟前',
    type: 'success',
    status: 'completed',
    statusText: '已完成'
  }
])

// 系统状态
const systemStatus = ref({
  overall: 'healthy',
  text: '系统运行正常',
  database: 'healthy',
  databaseText: '正常',
  ai: 'healthy',
  aiText: '正常',
  websocket: 'healthy',
  websocketText: '正常',
  storage: 'warning',
  storageText: '75%'
})

// 性能指标
const performanceMetrics = ref({
  responseTime: 245,
  responseTimePercent: 25,
  cpuUsage: 45,
  memoryUsage: 62,
  concurrentUsers: 23
})

// 计算属性
const testCasePassRate = computed(() => {
  if (!testCaseTrend.value.passedCases.length || !testCaseTrend.value.executedCases.length) return 0
  const totalPassed = testCaseTrend.value.passedCases.reduce((sum, val) => sum + val, 0)
  const totalExecuted = testCaseTrend.value.executedCases.reduce((sum, val) => sum + val, 0)
  return totalExecuted > 0 ? Math.round((totalPassed / totalExecuted) * 100) : 0
})

const testCoverage = computed(() => {
  return Math.round(Math.random() * 30 + 70) // 模拟70-100%的覆盖率
})

const platformActivity = computed(() => {
  return Math.round(Math.random() * 20 + 80) + '%' // 模拟80-100%的活跃度
})

const performanceScore = computed(() => {
  const score = Math.round((100 - performanceMetrics.value.responseTime / 10 +
                           (100 - performanceMetrics.value.cpuUsage) +
                           (100 - performanceMetrics.value.memoryUsage)) / 3)
  let level = 'excellent'
  if (score < 60) level = 'poor'
  else if (score < 80) level = 'good'

  return { value: score, level }
})

// API调用函数
const fetchDashboardStats = async () => {
  try {
    console.log('开始获取统计数据...')
    const response = await request.get('/api/v1/dashboard/stats')
    console.log('API响应:', response)

    if (response && response.data) {
      console.log('响应数据:', response.data)
      stats.value = {
        projects: response.data.totalProjects || 0,
        requirements: response.data.totalRequirements || 0,
        testcases: response.data.totalTestCases || 0,
        knowledge: response.data.totalKnowledge || 0,
        todayNew: response.data.todayNew || 0,
        weekCompleted: response.data.weekCompleted || 0
      }
      console.log('更新后的统计数据:', stats.value)
    } else {
      console.warn('API响应格式异常:', response)
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    console.error('错误详情:', error.response || error.message)
  }
}

// 安全导航函数 - 带错误处理
const safeNavigate = (path, fallbackMessage) => {
  try {
    console.log(`🔗 正在导航到: ${path}`)
    router.push(path).then(() => {
      console.log(`✅ 成功导航到: ${path}`)
    }).catch((error) => {
      console.error(`❌ 导航失败:`, error)
      console.warn(`页面功能开发中: ${fallbackMessage || path}`)
    })
  } catch (error) {
    console.error(`❌ 导航到 ${path} 失败:`, error)
    console.warn(`页面功能开发中: ${fallbackMessage || path}`)
  }
}

// 导航函数
const navigateToProjects = () => {
  console.log('🏗️ 导航到项目管理')
  safeNavigate(routeMap.projects, '项目管理页面')
}

const navigateToRequirements = () => {
  console.log('📋 导航到需求管理')
  safeNavigate(routeMap.requirements, '需求管理页面')
}

const navigateToTestCases = () => {
  console.log('🧪 导航到测试用例')
  safeNavigate(routeMap.testcases, '测试用例生成页面')
}

const navigateToKnowledge = () => {
  console.log('📚 导航到知识库')
  safeNavigate(routeMap.knowledge, '知识库页面')
}

// 快速操作函数
const refreshAllData = async () => {
  console.log('🔄 刷新所有数据...')
  try {
    await Promise.all([
      fetchDashboardStats(),
      fetchProjectDistribution(),
      fetchTestCaseTrend(),
      fetchRecentActivities()
    ])

    // 更新图表
    await nextTick()
    setTimeout(() => {
      initAllCharts()
    }, 200)

    console.log('✅ 数据刷新完成')
  } catch (error) {
    console.error('❌ 数据刷新失败:', error)
  }
}

const exportReport = () => {
  console.log('📊 导出报告功能')
  // 这里可以实现报告导出逻辑
  alert('报告导出功能开发中...')
}

const openSettings = () => {
  console.log('⚙️ 打开设置')
  safeNavigate(routeMap.systemUser, '系统设置页面')
}

const createNewProject = () => {
  console.log('➕ 创建新项目')
  safeNavigate(routeMap.projects, '项目管理页面')
}

const generateTestCase = () => {
  console.log('🧪 生成测试用例')
  safeNavigate(routeMap.testcases, '测试用例生成页面')
}

const analyzeRequirement = () => {
  console.log('📊 需求分析')
  safeNavigate(routeMap.analysisReq, '需求分析页面')
}

const viewReports = () => {
  console.log('📈 查看报告')
  safeNavigate(routeMap.testcasesList, '测试报告页面')
}

const refreshActivities = async () => {
  console.log('🔄 刷新活动数据')
  await fetchRecentActivities()
}

// 滚动相关函数
const scrollToTop = () => {
  const container = document.querySelector('.workbench-container')
  if (container) {
    container.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }
}

const handleScroll = () => {
  const container = document.querySelector('.workbench-container')
  if (container) {
    showBackToTop.value = container.scrollTop > 300
  }
}

const fetchProjectDistribution = async () => {
  try {
    const response = await request.get('/api/v1/dashboard/project-distribution')
    if (response.data) {
      projectDistribution.value = response.data
    }
  } catch (error) {
    console.error('获取项目分布数据失败:', error)
  }
}

const fetchTestCaseTrend = async () => {
  try {
    const response = await request.get(`/api/v1/dashboard/testcase-trend?days=${trendDays.value}`)
    if (response.data) {
      testCaseTrend.value = {
        dates: response.data.dates || [],
        newCases: response.data.newCases || [],
        executedCases: response.data.executedCases || [],
        passedCases: response.data.passedCases || []
      }

      // 更新图表
      if (testCaseChart) {
        initTestCaseChart()
      }
    }
  } catch (error) {
    console.error('获取测试用例趋势数据失败:', error)
  }
}

const fetchRecentActivities = async () => {
  try {
    const response = await request.get('/api/v1/dashboard/recent-activities')
    if (response.data) {
      activities.value = response.data
    }
  } catch (error) {
    console.error('获取最近活动数据失败:', error)
  }
}

// 测试数据库连接
const testDatabase = async () => {
  try {
    console.log('开始测试数据库连接...')
    const response = await request.get('/api/v1/dashboard/test-db')
    console.log('数据库测试结果:', response)

    if (response && response.data) {
      console.log('数据库测试详情:', response.data)
      alert(`数据库测试结果：
项目数量: ${response.data.projects?.count || '错误'}
需求数量: ${response.data.requirements?.count || '错误'}
测试用例数量: ${response.data.test_cases?.count || '错误'}
知识库数量: ${response.data.knowledge_items?.count || '错误'}`)
    }
  } catch (error) {
    console.error('数据库测试失败:', error)
    alert('数据库测试失败: ' + (error.message || '未知错误'))
  }
}

// 测试简化统计
const testSimpleStats = async () => {
  try {
    console.log('开始测试简化统计...')
    const response = await request.get('/api/v1/dashboard/simple-stats')
    console.log('简化统计结果:', response)

    if (response && response.data) {
      console.log('简化统计详情:', response.data)
      alert(`简化统计结果：
项目数量: ${response.data.projects}
需求数量: ${response.data.requirements}
测试用例数量: ${response.data.test_cases}
知识库数量: ${response.data.knowledge}`)
    }
  } catch (error) {
    console.error('简化统计测试失败:', error)
    alert('简化统计测试失败: ' + (error.message || '未知错误'))
  }
}

const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

// 初始化项目状态分布饼图
const initProjectChart = () => {
  if (!projectChartRef.value) return

  projectChart = echarts.init(projectChartRef.value)

  // 颜色映射
  const colorMap = {
    '进行中': new echarts.graphic.LinearGradient(0, 0, 1, 1, [
      { offset: 0, color: '#6366f1' },
      { offset: 1, color: '#8b5cf6' }
    ]),
    '已完成': new echarts.graphic.LinearGradient(0, 0, 1, 1, [
      { offset: 0, color: '#10b981' },
      { offset: 1, color: '#059669' }
    ]),
    '暂停': new echarts.graphic.LinearGradient(0, 0, 1, 1, [
      { offset: 0, color: '#f59e0b' },
      { offset: 1, color: '#d97706' }
    ]),
    '计划中': new echarts.graphic.LinearGradient(0, 0, 1, 1, [
      { offset: 0, color: '#ef4444' },
      { offset: 1, color: '#dc2626' }
    ])
  }

  // 处理数据，添加颜色
  const chartData = projectDistribution.value.map(item => ({
    ...item,
    itemStyle: {
      color: colorMap[item.name] || '#94a3b8'
    }
  }))

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}个 ({d}%)',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff',
        fontSize: 12
      }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'center',
      textStyle: {
        fontSize: 12,
        color: '#64748b'
      },
      itemWidth: 12,
      itemHeight: 12
    },
    series: [
      {
        name: '项目状态',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['65%', '50%'],
        avoidLabelOverlap: false,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: false,
          position: 'center'
        },
        labelLine: {
          show: false
        },
        data: chartData
      }
    ]
  }

  projectChart.setOption(option)

  // 添加点击事件
  projectChart.on('click', (params) => {
    console.log('点击了项目状态:', params.name, '数量:', params.value)
    // 这里可以添加跳转到具体项目列表的逻辑
  })
}

// 初始化测试用例执行趋势折线图
const initTestCaseChart = () => {
  if (!testCaseChartRef.value) return

  testCaseChart = echarts.init(testCaseChartRef.value)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff',
        fontSize: 12
      }
    },
    legend: {
      data: ['新增用例', '执行用例', '通过用例'],
      top: 10,
      textStyle: {
        fontSize: 12,
        color: '#64748b'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: testCaseTrend.value.dates,
        axisLabel: {
          fontSize: 11,
          color: '#64748b'
        },
        axisLine: {
          lineStyle: {
            color: '#e2e8f0'
          }
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          fontSize: 11,
          color: '#64748b'
        },
        axisLine: {
          lineStyle: {
            color: '#e2e8f0'
          }
        },
        splitLine: {
          lineStyle: {
            color: '#f1f5f9'
          }
        }
      }
    ],
    series: [
      {
        name: '新增用例',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 3,
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#6366f1' },
            { offset: 1, color: '#8b5cf6' }
          ])
        },
        itemStyle: {
          color: '#6366f1'
        },
        areaStyle: {
          opacity: 0.1,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#6366f1' },
            { offset: 1, color: 'transparent' }
          ])
        },
        data: testCaseTrend.value.newCases
      },
      {
        name: '执行用例',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 3,
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#10b981' },
            { offset: 1, color: '#059669' }
          ])
        },
        itemStyle: {
          color: '#10b981'
        },
        areaStyle: {
          opacity: 0.1,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#10b981' },
            { offset: 1, color: 'transparent' }
          ])
        },
        data: testCaseTrend.value.executedCases
      },
      {
        name: '通过用例',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 3,
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#f59e0b' },
            { offset: 1, color: '#d97706' }
          ])
        },
        itemStyle: {
          color: '#f59e0b'
        },
        areaStyle: {
          opacity: 0.1,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#f59e0b' },
            { offset: 1, color: 'transparent' }
          ])
        },
        data: testCaseTrend.value.passedCases
      }
    ]
  }

  testCaseChart.setOption(option)

  // 添加点击事件
  testCaseChart.on('click', (params) => {
    console.log('点击了测试数据:', params.seriesName, '日期:', params.name, '数值:', params.value)
    // 这里可以添加跳转到具体测试报告的逻辑
  })
}

// 初始化测试覆盖率图表
const initCoverageChart = () => {
  if (!coverageChartRef.value) return

  coverageChart = echarts.init(coverageChartRef.value)

  const option = {
    series: [
      {
        type: 'gauge',
        startAngle: 180,
        endAngle: 0,
        center: ['50%', '75%'],
        radius: '90%',
        min: 0,
        max: 100,
        splitNumber: 8,
        axisLine: {
          lineStyle: {
            width: 6,
            color: [
              [0.25, '#FF6E76'],
              [0.5, '#FDDD60'],
              [0.75, '#58D9F9'],
              [1, '#7CFFB2']
            ]
          }
        },
        pointer: {
          icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
          length: '12%',
          width: 20,
          offsetCenter: [0, '-60%'],
          itemStyle: {
            color: 'auto'
          }
        },
        axisTick: {
          length: 12,
          lineStyle: {
            color: 'auto',
            width: 2
          }
        },
        splitLine: {
          length: 20,
          lineStyle: {
            color: 'auto',
            width: 5
          }
        },
        axisLabel: {
          color: '#464646',
          fontSize: 12,
          distance: -60,
          rotate: 'tangential',
          formatter: function (value) {
            if (value === 87.5) {
              return 'A'
            } else if (value === 62.5) {
              return 'B'
            } else if (value === 37.5) {
              return 'C'
            } else if (value === 12.5) {
              return 'D'
            }
            return ''
          }
        },
        title: {
          offsetCenter: [0, '-10%'],
          fontSize: 14
        },
        detail: {
          fontSize: 30,
          offsetCenter: [0, '-35%'],
          valueAnimation: true,
          formatter: function (value) {
            return Math.round(value) + '%'
          },
          color: 'auto'
        },
        data: [
          {
            value: testCoverage.value,
            name: '测试覆盖率'
          }
        ]
      }
    ]
  }

  coverageChart.setOption(option)
}

// 初始化活跃度图表
const initActivityChart = () => {
  if (!activityChartRef.value) return

  activityChart = echarts.init(activityChartRef.value)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        axisTick: {
          alignWithLabel: true
        },
        axisLabel: {
          fontSize: 11,
          color: '#64748b'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          fontSize: 11,
          color: '#64748b'
        }
      }
    ],
    series: [
      {
        name: '活跃用户',
        type: 'bar',
        barWidth: '60%',
        data: [120, 132, 101, 134, 90, 230, 210],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }
    ]
  }

  activityChart.setOption(option)
}

// 初始化所有图表
const initAllCharts = () => {
  initProjectChart()
  initTestCaseChart()
  initCoverageChart()
  initActivityChart()
}

// 窗口大小变化时重新调整图表
const handleResize = () => {
  nextTick(() => {
    projectChart?.resize()
    testCaseChart?.resize()
    coverageChart?.resize()
    activityChart?.resize()
  })
}

// 刷新图表数据
const refreshCharts = async () => {
  // 重新获取数据并更新图表
  await fetchProjectDistribution()
  await fetchTestCaseTrend()

  // 更新图表
  if (projectChart && projectDistribution.value.length > 0) {
    initProjectChart()
  }
  if (testCaseChart && testCaseTrend.value.dates.length > 0) {
    initTestCaseChart()
  }
  if (coverageChart) {
    initCoverageChart()
  }
  if (activityChart) {
    initActivityChart()
  }
}

onMounted(async () => {
  console.log('✅ 工作台页面已成功加载！')

  updateTime()
  setInterval(updateTime, 1000)

  // 加载数据
  try {
    await Promise.all([
      fetchDashboardStats(),
      fetchProjectDistribution(),
      fetchTestCaseTrend(),
      fetchRecentActivities()
    ])
    console.log('📊 数据加载完成')
  } catch (error) {
    console.error('数据加载失败:', error)
  }

  // 延迟初始化图表，确保DOM已经渲染且数据已加载
  await nextTick()
  setTimeout(() => {
    initAllCharts()
    console.log('📊 所有图表初始化完成')
  }, 200)

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)

  // 监听滚动事件
  const container = document.querySelector('.workbench-container')
  if (container) {
    container.addEventListener('scroll', handleScroll)
  }
})

onUnmounted(() => {
  // 清理资源
  window.removeEventListener('resize', handleResize)

  // 清理滚动监听器
  const container = document.querySelector('.workbench-container')
  if (container) {
    container.removeEventListener('scroll', handleScroll)
  }

  projectChart?.dispose()
  testCaseChart?.dispose()
  coverageChart?.dispose()
  activityChart?.dispose()
})
</script>

<style scoped>
.workbench-container {
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(109, 40, 217, 0.3);
    border-radius: 4px;
    transition: background 0.3s ease;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(109, 40, 217, 0.5);
  }

  /* 确保内容可以正常滚动 */
  scroll-behavior: smooth;
}

/* 欢迎区域 */
.welcome-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 24px;
  color: white;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.welcome-left {
  flex: 1;
  min-width: 300px;
}

.welcome-right {
  flex-shrink: 0;
}

.welcome-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 12px 0;
}

.welcome-subtitle {
  font-size: 16px;
  margin: 0 0 8px 0;
  opacity: 0.9;
}

.current-time {
  font-size: 14px;
  margin: 0;
  opacity: 0.8;
}

/* 快速操作按钮 */
.quick-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.action-btn.primary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.25);
}

.action-btn.tertiary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-icon {
  font-size: 16px;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  border-radius: 20px;
  padding: 24px;
  color: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
}

.stat-card.primary {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
}

.stat-card.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stat-card.warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-card.danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stat-icon {
  font-size: 32px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
}

.stat-trend.positive {
  background: rgba(34, 197, 94, 0.2);
}

.trend-icon {
  font-size: 14px;
}

.stat-value {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 8px;
  line-height: 1;
}

.stat-label {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  opacity: 0.9;
}

.stat-desc {
  font-size: 13px;
  opacity: 0.7;
}

/* 主要图表区域 */
.main-charts {
  margin-bottom: 32px;
}

.chart-row {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-row:last-child {
  grid-template-columns: 1fr 1fr;
}

.chart-card {
  background: white;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(109, 40, 217, 0.1);
  transition: all 0.3s ease;
}

.chart-card:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.chart-card.large {
  min-height: 400px;
}

.chart-card.medium {
  min-height: 350px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.chart-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.chart-select {
  padding: 6px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

.chart-legend {
  display: flex;
  gap: 16px;
  align-items: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #64748b;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.legend-dot.active {
  background: #6366f1;
}

.legend-dot.completed {
  background: #10b981;
}

.coverage-summary, .activity-summary {
  display: flex;
  align-items: center;
  gap: 8px;
}

.coverage-value, .activity-value {
  font-size: 24px;
  font-weight: 700;
  color: #6366f1;
}

.chart-container {
  width: 100%;
  position: relative;
}

.chart-container.large {
  height: 320px;
}

.chart-container.medium {
  height: 280px;
}

/* 信息面板 */
.info-panels {
  margin-bottom: 32px;
}

.panel-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.info-panel {
  background: white;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(109, 40, 217, 0.1);
  transition: all 0.3s ease;
}

.info-panel:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f1f5f9;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.refresh-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px;
  border-radius: 8px;
  transition: background 0.2s ease;
}

.refresh-btn:hover {
  background: #f1f5f9;
}

.refresh-icon {
  font-size: 14px;
  color: #64748b;
}

/* 活动列表 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 300px;
  overflow-y: auto;
  padding-right: 4px;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(109, 40, 217, 0.2);
    border-radius: 2px;
    transition: background 0.3s ease;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(109, 40, 217, 0.4);
  }
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  border-radius: 12px;
  transition: background 0.2s ease;
}

.activity-item:hover {
  background: #f8fafc;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
}

.activity-icon.success {
  background: rgba(34, 197, 94, 0.1);
  color: #059669;
}

.activity-icon.info {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
}

.activity-icon.warning {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-title {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 4px;
}

.activity-desc {
  font-size: 13px;
  color: #64748b;
  margin-bottom: 4px;
  line-height: 1.4;
}

.activity-time {
  font-size: 12px;
  color: #94a3b8;
}

.activity-status {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
  flex-shrink: 0;
}

.activity-status.completed {
  background: rgba(34, 197, 94, 0.1);
  color: #059669;
}

.activity-status.processing {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
}

/* 快速操作 */
.quick-operations {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.operation-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #f1f5f9;
}

.operation-item:hover {
  background: #f8fafc;
  border-color: #e2e8f0;
  transform: translateX(4px);
}

.operation-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
}

.operation-content {
  flex: 1;
}

.operation-title {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 4px;
}

.operation-desc {
  font-size: 12px;
  color: #64748b;
  line-height: 1.4;
}

/* 系统状态 */
.system-status {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.system-status.healthy {
  background: rgba(34, 197, 94, 0.1);
  color: #059669;
}

.status-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.status-label {
  font-size: 14px;
  color: #64748b;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-indicator.healthy .status-dot {
  background: #10b981;
}

.status-indicator.warning .status-dot {
  background: #f59e0b;
}

.status-text {
  font-size: 12px;
  font-weight: 500;
}

.status-indicator.healthy .status-text {
  color: #059669;
}

.status-indicator.warning .status-text {
  color: #d97706;
}

/* 性能指标 */
.performance-panel {
  background: white;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(109, 40, 217, 0.1);
  transition: all 0.3s ease;
}

.performance-panel:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.performance-summary {
  display: flex;
  align-items: center;
  gap: 8px;
}

.performance-score {
  font-size: 24px;
  font-weight: 700;
  padding: 4px 12px;
  border-radius: 12px;
}

.performance-score.excellent {
  background: rgba(34, 197, 94, 0.1);
  color: #059669;
}

.performance-score.good {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
}

.performance-score.poor {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

.performance-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 20px;
}

.metric-item {
  padding: 16px;
  border-radius: 12px;
  background: #f8fafc;
  border: 1px solid #f1f5f9;
}

.metric-label {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
}

.metric-bar {
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .chart-row {
    grid-template-columns: 1fr;
  }

  .chart-row:last-child {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .workbench-container {
    padding: 16px;
  }

  .welcome-content {
    flex-direction: column;
    text-align: center;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .panel-row {
    grid-template-columns: 1fr;
  }

  .performance-metrics {
    grid-template-columns: 1fr;
  }
}

/* 回到顶部按钮 */
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.3);
  transition: all 0.3s ease;
  z-index: 1000;
  user-select: none;
}

.back-to-top:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 25px rgba(99, 102, 241, 0.4);
  background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
}

.back-to-top:active {
  transform: translateY(-1px);
}

.back-to-top-icon {
  color: white;
  font-size: 20px;
  font-weight: bold;
  line-height: 1;
}

/* 回到顶部按钮动画 */
.back-to-top {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
