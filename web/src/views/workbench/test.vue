<template>
  <div style="background: yellow; padding: 50px; text-align: center;">
    <h1 style="color: red; font-size: 48px;">🚨 紧急测试页面 🚨</h1>
    <p style="font-size: 24px; color: black;">如果您能看到这个黄色页面，说明Vue组件可以正常渲染！</p>
    <div style="background: red; color: white; padding: 30px; margin: 30px 0; border-radius: 10px;">
      <h2>调试信息</h2>
      <p>这是备用测试页面</p>
      <p>页面加载时间：{{ loadTime }}</p>
    </div>
    <button @click="testClick" style="background: blue; color: white; padding: 15px 30px; font-size: 18px; border: none; border-radius: 5px;">
      点击测试按钮
    </button>
    <p style="margin-top: 20px;">点击次数：{{ clickCount }}</p>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const loadTime = ref('')
const clickCount = ref(0)

const testClick = () => {
  clickCount.value++
  alert(`测试成功！点击了 ${clickCount.value} 次`)
  console.log('测试按钮被点击')
}

onMounted(() => {
  console.log('🚨 紧急测试页面已加载')
  loadTime.value = new Date().toLocaleString()
})
</script>

<style scoped>
.workbench-container {
  padding: 20px;
  background: transparent;
  min-height: 100vh;
}

.welcome-card {
  margin-bottom: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.3);
  margin-right: 20px;
}

.user-details {
  color: white;
}

.greeting {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: white;
}

.welcome-text {
  font-size: 14px;
  margin: 0 0 4px 0;
  opacity: 0.9;
}

.current-time {
  font-size: 12px;
  margin: 0;
  opacity: 0.8;
}

.quick-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  text-align: center;
  color: white;
}

.stat-item :deep(.n-statistic-value) {
  color: white !important;
  font-size: 28px !important;
  font-weight: 700 !important;
}

.stat-item :deep(.n-statistic-label) {
  color: rgba(255, 255, 255, 0.9) !important;
  font-size: 12px !important;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.metric-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(109, 40, 217, 0.1);
}

.metric-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.metric-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 8px;
}

.metric-trend {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 500;
}

.metric-trend.positive {
  color: #10b981;
}

.metric-trend.negative {
  color: #ef4444;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.chart-card {
  min-height: 350px;
}

.chart-container {
  height: 280px;
  width: 100%;
}
</style>
