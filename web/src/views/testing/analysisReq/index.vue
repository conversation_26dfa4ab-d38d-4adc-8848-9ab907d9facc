<script setup>
import { h, onMounted, ref, resolveDirective, withDirectives } from 'vue'
import { NButton, NForm, NFormItem, NInput, NInputNumber, NPopconfirm, NTreeSelect, NCard, NTabs, NTabPane } from 'naive-ui'

import AgentPage from '@/components/page/AgentPage.vue'
import AgentTable from '@/components/table/AgentTable.vue'
import ReqsInput from '@/components/reqs/ReqsInput.vue'
import StreamOutput from '@/components/reqs/StreamOutput.vue'
import FeedbackChat from '@/components/reqs/FeedbackChat.vue'
import TheIcon from '@/components/icon/TheIcon.vue'
import AgentExecutionOutput from '@/components/reqs/AgentExecutionOutput.vue'

import { renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
// import { loginTypeMap, loginTypeOptions } from '@/constant/data'
import api from '@/api'

defineOptions({ name: '需求分析' })

const $table = ref(null)
const queryItems = ref({})
const vPermission = resolveDirective('permission')

// 智能体执行输出相关状态
const agentOutputMessages = ref([])
const activeTab = ref('input')

// 清空智能体执行输出
const clearAgentOutput = () => {
  agentOutputMessages.value = []
}

// 添加智能体执行输出消息
const addAgentOutputMessage = (message) => {
  agentOutputMessages.value.push({
    source: message.source || 'agent',
    content: message.content,
    timestamp: new Date().toISOString(),
    type: message.type || 'normal'
  })
}

// 监听来自ReqsInput组件的消息
const handleAgentMessage = (message) => {
  addAgentOutputMessage(message)
}
</script>

<template>
  <!-- 业务页面 -->
  <AgentPage>
    <n-tabs v-model:value="activeTab" type="line" animated>
      <n-tab-pane name="input" tab="需求分析">
        <ReqsInput @agent-message="handleAgentMessage" />
      </n-tab-pane>
      <n-tab-pane name="output" tab="执行输出">
        <n-card title="智能体执行输出" size="small">
          <AgentExecutionOutput :messages="agentOutputMessages" @clear="clearAgentOutput" />
        </n-card>
      </n-tab-pane>
    </n-tabs>
  </AgentPage>
</template>

<style scoped>
.chat-container {
  position: absolute; /* 固定定位 */
  bottom: 0; /* 距离底部为0 */
  left: 10px; /* 距离左侧为0 */
  width: calc(100% - 20px); /* 宽度为100%减去左右边距 */
  /* 占满父容器宽度 */
  box-sizing: border-box;
  /* 包括 padding 和 border 在内计算宽度 */
  padding: 10px;
  /* 添加内边距 */
}

/* 标签页样式 */
.n-tabs {
  margin-bottom: 20px;
}

/* 卡片样式 */
.n-card {
  margin-bottom: 20px;
}
</style>
