<template>
    <div class="testcase-container flex h-full overflow-auto">
    <!-- 滚动到底部按钮 -->
    <div class="scroll-to-bottom-btn" @click="scrollToBottom(true)">
      <TheIcon icon="mdi:chevron-double-down" :size="20" />
    </div>

    <!-- 返回顶部按钮 -->
    <div class="scroll-to-top-btn" @click="scrollToTop()">
      <TheIcon icon="mdi:chevron-double-up" :size="20" />
    </div>

    <!-- 左侧需求列表区域 -->
    <div class="requirements-panel w-1/4 p-4 border-r border-[#e5e7eb]">
      <div class="search-area mb-4">
        <n-space vertical>
          <n-select
            v-model:value="searchParams.projectId"
            :options="projectList.map(p => ({ label: p.name, value: p.id }))"
            placeholder="请选择项目"
            clearable
            @update:value="handleProjectSelect"
          >
            <template #prefix>
              <n-icon><SearchOutline /></n-icon>
            </template>
          </n-select>
          <n-input
            v-model:value="searchParams.requirementName"
            placeholder="请输入需求名称或TAPD URL"
            clearable
            @update:value="handleSearch"
          >
            <template #prefix>
              <n-icon><SearchOutline /></n-icon>
            </template>
          </n-input>
        </n-space>
      </div>

      <div class="requirements-list">
        <n-data-table
          :columns="requirementColumns"
          :data="requirementList"
          :row-key="row => row.id"
          :pagination="false"
          @update:checked-row-keys="handleRequirementSelect"
        />
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="content-panel flex-1 p-4">
      <!-- 上方需求详情区域 - 改为可折叠区域 -->
      <n-collapse :default-expanded-names="['requirement-details']">
        <n-collapse-item name="requirement-details" title="需求详情">
      <div class="content-wrapper pl-2">
        <n-descriptions
          :column="2"
          :vertical="false"
          bordered
          label-placement="left"
          label-align="right"
          content-style="justify-content: start"
          size="small"
        >
          <n-descriptions-item
            label="需求名称："
            :span="2"
            label-style="width: 100px; text-align: left"
          >
            <span class="font-medium">
              {{ selectedRequirement?.name || '未选择需求' }}
            </span>
                <n-tag
                  v-if="selectedRequirement?.category"
                  :bordered="false"
                  type="info"
                  size="small"
                  class="ml-2"
                >
                  {{ selectedRequirement.category }}
                </n-tag>
          </n-descriptions-item>
        </n-descriptions>

        <n-tabs class="mt-4" size="medium">
          <n-tab-pane name="description" tab="需求描述">
            <n-input
              v-model:value="selectedRequirement.description"
              type="textarea"
              :placeholder="selectedRequirement?.description ? '' : '暂无描述'"
                  :rows="2"
                  :autosize="{ minRows: 1, maxRows: 3 }"
              size="small"
                  class="description-input"
            />
          </n-tab-pane>
          <n-tab-pane name="scenario" tab="场景说明">
            <n-input
              v-model:value="scenario"
              type="textarea"
              placeholder="请输入场景说明"
                  :rows="3"
                  :autosize="{ minRows: 1, maxRows: 3 }"
              size="small"
                  class="scenario-input"
            />
          </n-tab-pane>
        </n-tabs>
      </div>
        </n-collapse-item>
      </n-collapse>

      <!-- 中间指令输入区域 -->
      <div class="command-input bg-white rounded-lg shadow-sm p-6 pl-6 mt-4">
        <div class="flex items-center gap-3">
          <n-input
            v-model:value="aiCommand"
            type="textarea"
            placeholder="请输入 AI 指令..."
            :rows="2"
            :autosize="{ minRows: 1, maxRows: 3 }"
            class="flex-1"
            size="small"
          />
          <n-button
            type="primary"
            :disabled="!selectedRequirement || isGenerating"
            :loading="isGenerating"
            @click="handleGenerateTestCase"
            class="h-[34px]"
          >
            <template #icon>
              <TheIcon icon="mdi:robot" :class="{ 'animate-spin': isGenerating }" />
            </template>
            {{ isGenerating ? '生成中...' : '生成测试用例' }}
          </n-button>
          <n-button
            type="default"
            @click="testWebSocketConnection"
            class="h-[34px] ml-2"
            size="small"
          >
            <template #icon>
              <TheIcon icon="mdi:network" />
            </template>
            测试连接
          </n-button>
        </div>
      </div>
      <div class="output-container">
         <n-tabs v-model:value="activeTab" type="line">
          <n-tab-pane name="generation" tab="生成输出">
            <div class="output-display bg-white rounded-lg shadow-sm p-6 min-h-[400px]">
              <div
                v-if="messageBlocks.length > 0 || isGenerating"
                id="output"
                class="markdown-container"
                :class="{ 'has-content': messageBlocks.length || isGenerating }"
              >
                <div v-if="messageBlocks.length > 0">
                  <div
                    v-for="(msg, index) in messageBlocks"
                    :key="index"
                    class="message-item"
                    :class="[msg.source, {'streaming': msg.isStreaming}]"
                  >
                    <div class="source-tag">
                      <TheIcon
                        :icon="msg.source === 'user' ? 'mdi:account' : 'mdi:robot'"
                        :size="14"
                      />
                      {{ msg.source.toUpperCase() }}
              </div>
                    <div class="markdown-content" v-html="msg.content"></div>

                    <!-- 用户反馈区域 - 内嵌在消息中 -->
                    <div v-if="msg.source === 'user_proxy' && msg.showFeedback" class="feedback-container mt-4">
                      <n-input
                        v-model:value="msg.feedbackContent"
                        type="textarea"
                        placeholder="请输入您对AI助手的反馈..."
                        :rows="2"
                        class="feedback-input"
                      />
                      <div class="feedback-actions mt-2">
                        <n-button size="small" type="primary" @click="sendFeedback(index)">发送</n-button>
                        <n-button size="small" type="success" @click="approveResponse(index)" class="ml-2">同意</n-button>
                        <n-button size="small" @click="closeFeedback(index)" class="ml-2">取消</n-button>
    </div>
                    </div>
                  </div>
                </div>
                <!-- 加载状态 -->
                <div v-if="isGenerating" class="loading-indicator">
                  <n-spin size="small" />
                  <span>AI测试用例生成中...</span>
  </div>
              </div>
            </div>
          </n-tab-pane>
          <n-tab-pane name="list" tab="用例列表">
            <div class="testcase-list bg-white rounded-lg shadow-sm p-6 min-h-[400px] flex">
              <!-- 左侧用例列表 -->
              <div class="case-list-panel flex-1 pr-4">
                <!-- 采纳率统计 -->
                <div class="adoption-rate-container mb-4">
                  <div class="adoption-rate-display font-bold text-lg" :class="getAdoptionRateClass(adoptionRate)">
                    采纳率：{{ adoptionRate }}%
                  </div>
                </div>
                <div class="case-list-wrapper overflow-y-auto">
                  <!-- 按需求分组显示测试用例 -->
                  <div v-for="(group, groupIndex) in groupedTestCases" :key="groupIndex" class="requirement-group mb-6">
                    <!-- 需求标题 -->
                    <div class="requirement-title bg-blue-50 p-3 rounded-t-lg border-l-4 border-blue-500 mb-2 flex justify-between items-center">
                      <div class="font-medium text-blue-800">
                        <TheIcon icon="mdi:file-document-outline" class="mr-2" />
                        需求: {{ group.name }} ({{ group.testCases.length }} 个用例)
                      </div>
                      <n-button size="tiny" quaternary @click="expandCollapseGroup(groupIndex)">
                        {{ groupExpandState[groupIndex] ? '折叠全部' : '展开全部' }}
                      </n-button>
                    </div>

                    <!-- 该需求的测试用例列表 -->
                    <n-collapse>
                      <n-collapse-item
                        v-for="testCase in group.testCases"
                        :key="testCase.title"
                        :default-expanded="groupExpandState[groupIndex]">
                        <template #header>
                          <div class="testcase-header">
                            <div class="flex items-center">
                              <n-select
                                v-model:value="testCase.is_adopted"
                                :options="[
                                  { label: '已采纳', value: true },
                                  { label: '未采纳', value: false }
                                ]"
                                size="small"
                                style="width: 90px; margin-right: 10px;"
                                @update:value="updateTestCaseAdoption(testCase.id, $event)"
                                @click.stop
                              />
                              <div class="testcase-title">
                                {{ testCase.title }}
                                <!-- 相似用例标记 -->
                                <n-tooltip v-if="testCase.similarity_info" trigger="hover">
                                  <template #trigger>
                                    <n-badge dot color="#f0a020" class="ml-2">
                                      <TheIcon icon="mdi:content-duplicate" class="text-amber-500" />
                                    </n-badge>
                                  </template>
                                  <div>
                                    <div class="font-bold mb-1">相似用例提示</div>
                                    <div>此用例与其他需求点的用例相似度较高</div>
                                    <div class="text-xs text-gray-400 mt-1">系统已自动去除高度相似的用例</div>
                                  </div>
                                </n-tooltip>
                              </div>
                            </div>
                            <div class="testcase-meta">
                              <n-tag :type="getPriorityTagType(testCase.priority)" size="small">
                                {{ testCase.priority }}
                              </n-tag>
                              <n-tag :type="getStatusTagType(testCase.status)" size="small">
                                {{ testCase.status }}
                              </n-tag>
                              <span class="testcase-creator">{{ testCase.creator }}</span>
                            </div>
                          </div>
                        </template>

                        <div class="testcase-content">
                          <!-- 基本信息 -->
                          <div class="info-row">
                            <span class="info-label">用例描述：</span>
                            <span class="info-content">{{ testCase.desc }}</span>
                          </div>
                          <div class="info-row">
                            <span class="info-label">前置条件：</span>
                            <span class="info-content">{{ testCase.preconditions }}</span>
                          </div>
                          <div class="info-row">
                            <span class="info-label">后置条件：</span>
                            <span class="info-content">{{ testCase.postconditions }}</span>
                          </div>
                          <div class="info-row">
                            <span class="info-label">标签：</span>
                            <div class="tag-group">
                              <n-tag
                                v-if="testCase.tags && typeof testCase.tags === 'string'"
                                v-for="tag in testCase.tags.split(',')"
                                :key="tag"
                                size="small"
                                :bordered="false"
                              >
                                {{ tag }}
                              </n-tag>
                              <n-tag
                                v-else
                                size="small"
                                :bordered="false"
                              >
                                {{ testCase.tags }}
                              </n-tag>
                            </div>
                          </div>

                          <!-- 测试步骤 -->
                          <div class="steps-container">
                            <div
                              v-for="(step, index) in testCase.steps"
                              :key="index"
                              class="step-item"
                            >
                              <div class="step-description">
                                <strong>步骤 {{ index + 1 }}：</strong>{{ step.description || step.step_desc }}
                              </div>
                              <div class="step-result">
                                预期结果：{{ step.expected_result }}
                              </div>
                            </div>
                          </div>
                        </div>
                      </n-collapse-item>
                    </n-collapse>
                  </div>
                </div>
              </div>
            </div>
          </n-tab-pane>
          <n-tab-pane name="mindmap" tab="思维导图">
            <div class="mindmap-container h-full" ref="mindmapRef"></div>
          </n-tab-pane>
          <n-tab-pane name="knowledge" tab="相关知识点">
            <div class="knowledge-container bg-white rounded-lg shadow-sm p-6 pb-12 min-h-[400px] max-h-[calc(100vh-220px)] overflow-y-auto">
              <div v-if="knowledgePoints.total_points > 0">
                <div class="knowledge-header mb-4">
                  <div class="flex justify-between items-center">
                    <h3 class="text-lg font-medium">需求相关知识点 ({{ knowledgePoints.total_points }}条)</h3>
                    <n-tag type="success" size="medium">
                      <template #icon>
                        <TheIcon icon="mdi:check-circle" />
                      </template>
                      已关联知识点
                    </n-tag>
                  </div>
                  <p class="text-gray-500 text-sm mt-2">这些知识点与当前需求相关，可以帮助您更好地理解需求和生成测试用例</p>
                </div>

                <!-- 直接相关的知识点 -->
                <div v-if="knowledgePoints.direct_related.length > 0" class="knowledge-section mb-6">
                  <div class="section-header bg-blue-50 p-3 rounded-t-lg border-l-4 border-blue-500">
                    <h4 class="font-medium text-blue-800">直接相关 ({{ knowledgePoints.direct_related.length }}条)</h4>
                  </div>
                  <div class="section-content">
                    <n-collapse>
                      <n-collapse-item
                        v-for="point in knowledgePoints.direct_related"
                        :key="point.id"
                        :title="point.title"
                        @update:expanded-names="handleKnowledgeExpand"
                      >
                        <div class="knowledge-content">
                          <div class="info-row">
                            <span class="info-label">类型：</span>
                            <n-tag size="small">{{ point.item_type }}</n-tag>
                          </div>
                          <div class="info-row">
                            <span class="info-label">来源：</span>
                            <span class="info-content">{{ point.source }}</span>
                          </div>
                          <div class="info-row">
                            <span class="info-label">相关性：</span>
                            <n-progress
                              type="line"
                              :percentage="Math.round(point.relevance_score * 10)"
                              :indicator-placement="'inside'"
                              :color="'#18a058'"
                              :show-indicator="true"
                              :format="percentage => `${percentage}%`"
                            />
                          </div>
                          <div class="info-row">
                            <span class="info-label">内容：</span>
                            <div class="info-content whitespace-pre-wrap">{{ point.content }}</div>
                          </div>
                          <div v-if="point.tags" class="info-row">
                            <span class="info-label">标签：</span>
                            <div class="tag-group">
                              <n-tag
                                v-for="tag in point.tags.split(',')"
                                :key="tag"
                                size="small"
                                :bordered="false"
                              >
                                {{ tag }}
                              </n-tag>
                            </div>
                          </div>
                        </div>
                      </n-collapse-item>
                    </n-collapse>
                  </div>
                </div>

                <!-- 间接相关的知识点 -->
                <div v-if="knowledgePoints.indirect_related.length > 0" class="knowledge-section mb-6">
                  <div class="section-header bg-green-50 p-3 rounded-t-lg border-l-4 border-green-500">
                    <h4 class="font-medium text-green-800">间接相关 ({{ knowledgePoints.indirect_related.length }}条)</h4>
                  </div>
                  <div class="section-content">
                    <n-collapse>
                      <n-collapse-item
                        v-for="point in knowledgePoints.indirect_related"
                        :key="point.id"
                        :title="point.title"
                        @update:expanded-names="handleKnowledgeExpand"
                      >
                        <div class="knowledge-content">
                          <div class="info-row">
                            <span class="info-label">类型：</span>
                            <n-tag size="small">{{ point.item_type }}</n-tag>
                          </div>
                          <div class="info-row">
                            <span class="info-label">来源：</span>
                            <span class="info-content">{{ point.source }}</span>
                          </div>
                          <div class="info-row">
                            <span class="info-label">相关性：</span>
                            <n-progress
                              type="line"
                              :percentage="Math.round(point.relevance_score * 10)"
                              :indicator-placement="'inside'"
                              :color="'#2080f0'"
                              :show-indicator="true"
                              :format="percentage => `${percentage}%`"
                            />
                          </div>
                          <div class="info-row">
                            <span class="info-label">内容：</span>
                            <div class="info-content whitespace-pre-wrap">{{ point.content }}</div>
                          </div>
                          <div v-if="point.tags" class="info-row">
                            <span class="info-label">标签：</span>
                            <div class="tag-group">
                              <n-tag
                                v-for="tag in point.tags.split(',')"
                                :key="tag"
                                size="small"
                                :bordered="false"
                              >
                                {{ tag }}
                              </n-tag>
                            </div>
                          </div>
                        </div>
                      </n-collapse-item>
                    </n-collapse>
                  </div>
                </div>

                <!-- 背景相关的知识点 -->
                <div v-if="knowledgePoints.background_related.length > 0" class="knowledge-section mb-10">
                  <div class="section-header bg-gray-50 p-3 rounded-t-lg border-l-4 border-gray-500">
                    <h4 class="font-medium text-gray-800">背景相关 ({{ knowledgePoints.background_related.length }}条)</h4>
                  </div>
                  <div class="section-content">
                    <n-collapse>
                      <n-collapse-item
                        v-for="point in knowledgePoints.background_related"
                        :key="point.id"
                        :title="point.title"
                        @update:expanded-names="handleKnowledgeExpand"
                      >
                        <div class="knowledge-content">
                          <div class="info-row">
                            <span class="info-label">类型：</span>
                            <n-tag size="small">{{ point.item_type }}</n-tag>
                          </div>
                          <div class="info-row">
                            <span class="info-label">来源：</span>
                            <span class="info-content">{{ point.source }}</span>
                          </div>
                          <div class="info-row">
                            <span class="info-label">相关性：</span>
                            <n-progress
                              type="line"
                              :percentage="Math.round(point.relevance_score * 10)"
                              :indicator-placement="'inside'"
                              :color="'#d03050'"
                              :show-indicator="true"
                              :format="percentage => `${percentage}%`"
                            />
                          </div>
                          <div class="info-row">
                            <span class="info-label">内容：</span>
                            <div class="info-content whitespace-pre-wrap">{{ point.content }}</div>
                          </div>
                          <div v-if="point.tags" class="info-row">
                            <span class="info-label">标签：</span>
                            <div class="tag-group">
                              <n-tag
                                v-for="tag in point.tags.split(',')"
                                :key="tag"
                                size="small"
                                :bordered="false"
                              >
                                {{ tag }}
                              </n-tag>
                            </div>
                          </div>
                        </div>
                      </n-collapse-item>
                    </n-collapse>
                  </div>
                </div>
              </div>
              <div v-else class="empty-knowledge flex flex-col items-center justify-center h-[300px]">
                <TheIcon icon="mdi:book-open-page-variant-outline" :size="64" class="text-gray-300 mb-4" />
                <p class="text-gray-500">暂无相关知识点</p>
                <p class="text-gray-400 text-sm mt-2">选择一个需求并生成测试用例后，相关知识点将会显示在这里</p>
              </div>
            </div>
          </n-tab-pane>
        </n-tabs>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref, computed } from 'vue'
import { SearchOutline } from '@vicons/ionicons5'
import api from '@/api'
import { request } from '@/utils'
import { useMessage } from 'naive-ui'
import * as echarts from 'echarts'
import { marked } from 'marked'
import DOMPurify from 'dompurify'
import hljs from 'highlight.js';
import { nextTick, watch, onBeforeUnmount, onUnmounted } from 'vue'
import TheIcon from '@/components/icon/TheIcon.vue'
import { useRoute, useRouter } from 'vue-router'

// 添加路由相关
const route = useRoute()
const router = useRouter()

// 创建一个全局标记，避免路由守卫重复处理
if (!window.__testGeneratorRouteInitialized) {
  window.__testGeneratorRouteInitialized = true
}

// 标记页面是否已挂载，用于防止重复初始化和清理
const pageIsMounted = ref(false)
// 添加一个变量跟踪是否正在导航离开页面
const isNavigatingAway = ref(false)

// 状态定义
const searchParams = reactive({
  projectId: null,
  projectName: '',
  requirementName: '',
  caseName: ''
})
const scenario = ref('')
const requirementList = ref([])
const selectedRequirement = ref({
  description: '',
  scenarioDescription: '',
  tags: []
})

// 新增：存储所有选中的需求
const selectedRequirements = ref([])
const testCaseList = ref([])
const aiCommand = ref('开始生成测试用例')
const mindmapRef = ref(null)
let mindmapChart = null
const activeTab = ref('generation')

// 用于记录每个需求组的展开/折叠状态
const groupExpandState = ref({})

// 按需求分组的测试用例
const groupedTestCases = computed(() => {
  // 初始化分组对象
  const groups = {}

  // 将测试用例按需求ID分组
  testCaseList.value.forEach(testCase => {
    const reqId = testCase.requirement_id || 'unknown'
    const reqName = testCase.requirement_name || '未命名需求'

    if (!groups[reqId]) {
      groups[reqId] = {
        id: reqId,
        name: reqName,
        testCases: []
      }

      // 初始化该组的展开状态，默认为展开
      if (groupExpandState.value[reqId] === undefined) {
        groupExpandState.value[reqId] = true
      }
    }

    groups[reqId].testCases.push(testCase)
  })

  // 转换为数组并按需求ID排序
  return Object.values(groups).sort((a, b) => {
    // 尝试将ID转换为数字进行比较
    const idA = parseInt(a.id)
    const idB = parseInt(b.id)

    // 如果转换成功，按数字排序，否则按字符串排序
    if (!isNaN(idA) && !isNaN(idB)) {
      return idA - idB
    } else {
      return String(a.id).localeCompare(String(b.id))
    }
  })
})

// 计算简单文本相似度
const calculateSimpleTextSimilarity = (text1, text2) => {
  if (!text1 || !text2) return 0

  // 将文本转换为小写并分割成单词
  const words1 = text1.toLowerCase().split(/\s+/)
  const words2 = text2.toLowerCase().split(/\s+/)

  // 计算共同单词数
  const set1 = new Set(words1)
  const set2 = new Set(words2)

  let commonCount = 0
  for (const word of set1) {
    if (set2.has(word)) {
      commonCount++
    }
  }

  // 计算相似度
  const totalUniqueWords = set1.size + set2.size - commonCount
  return totalUniqueWords > 0 ? commonCount / totalUniqueWords : 0
}

// 计算采纳率
const adoptionRate = computed(() => {
  // 如果没有测试用例，返回100%
  if (testCaseList.value.length === 0) return 100

  // 计算已采纳的测试用例数量
  const adoptedCount = testCaseList.value.filter(testCase =>
    testCase.is_adopted === true || testCase.is_adopted === undefined
  ).length

  // 计算采纳率
  const rate = (adoptedCount / testCaseList.value.length) * 100

  // 返回保留两位小数的采纳率
  return Math.round(rate * 100) / 100
})

// 展开或折叠指定需求组的所有用例
const expandCollapseGroup = (groupIndex) => {
  const group = groupedTestCases.value[groupIndex]
  if (group) {
    // 切换展开状态
    groupExpandState.value[group.id] = !groupExpandState.value[group.id]
  }
}

const message = useMessage()
const isGenerating = ref(false)
const messageBlocks = ref([])
let socket = null

// 添加自动滚动相关的变量
const autoScroll = ref(true)
let userScrolled = ref(false)
let scrollTimer = null

// 添加滚动到顶部的函数
const scrollToTop = () => {
  console.log('执行滚动到顶部');

  try {
    // 首先强制滚动整个页面到顶部
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  } catch (e) {
    console.error('窗口滚动异常:', e);
    // 备用方法
    window.scrollTo(0, 0);
  }

  // 滚动页面容器到顶部
  const pageContainer = document.querySelector('.testcase-container');
  if (pageContainer) {
    try {
      pageContainer.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    } catch (e) {
      console.error('页面容器滚动异常:', e);
      // 备用方法
      pageContainer.scrollTop = 0;
    }
  }

  // 滚动各个容器到顶部
  nextTick(() => {
    // 尝试滚动需求列表容器
    const requirementsPanel = document.querySelector('.requirements-panel');
    if (requirementsPanel) {
      try {
        requirementsPanel.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      } catch (e) {
        console.error('需求列表容器滚动异常:', e);
        requirementsPanel.scrollTop = 0;
      }
    }

    // 尝试滚动需求列表
    const requirementsList = document.querySelector('.requirements-list');
    if (requirementsList) {
      try {
        requirementsList.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      } catch (e) {
        console.error('需求列表滚动异常:', e);
        requirementsList.scrollTop = 0;
      }
    }

    // 尝试滚动输出容器
    const outputContainer = document.querySelector('.output-container');
    if (outputContainer) {
      try {
        outputContainer.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      } catch (e) {
        console.error('输出容器滚动异常:', e);
        outputContainer.scrollTop = 0;
      }
    }

    // 尝试滚动markdown容器
    const markdownContainer = document.querySelector('.markdown-container');
    if (markdownContainer) {
      try {
        markdownContainer.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      } catch (e) {
        console.error('Markdown容器滚动异常:', e);
        markdownContainer.scrollTop = 0;
      }
    }

    // 尝试滚动所有可能的滚动容器
    const scrollContainers = [
      '.content-panel',
      '.case-list-panel',
      '.case-list-wrapper',
      '.output-display',
      '.testcase-list',
      '.mindmap-container',
      '.knowledge-container',
      '.n-scrollbar-container'
    ];

    scrollContainers.forEach(selector => {
      const container = document.querySelector(selector);
      if (container) {
        try {
          container.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        } catch (e) {
          console.error(`${selector} 滚动异常:`, e);
          container.scrollTop = 0;
        }
      }
    });

    // 显示成功消息
    message.success('已滚动到顶部');
  });
};

// 添加滚动到底部的函数
// const scrollToBottom = (force = false) => {
//   console.log('执行滚动到底部，force =', force);

//   // 首先尝试滚动整个页面容器
//   if (force) {
//     try {
//       // 滚动整个页面
//       window.scrollTo({
//         top: document.body.scrollHeight,
//         behavior: 'smooth'
//       });
//     } catch (e) {
//       console.error('窗口滚动异常:', e);
//       // 备用方法
//       window.scrollTo(0, document.body.scrollHeight);
//     }

//     // 滚动页面容器
//     const pageContainer = document.querySelector('.testcase-container');
//     if (pageContainer) {
//       try {
//         pageContainer.scrollTo({
//           top: pageContainer.scrollHeight,
//           behavior: 'smooth'
//         });
//       } catch (e) {
//         console.error('页面容器滚动异常:', e);
//         // 备用方法
//         pageContainer.scrollTop = pageContainer.scrollHeight;
//       }
//     }
//   }

//   nextTick(() => {
//     // 尝试滚动需求列表容器
//     const requirementsPanel = document.querySelector('.requirements-panel');
//     if (requirementsPanel) {
//       try {
//         requirementsPanel.scrollTo({
//           top: requirementsPanel.scrollHeight,
//           behavior: 'smooth'
//         });
//       } catch (e) {
//         console.error('需求列表容器滚动异常:', e);
//         requirementsPanel.scrollTop = requirementsPanel.scrollHeight;
//       }
//     }

//     // 尝试滚动需求列表
//     const requirementsList = document.querySelector('.requirements-list');
//     if (requirementsList) {
//       try {
//         requirementsList.scrollTo({
//           top: requirementsList.scrollHeight,
//           behavior: 'smooth'
//         });
//       } catch (e) {
//         console.error('需求列表滚动异常:', e);
//         requirementsList.scrollTop = requirementsList.scrollHeight;
//       }
//     }

//     // 尝试滚动输出容器
//     const outputContainer = document.querySelector('.output-container');
//     if (outputContainer) {
//       try {
//         outputContainer.scrollTo({
//           top: outputContainer.scrollHeight,
//           behavior: 'smooth'
//         });
//       } catch (e) {
//         console.error('输出容器滚动异常:', e);
//         outputContainer.scrollTop = outputContainer.scrollHeight;
//       }
//     }

//     // 尝试滚动markdown容器
//     const markdownContainer = document.querySelector('.markdown-container');
//     if (markdownContainer) {
//       try {
//         markdownContainer.scrollTo({
//           top: markdownContainer.scrollHeight,
//           behavior: 'smooth'
//         });
//       } catch (e) {
//         console.error('Markdown容器滚动异常:', e);
//         markdownContainer.scrollTop = markdownContainer.scrollHeight;
//       }
//     }

//     // 尝试滚动所有可能的滚动容器
//     const scrollContainers = [
//       '.content-panel',
//       '.case-list-panel',
//       '.case-list-wrapper',
//       '.output-display',
//       '.testcase-list',
//       '.mindmap-container',
//       '.knowledge-container',
//       '.n-scrollbar-container'
//     ];

//     scrollContainers.forEach(selector => {
//       const container = document.querySelector(selector);
//       if (container) {
//         try {
//           container.scrollTo({
//             top: container.scrollHeight,
//             behavior: 'smooth'
//           });
//         } catch (e) {
//           console.error(`${selector} 滚动异常:`, e);
//           container.scrollTop = container.scrollHeight;
//         }
//       }
//     });

//     // 显示成功消息
//     window.$message?.success('已滚动到底部');
//   });
// };

// 添加项目列表数据
const projectList = ref([])

// 存储已有测试用例的需求ID
const requirementsWithTestCases = ref(new Set())

// 存储需求相关的知识点
const knowledgePoints = ref({
  requirement_id: 0,
  requirement_name: '',
  direct_related: [],
  indirect_related: [],
  background_related: [],
  total_points: 0
})

// 检查需求是否已有测试用例
const checkRequirementHasTestCases = async (requirementId) => {
  try {
    const params = {
      requirement_id: requirementId
    }
    const { data } = await api.getTestCases(params)
    return data && data.length > 0
  } catch (error) {
    console.error(`检查需求 ${requirementId} 的测试用例失败:`, error)
    return false
  }
}

// 注释掉不再使用的函数
// const getRelevancePercentage = (relevanceLevel) => {
//   if (relevanceLevel === '直接相关') {
//     return 90
//   } else if (relevanceLevel === '间接相关') {
//     return 60
//   } else if (relevanceLevel === '背景相关') {
//     return 30
//   } else {
//     // 默认值或无法识别的相关性级别
//     return Math.round(34.48) // 保留原来的默认值
//   }
// }

// 获取需求相关的知识点
const loadKnowledgePoints = async (requirementId) => {
  try {
    if (!requirementId) return

    // 检查是否已有测试用例
    const hasTestCases = await checkRequirementHasTestCases(requirementId)
    if (!hasTestCases) {
      // 如果没有测试用例，清空知识点
      knowledgePoints.value = {
        requirement_id: requirementId,
        requirement_name: selectedRequirement.value.name || '',
        direct_related: [],
        indirect_related: [],
        background_related: [],
        total_points: 0
      }
      return
    }

    console.log(`开始获取需求 ${requirementId} 的相关知识点...`)

    try {
      // 显示加载提示，设置3秒后自动关闭
      const loadingMessage = message.loading('正在加载相关知识点...', { duration: 3000 })

      // 发起API请求获取知识点
      const response = await api.getRequirementKnowledge(requirementId)
      console.log(`获取需求 ${requirementId} 的相关知识点响应:`, response)

      // 关闭加载提示
      loadingMessage.destroy()

      // 处理响应数据
      if (response) {
        // 特殊处理：如果响应是 {code: 200, message: 'OK', error: {...}} 格式
        if (response.code === 200 && response.message === 'OK' && response.error) {
          // 使用error字段中的数据
          knowledgePoints.value = response.error
          console.log(`成功获取需求 ${requirementId} 的相关知识点(从error字段):`, knowledgePoints.value)

          // 如果有知识点，显示成功提示
          if (response.error.total_points > 0) {
            message.success(`已加载 ${response.error.total_points} 条相关知识点`)
          } else {
            message.info('该需求暂无相关知识点')
          }
        }
        // 如果响应是直接的数据对象
        else if (response.requirement_id !== undefined) {
          knowledgePoints.value = response
          console.log(`成功获取需求 ${requirementId} 的相关知识点:`, knowledgePoints.value)

          // 如果有知识点，显示成功提示
          if (response.total_points > 0) {
            message.success(`已加载 ${response.total_points} 条相关知识点`)
          } else {
            message.info('该需求暂无相关知识点')
          }
        }
        // 如果响应包含data字段
        else if (response.data) {
          knowledgePoints.value = response.data
          console.log(`成功获取需求 ${requirementId} 的相关知识点:`, knowledgePoints.value)

          // 如果有知识点，显示成功提示
          if (response.data.total_points > 0) {
            message.success(`已加载 ${response.data.total_points} 条相关知识点`)
          } else {
            message.info('该需求暂无相关知识点')
          }
        }
        // 如果响应格式不符合预期，但不是错误
        else {
          console.warn(`需求 ${requirementId} 的相关知识点响应格式不符合预期:`, response)
          // 使用默认空数据
          knowledgePoints.value = {
            requirement_id: requirementId,
            requirement_name: selectedRequirement.value.name || '',
            direct_related: [],
            indirect_related: [],
            background_related: [],
            total_points: 0
          }
          message.info('该需求暂无相关知识点')
        }
      } else {
        console.warn(`需求 ${requirementId} 的相关知识点响应为空`)
        // 使用默认空数据
        knowledgePoints.value = {
          requirement_id: requirementId,
          requirement_name: selectedRequirement.value.name || '',
          direct_related: [],
          indirect_related: [],
          background_related: [],
          total_points: 0
        }
        message.info('该需求暂无相关知识点')
      }
    } catch (apiError) {
      // 关闭可能存在的加载提示
      try {
        message.destroyAll();
      } catch (e) {
        console.error('关闭加载提示失败:', e);
      }

      console.error(`API调用失败: ${apiError.message || '未知错误'}`)
      console.error(apiError)

      // 特殊处理：如果apiError是 {code: 200, message: 'OK', error: {...}} 格式
      if (apiError.code === 200 && apiError.message === 'OK' && apiError.error) {
        // 使用error字段中的数据
        knowledgePoints.value = apiError.error
        console.log(`成功获取需求 ${requirementId} 的相关知识点(从apiError.error字段):`, knowledgePoints.value)

        // 如果有知识点，显示成功提示
        if (apiError.error.total_points > 0) {
          message.success(`已加载 ${apiError.error.total_points} 条相关知识点`)
        } else {
          message.info('该需求暂无相关知识点')
        }
        return
      }

      // 如果API调用失败，但不是服务器错误（例如网络问题），尝试使用默认空数据
      knowledgePoints.value = {
        requirement_id: requirementId,
        requirement_name: selectedRequirement.value.name || '',
        direct_related: [],
        indirect_related: [],
        background_related: [],
        total_points: 0
      }

      // 只有在真正的错误情况下才显示错误消息
      if (apiError.status >= 500 || apiError.message.includes('网络') || apiError.message.includes('timeout')) {
        message.error('获取相关知识点失败，请稍后重试')
      } else {
        console.log('该需求没有关联的知识点')
        message.info('该需求暂无相关知识点')
      }
    }
  } catch (error) {
    console.error(`获取需求 ${requirementId} 的相关知识点过程中发生错误:`, error)

    // 清空知识点
    knowledgePoints.value = {
      requirement_id: requirementId,
      requirement_name: selectedRequirement.value.name || '',
      direct_related: [],
      indirect_related: [],
      background_related: [],
      total_points: 0
    }

    // 显示友好的错误消息
    message.error('获取相关知识点时出现问题，已使用默认空数据')
  }
}

// 需求列表列定义
const requirementColumns = [
  {
    type: 'selection'
  },
  {
    title: '需求名称',
    key: 'name',
    render: (row) => {
      return h(
        'div',
        {
          class: 'relative cursor-pointer hover:text-primary',
          onClick: () => handleRequirementClick(row)
        },
        [
          row.name,
          // 如果需求已有测试用例，显示"已生成"标签
          requirementsWithTestCases.value.has(row.id) ?
          h(
            'n-tag',
            {
              size: 'small',
              type: 'success',
              class: 'absolute -top-2 -right-2',
              bordered: false,
              round: true
            },
            {
              default: () => '已生成',
              icon: () => h(TheIcon, { icon: 'mdi:check-circle', size: 14 })
            }
          ) : null
        ]
      )
    }
  }
]

// Markdown解析配置 - 修改这里以优化JSON显示
marked.setOptions({
  highlight: (code, lang) => {
    // 处理JSON格式
    if (lang === 'json') {
      try {
        // 尝试格式化JSON
        const formattedJson = JSON.stringify(JSON.parse(code), null, 2);
        return hljs.highlight(formattedJson, { language: 'json' }).value;
      } catch (e) {
        // 如果解析失败，作为普通代码处理
        return hljs.highlight(code, { language: 'json' }).value;
      }
    }

    // 处理其他语言
    const language = hljs.getLanguage(lang) ? lang : 'plaintext';
    try {
      return hljs.highlight(code, { language }).value;
    } catch (e) {
      return hljs.highlightAuto(code).value;
    }
  },
  breaks: true,
  gfm: true,
  pedantic: false,
  smartLists: true,
  smartypants: true
});

// JSON格式化函数 - 添加新函数
const formatJsonString = (jsonString) => {
  try {
    const parsed = JSON.parse(jsonString);
    return JSON.stringify(parsed, null, 2);
    } catch (e) {
    return jsonString; // 如果不是有效的JSON，返回原始字符串
    }
};

// 解析Markdown - 修改以更好地处理JSON
const parseMarkdown = (raw) => {
  if (!raw) return '';

  // 检测是否包含JSON内容
  const hasJsonBlock = raw.includes('```json');
  let processedContent = raw;

  // 替换JSON代码块，添加特殊标记
  if (hasJsonBlock) {
    processedContent = raw.replace(/```json\s*([\s\S]*?)```/g, (match, jsonContent) => {
      try {
        const formattedJson = formatJsonString(jsonContent.trim());
        // 使用具有唯一ID的wrapper，避免选择器问题
        const wrapperID = `json-wrapper-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
        return `<div class="json-wrapper" id="${wrapperID}">
          <div class="json-header">
            <span class="json-title">JSON</span>
            <button class="json-toggle" data-wrapper="${wrapperID}" title="展开/折叠">
              <span class="toggle-icon">▼</span>
            </button>
          </div>
          <pre class="language-json"><code>${formattedJson}</code></pre>
        </div>`;
      } catch (e) {
        return match; // 保持原样
      }
    });
  }

  const result = DOMPurify.sanitize(marked.parse(processedContent));

  // 如果包含JSON代码块，下一个tick时设置交互
  if (hasJsonBlock) {
    nextTick(() => {
      setupJsonInteractions();
    });
  }

  return result;
};

// 添加JSON代码块交互处理
const setupJsonInteractions = () => {
    nextTick(() => {
    // 使用document.body查询，确保可以找到元素，即使在scoped CSS中
    document.querySelectorAll('.json-toggle').forEach(button => {
      if (!button.hasJsonListener) {
        button.hasJsonListener = true;
        button.addEventListener('click', function() {
          // 使用data属性查找对应的wrapper
          const wrapperId = this.getAttribute('data-wrapper');
          const wrapper = document.getElementById(wrapperId);
          if (!wrapper) return;

          const codeBlock = wrapper.querySelector('pre');
          const icon = this.querySelector('.toggle-icon');

          if (codeBlock.classList.contains('collapsed')) {
            codeBlock.classList.remove('collapsed');
            icon.textContent = '▼';
          } else {
            codeBlock.classList.add('collapsed');
            icon.textContent = '▶';
          }
        });
      }
    });
  });
};

  const highlightCodeBlocks = () => {
  nextTick(() => {
  document.querySelectorAll('.markdown-content pre code').forEach(block => {
    if (!block.classList.contains('hljs')) {
      hljs.highlightElement(block);
    }
  });

    // 设置JSON交互功能
    setupJsonInteractions();
  });
};

// 方法定义
const handleSearch = async () => {
  try {
    const params = {}

    // 只有当 projectId 不为空时才添加 project_id 参数
    if (searchParams.projectId !== null && searchParams.projectId !== undefined) {
      // 确保 project_id 是数字类型
      params.project_id = Number(searchParams.projectId)
    }

    // 处理需求名称或TAPD URL
    if (searchParams.requirementName && searchParams.requirementName.trim() !== '') {
      const input = searchParams.requirementName.trim()

      // 检查是否是TAPD URL
      if (input.includes('tapd.cn') || input.startsWith('http') || input.includes('/')) {
        // 如果是URL，使用tapd_url参数
        console.log('检测到TAPD URL:', input)
        params.tapd_url = input
      } else {
        // 否则使用关键词参数
        params.keyword = input
      }
    }

    console.log('发送获取需求列表请求，参数:', params)

    const { data } = await api.getRequirementsList(params)
    console.log('获取需求列表成功，数据:', data)
    requirementList.value = data

    // 检查每个需求是否已有测试用例
    await checkRequirementsTestCases(data)
  } catch (error) {
    console.error('获取需求列表失败:', error)
    message.error('获取需求列表失败')
  }
}

// 检查需求列表中的需求是否已有测试用例
const checkRequirementsTestCases = async (requirements) => {
  try {
    // 创建一个临时集合存储已有测试用例的需求ID
    const tempSet = new Set()

    // 使用Promise.all并行检查所有需求
    await Promise.all(
      requirements.map(async (req) => {
        const hasTestCases = await checkRequirementHasTestCases(req.id)
        if (hasTestCases) {
          tempSet.add(req.id)
        }
      })
    )

    // 更新状态
    requirementsWithTestCases.value = tempSet
    console.log('已有测试用例的需求:', Array.from(tempSet))
  } catch (error) {
    console.error('检查需求测试用例状态失败:', error)
  }
}

const handleRequirementSelect = (_, rows) => {
  // 保存所有选中的需求
  selectedRequirements.value = rows.map(row => ({
    ...row,
    project_id: row.project_id || null,  // 确保project_id字段存在
    tapd_url: row.tapd_url || null,  // 确保tapd_url字段存在
    parent: row.parent || null,  // 确保parent字段存在
    level: row.level || '',  // 确保level字段存在
    estimate: row.estimate || null,  // 确保estimate字段存在
    category: row.category || '', // 确保category字段存在
    scenarioDescription: row.scenarioDescription || '',
    description: row.description || ''
  }))

  console.log('选中的需求数量:', selectedRequirements.value.length)
  console.log('选中的需求:', selectedRequirements.value)

  if (rows.length > 0) {
    // 为了兼容现有代码，仍然保留selectedRequirement，显示最后一个选中的需求
    selectedRequirement.value = {
      ...rows[rows.length - 1],
      project_id: rows[rows.length - 1].project_id || null,  // 确保project_id字段存在
      tapd_url: rows[rows.length - 1].tapd_url || null,  // 确保tapd_url字段存在
      parent: rows[rows.length - 1].parent || null,
      level: rows[rows.length - 1].level || '',  // 确保level字段存在
      estimate: rows[rows.length - 1].estimate || null,  // 确保estimate字段存在
      scenarioDescription: rows[rows.length - 1].scenarioDescription || '',
      description: rows[rows.length - 1].description || ''
    }

    // 检查是否已经在加载中
    if (!isLoadingTestCases) {
      // 检查是否已经加载了所有选中需求的测试用例
      const allRequirementsLoaded = selectedRequirements.value.every(req =>
        testCaseList.value.some(tc => tc.requirement_id === req.id)
      );

      if (!allRequirementsLoaded) {
        // 选中需求后自动加载测试用例
        console.log('选中需求后自动加载测试用例');
        loadTestCases();
      } else {
        console.log('所有选中需求的测试用例已加载，无需重复加载');
      }
    } else {
      console.log('正在加载测试用例，跳过选择需求触发的加载');
    }
  } else {
    // 创建一个空对象而不是null，避免空指针异常
    selectedRequirement.value = {
      description: '',
      scenarioDescription: '',
      tapd_url: null,  // 添加默认值
      tags: []
    }
    selectedRequirements.value = [] // 清空选中的需求列表
    testCaseList.value = []
  }
}

const handleRequirementClick = async (row) => {
  if (row) {
    // 确保必要的字段存在
    selectedRequirement.value = {
      ...row,
      description: row.description || '',
      scenarioDescription: row.scenarioDescription || ''
    }

    // 将点击的需求添加到selectedRequirements中，如果不存在的话
    const existingIndex = selectedRequirements.value.findIndex(req => req.id === row.id)
    if (existingIndex === -1) {
      selectedRequirements.value = [...selectedRequirements.value, {
        ...row,
        project_id: row.project_id || null,
        tapd_url: row.tapd_url || null,
        parent: row.parent || null,
        level: row.level || '',
        estimate: row.estimate || null,
        category: row.category || '',
        scenarioDescription: row.scenarioDescription || '',
        description: row.description || ''
      }]
    }

    // 检查是否已经在加载中
    if (!isLoadingTestCases) {
      // 检查是否已经加载了该需求的测试用例
      const hasTestCasesForRequirement = testCaseList.value.some(tc => tc.requirement_id === row.id);

      if (!hasTestCasesForRequirement) {
        // 点击需求后自动加载测试用例
        console.log('点击需求后自动加载测试用例');
        loadTestCases();
      } else {
        console.log('该需求的测试用例已加载，无需重复加载');

        // 如果测试用例已加载，尝试加载相关知识点
        if (requirementsWithTestCases.value.has(row.id)) {
          await loadKnowledgePoints(row.id);

          // 如果知识点加载成功且有数据，自动切换到知识点标签页
          if (knowledgePoints.value.total_points > 0) {
            activeTab.value = 'knowledge';

            // 在下一个渲染周期，确保知识点容器可滚动
            nextTick(() => {
              const knowledgeContainer = document.querySelector('.knowledge-container');
              if (knowledgeContainer) {
                knowledgeContainer.scrollTop = 0;

                // 添加一个小延迟，确保DOM已完全渲染
                setTimeout(() => {
                  // 尝试自动展开第一个知识点
                  const firstCollapseItem = knowledgeContainer.querySelector('.n-collapse-item');
                  if (firstCollapseItem) {
                    // 模拟点击展开
                    const header = firstCollapseItem.querySelector('.n-collapse-item__header');
                    if (header) {
                      header.click();
                    }
                  }
                }, 300);
              }
            });
          }
        }
      }
    } else {
      console.log('正在加载测试用例，跳过点击需求触发的加载');
    }
  } else {
    // 如果没有行数据，创建一个空对象
    selectedRequirement.value = {
      description: '',
      scenarioDescription: '',
      tags: []
    }

    // 清空知识点
    knowledgePoints.value = {
      requirement_id: 0,
      requirement_name: '',
      direct_related: [],
      indirect_related: [],
      background_related: [],
      total_points: 0
    }
  }
}
// 添加一个标志，防止重复加载
let isLoadingTestCases = false

const loadTestCases = async () => {
  // 检查是否有选中的需求
  if (selectedRequirements.value.length === 0) return

  // 防止重复加载
  if (isLoadingTestCases) {
    console.log('正在加载测试用例，跳过重复调用')
    return
  }

  try {
    // 设置加载标志
    isLoadingTestCases = true

    // 清空当前测试用例列表
    testCaseList.value = []

    // 重置展开状态
    groupExpandState.value = {}

    // 创建一个集合用于去重
    const uniqueTestCaseIds = new Set()
    let allTestCases = []

    // 用于检测相似用例的映射
    const titleMap = new Map() // 标题 -> 测试用例
    const descMap = new Map()  // 描述 -> 测试用例数组

    // 为每个选中的需求加载测试用例
    for (const req of selectedRequirements.value) {
      console.log(`加载需求 ${req.id} (${req.name}) 的测试用例`)

      const params = {
        requirement_id: req.id,
        project_id: req.project_id || 2
      }

      const { data } = await api.getTestCases(params)
      console.log(`需求 ${req.id} 的测试用例数量:`, data.length)

      // 将每个测试用例添加需求信息，并进行去重
      for (const testCase of data) {
        // 使用测试用例ID进行去重
        if (!uniqueTestCaseIds.has(testCase.id)) {
          uniqueTestCaseIds.add(testCase.id)

          // 添加需求信息
          const enhancedTestCase = {
            ...testCase,
            requirement_name: req.name, // 添加需求名称
            requirement_id: req.id // 确保需求ID存在
          }

          // 检测相似用例
          const title = testCase.title?.toLowerCase()
          const desc = testCase.desc?.toLowerCase()

          // 检查标题相似度
          if (title) {
            // 检查是否有完全相同的标题
            if (titleMap.has(title)) {
              // 标记为相似用例
              enhancedTestCase.similarity_info = {
                type: 'title_match',
                similar_to: titleMap.get(title)
              }
            } else {
              // 存储标题映射
              titleMap.set(title, enhancedTestCase)
            }
          }

          // 检查描述相似度
          if (desc) {
            // 如果描述长度超过20个字符，才进行相似度检查
            if (desc.length > 20) {
              // 检查是否有相似描述
              let similarFound = false
              descMap.forEach((cases, existingDesc) => {
                // 简单相似度检查：如果两个描述有80%以上的重叠，认为是相似的
                if (!similarFound && existingDesc.length > 20) {
                  const similarity = calculateSimpleTextSimilarity(desc, existingDesc)
                  if (similarity > 0.8) {
                    // 标记为相似用例
                    enhancedTestCase.similarity_info = enhancedTestCase.similarity_info || {
                      type: 'desc_match',
                      similarity: similarity,
                      similar_to: cases[0]
                    }
                    similarFound = true
                  }
                }
              })

              // 存储描述映射
              if (descMap.has(desc)) {
                descMap.get(desc).push(enhancedTestCase)
              } else {
                descMap.set(desc, [enhancedTestCase])
              }
            }
          }

          allTestCases.push(enhancedTestCase)
        }
      }
    }

    // 一次性更新测试用例列表，避免多次触发响应式更新
    testCaseList.value = allTestCases

    console.log('所有需求的测试用例总数(去重后):', testCaseList.value.length)

    // 更新需求状态，标记已有测试用例的需求
    for (const req of selectedRequirements.value) {
      if (allTestCases.some(tc => tc.requirement_id === req.id)) {
        requirementsWithTestCases.value.add(req.id)
      }
    }

    // 如果有测试用例，自动切换到用例列表标签页
    if (testCaseList.value.length > 0) {
      // 如果当前不在生成输出标签页，则切换到用例列表标签页
      if (activeTab.value !== 'generation') {
        activeTab.value = 'list'
      }

      // 渲染思维导图（即使不在思维导图标签页，也预先准备好数据）
      renderMindmap()

      // 加载相关知识点
      if (selectedRequirement.value && selectedRequirement.value.id) {
        await loadKnowledgePoints(selectedRequirement.value.id)

        // 如果知识点加载成功且有数据，自动切换到知识点标签页
        if (knowledgePoints.value.total_points > 0 && activeTab.value !== 'generation') {
          activeTab.value = 'knowledge'

          // 在下一个渲染周期，确保知识点容器可滚动
          nextTick(() => {
            const knowledgeContainer = document.querySelector('.knowledge-container');
            if (knowledgeContainer) {
              knowledgeContainer.scrollTop = 0;

              // 添加一个小延迟，确保DOM已完全渲染
              setTimeout(() => {
                // 尝试自动展开第一个知识点
                const firstCollapseItem = knowledgeContainer.querySelector('.n-collapse-item');
                if (firstCollapseItem) {
                  // 模拟点击展开
                  const header = firstCollapseItem.querySelector('.n-collapse-item__header');
                  if (header) {
                    header.click();
                  }
                }
              }, 300);
            }
          });
        }
      }

      // 显示提示消息
      message.success(`已加载 ${testCaseList.value.length} 个测试用例`)
    } else {
      message.info('该需求暂无测试用例')
    }
  } catch (error) {
    console.error('获取测试用例失败:', error)
    message.error('获取测试用例失败')
  } finally {
    // 无论成功还是失败，都重置加载标志
    isLoadingTestCases = false
  }
}

// 测试WebSocket连接
const testWebSocketConnection = async () => {
  try {
    console.log('🔧 开始详细的WebSocket连接诊断...')

    // 0. 环境信息诊断
    console.log('🌐 浏览器环境信息:')
    console.log('  - User Agent:', navigator.userAgent)
    console.log('  - 当前URL:', window.location.href)
    console.log('  - 协议:', window.location.protocol)
    console.log('  - 主机:', window.location.host)
    console.log('  - 端口:', window.location.port)

    // 1. 首先测试HTTP端点状态
    try {
      const statusResponse = await request.get('/api/v1/agent/ws/status')
      console.log('✅ WebSocket状态检查原始响应:', statusResponse)

      // 处理Success响应格式 {code: 200, msg: "OK", data: {...}}
      let statusData = statusResponse
      if (statusResponse && statusResponse.data) {
        statusData = statusResponse.data
      }
      // 如果是Success格式，提取data字段
      if (statusData && statusData.code === 200 && statusData.data) {
        statusData = statusData.data
      }

      console.log('✅ WebSocket状态数据:', statusData)

      // 检查状态是否正常
      if (statusData && (statusData.status === 'ok' || statusData.message === 'WebSocket服务正常运行')) {
        message.success('WebSocket服务状态正常')
      } else {
        console.warn('⚠️ WebSocket状态响应格式异常:', statusData)
        message.warning('WebSocket服务状态响应异常，但将继续测试连接')
      }
    } catch (error) {
      console.error('❌ WebSocket状态检查失败:', error)
      message.error('WebSocket服务状态检查失败，但将继续测试连接')
      // 不要return，继续测试WebSocket连接
    }

    // 2. 测试多个可能的WebSocket URL
    const possibleUrls = [
      // 当前页面的host
      `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/api/v1/agent/ws/generate`,
      // 明确指定localhost:9999
      'ws://localhost:9999/api/v1/agent/ws/generate',
      // 如果是开发环境，尝试127.0.0.1
      'ws://127.0.0.1:9999/api/v1/agent/ws/generate'
    ]

    console.log('🔗 将测试以下WebSocket URLs:', possibleUrls)

    for (let i = 0; i < possibleUrls.length; i++) {
      const wsUrl = possibleUrls[i]
      console.log(`\n🔗 测试URL ${i + 1}/${possibleUrls.length}: ${wsUrl}`)

      const testResult = await testSingleWebSocketUrl(wsUrl, i + 1)
      if (testResult.success) {
        message.success(`WebSocket连接成功！使用URL: ${wsUrl}`)
        return
      } else {
        console.log(`❌ URL ${i + 1} 测试失败:`, testResult.error)
      }
    }

    // 如果所有URL都失败了
    message.error('所有WebSocket URL测试都失败了，请检查后端服务器状态')

  } catch (error) {
    console.error('❌ WebSocket连接测试失败:', error)
    message.error('WebSocket连接测试失败: ' + (error.message || '未知错误'))
  }
}

// 测试单个WebSocket URL的函数
const testSingleWebSocketUrl = (wsUrl, urlIndex) => {
  return new Promise((resolve) => {
    try {
      console.log(`  📡 创建WebSocket连接...`)
      const testSocket = new WebSocket(wsUrl)

      // 设置连接超时（增加到10秒）
      const connectionTimeout = setTimeout(() => {
        console.error(`  ⏰ URL ${urlIndex} 连接超时（10秒）`)
        testSocket.close()
        resolve({ success: false, error: 'Connection timeout' })
      }, 10000)

      testSocket.onopen = () => {
        clearTimeout(connectionTimeout)
        console.log(`  ✅ URL ${urlIndex} 连接成功！`)

        // 发送测试消息
        const testMessage = {
          id: 999,
          name: '连接测试',
          description: '这是一个连接测试消息',
          task: '测试WebSocket连接',
          scenario: '连接测试场景'
        }

        try {
          testSocket.send(JSON.stringify(testMessage))
          console.log(`  📤 URL ${urlIndex} 测试消息已发送`)
        } catch (sendError) {
          console.error(`  ❌ URL ${urlIndex} 发送消息失败:`, sendError)
        }

        // 3秒后关闭测试连接
        setTimeout(() => {
          testSocket.close()
          console.log(`  🔚 URL ${urlIndex} 测试连接已关闭`)
          resolve({ success: true })
        }, 3000)
      }

      testSocket.onerror = (error) => {
        clearTimeout(connectionTimeout)
        console.error(`  ❌ URL ${urlIndex} 连接错误:`, error)
        console.error(`  ❌ URL ${urlIndex} 错误详情:`, {
          type: error.type,
          target: error.target,
          readyState: error.target?.readyState,
          url: error.target?.url
        })
        resolve({ success: false, error: error })
      }

      testSocket.onclose = (event) => {
        clearTimeout(connectionTimeout)
        console.log(`  🔚 URL ${urlIndex} 连接已关闭:`, {
          code: event.code,
          reason: event.reason,
          wasClean: event.wasClean
        })

        if (!event.wasClean && event.code !== 1000) {
          resolve({ success: false, error: `Connection closed abnormally: ${event.code} ${event.reason}` })
        }
      }

      testSocket.onmessage = (event) => {
        console.log(`  📥 URL ${urlIndex} 收到测试消息:`, event.data)
      }

    } catch (error) {
      console.error(`  ❌ URL ${urlIndex} 创建WebSocket失败:`, error)
      resolve({ success: false, error: error })
    }
  })
}

const handleGenerateTestCase = async () => {
  // 检查是否选择了需求
  if (selectedRequirements.value.length === 0) {
    message.warning('请先在左侧选择至少一条需求')
    return
  }

  // 检查AI指令是否为空
  if (!aiCommand.value || aiCommand.value.trim() === '') {
    message.warning('请输入 AI 指令')
    return
  }

  // 检查是否正在生成中
  if (isGenerating.value) {
    return
  }

  try {
    // 设置生成中状态
    isGenerating.value = true
    messageBlocks.value = []

    // 清空测试用例列表
    testCaseList.value = []

    // 初始化WebSocket连接
    initWebSocket()

    // 准备请求数据 - 使用新的数据结构支持多需求
    const requestData = {
      // 保留兼容性，使用最后一个选中的需求作为主需求
      ...selectedRequirement.value,
      // 添加所有选中的需求
      requirements: selectedRequirements.value,
      scenario: scenario.value,
      task: aiCommand.value,
      tapd_url: selectedRequirement.value.tapd_url || null  // 确保顶层也有tapd_url字段
    }

    console.log('发送的测试用例生成请求数据:', JSON.stringify(requestData, null, 2))

    // 等待连接建立，使用更可靠的方法
    let connectionEstablished = false

    // 尝试多次建立连接
    for (let attempt = 0; attempt < 3; attempt++) {
      if (isNavigatingAway.value) break // 如果正在离开页面，中止连接

      // 检查连接状态
      if (socket?.readyState === WebSocket.OPEN) {
        connectionEstablished = true
        break
      } else if (!socket || socket.readyState === WebSocket.CLOSED || socket.readyState === WebSocket.CLOSING) {
        // 如果连接已关闭或正在关闭，重新初始化
        console.log(`连接尝试 ${attempt + 1}: 重新初始化WebSocket`)
        initWebSocket()
    }

    // 等待连接建立
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    // 如果无法建立连接，抛出错误
    if (!connectionEstablished) {
      throw new Error('无法建立WebSocket连接')
    }

    // 发送数据
    socket.send(JSON.stringify(requestData))
    console.log('已发送测试用例生成请求')

  } catch (error) {
    console.error('生成失败:', error)
    message.error('生成失败: ' + error.message)
    handleGenerationError()
  }
}

// 工具方法
const getPriorityTagType = (priority) => {
  const types = {
    '高': 'error',
    '中': 'warning',
    '低': 'info'
  }
  return types[priority] || 'default'
}

const getStatusTagType = (status) => {
  const types = {
    '未开始': 'default',
    '进行中': 'info',
    '已完成': 'success',
    '已废弃': 'error'
  }
  return types[status] || 'default'
}

// 更新测试用例采纳状态
const updateTestCaseAdoption = async (caseId, isAdopted) => {
  try {
    console.log(`更新测试用例 ${caseId} 的采纳状态为: ${isAdopted ? '已采纳' : '未采纳'}`)

    // 调用API更新测试用例采纳状态
    await api.updateTestCaseAdoption({
      case_id: caseId,
      is_adopted: isAdopted
    })

    // 更新成功提示
    message.success(`测试用例${isAdopted ? '已采纳' : '未采纳'}`)

    // 更新本地数据
    const testCase = testCaseList.value.find(tc => tc.id === caseId)
    if (testCase) {
      testCase.is_adopted = isAdopted
    }
  } catch (error) {
    console.error('更新测试用例采纳状态失败:', error)
    message.error('更新测试用例采纳状态失败')

    // 恢复原来的状态
    const testCase = testCaseList.value.find(tc => tc.id === caseId)
    if (testCase) {
      testCase.is_adopted = !isAdopted
    }
  }
}

// 根据采纳率返回不同的CSS类
const getAdoptionRateClass = (rate) => {
  if (rate >= 90) return 'text-green-600' // 绿色，采纳率高
  if (rate >= 70) return 'text-blue-600'  // 蓝色，采纳率中等
  if (rate >= 50) return 'text-orange-500' // 橙色，采纳率一般
  return 'text-red-600' // 红色，采纳率低
}

// 处理知识点展开事件
const handleKnowledgeExpand = (expandedNames) => {
  if (expandedNames && expandedNames.length > 0) {
    // 在下一个渲染周期，确保展开的知识点可见
    nextTick(() => {
      // 找到展开的知识点元素
      const expandedItem = document.querySelector('.n-collapse-item--expanded');
      if (expandedItem) {
        // 获取知识点容器
        const knowledgeContainer = document.querySelector('.knowledge-container');
        if (knowledgeContainer) {
          // 计算元素在容器中的相对位置
          const containerRect = knowledgeContainer.getBoundingClientRect();
          const itemRect = expandedItem.getBoundingClientRect();
          const relativeTop = itemRect.top - containerRect.top;

          // 如果元素不在可视区域内，滚动到该元素
          if (relativeTop < 0 || relativeTop > containerRect.height - 100) {
            // 滚动到元素位置，留出一些顶部空间
            expandedItem.scrollIntoView({ behavior: 'smooth', block: 'start' });

            // 额外向上滚动一点，确保标题完全可见
            setTimeout(() => {
              knowledgeContainer.scrollTop -= 20;
            }, 100);
          }
        }
      }
    });
  }
}

// 思维导图渲染
const renderMindmap = () => {
  if (!mindmapRef.value) return

  // 确保有测试用例数据
  if (!testCaseList.value || testCaseList.value.length === 0) {
    if (mindmapChart) {
      mindmapChart.dispose()
      mindmapChart = null
    }
    return
  }

  // 等待DOM更新完成
  nextTick(() => {
    // 如果不在思维导图标签页，先设置一个标记，表示数据已准备好
    if (activeTab.value !== 'mindmap') {
      console.log('思维导图数据已准备好，等待用户切换到思维导图标签页');
      return;
    }

    // 检查容器是否可见
    if (!mindmapRef.value) {
      console.log('思维导图容器不存在，跳过渲染');
      return;
    }

    if (mindmapRef.value.offsetParent === null) {
      console.log('思维导图容器不可见，跳过渲染');
      return;
    }

    // 如果图表已存在，先销毁
    if (mindmapChart) {
      mindmapChart.dispose()
      mindmapChart = null
    }

    // 短暂延迟确保DOM已完全更新
    setTimeout(() => {
      // 初始化图表
      try {
        mindmapChart = echarts.init(mindmapRef.value)

        // 按需求分组测试用例
        const requirementGroups = {}

        // 将测试用例按需求ID分组
        testCaseList.value.forEach(testCase => {
          const reqId = testCase.requirement_id
          const reqName = testCase.requirement_name || '未命名需求'

          if (!requirementGroups[reqId]) {
            requirementGroups[reqId] = {
              name: reqName,
              testCases: []
            }
          }

          requirementGroups[reqId].testCases.push(testCase)
        })

        console.log('按需求分组的测试用例:', requirementGroups)

        // 创建思维导图数据
        const data = {
          name: '测试用例分析',
          children: Object.values(requirementGroups).map(group => ({
            name: group.name,
            children: group.testCases.map(testCase => ({
              name: testCase.title || '未命名用例',
              children: (testCase.steps || []).map(step => ({
                name: (step && (step.description || step.step_desc)) || '未命名步骤'
              }))
            }))
          }))
        }

        const option = {
          series: [
            {
              type: 'tree',
              data: [data],
              top: '5%',
              left: '5%',
              bottom: '5%',
              right: '15%',
              symbolSize: 7,
              initialTreeDepth: -1,
              layout: 'force',
              orient: 'LR',
              label: {
                position: 'left',
                verticalAlign: 'middle',
                align: 'right',
                fontSize: 12,
                distance: 8
              },
              leaves: {
                label: {
                  position: 'right',
                  verticalAlign: 'middle',
                  align: 'left'
                }
              },
              emphasis: {
                focus: 'descendant'
              },
              expandAndCollapse: true,
              animationDuration: 550,
              animationDurationUpdate: 750,
              roam: true,
              draggable: true,
              nodeDraggable: true,
              edgeDraggable: true,
              force: {
                layoutAnimation: true,
                repulsion: 1000,
                gravity: 0.1,
                edgeLength: 100,
                friction: 0.6
              },
              lineStyle: {
                width: 2,
                curveness: 0.3
              },
              itemStyle: {
                borderWidth: 2,
                borderColor: '#fff',
                shadowColor: 'rgba(0, 0, 0, 0.1)',
                shadowBlur: 10
              }
            }
          ],
          tooltip: {
            trigger: 'item',
            triggerOn: 'mousemove'
          }
        }

        mindmapChart.setOption(option)

        // 添加拖拽事件监听
        mindmapChart.on('mousedown', function(params) {
          if (params.dataType === 'node') {
            mindmapChart.setOption({
              series: [{
                data: [data],
                draggable: true,
                nodeDraggable: true,
                force: {
                  layoutAnimation: true,
                  friction: 0.1
                }
              }]
            })
          }
        })

        mindmapChart.on('mouseup', function() {
          mindmapChart.setOption({
            series: [{
              data: [data],
              force: {
                layoutAnimation: false,
                friction: 0.6
              }
            }]
          })
        })

        // 添加resize监听
        const resizeObserver = new ResizeObserver(() => {
          if (mindmapChart && mindmapRef.value && mindmapRef.value.offsetParent !== null) {
            mindmapChart.resize()
          }
        })
        if (mindmapRef.value) {
          resizeObserver.observe(mindmapRef.value)
        }
      } catch (error) {
        console.error('渲染思维导图失败:', error)
      }
    }, 300)
  })
}

// 添加标签页切换监听
watch(() => activeTab.value, (newValue) => {
  if (newValue === 'mindmap' && testCaseList.value && testCaseList.value.length > 0) {
    // 切换到思维导图标签页时重新渲染
    nextTick(() => {
      renderMindmap();
    });
  } else if (newValue === 'list' && selectedRequirements.value.length > 0 && testCaseList.value.length === 0) {
    // 切换到用例列表标签页时，如果有选中的需求但没有测试用例，则加载测试用例
    // 检查是否已经在加载中
    if (!isLoadingTestCases) {
      console.log('切换到用例列表标签页，自动加载测试用例');
      loadTestCases();
    } else {
      console.log('正在加载测试用例，跳过标签页切换触发的加载');
    }
  } else if (newValue === 'knowledge' && knowledgePoints.value.total_points > 0) {
    // 切换到知识点标签页时，确保容器可以滚动
    nextTick(() => {
      const knowledgeContainer = document.querySelector('.knowledge-container');
      if (knowledgeContainer) {
        knowledgeContainer.scrollTop = 0;
        console.log('已将知识点容器滚动到顶部');

        // 添加一个小延迟，确保DOM已完全渲染
        setTimeout(() => {
          // 尝试自动展开第一个知识点
          const firstCollapseItem = knowledgeContainer.querySelector('.n-collapse-item');
          if (firstCollapseItem) {
            // 模拟点击展开
            const header = firstCollapseItem.querySelector('.n-collapse-item__header');
            if (header) {
              header.click();
            }
          }
        }, 300);
      }
    });
  }
});

// 监听selectedRequirement变化
watch(() => selectedRequirement.value, (newValue, oldValue) => {
  // 只有当新值有效且与旧值不同时才加载测试用例
  if (newValue && newValue.id && (!oldValue || newValue.id !== oldValue.id)) {
    console.log('selectedRequirement变化，加载测试用例');

    // 检查是否已经在加载中
    if (isLoadingTestCases) {
      console.log('正在加载测试用例，跳过监听器触发的加载');
      return;
    }

    // 确保selectedRequirements中包含当前选中的需求
    const existingIndex = selectedRequirements.value.findIndex(req => req.id === newValue.id);
    if (existingIndex === -1) {
      selectedRequirements.value = [...selectedRequirements.value, newValue];
    } else {
      console.log('需求已在选中列表中，无需重复添加');
    }

    // 检查是否已经加载了该需求的测试用例
    const hasTestCasesForRequirement = testCaseList.value.some(tc => tc.requirement_id === newValue.id);
    if (hasTestCasesForRequirement) {
      console.log('已加载该需求的测试用例，无需重复加载');
      return;
    }

    // 加载测试用例
    loadTestCases();
  }
}, { deep: true });

// WebSocket初始化方法 - 完全重构
const initWebSocket = () => {
  // 确保在创建新连接前关闭现有连接
  closeWebSocket()

  try {
    console.log('初始化WebSocket连接...')

    // 智能选择WebSocket URL
    let wsUrl

    // 如果当前页面是通过localhost或127.0.0.1访问的，直接使用localhost:9999
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      wsUrl = 'ws://localhost:9999/api/v1/agent/ws/generate'
    } else {
      // 否则使用当前页面的host
      const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      wsUrl = `${wsProtocol}//${window.location.host}/api/v1/agent/ws/generate`
    }

    console.log('创建WebSocket URL:', wsUrl)
    console.log('当前页面hostname:', window.location.hostname)
    console.log('当前页面host:', window.location.host)

    socket = new WebSocket(wsUrl)
    // 设置连接超时（增加到15秒）
    const connectionTimeout = setTimeout(() => {
      if (socket && socket.readyState !== WebSocket.OPEN) {
        console.error('WebSocket连接超时（15秒）')
        console.error('连接状态:', socket.readyState)
        console.error('WebSocket状态说明:', {
          0: 'CONNECTING',
          1: 'OPEN',
          2: 'CLOSING',
          3: 'CLOSED'
        }[socket.readyState])
        message.error('连接超时，请重试')
        handleGenerationError()
      }
    }, 15000)

    // 连接打开时
    socket.onopen = () => {
      clearTimeout(connectionTimeout)
      console.log('WebSocket连接已建立')
    }

    // 接收消息时
    socket.onmessage = (event) => {
      // 如果正在导航离开，不处理新消息
      if (isNavigatingAway.value) {
        console.log('正在导航离开，忽略新消息')
        return
      }

      try {
        const data = JSON.parse(event.data);

        // 处理数据库返回的测试用例数据
        if (data.source === 'database') {
          try {
            console.log('数据库返回的测试用例数据:', data.content)
            const parsedContent = JSON.parse(data.content);
            console.log('解析后的测试用例数据:', parsedContent.testcases);
            testCaseList.value = Array.isArray(parsedContent.testcases) ? parsedContent.testcases : [];
            // 自动切换到用例列表标签页
            activeTab.value = 'list';
            // 渲染思维导图
            nextTick(() => {
              renderMindmap();
            });
          } catch (error) {
            console.error('测试用例数据解析失败:', error);
          }
        }
        // 处理用户反馈请求
        else if (data.source === 'user_proxy') {
          // 先隐藏所有已有的反馈区域，避免出现多个反馈框
          if (messageBlocks.value.length > 0) {
            const updatedMessages = [...messageBlocks.value];
            for (let i = 0; i < updatedMessages.length; i++) {
              if (updatedMessages[i].showFeedback) {
                updatedMessages[i] = {
                  ...updatedMessages[i],
                  showFeedback: false
                };
              }
            }
            messageBlocks.value = updatedMessages;
          }

          // 添加消息到消息列表，并设置显示反馈属性
          messageBlocks.value.push({
            source: data.source,
            content: parseMarkdown(data.content || '需要您的反馈'),
            rawContent: data.content || '需要您的反馈',
            timestamp: new Date().toISOString(),
            showFeedback: true,
            feedbackContent: '',
            isStreaming: false
          });

          // 滚动到底部显示反馈区域
          nextTick(() => {
            scrollToBottom(true);
          });
        }
        else {
          // 处理普通消息 - 修改这里实现连续相同source的消息合并
          const lastMessage = messageBlocks.value.length > 0 ? messageBlocks.value[messageBlocks.value.length - 1] : null;

          // 如果存在上一条消息，且source相同，且不是反馈消息，则合并内容
          if (lastMessage &&
              lastMessage.source === data.source &&
              !lastMessage.showFeedback &&
              !data.is_final) { // 非最终消息才合并

            // 更新现有消息的内容
            const updatedMessages = [...messageBlocks.value];
            const currentIndex = updatedMessages.length - 1;

            // 如果是第一次追加内容，保存原始markdown
            if (!lastMessage.isStreaming) {
              updatedMessages[currentIndex] = {
                ...updatedMessages[currentIndex],
                originalContent: updatedMessages[currentIndex].content,
                isStreaming: true
              };
            }

            // 合并原始内容
            const combinedRawContent = (lastMessage.rawContent || '') + (data.content || '');

            // 更新消息内容
            updatedMessages[currentIndex] = {
              ...updatedMessages[currentIndex],
              content: parseMarkdown(combinedRawContent),
              rawContent: combinedRawContent,
              timestamp: new Date().toISOString(),
              isStreaming: true
            };

            messageBlocks.value = updatedMessages;
          } else {
            // 创建新的消息块
            messageBlocks.value.push({
              source: data.source || 'ai',
              content: parseMarkdown(data.content),
              rawContent: data.content,
              timestamp: new Date().toISOString(),
              showFeedback: false,
              isStreaming: false
            });
          }
        }

        // 检查是否为最终消息
        if (data.is_final) {
          // 如果是最终消息，将当前消息标记为非流式
          if (messageBlocks.value.length > 0) {
            const updatedMessages = [...messageBlocks.value];
            const currentIndex = updatedMessages.length - 1;

            updatedMessages[currentIndex] = {
              ...updatedMessages[currentIndex],
              isStreaming: false
            };

            messageBlocks.value = updatedMessages;
          }

          nextTick(async () => {
            scrollToBottom(true); // 强制滚动到底部
            isGenerating.value = false; // 最后才更新状态

            // 生成完成后自动加载测试用例
            console.log('生成完成，开始加载测试用例')

            // 等待一小段时间，确保数据已写入数据库
            await new Promise(resolve => setTimeout(resolve, 1000))

            // 加载测试用例
            await loadTestCases()

            // 更新需求状态，标记为已生成测试用例
            for (const req of selectedRequirements.value) {
              requirementsWithTestCases.value.add(req.id)
            }

            // 切换到用例列表标签页
            activeTab.value = 'list'

            // 显示成功提示
            message.success('测试用例生成完成，已加载用例列表')
          });
        } else {
          nextTick(() => {
            scrollToBottom();
          });
        }

        nextTick(() => {
          highlightCodeBlocks();
          setupJsonInteractions(); // 额外调用一次确保JSON交互被设置
        });
      } catch (error) {
        console.error('消息解析失败:', error);
        if (!isNavigatingAway.value) {
          message.error('消息解析失败');
        }
        handleGenerationError();
      }
    };

    // 发生错误时
    socket.onerror = (error) => {
      clearTimeout(connectionTimeout)
      console.error('WebSocket错误:', error)
      console.error('WebSocket错误详情:', {
        type: error.type,
        target: error.target,
        readyState: error.target?.readyState,
        url: error.target?.url,
        timestamp: new Date().toISOString()
      })

      // 尝试分析错误原因
      let errorMessage = '连接失败，请重试'
      if (error.target?.readyState === WebSocket.CLOSED) {
        errorMessage = '连接被服务器拒绝，请检查服务器状态'
      } else if (error.target?.url) {
        errorMessage = `连接失败: ${error.target.url}`
      }

      if (!isNavigatingAway.value) {
        message.error(errorMessage)
      }
      handleGenerationError();
    }

    // 连接关闭时
    socket.onclose = (event) => {
      clearTimeout(connectionTimeout)
      console.log('WebSocket连接已关闭', {
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean,
        timestamp: new Date().toISOString()
      })

      // 分析关闭原因
      let closeMessage = ''
      switch (event.code) {
        case 1000:
          closeMessage = '正常关闭'
          break
        case 1001:
          closeMessage = '端点离开'
          break
        case 1002:
          closeMessage = '协议错误'
          break
        case 1003:
          closeMessage = '不支持的数据类型'
          break
        case 1006:
          closeMessage = '连接异常关闭（可能是网络问题）'
          break
        case 1011:
          closeMessage = '服务器错误'
          break
        default:
          closeMessage = `未知关闭原因 (${event.code})`
      }

      console.log('关闭原因分析:', closeMessage)

      // 只有在非导航离开且非正常关闭时才显示错误
      if (!isNavigatingAway.value && !event.wasClean) {
        message.error(`连接已断开: ${closeMessage}`)
        handleGenerationError();
      }
    }
  } catch (error) {
    console.error('WebSocket初始化失败:', error)
    if (!isNavigatingAway.value) {
      message.error('连接初始化失败，请重试')
    }
    handleGenerationError();
  }
}

// 强化关闭WebSocket连接的方法
const closeWebSocket = () => {
  if (!socket) return;

  console.log('正在关闭WebSocket连接...', socket.readyState)
  try {
    // 先解除所有事件监听器，防止错误事件触发
    socket.onopen = null
    socket.onmessage = null
    socket.onerror = null
    socket.onclose = null

    // 如果连接仍处于打开状态，先发送关闭信号
    if (socket.readyState === WebSocket.OPEN) {
      try {
        socket.send(JSON.stringify({
          source: 'client',
          content: 'CLIENT_DISCONNECTING'
        }))
      } catch (e) {
        console.warn('发送关闭信号失败:', e)
      }

      // 设置关闭超时，避免无限等待
      const closeTimeout = setTimeout(() => {
        console.warn('WebSocket关闭超时，强制清理')
        socket = null
      }, 1000)

      // 正常关闭连接
      socket.close(1000, '用户离开页面')

      // 关闭成功后清除超时
      socket.onclose = () => {
        clearTimeout(closeTimeout)
        console.log('WebSocket连接已正常关闭')
        socket = null
      }
    } else if (socket.readyState === WebSocket.CONNECTING) {
      // 如果还在连接中，直接关闭
      socket.close(1000, '连接被中止')
      socket = null
    } else {
      // 其他状态直接置空
      socket = null
    }
  } catch (e) {
    console.error('关闭WebSocket出错:', e)
    socket = null // 确保无论如何都重置socket引用
  }
}

// 完全重写错误处理方法
const handleGenerationError = () => {
  isGenerating.value = false
  closeWebSocket()

  // 重置UI状态
  try {
    messageBlocks.value = messageBlocks.value.map(msg => {
      if (msg.isStreaming) {
        return {
          ...msg,
          isStreaming: false
        }
      }
      return msg
    })
  } catch (e) {
    console.error('重置消息状态失败:', e)
  }
}

// 重写所有生命周期钩子和事件处理
onMounted(async () => {
  console.log('页面挂载')
  // 标记页面已挂载
  pageIsMounted.value = true
  isNavigatingAway.value = false

  // 延迟初始化资源，避免导航冲突
  await nextTick()

  // 初始化项目和需求列表
  try {
    await getProjectList()

    // 初始加载需求列表，直接调用API而不是 handleSearch
    const params = {}

    // 只有当 projectId 不为空时才添加 project_id 参数
    if (searchParams.projectId !== null && searchParams.projectId !== undefined) {
      // 确保 project_id 是数字类型
      params.project_id = Number(searchParams.projectId)
    }

    console.log('初始加载需求列表，参数:', params)

    const { data } = await api.getRequirementsList(params)
    console.log('初始加载需求列表成功，数据:', data)
    requirementList.value = data

    // 检查每个需求是否已有测试用例
    await checkRequirementsTestCases(data)

    // 检查URL参数中是否有需求ID
    const routeRequirementId = route.query.requirement_id
    if (routeRequirementId) {
      // 如果URL中有需求ID，尝试在列表中找到并选中它
      const requirement = data.find(req => req.id === Number(routeRequirementId))
      if (requirement) {
        console.log('从URL参数中找到需求:', requirement)
        selectedRequirement.value = {
          ...requirement,
          description: requirement.description || '',
          scenarioDescription: requirement.scenarioDescription || ''
        }
        selectedRequirements.value = [selectedRequirement.value]

        // 加载该需求的测试用例
        await loadTestCases()

        // 检查是否已有测试用例，如果有则自动加载相关知识点并切换到知识点标签页
        if (requirementsWithTestCases.value.has(Number(routeRequirementId))) {
          await loadKnowledgePoints(Number(routeRequirementId))
          // 如果知识点加载成功且有数据，自动切换到知识点标签页
          if (knowledgePoints.value.total_points > 0) {
            activeTab.value = 'knowledge'
          }
        }
      }
    } else if (selectedRequirements.value.length > 0) {
      // 如果已经有选中的需求，加载测试用例
      await loadTestCases()

      // 如果需求已有测试用例，加载相关知识点
      const reqId = selectedRequirement.value.id
      if (reqId && requirementsWithTestCases.value.has(reqId)) {
        await loadKnowledgePoints(reqId)
      }
    }
  } catch (e) {
    console.error('初始化数据失败:', e)
  }

  // 使用模块级变量存储引用，以便可以在任何地方删除
  window.__testGeneratorResizeHandler = handleWindowResize
  window.__testGeneratorUnloadHandler = handlePageUnload

  // 添加事件监听器
  window.addEventListener('resize', window.__testGeneratorResizeHandler)
  window.addEventListener('beforeunload', window.__testGeneratorUnloadHandler)

  // 设置滚动行为
  setupAutoScroll()

  // 观察输出容器变化
  const outputEl = document.querySelector('.markdown-container')
  if (outputEl) {
    window.__testGeneratorResizeObserver = new ResizeObserver(scrollToBottom)
    window.__testGeneratorResizeObserver.observe(outputEl)
  }

  // 添加路由守卫，但确保全局只添加一次
  if (!window.__testGeneratorRouteGuard) {
    window.__testGeneratorRouteGuard = (to, from, next) => {
      // 如果要离开当前页面
      if (from.path.includes('/testing/generator') && !to.path.includes('/testing/generator')) {
        console.log('检测到路由离开测试用例生成页面', from.path, '到', to.path)

        // 标记正在离开
        isNavigatingAway.value = true

        // 停止所有正在进行的操作
        isGenerating.value = false

        // 关闭WebSocket连接
        closeWebSocket()

        // 清理所有资源
        cleanupAllResources()
      }
      // 始终允许导航
      next()
    }

    // 添加到路由
    router.beforeEach(window.__testGeneratorRouteGuard)
  }

  // 添加激进的解决方案: 拦截所有导航链接点击
  setTimeout(() => {
    interceptNavigationLinks()
  }, 500)
})

// 新增: 拦截导航链接的点击事件
const interceptNavigationLinks = () => {
  // 找到所有导航链接
  const navLinks = document.querySelectorAll('.n-menu-item-content, .n-menu-item-content a')

  console.log('找到导航链接数量:', navLinks.length)

  // 添加点击事件拦截
  navLinks.forEach(link => {
    // 跳过已经处理过的链接
    if (link.__intercepted) return

    // 标记此链接已被处理
    link.__intercepted = true

    // 添加数据属性以标识原始URL
    const originalHref = link.getAttribute('href')
    if (originalHref) {
      link.setAttribute('data-original-href', originalHref)
    }

    // 获取菜单项的路由信息
    const routeInfo = link.getAttribute('data-route-info') || link.parentElement?.getAttribute('data-route-info')
    if (routeInfo) {
      link.setAttribute('data-route-info', routeInfo)
    }

    // 添加点击事件处理
    link.addEventListener('click', async (event) => {
      // 如果当前不在测试生成页面，使用正常导航
      if (!window.location.href.includes('/testing/generator')) {
        return
      }

      // 阻止默认导航
      event.preventDefault()
      event.stopPropagation()

      // 获取目标URL
      let targetPath = link.getAttribute('data-original-href') || link.getAttribute('href')

      // 如果没有找到URL但有路由信息，尝试构建URL
      if (!targetPath && routeInfo) {
        try {
          const routeData = JSON.parse(routeInfo)
          targetPath = routeData.path
        } catch (e) {
          console.error('解析路由数据失败:', e)
        }
      }

      if (!targetPath) {
        console.error('无法确定导航目标')
        return
      }

      console.log('拦截到导航请求，目标:', targetPath)

      // 执行清理
      isNavigatingAway.value = true
      isGenerating.value = false
      closeWebSocket()
      await cleanupAllResources()

      // 延迟一下以确保清理完成
      setTimeout(() => {
        // 使用更直接的导航方法
        safeNavigate(targetPath)
      }, 100)
    })
  })
}

// 新增: 安全导航方法
const safeNavigate = (path) => {
  console.log('执行安全导航到:', path)

  try {
    // 尝试使用Vue Router导航
    router.push(path).catch(error => {
      console.error('Vue Router导航失败:', error)

      // 如果Vue Router导航失败，使用window.location
      if (path.startsWith('/')) {
        // 相对路径
        window.location.href = path
      } else {
        // 绝对路径
        window.location.href = path
      }
    })
  } catch (e) {
    console.error('导航过程中发生错误:', e)

    // 最后的手段：硬重定向
    window.location.href = path
  }
}

// 修改现有的清理函数为async
const cleanupAllResources = async () => {
  console.log('执行全面资源清理')

  // 确保WebSocket连接已关闭
  closeWebSocket()

  // 移除DOM事件监听器
  // 检查函数是否存在再移除事件监听器
  if (typeof handleResize === 'function') {
    document.removeEventListener('mousemove', handleResize)
  }
  if (typeof stopResize === 'function') {
    document.removeEventListener('mouseup', stopResize)
  }

  // 处理JSON交互的事件监听器
  try {
    document.querySelectorAll('.json-toggle').forEach(button => {
      button.removeEventListener('click', null)
      button.hasJsonListener = false
    })
  } catch (e) {
    console.error('清理JSON交互监听器失败:', e)
  }

  // 销毁图表
  if (mindmapChart) {
    try {
      mindmapChart.dispose()
    } catch (e) {
      console.error('销毁图表失败:', e)
    } finally {
      mindmapChart = null
    }
  }

  // 重置状态
  isGenerating.value = false

  // 清除所有可能存在的定时器
  if (scrollTimer) {
    clearTimeout(scrollTimer)
    scrollTimer = null
  }

  // 等待一小段时间确保资源释放
  return new Promise(resolve => setTimeout(resolve, 50))
}

// 修改专门的预清理函数为async
const cleanupBeforeLeaving = async () => {
  console.log('执行预清理操作')

  // 关闭WebSocket
  closeWebSocket()

  // 移除全局事件监听器
  if (window.__testGeneratorResizeHandler) {
    window.removeEventListener('resize', window.__testGeneratorResizeHandler)
  }

  if (window.__testGeneratorUnloadHandler) {
    window.removeEventListener('beforeunload', window.__testGeneratorUnloadHandler)
  }

  // 移除ResizeObserver
  if (window.__testGeneratorResizeObserver) {
    window.__testGeneratorResizeObserver.disconnect()
    window.__testGeneratorResizeObserver = null
  }

  // 清理所有资源
  await cleanupAllResources()
}

// 修改卸载钩子
onBeforeUnmount(async () => {
  console.log('组件即将卸载 (Before)')
  // 立即标记离开状态，防止新事件处理
  isNavigatingAway.value = true

  // 清理资源
  await cleanupBeforeLeaving()
})

// 修改额外的卸载钩子
onUnmounted(async () => {
  console.log('组件已卸载 (After)')
  // 确保已标记离开状态
  isNavigatingAway.value = true

  // 再次清理所有资源，防止遗漏
  await cleanupBeforeLeaving()
})

// 修改为使用函数表达式定义事件处理器，以便可以精确移除
const handleWindowResize = () => {
  if (mindmapChart && !isNavigatingAway.value) {
    mindmapChart.resize()
  }
}

const handlePageUnload = () => {
  console.log('页面即将卸载')
  isNavigatingAway.value = true
  closeWebSocket()
  cleanupAllResources()
}

// 添加项目选择处理方法
const handleProjectSelect = (value) => {
  console.log('项目选择变更:', value, typeof value)

  // 如果 value 为 null 或者空，则清空项目选择
  if (value === null || value === undefined) {
    searchParams.projectName = ''
    searchParams.projectId = null
    // 清空需求选择，使用空对象而不是null
    selectedRequirement.value = {
      description: '',
      scenarioDescription: '',
      tags: []
    }
    testCaseList.value = []
    // 重新获取需求列表
    handleSearch()
    return
  }

  // 确保 value 是数字类型
  const projectId = Number(value)
  const selectedProject = projectList.value.find(p => p.id === projectId)

  console.log('选中的项目:', selectedProject)

  if (selectedProject) {
    searchParams.projectName = selectedProject.name
    searchParams.projectId = projectId  // 使用数字类型的 projectId
    // 清空需求选择，使用空对象而不是null
    selectedRequirement.value = {
      description: '',
      scenarioDescription: '',
      tags: []
    }
    testCaseList.value = []
    // 重新获取需求列表
    handleSearch()
  }
}

// 获取项目列表
const getProjectList = async () => {
  try {
    const { data } = await api.getProjts()
    projectList.value = data
  } catch (error) {
    message.error('获取项目列表失败')
  }
}

// 发送用户反馈 - 修改为接收消息索引
const sendFeedback = (index) => {
  if (socket && socket.readyState === WebSocket.OPEN) {
    socket.send(JSON.stringify({
      source: 'feedback',
      content: messageBlocks.value[index].feedbackContent || 'APPROVE'
    }));
  }

  // 创建消息副本并更新showFeedback属性，确保Vue检测到更改
  const updatedMessages = [...messageBlocks.value];
  updatedMessages[index] = {
    ...updatedMessages[index],
    showFeedback: false
  };
  messageBlocks.value = updatedMessages;
}

// 关闭反馈窗口并发送默认回复 - 修改为接收消息索引
const closeFeedback = (index) => {
  if (socket && socket.readyState === WebSocket.OPEN) {
    socket.send(JSON.stringify({
      source: 'feedback',
      content: 'APPROVE'
    }));
  }

  // 创建消息副本并更新showFeedback属性，确保Vue检测到更改
  const updatedMessages = [...messageBlocks.value];
  updatedMessages[index] = {
    ...updatedMessages[index],
    showFeedback: false
  };
  messageBlocks.value = updatedMessages;
}

// 添加同意响应的方法
const approveResponse = (index) => {
  if (socket && socket.readyState === WebSocket.OPEN) {
    socket.send(JSON.stringify({
      source: 'feedback',
      content: 'APPROVE'
    }));
  }

  // 创建消息副本并更新showFeedback属性，确保Vue检测到更改
  const updatedMessages = [...messageBlocks.value];
  updatedMessages[index] = {
    ...updatedMessages[index],
    showFeedback: false
  };
  messageBlocks.value = updatedMessages;
}

// 添加自动滚动到底部的函数
const scrollToBottom = (force = false) => {
  if (!autoScroll.value && !force) return;

  const outputContainer = document.querySelector('.markdown-container');
  if (outputContainer) {
    // 使用requestAnimationFrame确保在DOM更新后滚动
    requestAnimationFrame(() => {
      outputContainer.scrollTop = outputContainer.scrollHeight;
    });
  }
}

// 设置自动滚动功能
const setupAutoScroll = () => {
  const outputContainer = document.querySelector('.markdown-container');
  if (!outputContainer) return;

  // 监听滚动事件
  outputContainer.addEventListener('scroll', () => {
    // 如果用户手动向上滚动，则暂停自动滚动
    if (outputContainer.scrollHeight - outputContainer.scrollTop - outputContainer.clientHeight > 100) {
      autoScroll.value = false;
      userScrolled.value = true;
    } else {
      // 如果滚动到接近底部，恢复自动滚动
      autoScroll.value = true;
      userScrolled.value = false;
    }
  });
}
</script>

<style>
/* 将JSON样式放在非scoped区域，确保可以应用到动态生成的内容 */
.json-wrapper {
  position: relative;
  margin: 1.2em 0;
  border-radius: 8px;
  overflow: hidden;
  background: #f8f8f8;
}

.json-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #2c3e50;
  color: white;
  padding: 8px 12px;
  font-family: monospace;
  font-size: 0.9em;
}

.json-title {
  font-weight: 600;
}

.json-toggle {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 0.8em;
  padding: 2px 6px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.json-toggle:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.json-wrapper pre {
  margin: 0;
  max-height: 400px;
  overflow: auto;
  transition: max-height 0.3s ease;
  padding: 12px;
  background: #f8f8f8;
}

.json-wrapper pre.collapsed {
  max-height: 60px;
  overflow: hidden;
}

/* JSON语法高亮自定义样式 */
.json-wrapper .hljs-string {
  color: #42b983;
}

.json-wrapper .hljs-number {
  color: #ae81ff;
}

.json-wrapper .hljs-literal {
  color: #fd971f;
}

.json-wrapper .hljs-punctuation {
  color: #89bdff;
}

.json-wrapper .hljs-property {
  color: #66d9ef;
}
</style>

<style scoped>
/* 添加打字机动画 */
@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

.streaming-text {
  display: inline-block;
  white-space: pre-wrap;
  word-break: break-word;
  animation: fadeIn 0.2s ease-out;
}

/* 流式块样式 */
.streaming-block {
  position: relative;
  min-height: 1.2em;
  line-height: 1.6;
  padding: 4px 0;
}

.streaming-block::after {
  content: "▋";
  display: inline-block;
  vertical-align: middle;
  animation: blink 0.8s infinite;
  color: rgba(14, 165, 233, 0.6);
  margin-left: 2px;
}

/* 最终块样式 */
.final-block .streaming-text {
  animation: none;
}

.final-block::after {
  display: none;
}
.streaming-content {
  padding: 12px 16px;
  white-space: pre-wrap;
}

.testcase-container {
  height: 100vh;
  display: flex;
  overflow: hidden;
}

.requirements-panel {
  height: 100%;
  overflow-y: auto;
  border-right: 1px solid #e5e7eb;
  padding: 16px;
}

.content-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 16px;
}

.requirement-detail {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.content-wrapper {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.command-input {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.output-container {
  flex: 1;
  min-height: 0; /* 关键：允许容器缩小 */
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.n-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.n-tabs :deep(.n-tabs-nav) {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.n-tabs :deep(.n-tabs-pane-wrapper) {
  flex: 1;
  overflow: hidden;
  height: 100%;
}

.n-tab-pane {
  height: 100%;
}

.output-display {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  padding: 0; /* 移除所有内边距 */
}

.markdown-container {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  padding-bottom: 80px; /* 增加更多底部空间 */
  margin-bottom: 40px; /* 添加底部边距 */
  background: #ffffff;
  border-radius: 12px;
  position: relative;
  scroll-behavior: smooth; /* 添加平滑滚动 */

  /* 自定义滚动条样式 */

  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(14, 165, 233, 0.3);
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(14, 165, 233, 0.5);
    }
  }
}

.markdown-container.has-content {
  border: 1px solid #e0e0e0;
}

.message-item {
  margin-bottom: 20px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  animation: fadeIn 0.3s ease;
  transition: all 0.3s ease;

  &:last-child {
    margin-bottom: 20px; /* 为最后一个消息添加底部边距 */
  }

  &.new-message {
    animation: slideInUp 0.3s ease;
  }
}

.message-item.ai {
  background: #f8ffff;
  border-left: 3px solid #2185d0;
}

.message-item.user {
  background: #fff8f6;
  border-left: 3px solid #db2828;
}

.source-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
  font-size: 0.85em;
  margin-bottom: 8px;
  font-weight: 500;
}

.markdown-content {
  font-family: 'Inter', system-ui, sans-serif;
  color: #333;
  line-height: 1.7;
  overflow-wrap: break-word;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3 {
  margin: 1.5em 0 1em;
  font-weight: 600;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.3em;
}

.markdown-content pre {
  background: #f6f8fa;
  border-radius: 8px;
  padding: 1em;
  margin: 1.2em 0;
  overflow-x: auto;
}

.markdown-content code {
  font-family: 'Fira Code', monospace;
  font-size: 0.9em;
  background: rgba(175, 184, 193, 0.2);
  padding: 0.2em 0.4em;
  border-radius: 4px;
}

.markdown-content blockquote {
  color: #6a737d;
  border-left: 4px solid #dfe2e5;
  margin: 1em 0;
  padding: 0 1em;
  background: #f8f9fa;
}

.markdown-content table {
  border-collapse: collapse;
  margin: 1.5em 0;
  width: 100%;
}

.markdown-content td,
.markdown-content th {
  border: 1px solid #dfe2e5;
  padding: 0.6em 1em;
}

.markdown-content tr:nth-child(even) {
  background: #f6f8fa;
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px 24px;
  margin: 16px 0;
  color: #666;
  font-size: 0.9em;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  min-height: 60px;
  width: 100%;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.output-container {
  position: relative;
  flex: 1;
}

/* 测试用例列表样式 */
.testcase-list {
  height: 100%;
  display: flex;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  padding: 24px;
  gap: 24px;
  overflow: hidden; /* 添加overflow: hidden */
}

.case-list-panel {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  height: 100%; /* 确保面板占满高度 */
  overflow: hidden; /* 添加overflow: hidden */
}

.case-list-wrapper {
  flex: 1; /* 改为flex: 1 */
  overflow-y: auto;
  padding-right: 8px;
  height: 100%; /* 确保wrapper占满高度 */
  min-height: 0; /* 添加min-height: 0 */

  /* 自定义滚动条样式 */

  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(14, 165, 233, 0.3);
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(14, 165, 233, 0.5);
    }
  }
}

.mindmap-panel {
  display: flex;
  flex-direction: column;
  min-width: 500px;
  border-left: 1px solid #e5e7eb;
  padding-left: 24px;
}

.mindmap-container {
  height: 100%;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: auto;
  position: relative;
  background: #ffffff;
  padding: 16px;

  /* 自定义滚动条样式 */

  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(14, 165, 233, 0.3);
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(14, 165, 233, 0.5);
    }
  }
}

.mindmap-container :deep(.echarts) {
  min-height: 100%;
  min-width: 100%;
  width: 100% !important;
  height: 100% !important;
}

/* 添加测试用例卡片样式 */
.testcase-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.testcase-title {
  font-weight: 500;
  font-size: 14px;
  color: #333;
  flex: 1;
}

.testcase-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.testcase-creator {
  font-size: 12px;
  color: #666;
}

.step-description {
  margin-bottom: 8px;
}

.step-result {
  color: #666;
  font-size: 13px;
  padding-left: 24px;
  margin-bottom: 16px;
}

.tag-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.info-row {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.info-label {
  color: #666;
  min-width: 80px;
}

.info-content {
  color: #333;
  flex: 1;
}

.steps-container {
  margin-top: 16px;
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.step-item {
  margin-bottom: 16px;
  padding-left: 16px;
  border-left: 2px solid #e8e8e8;
}

.step-item:last-child {
  margin-bottom: 0;
}

/* 需求列表样式 */
.requirements-list {
  height: calc(100% - 80px);
  overflow-y: auto;

  /* 自定义滚动条样式 */

  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(14, 165, 233, 0.3);
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(14, 165, 233, 0.5);
    }
  }
}

.n-input :deep(.n-input__border) {
  border-radius: 6px;
}

/* 调整输入框对齐 */
.command-input .n-input {
  margin-left: -4px; /* 补偿按钮间距 */
}

/* 统一内容区域内边距 */
.content-wrapper,
.n-tabs,
.n-collapse {
  padding-left: 8px;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.scenario-input {
  height: 150px;
  overflow-y: auto;

  :deep(.n-input__textarea-el) {
    height: 100% !important;
    resize: none;
  }

  /* 自定义滚动条样式 */

  :deep(.n-input__textarea-el::-webkit-scrollbar) {
    width: 6px;
    height: 6px;
  }

  :deep(.n-input__textarea-el::-webkit-scrollbar-track) {
    background: #f1f1f1;
    border-radius: 3px;
  }

  :deep(.n-input__textarea-el::-webkit-scrollbar-thumb) {
    background: rgba(14, 165, 233, 0.3);
    border-radius: 3px;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(14, 165, 233, 0.5);
    }
  }
}

.description-input {
  height: 50px;
  overflow-y: auto;

  :deep(.n-input__textarea-el) {
    height: 100% !important;
    resize: none;
  }

  /* 自定义滚动条样式 */

  :deep(.n-input__textarea-el::-webkit-scrollbar) {
    width: 6px;
    height: 6px;
  }

  :deep(.n-input__textarea-el::-webkit-scrollbar-track) {
    background: #f1f1f1;
    border-radius: 3px;
  }

  :deep(.n-input__textarea-el::-webkit-scrollbar-thumb) {
    background: rgba(14, 165, 233, 0.3);
    border-radius: 3px;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(14, 165, 233, 0.5);
    }
  }
}

/* 添加反馈相关样式 */
.feedback-container {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 12px;
  border: 1px dashed #d9d9d9;
}

.feedback-input {
  width: 100%;
}

.feedback-actions {
  display: flex;
  justify-content: flex-end;
}

/* 调整user_proxy消息的样式 */
.message-item.user_proxy {
  background: #fff8dc;
  border-left: 3px solid #ff9800;
}

/* 添加流式输出相关的样式 */
.message-item.streaming {
  position: relative;
}

.message-item.streaming::after {
  content: "▋";
  display: inline-block;
  vertical-align: middle;
  animation: blink 0.8s infinite;
  color: rgba(14, 165, 233, 0.6);
  font-size: 1.2em;
  position: absolute;
  bottom: 16px;
  right: 16px;
}

@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

/* 固定的滚动按钮 - 底部 */
.scroll-to-bottom-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #2080f0;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
}

.scroll-to-bottom-btn:hover {
  bottom: 30px; /* 固定在底部 */
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

/* 固定的滚动按钮 - 顶部 */
.scroll-to-top-btn {
  position: fixed;
  bottom: 100px; /* 位于底部按钮上方 */
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #18a058; /* 使用不同的颜色区分 */
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
}

.scroll-to-top-btn:hover {
  bottom: 90px;
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

/* 需求组标题样式 */
.requirement-group {
  margin-bottom: 1.5rem;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.requirement-title {
  font-weight: 600;
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: #f0f7ff;
  border-left: 4px solid #3b82f6;
  border-radius: 6px 6px 0 0;
  margin-bottom: 0;
  transition: all 0.2s ease;
}

.requirement-title:hover {
  background-color: #e0f0ff;
}

/* 采纳率统计样式 */
.adoption-rate-container {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 10px 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #2080f0;
  margin-bottom: 15px;
}

.adoption-rate-display {
  font-size: 18px;
  font-weight: bold;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

/* 知识点容器样式 */
.knowledge-container {
  position: relative;
  display: flex;
  flex-direction: column;

  /* 确保内容可以完全滚动到底部 */
  &::after {
    content: '';
    display: block;
    height: 20px; /* 底部额外空间 */
    width: 100%;
  }

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(14, 165, 233, 0.3);
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(14, 165, 233, 0.5);
    }
  }
}

/* 知识点部分样式 */
.knowledge-section {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 30px; /* 确保最后一个部分有足够的底部边距 */
  }
}
</style>
