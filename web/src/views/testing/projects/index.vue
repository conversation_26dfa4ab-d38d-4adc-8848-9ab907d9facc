<script setup>
import { h, onMounted, ref, resolveDirective, withDirectives } from 'vue'
import { NButton, NForm, NFormItem, NInput, NInputNumber, NPopconfirm, NTreeSelect } from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'
import TheIcon from '@/components/icon/TheIcon.vue'

import { renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
// import { loginTypeMap, loginTypeOptions } from '@/constant/data'
import api from '@/api'

defineOptions({ name: '项目管理' })

const $table = ref(null)
const queryItems = ref({})
const vPermission = resolveDirective('permission')

const {
  modalVisible,
  modalTitle,
  modalLoading,
  handleSave,
  modalForm,
  modalFormRef,
  handleEdit,
  handleDelete,
  handleAdd,
} = useCRUD({
  name: '项目',
  initForm: { order: 0, desc: '' },
  doCreate: api.createProj,
  doUpdate: api.updateProj,
  doDelete: api.deleteProj,
  refresh: () => $table.value?.handleSearch(),
})

const projOption = ref([])
const isDisabled = ref(false)

onMounted(() => {
  $table.value?.handleSearch()
  api.getProjts().then((res) => {
    console.log('项目管理页面获取项目列表响应:', res)
    projOption.value = res.data
    console.log('项目管理页面项目选项:', projOption.value)
  })
})

const projectRules = {
  name: [
    {
      required: true,
      message: '请输入项目名称',
      trigger: ['input', 'blur', 'change'],
    },
  ],
  desc: [
    {
      required: true,
      message: '请输入项目内容',
      trigger: ['input', 'blur', 'change'],
    },
  ],
}

async function addProjects() {
  isDisabled.value = false
  handleAdd()
}

const columns = [
  {
    title: '项目名称',
    key: 'name',
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: '项目内容',
    key: 'desc',
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '创建时间',
    key: 'created_at',
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '操作',
    key: 'actions',
    width: 'auto',
    align: 'center',
    fixed: 'right',
    render(row) {
      return [
        withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              style: 'margin-left: 8px;',
              onClick: () => {
                console.log('row', row.id)
                if (row.id === 0) {
                  isDisabled.value = true
                } else {
                  isDisabled.value = false
                }
                handleEdit(row)
              },
            },
            {
              default: () => '编辑',
              icon: renderIcon('material-symbols:edit', { size: 16 }),
            }
          ),
          [[vPermission, 'post/api/v1/project/update']]
        ),
        h(
          NPopconfirm,
          {
            onPositiveClick: () => handleDelete({ proj_id: row.id }, false),
            onNegativeClick: () => {},
          },
          {
            trigger: () =>
              withDirectives(
                h(
                  NButton,
                  {
                    size: 'small',
                    type: 'error',
                    style: 'margin-left: 8px;',
                  },
                  {
                    default: () => '删除',
                    icon: renderIcon('material-symbols:delete-outline', { size: 16 }),
                  }
                ),
                [[vPermission, 'delete/api/v1/project/delete']]
              ),
            default: () => h('div', {}, '确定删除该项目吗?'),
          }
        ),
      ]
    },
  },
]
</script>

<template>
  <!-- 业务页面 -->
  <CommonPage show-footer title="项目列表">
    <template #action>
      <div>
        <NButton
          v-permission="'post/api/v1/project/create'"
          class="float-right mr-15"
          type="primary"
          @click="addProjects"
        >
          <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />新建项目
        </NButton>
      </div>
    </template>
    <!-- 表格 -->
    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="api.getProjts"
    >
      <template #queryBar>
        <QueryBarItem label="项目名称" :label-width="80">
          <NInput
            v-model:value="queryItems.name"
            clearable
            type="text"
            placeholder="请输入项目名称"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
      </template>
    </CrudTable>

    <!-- 新增/编辑 弹窗 -->
    <CrudModal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :loading="modalLoading"
      @save="handleSave"
    >
      <NForm
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="80"
        :model="modalForm"
        :rules="projectRules"
      >
        <NFormItem label="项目名称" path="name">
          <NInput v-model:value="modalForm.name" clearable placeholder="请输入项目名称" />
        </NFormItem>
        <NFormItem label="项目内容" path="desc">
          <NInput v-model:value="modalForm.desc" type="textarea" clearable placeholder="请输入项目内容（必填）" />
        </NFormItem>
      </NForm>
    </CrudModal>
  </CommonPage>
</template>
