<template>
  <!-- 业务页面 -->
  <CommonPage show-footer title="项目知识库" class="overflow-auto">
    <template #action>
      <div>
        <NButton
          v-permission="'post/api/v1/knowledge/'"
          class="float-right mr-15"
          type="primary"
          @click="handleAdd"
        >
          <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />新增知识条目
        </NButton>
      </div>
    </template>

    <!-- 滚动到底部按钮 -->
    <div class="scroll-to-bottom-btn" @click="scrollToBottom(true)">
      <TheIcon icon="mdi:chevron-double-down" :size="20" />
    </div>

    <!-- 返回顶部按钮 -->
    <div class="scroll-to-top-btn" @click="scrollToTop()">
      <TheIcon icon="mdi:chevron-double-up" :size="20" />
    </div>

    <!-- 表格 -->
    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="api.getKnowledgeItems"
    >
      <template #queryBar>
        <QueryBarItem label="项目" :label-width="60">
          <NTreeSelect
            v-model:value="queryItems.project_id"
            :options="projOption"
            key-field="key"
            label-field="label"
            value-field="value"
            placeholder="请选择项目"
            clearable
            default-expand-all
            style="min-width: 200px;"
            @update:value="$table?.handleSearch()"
          ></NTreeSelect>
        </QueryBarItem>
        <QueryBarItem label="类型" :label-width="60">
          <NSelect
            v-model:value="queryItems.item_type"
            :options="itemTypeOptions"
            placeholder="请选择类型"
            clearable
            style="min-width: 200px;"
            @update:value="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="关键词" :label-width="60">
          <NInput
            v-model:value="queryItems.search"
            clearable
            type="text"
            placeholder="请输入标题/内容/标签关键词"
            style="min-width: 200px;"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="TAPD链接" :label-width="80">
          <NInput
            v-model:value="queryItems.source_url"
            clearable
            type="text"
            placeholder="请输入TAPD需求链接"
            style="min-width: 200px;"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
      </template>
    </CrudTable>

    <!-- 新增/编辑 弹窗 -->
    <CrudModal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :loading="modalLoading"
      @save="handleSave"
    >
      <NForm
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="80"
        :model="modalForm"
        :rules="formRules"
      >
        <NFormItem label="关联项目" path="project_id">
          <NTreeSelect
            v-model:value="modalForm.project_id"
            :options="projOption"
            key-field="key"
            label-field="label"
            value-field="value"
            placeholder="请选择项目"
            clearable
            default-expand-all
          ></NTreeSelect>
        </NFormItem>
        <NFormItem label="知识标题" path="title">
          <NInput v-model:value="modalForm.title" clearable placeholder="请输入知识条目标题" />
        </NFormItem>
        <NFormItem label="知识类型" path="item_type">
          <NSelect
            v-model:value="modalForm.item_type"
            :options="itemTypeOptions"
            placeholder="请选择知识类型"
          />
        </NFormItem>
        <NFormItem label="标签" path="tags">
          <NInput v-model:value="modalForm.tags" clearable placeholder="请输入标签，多个标签用逗号分隔" />
        </NFormItem>
        <NFormItem label="来源" path="source">
          <NSelect
            v-model:value="modalForm.source"
            :options="sourceOptions"
            placeholder="请选择来源"
          />
        </NFormItem>
        <NFormItem label="详细内容" path="content">
          <NInput
            v-model:value="modalForm.content"
            type="textarea"
            :autosize="{ minRows: 6, maxRows: 12 }"
            clearable
            placeholder="请输入详细内容"
          />
        </NFormItem>
      </NForm>
    </CrudModal>
  </CommonPage>
</template>

<script setup>
import { ref, onMounted, h, withDirectives, nextTick } from 'vue'
import { NButton, NTag, NPopconfirm, useMessage } from 'naive-ui'
import { formatDate, renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
import api from '@/api'
import CommonPage from '@/components/page/CommonPage.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import TheIcon from '@/components/icon/TheIcon.vue'
import { resolveDirective } from 'vue'

defineOptions({ name: '项目知识库' })

const $table = ref(null)
const queryItems = ref({
  project_id: null,
  item_type: null,
  search: '',
  source_url: '',
})
const vPermission = resolveDirective('permission')
const message = useMessage()

// 项目选项
const projOption = ref([])

// 知识条目类型选项
const itemTypeOptions = [
  { label: '业务规则', value: '业务规则' },
  { label: '核心功能', value: '核心功能' },
  { label: '术语解释', value: '术语解释' },
  { label: '测试要点', value: '测试要点' },
  { label: '历史缺陷总结', value: '历史缺陷总结' },
  { label: '非功能要求', value: '非功能要求' },
  { label: '环境配置', value: '环境配置' },
  { label: '其他', value: '其他' },
]

// 来源选项
const sourceOptions = [
  { label: '手动添加', value: '手动添加' },
  { label: '需求分析导入', value: '需求分析导入' },
  { label: '测试用例导入', value: '测试用例导入' },
  { label: '缺陷导入', value: '缺陷导入' },
  { label: '其他来源', value: '其他来源' },
]

// 表单验证规则
const formRules = {
  project_id: {
    required: true,
    message: '请选择关联项目',
    trigger: ['blur', 'change'],
    type: 'number',
  },
  title: {
    required: true,
    message: '请输入知识条目标题',
    trigger: ['blur', 'input'],
    type: 'string',
  },
  content: {
    required: true,
    message: '请输入详细内容',
    trigger: ['blur', 'input'],
    type: 'string',
  },
  item_type: {
    required: true,
    message: '请选择知识类型',
    trigger: ['blur', 'change'],
    type: 'string',
  },
}

// 表单初始化内容
const initForm = {
  project_id: null,
  title: '',
  content: '',
  item_type: '业务规则',
  tags: '',
  source: '手动添加',
}

// 重置表单
function resetForm() {
  modalForm.value = { ...initForm };
  // 如果有项目数据，默认选择第一个项目
  if (projOption.value.length > 0) {
    modalForm.value.project_id = projOption.value[0].value;
  }
}

// 获取CRUD操作
const crudOps = useCRUD({
  name: '知识条目',
  initForm,
  doCreate: (data) => {
    console.log('创建知识条目，数据:', data);
    return api.createKnowledgeItem(data).then(response => {
      // 创建成功后立即刷新表格
      nextTick(() => {
        $table.value?.handleSearch();
      });
      return response;
    });
  },
  doDelete: (id) => api.deleteKnowledgeItem(id).then(response => {
    // 删除成功后立即刷新表格
    nextTick(() => {
      $table.value?.handleSearch();
    });
    return response;
  }),
  doUpdate: (data) => {
    console.log('更新知识条目，数据:', data);
    return api.updateKnowledgeItem(data.id, data).then(response => {
      // 更新成功后立即刷新表格
      nextTick(() => {
        $table.value?.handleSearch();
        console.log('知识条目更新后刷新表格');
      });
      return response;
    });
  },
  refresh: () => $table.value?.handleSearch(),
})

// 解构CRUD操作
const {
  modalVisible,
  modalTitle,
  modalLoading,
  handleAdd: originalHandleAdd,
  handleDelete,
  handleEdit,
  handleSave,
  modalForm,
  modalFormRef,
} = crudOps

// 自定义添加操作，先重置表单再调用原始添加方法
const handleAdd = () => {
  resetForm();
  originalHandleAdd();
}

// 表格列定义
const columns = [
  { title: 'ID', key: 'id', width: 60, ellipsis: { tooltip: true }, align: 'center' },
  { title: '标题', key: 'title', width: 150, ellipsis: { tooltip: true } },
  {
    title: '类型',
    key: 'item_type',
    width: 100,
    align: 'center',
    render(row) {
      return h(NTag, { type: 'info' }, { default: () => row.item_type })
    }
  },
  {
    title: '标签',
    key: 'tags',
    width: 150,
    render(row) {
      if (!row.tags) return '无标签'
      return row.tags.split(',').map(tag =>
        h(NTag, { style: 'margin-right: 5px', type: 'success', size: 'small' }, { default: () => tag.trim() })
      )
    }
  },
  {
    title: '来源',
    key: 'source',
    width: 100,
    align: 'center',
    render(row) {
      return h(NTag, { type: 'warning' }, { default: () => row.source })
    }
  },
  { title: '创建时间', key: 'created_at', width: 150, render: (row) => formatDate(row.created_at) },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    align: 'center',
    fixed: 'right',
    render(row) {
      return [
        withDirectives(
          h(
            NButton,
            {
              size: 'tiny',
              quaternary: true,
              type: 'info',
              onClick: () => handleEdit(row),
            },
            { default: () => '编辑', icon: renderIcon('material-symbols:edit-outline', { size: 16 }) }
          ),
          [[vPermission, 'put/api/v1/knowledge/{id}']]
        ),
        withDirectives(
          h(
            NPopconfirm,
            {
              onPositiveClick: () => handleDelete(row.id),
            },
            {
              default: () => '确认删除？',
              trigger: () =>
                h(
                  NButton,
                  {
                    size: 'tiny',
                    quaternary: true,
                    type: 'error',
                  },
                  { default: () => '删除', icon: renderIcon('material-symbols:delete-outline', { size: 16 }) }
                ),
            }
          ),
          [[vPermission, 'delete/api/v1/knowledge/{id}']]
        ),
      ]
    },
  },
]

// 获取项目列表
async function getProjects() {
  try {
    console.log('开始获取项目列表')
    const { data } = await api.getProjts()
    console.log('获取到项目列表数据:', data)

    if (Array.isArray(data) && data.length > 0) {
      projOption.value = data.map(item => ({
        key: item.id,
        label: item.name,
        value: item.id
      }))
      console.log('处理后的项目选项:', projOption.value)

      // 如果有项目数据，默认选择第一个项目
      if (projOption.value.length > 0) {
        modalForm.value.project_id = projOption.value[0].value;
        console.log('默认选择第一个项目:', modalForm.value.project_id);
      }
    } else {
      console.warn('项目列表数据为空或格式不正确')
      message.warning('项目列表数据为空，请检查系统配置')
    }
  } catch (error) {
    console.error('获取项目列表失败:', error)
    message.error('获取项目列表失败')
  }
}

// 添加滚动到顶部的函数
const scrollToTop = () => {
  console.log('执行滚动到顶部');

  try {
    // 首先强制滚动整个页面到顶部
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  } catch (e) {
    console.error('窗口滚动异常:', e);
    // 备用方法
    window.scrollTo(0, 0);
  }

  // 滚动页面容器到顶部
  const pageContainer = document.querySelector('.page-container');
  if (pageContainer) {
    try {
      pageContainer.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    } catch (e) {
      console.error('页面容器滚动异常:', e);
      // 备用方法
      pageContainer.scrollTop = 0;
    }
  }

  // 滚动各个容器到顶部
  nextTick(() => {
    // 尝试滚动表格容器
    const tableContainer = document.querySelector('.n-data-table-base-table-body');
    if (tableContainer) {
      try {
        tableContainer.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      } catch (e) {
        console.error('表格容器滚动异常:', e);
        tableContainer.scrollTop = 0;
      }
    }

    // 尝试滚动表格内容
    const tableBody = document.querySelector('.n-scrollbar-container');
    if (tableBody) {
      try {
        tableBody.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      } catch (e) {
        console.error('表格内容滚动异常:', e);
        tableBody.scrollTop = 0;
      }
    }

    // 尝试滚动所有可能的滚动容器
    const scrollContainers = [
      '.n-data-table-wrapper',
      '.n-data-table',
      '.crud-table-container',
      '.knowledge-container',
      '.knowledge-list'
    ];

    scrollContainers.forEach(selector => {
      const container = document.querySelector(selector);
      if (container) {
        try {
          container.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        } catch (e) {
          console.error(`${selector} 滚动异常:`, e);
          container.scrollTop = 0;
        }
      }
    });

    // 显示成功消息
    window.$message?.success('已滚动到顶部');
  });
};

// 添加滚动到底部的函数
const scrollToBottom = (force = false) => {
  console.log('执行滚动到底部，force =', force);

  // 首先尝试滚动整个页面容器
  if (force) {
    try {
      // 滚动整个页面
      window.scrollTo({
        top: document.body.scrollHeight,
        behavior: 'smooth'
      });
    } catch (e) {
      console.error('窗口滚动异常:', e);
      // 备用方法
      window.scrollTo(0, document.body.scrollHeight);
    }

    // 滚动页面容器
    const pageContainer = document.querySelector('.page-container');
    if (pageContainer) {
      try {
        pageContainer.scrollTo({
          top: pageContainer.scrollHeight,
          behavior: 'smooth'
        });
      } catch (e) {
        console.error('页面容器滚动异常:', e);
        // 备用方法
        pageContainer.scrollTop = pageContainer.scrollHeight;
      }
    }
  }

  nextTick(() => {
    // 尝试滚动表格容器
    const tableContainer = document.querySelector('.n-data-table-base-table-body');
    if (tableContainer) {
      try {
        tableContainer.scrollTo({
          top: tableContainer.scrollHeight,
          behavior: 'smooth'
        });
      } catch (e) {
        console.error('表格容器滚动异常:', e);
        tableContainer.scrollTop = tableContainer.scrollHeight;
      }
    }

    // 尝试滚动表格内容
    const tableBody = document.querySelector('.n-scrollbar-container');
    if (tableBody) {
      try {
        tableBody.scrollTo({
          top: tableBody.scrollHeight,
          behavior: 'smooth'
        });
      } catch (e) {
        console.error('表格内容滚动异常:', e);
        tableBody.scrollTop = tableBody.scrollHeight;
      }
    }

    // 尝试滚动所有可能的滚动容器
    const scrollContainers = [
      '.n-data-table-wrapper',
      '.n-data-table',
      '.crud-table-container',
      '.knowledge-container',
      '.knowledge-list'
    ];

    scrollContainers.forEach(selector => {
      const container = document.querySelector(selector);
      if (container) {
        try {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth'
          });
        } catch (e) {
          console.error(`${selector} 滚动异常:`, e);
          container.scrollTop = container.scrollHeight;
        }
      }
    });

    // 显示成功消息
    window.$message?.success('已滚动到底部');
  });
};

// 初始化页面
async function initPage() {
  console.log('初始化知识库页面')
  await getProjects()
  console.log('项目列表加载完成，开始加载表格数据')
  $table.value?.handleSearch()
}

onMounted(() => {
  console.log('知识库页面已挂载')
  initPage()
})
</script>

<style scoped>
/* 知识库页面样式 */
:deep(.n-tree-select) {
  min-width: 200px;
}

:deep(.n-select) {
  min-width: 200px;
}

:deep(.n-input) {
  min-width: 200px;
}

:deep(.crud-table-query-bar) {
  display: block;
  width: 100%;
}

/* 固定的滚动按钮 - 底部 */
.scroll-to-bottom-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #2080f0;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
}

.scroll-to-bottom-btn:hover {
  bottom: 30px; /* 固定在底部 */
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

/* 固定的滚动按钮 - 顶部 */
.scroll-to-top-btn {
  position: fixed;
  bottom: 100px; /* 位于底部按钮上方 */
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #18a058; /* 使用不同的颜色区分 */
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
}

.scroll-to-top-btn:hover {
  bottom: 90px;
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}
</style>
