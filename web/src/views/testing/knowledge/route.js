import i18n from '~/i18n'
const { t } = i18n.global

export default {
  name: t('views.knowledge.label_knowledge'),
  path: '/testing/knowledge',
  component: () => import('@/layout/index.vue'),
  meta: {
    order: 4,
  },
  children: [
    {
      name: `${t('views.knowledge.label_knowledge')}Default`,
      path: '',
      component: () => import('./index.vue'),
      meta: {
        title: t('views.knowledge.label_knowledge'),
        icon: 'mdi:book-open-page-variant',
      },
    },
  ],
}
