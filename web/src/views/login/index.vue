<template>
  <AppPage :show-footer="true" bg-cover :style="{ backgroundImage: `url(${bgImg})` }">
    <div
      class="login-container m-auto max-w-1500 min-w-345 f-c-c rounded-10 bg-white bg-opacity-60 p-15 card-shadow"
      dark:bg-dark
    >
      <div hidden w-380 px-20 py-35 md:block>
        <icon-custom-front-page pt-10 text-300 color-primary></icon-custom-front-page>
      </div>

      <div w-320 flex-col px-20 py-35>
        <div class="site-title-container">
          <div class="title-background"></div>
          <div class="shimmer"></div>
          <icon-custom-logo class="site-logo" color-primary />
          <h1 class="site-title">{{ $t('app_name') }}</h1>
        </div>
        <div mt-30>
          <n-input
            v-model:value="loginInfo.username"
            autofocus
            class="h-50 items-center pl-10 text-16"
            placeholder="admin"
            :maxlength="20"
          />
        </div>
        <div mt-30>
          <n-input
            v-model:value="loginInfo.password"
            class="h-50 items-center pl-10 text-16"
            type="password"
            show-password-on="mousedown"
            placeholder="123456"
            :maxlength="20"
            @keypress.enter="handleLogin"
          />
        </div>

        <div mt-20>
          <n-button
            h-50
            w-full
            rounded-5
            text-16
            type="primary"
            :loading="loading"
            @click="handleLogin"
          >
            {{ $t('views.login.text_login') }}
          </n-button>
        </div>
      </div>
    </div>
  </AppPage>
</template>

<script setup>
import { lStorage, setToken } from '@/utils'
import bgImg from '@/assets/images/login_bg.webp'
import api from '@/api'
import { addDynamicRoutes } from '@/router'
import { useI18n } from 'vue-i18n'

const router = useRouter()
const { query } = useRoute()
const { t } = useI18n({ useScope: 'global' })

const loginInfo = ref({
  username: '',
  password: '',
})

initLoginInfo()

function initLoginInfo() {
  const localLoginInfo = lStorage.get('loginInfo')
  if (localLoginInfo) {
    loginInfo.value.username = localLoginInfo.username || ''
    loginInfo.value.password = localLoginInfo.password || ''
  }
}

const loading = ref(false)
async function handleLogin() {
  const { username, password } = loginInfo.value
  if (!username || !password) {
    $message.warning(t('views.login.message_input_username_password'))
    return
  }
  try {
    loading.value = true
    $message.loading(t('views.login.message_login_success'))
    console.log('发送登录请求:', { username, password: '******' });

    const res = await api.login({ username, password: password.toString() })
    console.log('登录响应:', res);

    if (!res || !res.data || !res.data.access_token) {
      console.error('登录响应缺少access_token:', res);
      $message.error('登录失败: 服务器响应不正确');
      loading.value = false;
      return;
    }

    $message.success(t('views.login.message_login_success'))
    setToken(res.data.access_token)
    await addDynamicRoutes()
    if (query.redirect) {
      const path = query.redirect
      console.log('path', { path, query })
      Reflect.deleteProperty(query, 'redirect')
      router.push({ path, query })
    } else {
      router.push('/')
    }
  } catch (e) {
    console.error('登录错误:', e);
    // 显示更详细的错误信息
    if (e.response) {
      console.error('响应数据:', e.response.data);
      console.error('状态码:', e.response.status);
      $message.error(`登录失败: ${e.response.status} - ${e.response.data?.detail || '服务器错误'}`);
    } else if (e.request) {
      console.error('请求已发送但没有收到响应');
      $message.error('登录失败: 服务器无响应，请检查网络连接');
    } else {
      console.error('请求配置错误:', e.message);
      $message.error(`登录失败: ${e.message}`);
    }
  }
  loading.value = false
}
</script>

<style scoped>
.login-container {
  transform: translateY(25px);
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(109, 40, 217, 0.15);
  border: 1px solid rgba(109, 40, 217, 0.1);
}

.site-title-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
  position: relative;
  padding: 15px 0;
  transform: scale(1.05);
  overflow: hidden;
}

.site-logo {
  font-size: 70px;
  margin-right: 15px;
  animation: pulse 2s infinite ease-in-out;
  filter: drop-shadow(0 0 8px rgba(109, 40, 217, 0.3));
  transition: all 0.3s ease;
}

.site-title-container:hover .site-logo {
  transform: scale(1.1) rotate(5deg);
  filter: drop-shadow(0 0 12px rgba(109, 40, 217, 0.5));
}

.site-title {
  font-size: 36px;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--info-color) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: 0 4px 12px rgba(109, 40, 217, 0.3);
  letter-spacing: 1.5px;
  position: relative;
  transform: translateY(0);
  transition: all 0.3s ease;
  white-space: nowrap;
}

.site-title-container:hover .site-title {
  transform: translateY(-2px);
  text-shadow: 0 6px 16px rgba(109, 40, 217, 0.4);
  background: linear-gradient(135deg, var(--info-color) 0%, var(--primary-color) 100%);
  -webkit-background-clip: text;
  background-clip: text;
}

.title-background {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(109, 40, 217, 0.08) 0%, transparent 70%);
  z-index: -1;
}

.site-title-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  border-radius: 3px;
  opacity: 0.6;
}

.site-title-container::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  border-radius: 3px;
  animation: expand 3s infinite ease-in-out;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes expand {
  0%, 100% {
    width: 80px;
    opacity: 0.7;
  }
  50% {
    width: 120px;
    opacity: 1;
  }
}

/* 添加闪光效果 */

.site-title-container .shimmer {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: shimmer 4s infinite;
  z-index: 1;
  pointer-events: none;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 200%;
  }
}
</style>
