<template>
  <div class="query-bar-item" flex items-center>
    <label
      v-if="!isNullOrWhitespace(label)"
      class="query-bar-item-label"
      flex-shrink-0
      :style="{ width: labelWidth + 'px' }"
    >
      {{ label }}
    </label>
    <div class="query-bar-item-content" :style="{ minWidth: contentWidth + 'px' }">
      <slot />
    </div>
  </div>
</template>

<script setup>
import { isNullOrWhitespace } from '@/utils'

defineProps({
  label: {
    type: String,
    default: '',
  },
  labelWidth: {
    type: Number,
    default: 80,
  },
  contentWidth: {
    type: Number,
    default: 220,
  },
})
</script>

<style scoped>
.query-bar-item {
  display: flex;
  align-items: center;
  margin-right: 15px;
  margin-bottom: 10px;
}

.query-bar-item-label {
  font-weight: 500;
  margin-right: 8px;
  white-space: nowrap;
}

.query-bar-item-content {
  flex: 1;
  min-width: 200px;
}
</style>
