<template>
  <div
    class="query-bar-container"
    bg="#fafafc"
    min-h-60
    flex
    items-start
    justify-between
    b-1
    rounded-8
    p-15
    bc-ccc
    dark:bg-black
  >
    <n-space wrap :size="[35, 15]" style="width: 100%;">
      <slot />
      <div>
        <n-button secondary type="primary" @click="emit('reset')">重置</n-button>
        <n-button ml-20 type="primary" @click="emit('search')">搜索</n-button>
      </div>
    </n-space>
  </div>
</template>

<script setup>
const emit = defineEmits(['search', 'reset'])
</script>

<style scoped>
.query-bar-container {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  margin-bottom: 20px;
}

.query-bar-container :deep(.n-space) {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

.query-bar-container :deep(.n-space-item) {
  margin-bottom: 10px;
  min-width: 200px;
}
</style>
