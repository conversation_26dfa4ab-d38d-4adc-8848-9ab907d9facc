<template>
  <div class="processing-steps">
    <div class="steps-container">
      <div 
        v-for="(step, index) in steps" 
        :key="index" 
        class="step-item"
        :class="{ 
          'active': currentStep >= index, 
          'completed': currentStep > index,
          'current': currentStep === index
        }"
      >
        <div class="step-icon">
          <n-icon v-if="currentStep > index">
            <TheIcon icon="material-symbols:check-circle" />
          </n-icon>
          <n-icon v-else-if="currentStep === index && isProcessing">
            <n-spin size="small" />
          </n-icon>
          <span v-else>{{ index + 1 }}</span>
        </div>
        <div class="step-content">
          <div class="step-title">{{ step.title }}</div>
          <div class="step-description">{{ step.description }}</div>
          <div v-if="currentStep === index && step.detail" class="step-detail">
            {{ step.detail }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { NIcon, NSpin } from 'naive-ui'
import TheIcon from '@/components/icon/TheIcon.vue'

const props = defineProps({
  currentStep: {
    type: Number,
    default: 0
  },
  isProcessing: {
    type: Boolean,
    default: false
  },
  steps: {
    type: Array,
    default: () => [
      {
        title: '准备分析',
        description: '正在准备需求文档和分析环境',
        detail: '正在初始化分析环境并处理输入数据...'
      },
      {
        title: '文档解析',
        description: '解析需求文档内容',
        detail: '正在提取文档中的关键信息...'
      },
      {
        title: 'AI分析中',
        description: 'AI模型正在分析需求',
        detail: 'AI正在深入分析需求内容，识别关键点...'
      },
      {
        title: '生成结果',
        description: '整理分析结果',
        detail: '正在整理分析结果并生成报告...'
      },
      {
        title: '完成',
        description: '分析完成',
        detail: '需求分析已完成，可以查看结果'
      }
    ]
  },
  customDetail: {
    type: String,
    default: ''
  }
})

// 当有自定义详情时，更新当前步骤的详情
watch(() => props.customDetail, (newDetail) => {
  if (newDetail && props.currentStep >= 0 && props.currentStep < props.steps.length) {
    props.steps[props.currentStep].detail = newDetail
  }
})
</script>

<style scoped>
.processing-steps {
  margin: 20px 0;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #eaeaea;
}

.steps-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  padding: 10px;
  border-radius: 6px;
  transition: all 0.3s ease;
  opacity: 0.6;
  border-left: 3px solid #e0e0e0;
}

.step-item.active {
  opacity: 1;
  border-left-color: #18a058;
}

.step-item.current {
  background-color: #f0f9f4;
  box-shadow: 0 2px 8px rgba(24, 160, 88, 0.1);
}

.step-item.completed {
  border-left-color: #18a058;
}

.step-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: #666;
  margin-right: 12px;
  flex-shrink: 0;
  font-size: 14px;
  font-weight: 500;
}

.step-item.active .step-icon {
  background-color: #18a058;
  color: white;
}

.step-content {
  flex: 1;
}

.step-title {
  font-weight: 500;
  font-size: 15px;
  color: #333;
  margin-bottom: 4px;
}

.step-description {
  font-size: 13px;
  color: #666;
}

.step-detail {
  margin-top: 8px;
  padding: 8px;
  background-color: #e6f7ef;
  border-radius: 4px;
  font-size: 13px;
  color: #18a058;
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式适配 */
@media (max-width: 768px) {
  .processing-steps {
    padding: 10px;
  }
  
  .step-icon {
    width: 24px;
    height: 24px;
    font-size: 12px;
  }
  
  .step-title {
    font-size: 14px;
  }
  
  .step-description, .step-detail {
    font-size: 12px;
  }
}
</style>
