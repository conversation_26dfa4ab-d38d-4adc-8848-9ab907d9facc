<template>
  <div class="agent-execution-output">
    <div class="output-header">
      <div class="header-title">
        <TheIcon icon="mdi:console" :size="18" />
        <span>智能体执行输出</span>
      </div>
      <div class="header-actions">
        <n-button text @click="clearOutput" v-if="messages.length > 0">
          <TheIcon icon="mdi:delete-outline" :size="16" />
          清空
        </n-button>
        <n-button text @click="toggleCollapse">
          <TheIcon :icon="collapsed ? 'mdi:chevron-down' : 'mdi:chevron-up'" :size="16" />
          {{ collapsed ? '展开' : '收起' }}
        </n-button>
      </div>
    </div>
    
    <n-collapse-transition :show="!collapsed">
      <div class="output-content" id="agent-output">
        <div v-if="messages.length === 0" class="empty-output">
          <TheIcon icon="mdi:robot-outline" :size="32" />
          <p>智能体执行过程中的输出将显示在这里</p>
        </div>
        <div v-else class="output-messages">
          <div 
            v-for="(msg, index) in messages" 
            :key="index" 
            class="output-message"
            :class="{ 'system-message': msg.type === 'system' }"
          >
            <div class="message-source">
              <TheIcon :icon="getSourceIcon(msg.source)" :size="14" />
              <span>{{ getSourceName(msg.source) }}</span>
              <span class="message-time">{{ formatTime(msg.timestamp) }}</span>
            </div>
            <div class="message-content" v-html="msg.content"></div>
          </div>
        </div>
      </div>
    </n-collapse-transition>
  </div>
</template>

<script setup>
import { ref, watch, nextTick, onMounted, onBeforeUnmount } from 'vue'
import { NButton, NCollapseTransition } from 'naive-ui'
import TheIcon from '@/components/icon/TheIcon.vue'
import { marked } from 'marked'
import DOMPurify from 'dompurify'

// 配置Markdown解析器
marked.setOptions({
  breaks: true,
  gfm: true
})

// 创建Markdown解析函数
const parseMarkdown = (raw) => {
  return DOMPurify.sanitize(marked.parse(raw || ''));
}

const props = defineProps({
  messages: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['clear'])

const collapsed = ref(false)

const toggleCollapse = () => {
  collapsed.value = !collapsed.value
}

const clearOutput = () => {
  emit('clear')
}

const getSourceIcon = (source) => {
  const icons = {
    'system': 'mdi:information-outline',
    'agent': 'mdi:robot',
    'user': 'mdi:account',
    'error': 'mdi:alert-circle-outline',
    'default': 'mdi:message-outline'
  }
  return icons[source] || icons.default
}

const getSourceName = (source) => {
  const names = {
    'system': '系统',
    'agent': '智能体',
    'user': '用户',
    'error': '错误',
    'default': '消息'
  }
  return names[source] || source || names.default
}

const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
}

// 自动滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    const outputEl = document.getElementById('agent-output')
    if (outputEl) {
      outputEl.scrollTop = outputEl.scrollHeight
    }
  })
}

// 监听消息变化自动滚动
watch(
  () => props.messages.length,
  () => scrollToBottom(),
  { immediate: true }
)

// 当容器尺寸变化时也触发滚动
let resizeObserver
onMounted(() => {
  const outputEl = document.getElementById('agent-output')
  if (outputEl && window.ResizeObserver) {
    resizeObserver = new ResizeObserver(scrollToBottom)
    resizeObserver.observe(outputEl)
  }
})

onBeforeUnmount(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
})
</script>

<style scoped>
.agent-execution-output {
  margin-top: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;
  overflow: hidden;
}

.output-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f0f0f0;
  border-bottom: 1px solid #e0e0e0;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.output-content {
  max-height: 300px;
  overflow-y: auto;
  padding: 16px;
}

.empty-output {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #999;
  gap: 12px;
}

.output-messages {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.output-message {
  padding: 10px;
  border-radius: 6px;
  background-color: #f5f5f5;
  border-left: 3px solid #ccc;
}

.output-message.system-message {
  background-color: #f0f7ff;
  border-left-color: #1890ff;
}

.message-source {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 6px;
  font-size: 12px;
  color: #666;
}

.message-time {
  margin-left: auto;
  color: #999;
  font-size: 11px;
}

.message-content {
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 滚动条样式 */
.output-content::-webkit-scrollbar {
  width: 6px;
}

.output-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.output-content::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 4px;
}

.output-content::-webkit-scrollbar-thumb:hover {
  background: #ccc;
}
</style>
