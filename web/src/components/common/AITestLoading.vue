<template>
  <transition name="fade">
    <div v-if="show" class="ai-test-loading-container">
      <div class="ai-test-loading-content">
        <svg class="ai-test-loading-icon" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
          <!-- 大脑图标（代表AI） -->
          <path class="brain" d="M50 15 C60 15, 70 25, 70 35 C70 45, 60 55, 50 55 C40 55, 30 45, 30 35 C30 25, 40 15, 50 15" fill="none" stroke="var(--primary-color)" stroke-width="2" />

          <!-- 神经网络连接线 -->
          <g class="neural-network">
            <line x1="35" y1="40" x2="25" y2="60" stroke="var(--primary-color)" stroke-width="1.5" />
            <line x1="50" y1="55" x2="50" y2="70" stroke="var(--primary-color)" stroke-width="1.5" />
            <line x1="65" y1="40" x2="75" y2="60" stroke="var(--primary-color)" stroke-width="1.5" />

            <!-- 神经节点 -->
            <circle class="node node1" cx="25" cy="60" r="4" fill="var(--primary-color)" />
            <circle class="node node2" cx="50" cy="70" r="4" fill="var(--primary-color)" />
            <circle class="node node3" cx="75" cy="60" r="4" fill="var(--primary-color)" />
          </g>

          <!-- 测试元素 -->
          <g class="test-elements">
            <rect class="test-case" x="15" y="75" width="20" height="10" rx="2" fill="none" stroke="var(--primary-color)" stroke-width="1.5" />
            <rect class="test-case" x="40" y="75" width="20" height="10" rx="2" fill="none" stroke="var(--primary-color)" stroke-width="1.5" />
            <rect class="test-case" x="65" y="75" width="20" height="10" rx="2" fill="none" stroke="var(--primary-color)" stroke-width="1.5" />

            <!-- 对勾标记（代表测试通过） -->
            <path class="checkmark" d="M20 80 L25 85 L35 75" fill="none" stroke="var(--success-color)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />

            <!-- 测试中标记（代表测试进行中） -->
            <circle class="testing" cx="50" cy="80" r="3" fill="var(--warning-color)" />

            <!-- 错误标记（代表测试失败） -->
            <path class="error" d="M70 75 L80 85 M80 75 L70 85" fill="none" stroke="var(--error-color)" stroke-width="2" stroke-linecap="round" />
          </g>

          <!-- 数据流动效果 -->
          <g class="data-flow">
            <circle class="data-point dp1" cx="0" cy="0" r="1.5" fill="var(--primary-color)" />
            <circle class="data-point dp2" cx="0" cy="0" r="1.5" fill="var(--primary-color)" />
            <circle class="data-point dp3" cx="0" cy="0" r="1.5" fill="var(--primary-color)" />
          </g>
        </svg>
        <div class="ai-test-loading-text">AItest系统加载中...</div>
      </div>
    </div>
  </transition>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'

defineOptions({ name: 'AITestLoading' })

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})
</script>

<style scoped>
.ai-test-loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.85);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(3px);
  pointer-events: none; /* 允许点击穿透，避免阻止用户交互 */
  transition: background-color 0.3s ease;
}

/* 暗黑模式下的背景颜色 */
:root.dark .ai-test-loading-container {
  background-color: rgba(18, 18, 18, 0.85);
}

.ai-test-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.ai-test-loading-icon {
  width: 120px;
  height: 120px;
}

.ai-test-loading-text {
  margin-top: 16px;
  font-size: 16px;
  color: var(--primary-color);
  font-weight: 500;
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 脑部动画 */
.brain {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    stroke-width: 2;
    opacity: 0.8;
  }
  50% {
    stroke-width: 3;
    opacity: 1;
  }
  100% {
    stroke-width: 2;
    opacity: 0.8;
  }
}

/* 神经节点动画 */
.node {
  animation: nodeGlow 2s infinite;
}

.node1 {
  animation-delay: 0s;
}

.node2 {
  animation-delay: 0.3s;
}

.node3 {
  animation-delay: 0.6s;
}

@keyframes nodeGlow {
  0% {
    r: 4;
    opacity: 0.7;
  }
  50% {
    r: 5;
    opacity: 1;
  }
  100% {
    r: 4;
    opacity: 0.7;
  }
}

/* 测试元素动画 */
.test-case {
  animation: testCasePulse 3s infinite;
}

@keyframes testCasePulse {
  0% {
    stroke-width: 1.5;
  }
  50% {
    stroke-width: 2;
  }
  100% {
    stroke-width: 1.5;
  }
}

.checkmark {
  animation: drawCheckmark 3s infinite;
  stroke-dasharray: 30;
  stroke-dashoffset: 30;
}

@keyframes drawCheckmark {
  0% {
    stroke-dashoffset: 30;
  }
  50% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

.testing {
  animation: testingPulse 1s infinite;
}

@keyframes testingPulse {
  0% {
    r: 3;
    opacity: 0.7;
  }
  50% {
    r: 4;
    opacity: 1;
  }
  100% {
    r: 3;
    opacity: 0.7;
  }
}

.error {
  animation: errorFlash 2s infinite;
}

@keyframes errorFlash {
  0% {
    stroke-width: 2;
    opacity: 0.7;
  }
  50% {
    stroke-width: 2.5;
    opacity: 1;
  }
  100% {
    stroke-width: 2;
    opacity: 0.7;
  }
}

/* 数据流动动画 */
.data-point {
  animation: moveAlongPath 4s infinite linear;
}

.dp1 {
  animation-delay: 0s;
}

.dp2 {
  animation-delay: 1.3s;
}

.dp3 {
  animation-delay: 2.6s;
}

@keyframes moveAlongPath {
  0% {
    transform: translate(50px, 35px);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  30% {
    transform: translate(35px, 40px);
  }
  50% {
    transform: translate(25px, 60px);
  }
  70% {
    transform: translate(25px, 75px);
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translate(25px, 85px);
    opacity: 0;
  }
}
</style>
