<template>
  <router-view v-slot="{ Component, route }">
    <transition name="fade-slide" mode="out-in" appear>
      <component :is="Component" v-if="Component" :key="route.path" />
    </transition>
  </router-view>
</template>

<script setup>
console.log('AppMain组件已加载')
</script>

<style lang="scss">
/* 页面过渡动画 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}
.n-card {
  box-shadow: 0 4px 20px rgba(148, 163, 184, 0.1);
  transition: all 0.3s ease;
  background: #FFFFFF !important;
  border: 1px solid rgba(109, 40, 217, 0.08);
  border-radius: 12px;

  &:hover {
    box-shadow: 0 8px 30px rgba(148, 163, 184, 0.15);
    border-color: rgba(109, 40, 217, 0.2);
  }
}

.n-card > .n-card__content {
  background: #F8FAFC !important;
  border-radius: 0 0 12px 12px;
  padding: 20px !important;
}

.n-card-header {
  background: #FFFFFF !important;
  border-bottom: 1px solid rgba(109, 40, 217, 0.08);
  color: #1E293B !important;
  font-weight: 600;
  border-radius: 12px 12px 0 0;
  padding: 20px !important;
}

.n-card__footer {
  background: #F8FAFC !important;
  border-top: 1px solid rgba(109, 40, 217, 0.08);
  padding: 16px 20px !important;
}

.dark .dark\:bg-black, .dark [dark\:bg-black=""] {
  background-color: #F8FAFC;
}
</style>