import { request } from '@/utils'

// 获取知识库条目列表
export function getKnowledgeItems(params = {}) {
  return request.get('/knowledge/', { params })
}

// 获取单个知识库条目详情
export function getKnowledgeItem(id) {
  return request.get(`/knowledge/${id}`)
}

// 创建知识库条目
export function createKnowledgeItem(data = {}) {
  console.log('API调用 - 创建知识条目:', data);

  try {
    // 确保时间戳字段不存在，让后端处理
    const cleanData = { ...data };
    delete cleanData.created_at;
    delete cleanData.updated_at;

    // 确保枚举类型字段是字符串
    if (cleanData.item_type && typeof cleanData.item_type === 'object' && cleanData.item_type.value) {
      cleanData.item_type = cleanData.item_type.value;
    }

    if (cleanData.source && typeof cleanData.source === 'object' && cleanData.source.value) {
      cleanData.source = cleanData.source.value;
    }

    console.log('清理后的数据:', cleanData);

    return request.post('/knowledge/', cleanData)
      .then(response => {
        console.log('API响应 - 创建知识条目成功:', response);
        return response;
      })
      .catch(error => {
        console.error('API错误 - 创建知识条目失败:', error);
        throw error;
      });
  } catch (error) {
    console.error('API调用前处理数据失败:', error);
    throw error;
  }
}

// 更新知识库条目
export function updateKnowledgeItem(id, data = {}) {
  console.log('API调用 - 更新知识条目:', id, data);

  try {
    // 确保时间戳字段不存在，让后端处理
    const cleanData = { ...data };
    delete cleanData.created_at;
    delete cleanData.updated_at;

    // 确保枚举类型字段是字符串
    if (cleanData.item_type && typeof cleanData.item_type === 'object' && cleanData.item_type.value) {
      cleanData.item_type = cleanData.item_type.value;
    }

    if (cleanData.source && typeof cleanData.source === 'object' && cleanData.source.value) {
      cleanData.source = cleanData.source.value;
    }

    console.log('清理后的数据:', cleanData);

    // 添加时间戳参数，避免缓存
    const timestamp = new Date().getTime();

    return request.put(`/knowledge/${id}?_t=${timestamp}`, cleanData)
      .then(response => {
        console.log('API响应 - 更新知识条目成功:', response);

        // 显示成功消息
        try {
          // 尝试使用全局消息组件
          if (window.$message) {
            window.$message.success('知识条目更新成功');
          } else if (window.parent.$message) {
            window.parent.$message.success('知识条目更新成功');
          }
        } catch (e) {
          console.log('无法显示成功消息:', e);
        }

        return response;
      })
      .catch(error => {
        console.error('API错误 - 更新知识条目失败:', error);

        // 显示错误消息
        try {
          // 尝试使用全局消息组件
          if (window.$message) {
            window.$message.error(`知识条目更新失败: ${error.message || '未知错误'}`);
          } else if (window.parent.$message) {
            window.parent.$message.error(`知识条目更新失败: ${error.message || '未知错误'}`);
          }
        } catch (e) {
          console.log('无法显示错误消息:', e);
        }

        throw error;
      });
  } catch (error) {
    console.error('API调用前处理数据失败:', error);

    // 显示错误消息
    try {
      // 尝试使用全局消息组件
      if (window.$message) {
        window.$message.error(`知识条目数据处理失败: ${error.message || '未知错误'}`);
      } else if (window.parent.$message) {
        window.parent.$message.error(`知识条目数据处理失败: ${error.message || '未知错误'}`);
      }
    } catch (e) {
      console.log('无法显示错误消息:', e);
    }

    throw error;
  }
}

// 删除知识库条目
export function deleteKnowledgeItem(id) {
  console.log('API调用 - 删除知识条目:', id);
  return request.delete(`/knowledge/${id}`)
    .then(response => {
      console.log('API响应 - 删除知识条目成功:', response);
      return response;
    })
    .catch(error => {
      console.error('API错误 - 删除知识条目失败:', error);
      throw error;
    });
}

export default {
  getKnowledgeItems,
  getKnowledgeItem,
  createKnowledgeItem,
  updateKnowledgeItem,
  deleteKnowledgeItem
}
