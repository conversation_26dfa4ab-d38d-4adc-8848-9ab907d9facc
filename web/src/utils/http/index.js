import axios from 'axios'
import { resReject, resResolve, reqReject, reqResolve } from './interceptors'

export function createAxios(options = {}) {
  const defaultOptions = {
    timeout: 12000,
  }
  const service = axios.create({
    ...defaultOptions,
    ...options,
  })
  service.interceptors.request.use(reqResolve, reqReject)
  service.interceptors.response.use(resResolve, resReject)
  return service
}

// 打印环境变量，便于调试
console.log('环境变量:', {
  VITE_BASE_API: import.meta.env.VITE_BASE_API,
  VITE_API_PREFIX: import.meta.env.VITE_API_PREFIX
});

// 创建一个检测 URL 是否已包含 /api/v1 的函数
const getBaseUrl = () => {
  // 使用当前窗口的location.origin作为基础URL，确保使用当前访问的服务器地址
  // 如果有环境变量设置，优先使用环境变量
  const baseApi = import.meta.env.VITE_BASE_API || window.location.origin;
  console.log('HTTP模块使用API基础URL:', baseApi);

  // 如果 baseApi 已经包含 /api/v1，则直接返回
  if (baseApi.includes('/api/v1')) {
    return baseApi;
  }
  // 否则，添加 /api/v1 前缀
  return `${baseApi}${import.meta.env.VITE_API_PREFIX || '/api/v1'}`;
};

// 获取最终的 baseURL
const baseURL = getBaseUrl();

console.log('最终baseURL:', baseURL);

export const request = createAxios({
  baseURL: baseURL,
})
