import axios from 'axios'
import { getToken } from '@/utils'

// 创建 axios 实例
const request = axios.create({
  // 不要在这里加 /api/v1 前缀，让 Nginx 处理
  baseURL: '',
  timeout: 12000,
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 检查 URL 是否已经包含 /api/v1 前缀
    if (!config.url.startsWith('/api/v1')) {
      // 在这里添加 /api/v1 前缀
      config.url = `/api/v1${config.url}`
    }

    // 打印请求 URL，便于调试
    console.log('请求 URL:', config.url);

    // token 处理
    const token = getToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error);
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    // 如果响应成功，直接返回数据
    const data = response.data;

    // 特殊处理：如果返回的是 {code: 200, message: 'OK', error: {...}} 格式
    // 这种情况下，虽然有error字段，但code是200，表示操作实际上是成功的
    if (data && data.code === 200 && data.message === 'OK') {
      console.log('响应成功，但包含error字段:', data);
      // 如果error字段包含有效数据，直接返回error字段内容
      if (data.error && typeof data.error === 'object' && Object.keys(data.error).length > 0) {
        console.log('返回error字段中的数据:', data.error);
        return data.error;
      }
      // 否则返回整个数据对象，让业务代码决定如何处理
      return data;
    }

    return data;
  },
  (error) => {
    console.error('响应拦截器错误:', error);

    // 如果有响应数据，返回错误信息
    if (error.response && error.response.data) {
      // 特殊处理：如果返回的是 {code: 200, message: 'OK', error: {...}} 格式
      const data = error.response.data;
      if (data && data.code === 200 && data.message === 'OK') {
        console.log('响应成功，但包含error字段:', data);
        // 如果error字段包含有效数据，直接返回error字段内容
        if (data.error && typeof data.error === 'object' && Object.keys(data.error).length > 0) {
          console.log('返回error字段中的数据:', data.error);
          return data.error;
        }
        // 返回数据而不是拒绝Promise
        return data;
      }

      return Promise.reject(error.response.data);
    }

    // 否则返回一个通用错误
    return Promise.reject({ message: error.message || '请求失败' });
  }
)

export { request }