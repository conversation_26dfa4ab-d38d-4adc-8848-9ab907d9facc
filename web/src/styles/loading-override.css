/* 隐藏原始的Naive UI loading进度条 */
.n-loading-bar {
  display: none !important;
}

/* 确保我们的自定义加载组件正常显示 */
.ai-test-loading-container,
.ai-loading-container {
  z-index: 9999 !important;
}

/* 确保初始加载动画和路由加载动画样式一致 */
.ai-loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 暗黑模式下的背景颜色 */
:root.dark .ai-loading-container {
  background-color: rgba(18, 18, 18, 0.95);
}
