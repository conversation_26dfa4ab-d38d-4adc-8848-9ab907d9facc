import { aiTestLoading } from '@/utils/loading'

export function createPageLoadingGuard(router) {
  router.beforeEach(() => {
    // 只使用自定义的AI测试加载组件，不再使用原始的loadingBar
    aiTestLoading.start()
  })

  router.afterEach(() => {
    setTimeout(() => {
      // 只使用自定义的AI测试加载组件，不再使用原始的loadingBar
      aiTestLoading.finish()
    }, 500) // 增加一点时间，使动画效果更明显
  })

  router.onError(() => {
    // 只使用自定义的AI测试加载组件，不再使用原始的loadingBar
    aiTestLoading.error()
  })
}
