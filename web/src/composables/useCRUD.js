import { isNullOrWhitespace } from '@/utils'

// 全局消息提示
const $message = window.$message

const ACTIONS = {
  view: '查看',
  edit: '编辑',
  add: '新增',
}

export default function ({ name, initForm = {}, doCreate, doDelete, doUpdate, refresh }) {
  const modalVisible = ref(false)
  const modalAction = ref('')
  const modalTitle = computed(() => ACTIONS[modalAction.value] + name)
  const modalLoading = ref(false)
  const modalFormRef = ref(null)
  const modalForm = ref({ ...initForm })

  /** 新增 */
  function handleAdd() {
    modalAction.value = 'add'
    modalVisible.value = true
    modalForm.value = { ...initForm }
  }

  /** 修改 */
  function handleEdit(row) {
    modalAction.value = 'edit'
    modalVisible.value = true
    modalForm.value = { ...row }
  }

  /** 查看 */
  function handleView(row) {
    modalAction.value = 'view'
    modalVisible.value = true
    modalForm.value = { ...row }
  }

  /** 保存 */
  function handleSave(...callbacks) {
    if (!['edit', 'add'].includes(modalAction.value)) {
      modalVisible.value = false
      return
    }
    modalFormRef.value?.validate(async (err) => {
      if (err) return
      const actions = {
        add: {
          api: () => doCreate(modalForm.value),
          cb: () => {
            callbacks.forEach((callback) => callback && callback())
          },
          msg: () => $message.success('新增成功'),
        },
        edit: {
          api: () => doUpdate(modalForm.value),
          cb: () => {
            callbacks.forEach((callback) => callback && callback())
          },
          msg: () => $message.success('编辑成功'),
        },
      }
      const action = actions[modalAction.value]

      try {
        modalLoading.value = true
        console.log('调用API:', modalAction.value, modalForm.value)

        // 清理表单数据，移除可能导致问题的字段
        const cleanForm = { ...modalForm.value }
        delete cleanForm.created_at
        delete cleanForm.updated_at

        console.log('清理后的表单数据:', cleanForm)

        // 使用清理后的表单数据调用API
        const data = await action.api(cleanForm)
        console.log('API返回结果:', data)

        // 特殊处理：如果返回的是 {code: 200, message: 'OK', error: {...}} 格式
        // 这种情况下，虽然有error字段，但code是200，表示操作实际上是成功的
        if (data && data.code === 200 && data.message === 'OK') {
          console.log('操作成功，忽略error字段:', data)
          action.cb()
          action.msg()
          modalLoading.value = modalVisible.value = false
          data && refresh(data)
          return
        }

        // 检查返回的数据是否包含错误信息
        if (data && data.error) {
          console.error('API返回错误:', data.error)
          throw new Error(data.error.detail || data.error.message || '操作失败')
        }

        action.cb()
        action.msg()
        modalLoading.value = modalVisible.value = false
        data && refresh(data)
      } catch (error) {
        console.error('API调用失败:', error)
        modalLoading.value = false

        // 提取错误信息
        let errorMessage = '操作失败'
        if (error.message) {
          errorMessage = error.message
        } else if (error.error && error.error.detail) {
          errorMessage = error.error.detail
        } else if (error.detail) {
          errorMessage = error.detail
        }

        // 特殊处理：如果错误对象是 {code: 200, message: 'OK', error: {...}} 格式
        // 这种情况下，虽然有error字段，但code是200，表示操作实际上是成功的
        if (error.code === 200 && error.message === 'OK') {
          console.log('操作成功，忽略error字段:', error)
          action.cb()
          action.msg()
          modalLoading.value = modalVisible.value = false
          error && refresh(error)
          return
        }

        $message.error(errorMessage)
      }
    })
  }

  /** 删除 */
  async function handleDelete(params = {}) {
    if (isNullOrWhitespace(params)) return
    try {
      modalLoading.value = true
      console.log("删除请求参数：", params)
      const data = await doDelete(params)
      $message.success('删除成功')
      modalLoading.value = false

      // 确保刷新表格数据
      console.log("删除成功，刷新表格数据")
      if (typeof refresh === 'function') {
        // 添加延迟确保后端处理完成
        setTimeout(() => {
          refresh(data)
        }, 100)
      }
    } catch (error) {
      console.error("删除失败：", error)
      modalLoading.value = false

      // 显示错误消息
      let errorMessage = '删除失败'
      if (error.response && error.response.data) {
        errorMessage = error.response.data.detail || error.response.data.msg || '删除失败'
      } else if (error.message) {
        errorMessage = error.message
      }

      $message.error(errorMessage)
    }
  }

  return {
    modalVisible,
    modalAction,
    modalTitle,
    modalLoading,
    handleAdd,
    handleDelete,
    handleEdit,
    handleView,
    handleSave,
    modalForm,
    modalFormRef,
  }
}
