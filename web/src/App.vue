<template>
  <AppProvider>
      <router-view v-slot="{ Component }">
          <component :is="Component" />
      </router-view>
      <!-- AI测试加载组件 -->
      <AITestLoading :show="aiTestLoading.isLoading.value" />
  </AppProvider>
</template>

<script setup>
import AppProvider from '@/components/common/AppProvider.vue'
import AITestLoading from '@/components/common/AITestLoading.vue'
import { aiTestLoading } from '@/utils/loading'
</script>

