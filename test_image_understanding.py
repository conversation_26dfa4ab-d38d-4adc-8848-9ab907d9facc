"""
测试图片理解功能
"""
import os
import sys
import logging

# 配置日志输出到控制台
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    handlers=[logging.StreamHandler()])

# 导入图片解析功能
from app.api.v1.reqAgent.image_markdown_converter import html_to_markdown_with_image_analysis

# 设置环境变量，启用图片分析
os.environ["ENABLE_IMAGE_ANALYSIS"] = "true"

# 设置 OpenAI API 密钥 - 请替换为您自己的API密钥
# os.environ["LLM_API_KEY"] = "sk-your-openai-api-key-here"

# 测试不同类型的图片
test_images = [
    # 1. 自然风景图
    {
        "name": "自然风景图",
        "html": '<img src="https://images.unsplash.com/photo-1506744038136-46273834b3fb" alt="山水风景">'
    },
    # 2. 人物图片
    {
        "name": "人物图片",
        "html": '<img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80" alt="女性肖像">'
    },
    # 3. 图表/数据图
    {
        "name": "图表/数据图",
        "html": '<img src="https://www.researchgate.net/profile/Serkan-Kiranyaz/publication/329277449/figure/fig1/AS:699592479506433@1543807253596/Simple-decision-tree-for-the-classification-of-fruits.png" alt="水果分类决策树">'
    },
    # 4. 技术图片
    {
        "name": "技术图片",
        "html": '<img src="https://miro.medium.com/max/1400/1*XbuW8WuRrAY5pC4t-9DZAQ.jpeg" alt="计算机架构图">'
    }
]

def run_test():
    """运行图片理解测试"""
    print("\n" + "="*50)
    print("图片理解功能测试")
    print("="*50)
    
    for i, test in enumerate(test_images, 1):
        print(f"\n\n测试 {i}: {test['name']}")
        print("-"*50)
        
        try:
            # 转换HTML并分析图片
            result = html_to_markdown_with_image_analysis(test["html"])
            
            # 打印结果
            print(f"HTML输入: {test['html']}")
            print(f"\nMarkdown输出:")
            print(result)
            
        except Exception as e:
            print(f"测试 {i} 失败: {str(e)}")
    
    print("\n" + "="*50)
    print("测试完成")
    print("="*50)

if __name__ == "__main__":
    run_test()
