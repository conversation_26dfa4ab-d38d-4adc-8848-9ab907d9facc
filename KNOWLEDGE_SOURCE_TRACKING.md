# 知识点源链接追踪功能

## 功能概述

本功能为知识点管理系统添加了源链接追踪能力，解决了当TAPD需求作废时无法有效清理相关知识点的问题。

## 主要改进

### 1. 数据库模型增强

在 `knowledge_items` 表中添加了两个新字段：
- `source_url`: 存储源TAPD需求链接 (VARCHAR(500))
- `source_reference_id`: 存储源需求引用ID (VARCHAR(100))

### 2. 知识提取流程改进

- 需求分析和用例生成过程中，自动传递TAPD链接信息
- 知识点保存时自动关联源链接信息
- 支持追溯知识点的来源需求

### 3. 清理功能

提供了完整的知识点清理API：
- 根据TAPD链接批量删除知识点
- 根据源引用ID批量删除知识点
- 查询特定源链接关联的知识点

## 使用方法

### 1. 数据库迁移

首先运行数据库迁移脚本：

```bash
python run_knowledge_migration.py
```

### 2. API接口使用

#### 查询特定源链接的知识点

```bash
GET /api/v1/knowledge/by-source?source_url=https://tapd.cn/xxx
```

#### 清理特定源链接的知识点

```bash
POST /api/v1/knowledge/cleanup
Content-Type: application/json

{
    "source_url": "https://tapd.cn/xxx"
}
```

#### 根据引用ID清理知识点

```bash
POST /api/v1/knowledge/cleanup
Content-Type: application/json

{
    "source_reference_id": "https://tapd.cn/xxx"
}
```

### 3. 自动化流程

现在当进行需求分析或用例生成时：

1. 系统自动提取TAPD链接信息
2. 在知识点提取过程中传递源链接
3. 保存知识点时自动关联源信息
4. 支持后续根据源链接进行批量清理

## 权限控制

- 知识点清理操作需要管理员权限
- 普通用户只能查询知识点，不能执行清理操作

## 注意事项

1. 迁移脚本是幂等的，可以安全地重复执行
2. 清理操作不可逆，请谨慎使用
3. 建议在清理前先使用查询接口确认要删除的知识点
4. 现有的知识点不会自动填充源链接信息，只有新生成的知识点才会包含源链接

## 技术实现

### 文件修改清单

1. **数据库模型**
   - `app/models/knowledge.py`: 添加源链接字段
   - `app/schemas/knowledge.py`: 更新Schema模型

2. **知识提取流程**
   - `app/api/v1/agent/common_messages.py`: 扩展消息模型
   - `app/api/v1/agent/knowledge_agents.py`: 修改知识保存逻辑
   - `app/api/v1/agent/requirement_agents.py`: 传递TAPD链接
   - `app/api/v1/agent/testcase_agents.py`: 传递TAPD链接

3. **控制器和API**
   - `app/controllers/knowledge.py`: 添加源链接查询和删除方法
   - `app/api/v1/knowledge/knowledge.py`: 添加清理和查询API

4. **数据库迁移**
   - `migrations/models/11_20250115000000_add_knowledge_source_fields.py`: 迁移脚本
   - `run_knowledge_migration.py`: 迁移执行工具

## 测试建议

1. 运行迁移脚本，确认字段添加成功
2. 进行一次需求分析，检查生成的知识点是否包含源链接
3. 进行一次用例生成，检查生成的知识点是否包含源链接
4. 使用查询API确认知识点源链接信息
5. 使用清理API测试批量删除功能

## 后续扩展

1. 可以考虑添加知识点来源统计功能
2. 可以添加定期清理无效源链接的定时任务
3. 可以扩展支持其他类型的源链接（如Jira、GitHub等）
