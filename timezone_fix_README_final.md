# PostgreSQL时区问题最终解决方案

本文档说明了如何彻底解决PostgreSQL数据库中的时区问题，特别是"can't subtract offset-naive and offset-aware datetimes"错误。

## 问题描述

在使用PostgreSQL数据库时，如果混合使用了带时区信息(offset-aware)和不带时区信息(offset-naive)的datetime对象，会出现以下错误：

```
需求入库过程出错: invalid input for query argument $1: datetime.datetime(2025, 4, 26, 17, 5, 37... (can't subtract offset-naive and offset-aware datetimes)
```

## 根本原因

经过深入分析，我们发现问题的根本原因是：

1. PostgreSQL数据库中的timestamp字段类型是`timestamp without time zone`
2. Python代码中使用的是带时区的datetime对象
3. 当ORM尝试将带时区的datetime对象保存到不带时区的字段时，出现了类型不匹配错误

## 解决方案

我们采用了以下综合方案来彻底解决这个问题：

### 1. 修改数据库表结构

将timestamp without time zone类型的字段修改为timestamp with time zone类型：

```sql
ALTER TABLE requirement 
ALTER COLUMN created_at TYPE timestamp with time zone 
USING created_at AT TIME ZONE 'UTC';

ALTER TABLE requirement 
ALTER COLUMN updated_at TYPE timestamp with time zone 
USING updated_at AT TIME ZONE 'UTC';
```

### 2. 绕过ORM直接使用SQL

为了彻底解决时区问题，我们修改了RequirementDatabaseAgent类，使用原始SQL语句直接插入数据：

```python
# 构建SQL插入语句
fields = []
values = []
params = []

for field_name, field_value in minimal_dict.items():
    if field_value is not None:
        fields.append(field_name)
        values.append(f"${len(params) + 1}")
        params.append(field_value)

# 添加created_at和updated_at字段
fields.extend(["created_at", "updated_at"])
values.extend(["NOW()", "NOW()"])

# 构建完整的SQL语句
sql = f"INSERT INTO requirement ({', '.join(fields)}) VALUES ({', '.join(values)}) RETURNING id"

# 执行SQL语句
result = await conn.execute_query(sql, params)
```

### 3. 设置数据库会话时区

在每次连接数据库时，设置会话时区为UTC：

```sql
SET timezone TO 'UTC';
```

### 4. 设置数据库默认时区

修改数据库默认时区设置为UTC：

```sql
ALTER DATABASE your_database_name SET timezone TO 'UTC';
```

## 测试结果

我们已经成功测试了这个解决方案，并确认它能够解决时区问题：

1. 运行`fix_timezone_direct.py`脚本修改了表结构，将timestamp字段类型从`timestamp without time zone`修改为`timestamp with time zone`
2. 运行`direct_insert_requirement.py`脚本测试了直接SQL插入，成功创建了一个测试需求（ID: 15）
3. 修改了RequirementDatabaseAgent类，使用直接SQL插入方法，避免了ORM的时区处理

## 如何应用修复

1. 首先运行数据库修复脚本修改表结构：

```bash
python fix_timezone_direct.py
```

2. 测试直接SQL插入是否能解决问题：

```bash
python direct_insert_requirement.py
```

3. 重启应用服务器

## 注意事项

- 确保PostgreSQL数据库的时区设置为UTC
- 对于关键的数据库操作，考虑使用直接SQL插入而不是ORM
- 如果使用ORM，确保所有datetime对象都有时区信息
- 在创建表时，明确指定timestamp字段为`timestamp with time zone`类型
- 使用PostgreSQL的`NOW()`函数来设置时间字段，避免Python和数据库之间的时区转换问题
