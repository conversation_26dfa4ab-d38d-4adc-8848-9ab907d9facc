"""
临时脚本，用于修复ORM模型和数据库表之间的映射关系。
"""
import asyncio
from tortoise import Tortoise
from app.core.config import settings

async def main():
    # 连接到数据库
    await Tortoise.init(
        db_url=settings.DATABASE_URL,
        modules={"models": ["app.models.admin"]}
    )
    
    # 打印连接信息
    print("已连接到数据库:", settings.DATABASE_URL)
    
    # 执行一个简单的查询，验证连接是否正常
    conn = Tortoise.get_connection("default")
    result = await conn.execute_query("SELECT COUNT(*) FROM test_cases")
    print("测试用例数量:", result[0][0][0] if result and result[0] and result[0][0] else 0)
    
    # 关闭连接
    await Tortoise.close_connections()
    print("数据库连接已关闭")

if __name__ == "__main__":
    asyncio.run(main())
