#!/usr/bin/env python
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.knowledge import KnowledgeItem
from app.core.init_app import init_db

async def main():
    # 初始化数据库连接
    await init_db()
    
    # 获取知识点总数
    count = await KnowledgeItem.all().count()
    print(f"知识库中共有 {count} 条知识点")
    
    # 获取最新的10条知识点
    if count > 0:
        latest_items = await KnowledgeItem.all().order_by('-created_at').limit(10)
        print("\n最新的10条知识点:")
        for i, item in enumerate(latest_items, 1):
            print(f"{i}. [{item.item_type}] {item.title} (项目ID: {item.project_id}, 来源: {item.source})")
    
    # 按项目ID分组统计
    from tortoise.functions import Count
    project_stats = await KnowledgeItem.all().group_by('project_id').annotate(count=Count('id')).values('project_id', 'count')
    print("\n按项目统计:")
    for stat in project_stats:
        print(f"项目ID {stat['project_id']}: {stat['count']} 条知识点")

if __name__ == "__main__":
    asyncio.run(main())
