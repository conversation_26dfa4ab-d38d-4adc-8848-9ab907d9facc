#!/usr/bin/env python
# -*- coding: utf-8 -*-

import asyncio
import logging
from tortoise import Tortoise
from app.settings.config import settings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def run_migration():
    """运行知识点表字段添加迁移"""
    try:
        # 初始化数据库连接
        logger.info("正在初始化数据库连接...")
        await Tortoise.init(config=settings.TORTOISE_ORM)
        logger.info("数据库连接初始化完成")

        # 获取数据库连接
        conn = Tortoise.get_connection("default")
        
        # 执行迁移SQL
        migration_sql = """
        -- 添加源链接字段
        ALTER TABLE knowledge_items ADD COLUMN IF NOT EXISTS source_url VARCHAR(500);
        ALTER TABLE knowledge_items ADD COLUMN IF NOT EXISTS source_reference_id VARCHAR(100);
        
        -- 创建索引
        CREATE INDEX IF NOT EXISTS idx_knowledge_items_source_reference_id ON knowledge_items(source_reference_id);
        CREATE INDEX IF NOT EXISTS idx_knowledge_items_source_url ON knowledge_items(source_url);
        """
        
        logger.info("开始执行迁移SQL...")
        await conn.execute_script(migration_sql)
        logger.info("迁移SQL执行完成")
        
        # 验证字段是否添加成功
        logger.info("验证字段是否添加成功...")
        result = await conn.execute_query("""
            SELECT column_name, data_type, character_maximum_length 
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'knowledge_items'
            AND column_name IN ('source_url', 'source_reference_id');
        """)
        
        if result and len(result) > 1:
            logger.info("字段验证结果:")
            for row in result[1]:
                logger.info(f"  列名: {row[0]}, 数据类型: {row[1]}, 最大长度: {row[2]}")
        
        logger.info("迁移完成！")
        
    except Exception as e:
        logger.error(f"迁移失败: {str(e)}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        raise
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()
        logger.info("数据库连接已关闭")

if __name__ == "__main__":
    asyncio.run(run_migration())
