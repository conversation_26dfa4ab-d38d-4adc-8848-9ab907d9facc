<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试用例编辑模态窗口优化预览</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .modal-preview {
            width: 1200px;
            max-width: 95vw;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .modal-header {
            padding: 20px;
            background: #ffffff;
            border-bottom: 1px solid #e5e7eb;
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }
        
        .modal-body {
            padding: 20px;
            background: #f8fafc;
        }
        
        .form-item {
            display: flex;
            margin-bottom: 24px;
            align-items: flex-start;
        }
        
        .form-label {
            min-width: 120px;
            padding-right: 16px;
            font-weight: 600;
            color: #374151;
            padding-top: 8px;
        }
        
        .form-control {
            flex: 1;
        }
        
        .form-input, .form-textarea, .form-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            line-height: 1.5;
            font-family: inherit;
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 40px;
            line-height: 1.6;
            word-wrap: break-word;
        }
        
        .test-steps {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            background: white;
        }
        
        .step-row {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            padding: 16px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .step-row:last-child {
            border-bottom: none;
        }
        
        .step-number {
            width: 40px;
            font-weight: 600;
            color: #6366f1;
            text-align: center;
            padding-top: 8px;
            font-size: 16px;
        }
        
        .step-operation, .step-expected {
            flex: 1;
        }
        
        .step-connector {
            width: 40px;
            text-align: center;
            color: #6366f1;
            padding-top: 8px;
        }
        
        .add-step-btn {
            background: linear-gradient(45deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            border: none;
            padding: 8px 24px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            margin-top: 16px;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }
        
        .modal-footer {
            padding: 20px;
            background: white;
            border-top: 1px solid #e5e7eb;
            text-align: right;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            margin-left: 12px;
        }
        
        .btn-cancel {
            background: #f3f4f6;
            color: #374151;
        }
        
        .btn-save {
            background: #6366f1;
            color: white;
        }
        
        @media (max-width: 1400px) {
            .modal-preview {
                width: 95vw;
            }
            
            .step-row {
                flex-direction: column;
                gap: 12px;
            }
            
            .step-number, .step-connector {
                width: 100%;
                text-align: left;
            }
        }
    </style>
</head>
<body>
    <div class="modal-preview">
        <div class="modal-header">
            编辑用例 - 优化后的宽屏显示效果
        </div>
        
        <div class="modal-body">
            <div class="form-item">
                <div class="form-label">关联项目</div>
                <div class="form-control">
                    <select class="form-select">
                        <option>AI测试平台项目</option>
                    </select>
                </div>
            </div>
            
            <div class="form-item">
                <div class="form-label">关联需求</div>
                <div class="form-control">
                    <select class="form-select">
                        <option>用户登录功能需求</option>
                    </select>
                </div>
            </div>
            
            <div class="form-item">
                <div class="form-label">用例标题</div>
                <div class="form-control">
                    <textarea class="form-textarea" rows="2" placeholder="请输入用例标题">验证用户使用正确的用户名和密码能够成功登录系统，并跳转到主页面显示用户信息</textarea>
                </div>
            </div>
            
            <div class="form-item">
                <div class="form-label">用例描述</div>
                <div class="form-control">
                    <textarea class="form-textarea" rows="3" placeholder="请输入用例描述">本测试用例用于验证用户登录功能的正常流程，包括输入验证、身份认证、页面跳转等关键步骤。确保系统能够正确处理有效的登录凭据，并为用户提供良好的登录体验。</textarea>
                </div>
            </div>
            
            <div class="form-item">
                <div class="form-label">前置条件</div>
                <div class="form-control">
                    <textarea class="form-textarea" rows="2" placeholder="请输入前置条件">1. 系统已部署并正常运行
2. 测试用户账号已创建且状态正常
3. 浏览器已打开并能正常访问系统登录页面</textarea>
                </div>
            </div>
            
            <div class="form-item">
                <div class="form-label">用例步骤</div>
                <div class="form-control">
                    <div class="test-steps">
                        <div class="step-row">
                            <div class="step-number">1.</div>
                            <div class="step-operation">
                                <textarea class="form-textarea" rows="2" placeholder="请输入操作步骤">打开浏览器，访问系统登录页面 http://localhost:3000/login</textarea>
                            </div>
                            <div class="step-connector">→</div>
                            <div class="step-expected">
                                <textarea class="form-textarea" rows="2" placeholder="请输入预期结果">页面正常加载，显示登录表单，包含用户名输入框、密码输入框和登录按钮</textarea>
                            </div>
                        </div>
                        
                        <div class="step-row">
                            <div class="step-number">2.</div>
                            <div class="step-operation">
                                <textarea class="form-textarea" rows="2" placeholder="请输入操作步骤">在用户名输入框中输入有效的用户名 "<EMAIL>"</textarea>
                            </div>
                            <div class="step-connector">→</div>
                            <div class="step-expected">
                                <textarea class="form-textarea" rows="2" placeholder="请输入预期结果">用户名正确显示在输入框中，无格式错误提示</textarea>
                            </div>
                        </div>
                        
                        <div class="step-row">
                            <div class="step-number">3.</div>
                            <div class="step-operation">
                                <textarea class="form-textarea" rows="2" placeholder="请输入操作步骤">在密码输入框中输入正确的密码 "Password123!"</textarea>
                            </div>
                            <div class="step-connector">→</div>
                            <div class="step-expected">
                                <textarea class="form-textarea" rows="2" placeholder="请输入预期结果">密码以掩码形式显示，长度符合要求</textarea>
                            </div>
                        </div>
                        
                        <button class="add-step-btn">+ 新增步骤</button>
                    </div>
                </div>
            </div>
            
            <div class="form-item">
                <div class="form-label">后置条件</div>
                <div class="form-control">
                    <textarea class="form-textarea" rows="2" placeholder="请输入后置条件">用户成功登录后应清理测试数据，确保不影响其他测试用例的执行</textarea>
                </div>
            </div>
            
            <div class="form-item">
                <div class="form-label">TAPD需求URL</div>
                <div class="form-control">
                    <textarea class="form-textarea" rows="1" placeholder="请输入TAPD需求URL">https://tapd.cn/12345678/prong/stories/view/1012345678901234567</textarea>
                </div>
            </div>
        </div>
        
        <div class="modal-footer">
            <button class="btn btn-cancel">取消</button>
            <button class="btn btn-save">保存</button>
        </div>
    </div>
    
    <div style="margin-top: 40px; text-align: center; color: #6b7280;">
        <h3>优化效果展示</h3>
        <p>✅ 模态窗口宽度增加到1200px，提供更宽敞的编辑空间</p>
        <p>✅ 所有文本字段支持自动换行，避免内容被截断</p>
        <p>✅ 测试步骤布局优化，操作步骤和预期结果并排显示</p>
        <p>✅ 响应式设计，在小屏幕上自动调整布局</p>
    </div>
</body>
</html>
