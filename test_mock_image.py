"""
测试模拟图片解析功能
"""
import os
import sys
import traceback

# 添加当前目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

try:
    # 导入需要的函数
    from app.api.v1.reqAgent.image_markdown_converter import html_to_markdown_with_image_analysis
    
    # 设置环境变量，启用图片分析
    os.environ["ENABLE_IMAGE_ANALYSIS"] = "true"
    
    # 测试不同类型的图片
    test_images = [
        {
            "name": "用户界面图片",
            "html": '<img src="https://example.com/ui.jpg" alt="用户界面示例">'
        },
        {
            "name": "流程图",
            "html": '<img src="https://example.com/flow.jpg" alt="业务流程图">'
        },
        {
            "name": "数据图表",
            "html": '<img src="https://example.com/chart.jpg" alt="数据统计图表">'
        },
        {
            "name": "架构图",
            "html": '<img src="https://example.com/arch.jpg" alt="系统架构图">'
        },
        {
            "name": "默认图片",
            "html": '<img src="https://example.com/default.jpg" alt="需求文档图片">'
        }
    ]
    
    print("="*50)
    print("测试模拟图片解析功能")
    print("="*50)
    
    for i, test in enumerate(test_images, 1):
        print(f"\n\n测试 {i}: {test['name']}")
        print("-"*50)
        
        try:
            # 转换HTML并分析图片
            result = html_to_markdown_with_image_analysis(test["html"])
            
            # 打印结果
            print(f"HTML输入: {test['html']}")
            print(f"\nMarkdown输出:")
            print(result)
            
            # 检查是否包含图片解析内容
            if "<!-- 图片分析" in result:
                print("\n✅ 成功: 包含图片分析内容")
            else:
                print("\n❌ 失败: 不包含图片分析内容")
            
        except Exception as e:
            print(f"测试 {i} 失败: {str(e)}")
    
    print("\n" + "="*50)
    print("测试完成")
    print("="*50)
    
except Exception as e:
    print(f"\n❌ 测试失败: {str(e)}")
    traceback.print_exc()
