import asyncio
from tortoise import Tortoise
from app.settings.config import TORTOISE_ORM

async def verify_deletion():
    # Initialize Tortoise ORM
    await Tortoise.init(config=TORTOISE_ORM)
    
    # Get connection
    conn = Tortoise.get_connection("default")
    
    try:
        # 1. 验证测试用例步骤关联表
        result = await conn.execute_query("SELECT COUNT(*) FROM test_cases_test_steps;")
        count = result[1][0][0]
        print(f"test_cases_test_steps 表中剩余 {count} 条记录")
        
        # 2. 验证测试步骤表
        result = await conn.execute_query("SELECT COUNT(*) FROM test_steps;")
        count = result[1][0][0]
        print(f"test_steps 表中剩余 {count} 条记录")
        
        # 3. 验证测试用例表
        result = await conn.execute_query("SELECT COUNT(*) FROM test_cases;")
        count = result[1][0][0]
        print(f"test_cases 表中剩余 {count} 条记录")
        
        # 4. 验证需求文档表
        result = await conn.execute_query("SELECT COUNT(*) FROM requirement_doc;")
        count = result[1][0][0]
        print(f"requirement_doc 表中剩余 {count} 条记录")
        
        # 5. 验证需求表
        result = await conn.execute_query("SELECT COUNT(*) FROM requirement;")
        count = result[1][0][0]
        print(f"requirement 表中剩余 {count} 条记录")
        
        print("\n验证完成！")
    except Exception as e:
        print(f"验证时出错: {e}")
    
    # Close connections
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(verify_deletion())
