import logging
import os
import uvicorn
import time
from dotenv import load_dotenv

# 加载.env文件中的环境变量
load_dotenv()
print("已加载.env文件中的环境变量")
print(f"OPENAI_API_KEY = {os.environ.get('OPENAI_API_KEY', 'Not set')}")
print(f"LLM_API_KEY = {os.environ.get('LLM_API_KEY', 'Not set')}")

# 创建日志目录
os.makedirs("logs", exist_ok=True)

# 导入模型预加载工具
try:
    from app.utils.model_preloader import preload_embedding_model_async
    # 启动后台线程预加载模型
    preload_thread = preload_embedding_model_async()
    print("模型预加载已在后台启动")
except ImportError as e:
    print(f"模型预加载模块导入失败: {e}")
except Exception as e:
    print(f"模型预加载启动失败: {e}")

if __name__ == "__main__":
    # 配置uvicorn日志
    log_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "()": "uvicorn.logging.DefaultFormatter",
                "fmt": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
        },
        "handlers": {
            "default": {
                "formatter": "default",
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stderr",
            },
            "file": {
                "formatter": "default",
                "class": "logging.handlers.RotatingFileHandler",
                "filename": "logs/uvicorn.log",
                "maxBytes": 10*1024*1024,  # 10MB
                "backupCount": 3,
                "encoding": "utf-8",
            },
        },
        "loggers": {
            "uvicorn": {"handlers": ["default", "file"], "level": "INFO"},
            "uvicorn.error": {"level": "INFO"},
            "uvicorn.access": {"handlers": ["default", "file"], "level": "INFO", "propagate": False},
        },
    }

    # 从环境变量获取端口，默认为9999
    port = int(os.environ.get("API_PORT", "9999"))

    # 获取日志级别，默认为INFO
    log_level = os.environ.get("LOG_LEVEL", "INFO").upper()

    # 启动服务器
    uvicorn.run(
        "app:app",  # 这里指向app/__init__.py中的app实例
        host="0.0.0.0",
        port=port,
        reload=True,
        log_config=log_config,
        log_level=log_level.lower()
    )
