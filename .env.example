# 数据库配置
DB_HOST=host.docker.internal
DB_PORT=5432
DB_USER=admin
DB_PASSWORD=admin123
DB_NAME=agent_testing

# API配置
API_PORT=9999
LOG_LEVEL=INFO

# JWT配置
SECRET_KEY=3488a63e1765035d386f05409663f55c83bfae3b3c61a932744b20ad14244dcf
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=10080

# 应用名称
APP_NAME=agent_testing

# 前端配置
BACKEND_URL=http://localhost:9999
API_PREFIX=""

# 大模型配置 - DeepSeek
DEFAULT_LLM_MODEL=deepseek-chat
LLM_API_BASE=https://api.deepseek.com/v1
LLM_API_KEY=***********************************
LLM_TIMEOUT_SECONDS=120
LLM_MAX_RETRIES=3

# 大模型配置 - DashScope (阿里云)
DASHSCOPE_API_BASE=https://dashscope.aliyuncs.com/compatible-mode/v1
DASHSCOPE_API_KEY=
DASHSCOPE_MODEL=deepseek-v3

# 大模型配置 - Qwen VL (阿里云视觉模型)
QWEN_VL_MODEL=qwen-vl-plus-latest
QWEN_VL_API_BASE=https://dashscope.aliyuncs.com/compatible-mode/v1
QWEN_VL_API_KEY=sk-85477c3eb0424bb89d5421d2b28d2051
