version: '3'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "9999:80"
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    environment:
      # 数据库配置
      - DB_HOST=${DB_HOST:-host.docker.internal}
      - DB_PORT=${DB_PORT:-5432}
      - DB_USER=${DB_USER:-admin}
      - DB_PASSWORD=${DB_PASSWORD:-admin123}
      - DB_NAME=${DB_NAME:-agent_testing}
      # 这些变量保留用于兼容性
      - POSTGRES_HOST=${DB_HOST:-host.docker.internal}
      - POSTGRES_PORT=${DB_PORT:-5432}
      - POSTGRES_USER=${DB_USER:-admin}
      - POSTGRES_PASSWORD=${DB_PASSWORD:-admin123}
      - POSTGRES_DB=${DB_NAME:-agent_testing}
      # API配置
      - API_PORT=${API_PORT:-9999}
      # 日志级别配置，可选值: DEBUG, INFO, WARNING, ERROR, CRITICAL
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      # JWT配置
      - SECRET_KEY=${SECRET_KEY:-3488a63e1765035d386f05409663f55c83bfae3b3c61a932744b20ad14244dcf}
      - JWT_ALGORITHM=${JWT_ALGORITHM:-HS256}
      - JWT_EXPIRE_MINUTES=${JWT_EXPIRE_MINUTES:-10080}
      # 应用名称
      - APP_NAME=${APP_NAME:-agent_testing}
      # 前端配置
      - BACKEND_URL=${BACKEND_URL:-http://localhost:9999}
      - API_PREFIX=${API_PREFIX:-""}
      # 大模型配置 - DeepSeek
      - DEFAULT_LLM_MODEL=${DEFAULT_LLM_MODEL:-deepseek-chat}
      - LLM_API_BASE=${LLM_API_BASE:-https://api.deepseek.com/v1}
      - LLM_API_KEY=${LLM_API_KEY:-sk-2cdc85223c2f4f9a976a57e3ae9ba4b9}
      - LLM_TIMEOUT_SECONDS=${LLM_TIMEOUT_SECONDS:-120}
      - LLM_MAX_RETRIES=${LLM_MAX_RETRIES:-3}
      # 大模型配置 - DashScope (阿里云)
      - DASHSCOPE_API_BASE=${DASHSCOPE_API_BASE:-https://dashscope.aliyuncs.com/compatible-mode/v1}
      - DASHSCOPE_API_KEY=${DASHSCOPE_API_KEY:-""}
      - DASHSCOPE_MODEL=${DASHSCOPE_MODEL:-deepseek-v3}
      # 大模型配置 - Qwen VL (阿里云视觉模型)
      - QWEN_VL_MODEL=${QWEN_VL_MODEL:-qwen-vl-plus-latest}
      - QWEN_VL_API_BASE=${QWEN_VL_API_BASE:-https://dashscope.aliyuncs.com/compatible-mode/v1}
      - QWEN_VL_API_KEY=${QWEN_VL_API_KEY:-sk-85477c3eb0424bb89d5421d2b28d2051}


    volumes:
      # 挂载日志目录到宿主机，方便查看
      - ./logs:/app/logs
      - ./logs:/var/log/app
      - ./logs/nginx:/var/log/nginx
      - ./logs/supervisor:/var/log/supervisor
