import asyncio
from tortoise import Tortoise
from app.settings.config import TORTOISE_ORM

async def list_all_tables():
    # Initialize Tortoise ORM
    await Tortoise.init(config=TORTOISE_ORM)
    
    # Get connection
    conn = Tortoise.get_connection("default")
    
    # For PostgreSQL
    try:
        result = await conn.execute_query("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
        """)
        tables = [row[0] for row in result[1]]
        print("PostgreSQL 数据库中的表:")
        for table in tables:
            print(f"- {table}")
    except Exception as e:
        print(f"PostgreSQL查询失败: {e}")
        
        # Try SQLite query
        try:
            result = await conn.execute_query("SELECT name FROM sqlite_master WHERE type='table';")
            tables = [row[0] for row in result[1]]
            print("SQLite 数据库中的表:")
            for table in tables:
                print(f"- {table}")
        except Exception as e2:
            print(f"SQLite查询也失败: {e2}")
    
    # Close connections
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(list_all_tables())
