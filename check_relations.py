#!/usr/bin/env python
# -*- coding: utf-8 -*-

import asyncio
from tortoise import Tortoise
from app.settings.config import settings
from app.models.knowledge_relation import RequirementKnowledgeRelation
from app.models.knowledge import Knowledge
from app.models.requirement import Requirement

async def check_relations():
    # 初始化数据库连接
    print("正在初始化数据库连接...")
    await Tortoise.init(config=settings.TORTOISE_ORM)
    print("数据库连接初始化完成")
    
    # 检查需求是否存在
    requirement = await Requirement.filter(id=276).first()
    if requirement:
        print(f"需求 ID 276 存在，名称: {requirement.name}")
    else:
        print("需求 ID 276 不存在")
    
    # 查询关联关系
    try:
        relations = await RequirementKnowledgeRelation.filter(requirement_id=276).all()
        print(f'找到 {len(relations)} 条与需求 ID 276 相关的知识点关联')
        
        # 如果有关联关系，打印详细信息
        if relations:
            for relation in relations:
                print(f'关联 ID: {relation.id}, 知识点 ID: {relation.knowledge_id}, 相关性级别: {relation.relevance_level}')
                
                # 尝试获取知识点详情
                knowledge = await Knowledge.filter(id=relation.knowledge_id).first()
                if knowledge:
                    print(f'  知识点标题: {knowledge.title}')
                    print(f'  知识点类型: {knowledge.item_type}')
                    print(f'  知识点来源: {knowledge.source}')
                else:
                    print(f'  知识点 ID {relation.knowledge_id} 不存在')
        
    except Exception as e:
        print(f"查询关联关系时出错: {str(e)}")
        import traceback
        print(f"错误堆栈: {traceback.format_exc()}")
    
    # 关闭数据库连接
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(check_relations())
