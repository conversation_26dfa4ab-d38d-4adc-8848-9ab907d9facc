import asyncio
from tortoise import Tortoise, connections
from app.settings.config import TORTOISE_ORM

async def check_saved_records():
    # 初始化Tortoise ORM
    await Tortoise.init(config=TORTOISE_ORM)
    
    # 获取数据库连接
    conn = connections.get("default")
    
    # 查询最近创建的记录
    result = await conn.execute_query("""
    SELECT id, name, remark, created_at 
    FROM requirement 
    ORDER BY id DESC 
    LIMIT 10;
    """)
    
    print("最近创建的记录:")
    for row in result[1]:
        print(f"ID: {row[0]}, 名称: {row[1]}, 备注: {row[2]}, 创建时间: {row[3]}")
    
    # 关闭连接
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(check_saved_records())
