#!/bin/bash

# 停止当前运行的容器
echo "停止当前运行的容器..."
docker compose down

# 启动容器
echo "启动容器..."
docker compose up -d

# 等待容器启动
echo "等待容器启动..."
sleep 5

# 获取容器ID
CONTAINER_ID=$(docker compose ps -q app)

# 复制修复后的文件到容器
echo "复制修复后的文件到容器..."
docker cp fixed_nginx.conf $CONTAINER_ID:/etc/nginx/conf.d/default.conf
docker cp fixed_run.py $CONTAINER_ID:/app/run.py
docker cp fixed_supervisord.conf $CONTAINER_ID:/etc/supervisor/conf.d/supervisord.conf

# 重启容器中的服务
echo "重启容器中的服务..."
docker exec $CONTAINER_ID supervisorctl reload
docker exec $CONTAINER_ID nginx -s reload

echo "完成！现在可以尝试登录了。"
echo "请访问 http://localhost:9999 并尝试登录"
