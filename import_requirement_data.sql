-- 创建临时表
CREATE TEMP TABLE temp_requirement (
    id INTEGER,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    name VARCHAR(255),
    description TEXT,
    category VARCHAR(20),
    parent VARCHAR(50),
    module VARCHAR(50),
    level VARCHAR(20),
    reviewer VARCHAR(50),
    keywords VARCHAR(100),
    estimate INTEGER,
    criteria TEXT,
    remark TEXT,
    project_id INTEGER,
    tapd_url VARCHAR(500)
);

-- 从CSV文件导入数据到临时表
\COPY temp_requirement FROM 'formatted_requirement_data.csv' WITH (FORMAT csv, DELIMITER ',');

-- 将数据从临时表插入到requirement表
INSERT INTO requirement (
    id, created_at, updated_at, name, description, category, parent, module, level, 
    reviewer, keywords, estimate, criteria, remark, project_id, tapd_url
)
SELECT 
    id, created_at, updated_at, name, description, category, parent, module, level, 
    reviewer, keywords, CASE WHEN estimate = '' THEN NULL ELSE estimate::INTEGER END, 
    criteria, remark, project_id, tapd_url
FROM temp_requirement
ON CONFLICT (id) DO NOTHING;

-- 重置序列
SELECT setval('requirement_id_seq', (SELECT MAX(id) FROM requirement), true);

-- 删除临时表
DROP TABLE temp_requirement;
