"""
测试嵌入模型加载时间

这个脚本用于测试嵌入模型的加载时间，并提供一些优化建议。
"""

import os
import time
import logging
import torch
from sentence_transformers import SentenceTransformer
from sentence_transformers.util import batch_to_device

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 模型配置
EMBEDDING_MODEL = os.environ.get("EMBEDDING_MODEL", "BAAI/bge-large-zh")
EMBEDDING_DEVICE = os.environ.get("EMBEDDING_DEVICE", "cpu")
EMBEDDING_CACHE_DIR = os.environ.get("EMBEDDING_CACHE_DIR", "./embedding_models")

# 测试文本
TEST_TEXTS = [
    "这是第一个测试文本，用于预热嵌入模型",
    "这是第二个测试文本，包含不同的内容",
    "这是第三个测试文本，用于确保模型已完全加载"
]


def test_embedding_load_time(model_name=EMBEDDING_MODEL, device=EMBEDDING_DEVICE, cache_folder=EMBEDDING_CACHE_DIR,
                      use_fp16=False, normalize_embeddings=True, use_preload=False):
    """测试嵌入模型加载时间"""
    logger.info(f"开始测试嵌入模型加载时间: {model_name}")
    logger.info(f"设备: {device}")
    logger.info(f"缓存目录: {cache_folder}")
    logger.info(f"使用FP16: {use_fp16}")
    logger.info(f"归一化嵌入: {normalize_embeddings}")
    logger.info(f"使用预加载: {use_preload}")

    # 如果使用预加载，先加载模型但不计时
    if use_preload:
        logger.info("预加载模型（不计时）...")
        _ = SentenceTransformer(model_name, device=device, cache_folder=cache_folder)
        logger.info("预加载完成")

    # 测量模型加载时间
    start_time = time.time()
    logger.info("开始加载模型...")

    # 创建模型
    model_kwargs = {}
    if use_fp16 and device != "cpu":
        model_kwargs["torch_dtype"] = torch.float16

    model = SentenceTransformer(model_name, device=device, cache_folder=cache_folder, **model_kwargs)

    load_time = time.time() - start_time
    logger.info(f"模型加载完成，耗时: {load_time:.2f}秒")

    # 测量第一次编码时间
    start_encode_time = time.time()
    logger.info("开始第一次编码...")

    encode_kwargs = {"normalize_embeddings": normalize_embeddings}
    embeddings = model.encode(TEST_TEXTS[0], **encode_kwargs)

    first_encode_time = time.time() - start_encode_time
    logger.info(f"第一次编码完成，耗时: {first_encode_time:.2f}秒")

    # 测量批量编码时间
    start_batch_time = time.time()
    logger.info("开始批量编码...")

    batch_embeddings = model.encode(TEST_TEXTS, batch_size=len(TEST_TEXTS), **encode_kwargs)

    batch_time = time.time() - start_batch_time
    logger.info(f"批量编码完成，耗时: {batch_time:.2f}秒")

    # 总时间
    total_time = time.time() - start_time
    logger.info(f"总耗时: {total_time:.2f}秒")

    return {
        "model_name": model_name,
        "device": device,
        "use_fp16": use_fp16,
        "normalize_embeddings": normalize_embeddings,
        "use_preload": use_preload,
        "load_time": load_time,
        "first_encode_time": first_encode_time,
        "batch_time": batch_time,
        "total_time": total_time
    }


def compare_models():
    """比较不同模型的加载时间"""
    results = []

    # 测试大模型
    logger.info("=" * 50)
    logger.info("测试大模型 (BAAI/bge-large-zh)")
    large_result = test_embedding_load_time("BAAI/bge-large-zh")
    results.append(large_result)

    # 测试小模型
    logger.info("=" * 50)
    logger.info("测试小模型 (BAAI/bge-small-zh)")
    small_result = test_embedding_load_time("BAAI/bge-small-zh")
    results.append(small_result)

    # 打印比较结果
    logger.info("=" * 50)
    logger.info("模型比较结果:")
    logger.info(f"{'模型名称':<20} {'加载时间(秒)':<15} {'首次编码(秒)':<15} {'批量编码(秒)':<15} {'总时间(秒)':<15}")
    logger.info("-" * 80)

    for result in results:
        logger.info(
            f"{result['model_name']:<20} "
            f"{result['load_time']:<15.2f} "
            f"{result['first_encode_time']:<15.2f} "
            f"{result['batch_time']:<15.2f} "
            f"{result['total_time']:<15.2f}"
        )


def test_with_optimizations():
    """测试不同优化选项的效果"""
    results = []

    # 基准测试 - 无优化
    logger.info("=" * 50)
    logger.info("基准测试 - 无优化")
    base_result = test_embedding_load_time(
        use_fp16=False,
        normalize_embeddings=True,
        use_preload=False
    )
    results.append(base_result)

    # 测试预加载
    logger.info("=" * 50)
    logger.info("测试预加载")
    preload_result = test_embedding_load_time(
        use_fp16=False,
        normalize_embeddings=True,
        use_preload=True
    )
    results.append(preload_result)

    # 打印比较结果
    logger.info("=" * 50)
    logger.info("优化比较结果:")
    logger.info(f"{'优化方式':<20} {'加载时间(秒)':<15} {'首次编码(秒)':<15} {'批量编码(秒)':<15} {'总时间(秒)':<15}")
    logger.info("-" * 80)

    for result in results:
        optimization = "基准测试"
        if result["use_preload"]:
            optimization = "使用预加载"

        logger.info(
            f"{optimization:<20} "
            f"{result['load_time']:<15.2f} "
            f"{result['first_encode_time']:<15.2f} "
            f"{result['batch_time']:<15.2f} "
            f"{result['total_time']:<15.2f}"
        )


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="测试嵌入模型加载时间")
    parser.add_argument("--model", type=str, default=EMBEDDING_MODEL, help="模型名称")
    parser.add_argument("--device", type=str, default=EMBEDDING_DEVICE, help="设备 (cpu 或 cuda)")
    parser.add_argument("--cache", type=str, default=EMBEDDING_CACHE_DIR, help="缓存目录")
    parser.add_argument("--compare", action="store_true", help="比较大小模型")
    parser.add_argument("--optimize", action="store_true", help="测试不同优化选项")
    parser.add_argument("--fp16", action="store_true", help="使用FP16精度")
    parser.add_argument("--no-normalize", action="store_true", help="不归一化嵌入")
    parser.add_argument("--preload", action="store_true", help="使用预加载")

    args = parser.parse_args()

    if args.compare:
        compare_models()
    elif args.optimize:
        test_with_optimizations()
    else:
        test_embedding_load_time(
            model_name=args.model,
            device=args.device,
            cache_folder=args.cache,
            use_fp16=args.fp16,
            normalize_embeddings=not args.no_normalize,
            use_preload=args.preload
        )
