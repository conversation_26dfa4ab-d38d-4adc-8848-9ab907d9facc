# PostgreSQL时区问题解决方案

本文档说明了如何解决PostgreSQL数据库中的时区问题，特别是"can't subtract offset-naive and offset-aware datetimes"错误。

## 问题描述

在使用PostgreSQL数据库时，如果混合使用了带时区信息(offset-aware)和不带时区信息(offset-naive)的datetime对象，会出现以下错误：

```
需求入库过程出错: invalid input for query argument $1: datetime.datetime(2025, 4, 26, 17, 5, 37... (can't subtract offset-naive and offset-aware datetimes)
```

## 解决方案

我们采用了以下综合方案来解决这个问题：

### 1. 统一时区配置

将PostgreSQL和应用代码都统一使用UTC时区：

```python
# 在app/settings/config.py中
"server_settings": {
    "timezone": "UTC",  # 统一使用UTC时区
    "application_name": "agent_testing"
}

# 在TORTOISE_ORM配置中
"use_tz": True,  # 是否使用时区感知的时间，启用
"timezone": "UTC",  # 统一使用UTC时区
```

### 2. 中心化时区处理函数

在app/models/base.py中定义了统一的时区处理函数：

```python
def get_now_with_timezone():
    """获取带UTC时区的当前时间"""
    return datetime.now(pytz.UTC)

def ensure_timezone(dt):
    """确保datetime对象有时区信息"""
    if dt is None:
        return None
    if dt.tzinfo is None:
        return dt.replace(tzinfo=pytz.UTC)
    return dt

def ensure_all_datetimes_have_timezone(obj_dict):
    """确保字典中所有datetime对象都有时区信息"""
    # 实现详见代码
```

### 3. 修改控制器类

在RequirementTimezoneController类中使用统一的时区处理函数：

```python
@atomic()
async def create(self, obj_in: RequirementCreate) -> Requirement:
    # ...
    # 使用统一的时区处理函数确保所有datetime对象都有时区信息
    obj_dict = ensure_all_datetimes_have_timezone(obj_dict)
    # ...
```

### 4. 修改RequirementDatabaseAgent类

在RequirementDatabaseAgent类中处理JSON数据时确保时区信息：

```python
# 处理JSON中的所有需求，确保datetime字段有时区信息
if 'requirements' in requirement_data:
    for req in requirement_data['requirements']:
        # 使用统一的时区处理函数确保所有datetime对象都有时区信息
        req = ensure_all_datetimes_have_timezone(req)
```

### 5. 修复现有数据

创建了数据库迁移脚本(migrations/fix_timezone.sql)和执行脚本(fix_timezone.py)来修复现有数据的时区问题。

## 如何应用修复

1. 确保已经更新了代码中的时区处理函数
2. 运行数据库迁移脚本修复现有数据：

```bash
python fix_timezone.py
```

3. 重启应用服务器

## 注意事项

- 所有新创建的datetime对象都应该使用`get_now_with_timezone()`函数
- 所有从外部来源(如JSON)获取的datetime对象都应该通过`ensure_timezone()`或`ensure_all_datetimes_have_timezone()`函数处理
- 如果遇到类似的时区问题，请检查是否有新的代码没有使用统一的时区处理函数
