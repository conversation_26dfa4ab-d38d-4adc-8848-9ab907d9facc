import logging
import os

from fastapi import FastAP<PERSON>, Body, Request, Header, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, HTMLResponse, FileResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import pathlib

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="Agent Testing API",
    description="API for Agent Testing",
    version="1.0.0",
    openapi_url="/openapi.json",
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头部
)

# 创建静态文件目录
os.makedirs("app/static", exist_ok=True)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# 定义登录请求模型
class LoginRequest(BaseModel):
    username: str
    password: str

# 创建一个简单的路由
@app.get("/")
async def root():
    return {"message": "Welcome to Agent Testing API"}

# 添加登录测试页面路由
@app.get("/login-test", response_class=HTMLResponse)
async def login_test():
    html_file = pathlib.Path("app/static/login_test.html")
    if html_file.exists():
        return FileResponse("app/static/login_test.html")
    else:
        return HTMLResponse(content="<h1>Login test page not found</h1>", status_code=404)

# 添加登录格式测试页面路由
@app.get("/login-formats", response_class=HTMLResponse)
async def login_formats():
    html_file = pathlib.Path("app/static/login_formats.html")
    if html_file.exists():
        return FileResponse("app/static/login_formats.html")
    else:
        return HTMLResponse(content="<h1>Login formats test page not found</h1>", status_code=404)

# 添加测试登录页面路由
@app.get("/test", response_class=HTMLResponse)
async def test_login_page():
    html_file = pathlib.Path("app/static/test_login.html")
    if html_file.exists():
        return FileResponse("app/static/test_login.html")
    else:
        return HTMLResponse(content="<h1>Test login page not found</h1>", status_code=404)

# 添加TAPD插件下载路由
@app.get("/download/tapd-plugin")
async def download_tapd_plugin():
    """下载TAPD页面提取插件"""
    plugin_file = pathlib.Path("app/static/tapd_extractor_plugin.zip")
    if plugin_file.exists():
        return FileResponse(
            path=str(plugin_file),
            filename="tapd页面提取插件.zip",
            media_type="application/zip"
        )
    else:
        return {"error": "插件文件不存在"}

# 添加一个路由，用于处理前端的登录请求
@app.post("/auth/login")
async def auth_login(request: LoginRequest = Body(...)):
    logger.info(f"前端登录请求: username={request.username}, password={request.password}")
    # 直接返回JWT格式的响应
    return {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
        "token_type": "bearer"
    }

# 添加调试路由
@app.post("/api/v1/debug")
async def debug(request: dict = Body(...)):
    logger.info(f"调试请求: {request}")
    return request

# 添加响应格式测试路由
@app.get("/api/v1/response-formats")
async def response_formats():
    """返回所有可能的响应格式"""
    return {
        "formats": [
            {
                "name": "Format 1: Simple token",
                "example": {
                    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
                }
            },
            {
                "name": "Format 2: JWT standard",
                "example": {
                    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
                    "token_type": "bearer"
                }
            },
            {
                "name": "Format 3: Data wrapped token",
                "example": {
                    "data": {
                        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
                    }
                }
            },
            {
                "name": "Format 4: Data wrapped JWT standard",
                "example": {
                    "data": {
                        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
                        "token_type": "bearer"
                    }
                }
            },
            {
                "name": "Format 5: Code and data",
                "example": {
                    "code": 0,
                    "data": {
                        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
                    }
                }
            },
            {
                "name": "Format 6: Code, message and data",
                "example": {
                    "code": 0,
                    "message": "Login successful",
                    "data": {
                        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
                    }
                }
            }
        ]
    }

# 创建一个路由来获取请求头
@app.get("/api/v1/headers")
async def get_headers(request: Request):
    headers = dict(request.headers)
    logger.info(f"请求头: {headers}")
    return headers

# 创建多种格式的登录路由
@app.post("/api/v1/auth/login/format1")
async def login_format1(request: LoginRequest = Body(...)):
    logger.info(f"登录请求(格式1): username={request.username}")
    # 格式1: 简单token
    return {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
    }

@app.post("/api/v1/auth/login/format2")
async def login_format2(request: LoginRequest = Body(...)):
    logger.info(f"登录请求(格式2): username={request.username}")
    # 格式2: JWT标准
    return {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
        "token_type": "bearer"
    }

@app.post("/api/v1/auth/login/format3")
async def login_format3(request: LoginRequest = Body(...)):
    logger.info(f"登录请求(格式3): username={request.username}")
    # 格式3: Data包装的token
    return {
        "data": {
            "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
        }
    }

@app.post("/api/v1/auth/login/format4")
async def login_format4(request: LoginRequest = Body(...)):
    logger.info(f"登录请求(格式4): username={request.username}")
    # 格式4: Data包装的JWT标准
    return {
        "data": {
            "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
            "token_type": "bearer"
        }
    }

@app.post("/api/v1/auth/login/format5")
async def login_format5(request: LoginRequest = Body(...)):
    logger.info(f"登录请求(格式5): username={request.username}")
    # 格式5: Code和data
    return {
        "code": 0,
        "data": {
            "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
        }
    }

@app.post("/api/v1/auth/login/format6")
async def login_format6(request: LoginRequest = Body(...)):
    logger.info(f"登录请求(格式6): username={request.username}")
    # 格式6: Code, message和data
    return {
        "code": 0,
        "message": "Login successful",
        "data": {
            "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
        }
    }

# 创建登录路由 - 匹配前端期望的路径
@app.post("/api/v1/auth/login")
async def login_direct(request: LoginRequest = Body(...), req: Request = None):
    logger.info(f"直接登录请求: username={request.username}, password={request.password}")

    # 记录请求头和请求体
    if req:
        headers = dict(req.headers)
        logger.info(f"直接登录请求头: {headers}")

    # 尝试不同的响应格式

    # 格式1: 简单token
    # return {
    #     "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
    # }

    # 格式2: JWT标准
    return {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
        "token_type": "bearer"
    }

    # 格式3: Data包装的token
    # return {
    #     "data": {
    #         "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
    #     }
    # }

    # 格式4: Data包装的JWT标准
    # return {
    #     "data": {
    #         "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
    #         "token_type": "bearer"
    #     }
    # }

    # 格式5: Code和data
    # return {
    #     "code": 0,
    #     "data": {
    #         "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
    #         "user": {
    #             "id": 1,
    #             "username": request.username,
    #             "name": "Admin User",
    #             "role": "admin",
    #             "permissions": ["admin", "user"]
    #         }
    #     }
    # }

    # 格式6: Code, message和data
    # return {
    #     "code": 0,
    #     "message": "Login successful",
    #     "data": {
    #         "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
    #         "user": {
    #             "id": 1,
    #             "username": request.username,
    #             "name": "Admin User",
    #             "role": "admin",
    #             "permissions": ["admin", "user"]
    #         }
    #     }
    # }

# 创建一个测试路由
@app.get("/test-login")
async def test_login():
    """返回所有可能的登录响应格式"""
    return {
        "formats": [
            {
                "name": "Format 1: Simple token",
                "url": "/auth/login/format1",
                "example": {
                    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
                }
            },
            {
                "name": "Format 2: JWT standard",
                "url": "/auth/login/format2",
                "example": {
                    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
                    "token_type": "bearer"
                }
            },
            {
                "name": "Format 3: Data wrapped token",
                "url": "/auth/login/format3",
                "example": {
                    "data": {
                        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
                    }
                }
            },
            {
                "name": "Format 4: Data wrapped JWT standard",
                "url": "/auth/login/format4",
                "example": {
                    "data": {
                        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
                        "token_type": "bearer"
                    }
                }
            },
            {
                "name": "Format 5: Code and data",
                "url": "/auth/login/format5",
                "example": {
                    "code": 0,
                    "data": {
                        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
                    }
                }
            },
            {
                "name": "Format 6: Code, message and data",
                "url": "/auth/login/format6",
                "example": {
                    "code": 0,
                    "message": "Login successful",
                    "data": {
                        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
                    }
                }
            },
            {
                "name": "Current format",
                "url": "/auth/login",
                "example": {
                    "code": 0,
                    "message": "Login successful",
                    "data": {
                        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
                        "user": {
                            "id": 1,
                            "username": "admin",
                            "name": "Admin User",
                            "role": "admin",
                            "permissions": ["admin", "user"]
                        }
                    }
                }
            }
        ]
    }

# 打印环境变量
logger.info(f"环境变量: DB_HOST={os.environ.get('DB_HOST', 'not set')}")
logger.info(f"环境变量: DB_PORT={os.environ.get('DB_PORT', 'not set')}")
logger.info(f"环境变量: DB_USER={os.environ.get('DB_USER', 'not set')}")
logger.info(f"环境变量: DB_NAME={os.environ.get('DB_NAME', 'not set')}")
logger.info(f"环境变量: API_PORT={os.environ.get('API_PORT', 'not set')}")
