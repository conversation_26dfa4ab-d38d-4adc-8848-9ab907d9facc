"""
测试TAPD API接口
"""
import os
import sys
import json
import requests
import traceback

# 添加当前目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 设置环境变量，启用图片分析
os.environ["ENABLE_IMAGE_ANALYSIS"] = "true"

# 测试数据
test_data = {
    "url": "https://www.tapd.cn/12345",
    "title": "测试需求标题",
    "content": """
    <div class="tapd-detail-content">
        <h1>需求标题：用户管理功能</h1>
        <div class="description">
            <p>本需求主要实现系统的用户管理功能</p>
            <p>包含用户的增删改查等基本操作</p>
        </div>
        <div class="requirement-details">
            <h2>功能点</h2>
            <ul>
                <li>用户注册</li>
                <li>用户登录</li>
                <li>用户信息修改</li>
                <li>用户权限管理</li>
            </ul>
            <h2>界面设计</h2>
            <p>界面需要简洁大方符合整体风格</p>
            <img src="https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7" alt="用户界面示例">
            <p>上图为用户界面参考设计</p>
            <h2>技术要求</h2>
            <p>前端使用Vue框架开发</p>
            <p>后端使用Spring Boot</p>
            <p>数据库使用MySQL</p>
        </div>
    </div>
    """
}

def test_parse_api():
    """测试解析API"""
    try:
        # 发送请求到解析API
        response = requests.post(
            "http://localhost:8000/api/v1/reqAgent/tapd/parse",
            json=test_data
        )
        
        # 检查响应
        if response.status_code == 200:
            result = response.json()
            print("\n解析API响应:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 检查是否包含图片解析内容
            if "content" in result["data"]:
                content = result["data"]["content"]
                if "<!-- 图片分析 -->" in content or "<!-- 示例图分析 -->" in content:
                    print("\n✅ 成功: 解析结果包含图片分析内容")
                else:
                    print("\n❌ 失败: 解析结果不包含图片分析内容")
                    print("解析结果内容:")
                    print(content)
            else:
                print("\n❌ 失败: 解析结果不包含content字段")
        else:
            print(f"\n❌ 失败: API请求失败，状态码: {response.status_code}")
            print(response.text)
    
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        traceback.print_exc()

def test_list_api():
    """测试列表API"""
    try:
        # 发送请求到列表API
        response = requests.get("http://localhost:8000/api/v1/reqAgent/tapd/list")
        
        # 检查响应
        if response.status_code == 200:
            result = response.json()
            print("\n列表API响应:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 检查是否有数据
            if result and len(result) > 0:
                # 检查第一个需求是否包含图片解析内容
                if "content" in result[0]:
                    content = result[0]["content"]
                    if "<!-- 图片分析 -->" in content or "<!-- 示例图分析 -->" in content:
                        print("\n✅ 成功: 列表结果包含图片分析内容")
                    else:
                        print("\n❌ 失败: 列表结果不包含图片分析内容")
                        print("列表结果内容:")
                        print(content)
                else:
                    print("\n❌ 失败: 列表结果不包含content字段")
            else:
                print("\n❓ 注意: 列表API返回空结果")
        else:
            print(f"\n❌ 失败: API请求失败，状态码: {response.status_code}")
            print(response.text)
    
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    print("="*50)
    print("测试TAPD API接口")
    print("="*50)
    
    # 测试解析API
    print("\n1. 测试解析API")
    test_parse_api()
    
    # 测试列表API
    print("\n2. 测试列表API")
    test_list_api()
    
    print("\n"+"="*50)
    print("测试完成")
    print("="*50)
