// 插件安装或更新时执行
chrome.runtime.onInstalled.addListener(function() {
  console.log('TAPD Cookie Extractor 已安装/更新');
});

// 监听来自弹出窗口的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  // 处理获取Cookie的请求
  if (request.action === 'getCookies') {
    chrome.cookies.getAll({domain: 'tapd.cn'}, function(cookies) {
      sendResponse({cookies: cookies});
    });
    return true; // 保持消息通道开放以支持异步响应
  }
  
  // 处理获取Local Storage的请求
  if (request.action === 'getLocalStorage') {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      if (tabs.length === 0) {
        sendResponse({error: '没有活动标签页'});
        return;
      }
      
      chrome.tabs.sendMessage(tabs[0].id, {action: 'getLocalStorage'}, function(response) {
        sendResponse(response);
      });
    });
    return true; // 保持消息通道开放以支持异步响应
  }
  
  // 处理获取Session Storage的请求
  if (request.action === 'getSessionStorage') {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      if (tabs.length === 0) {
        sendResponse({error: '没有活动标签页'});
        return;
      }
      
      chrome.tabs.sendMessage(tabs[0].id, {action: 'getSessionStorage'}, function(response) {
        sendResponse(response);
      });
    });
    return true; // 保持消息通道开放以支持异步响应
  }
});

// 当标签页更新时，检查是否是TAPD页面
chrome.tabs.onUpdated.addListener(function(tabId, changeInfo, tab) {
  if (changeInfo.status === 'complete' && tab.url && tab.url.includes('tapd.cn')) {
    // 更新图标状态，表示当前页面是TAPD
    chrome.action.setBadgeText({
      text: 'TAPD',
      tabId: tabId
    });
    chrome.action.setBadgeBackgroundColor({
      color: '#4285F4',
      tabId: tabId
    });
  } else if (changeInfo.status === 'complete') {
    // 清除图标状态
    chrome.action.setBadgeText({
      text: '',
      tabId: tabId
    });
  }
});
