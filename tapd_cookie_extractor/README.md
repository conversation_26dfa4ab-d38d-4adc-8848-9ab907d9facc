# TAPD Cookie提取器Chrome插件

这个Chrome插件可以帮助您从TAPD页面提取认证信息（Cookies、Local Storage和Session Storage），并将其发送到您的应用程序。

## 功能特点

- 提取TAPD的Cookies信息
- 提取TAPD页面的Local Storage数据
- 提取TAPD页面的Session Storage数据
- 将提取的认证信息发送到您指定的应用接口
- 简单易用的用户界面

## 安装步骤

1. 下载插件文件夹
2. 打开Chrome浏览器，进入扩展程序页面：`chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择插件文件夹（tapd_cookie_extractor）
6. 插件将被安装到Chrome浏览器中

## 使用方法

1. 登录TAPD网站
2. 点击Chrome工具栏中的插件图标
3. 在弹出窗口中，您可以：
   - 点击"提取TAPD Cookies"按钮提取Cookies
   - 点击"提取TAPD Local Storage"按钮提取Local Storage数据
   - 点击"提取TAPD Session Storage"按钮提取Session Storage数据
4. 提取的数据将显示在相应的标签页中
5. 设置您的应用接收URL（例如：`http://localhost:9999/api/v1/requirement/tapd/auth`）
6. 点击"保存URL设置"按钮
7. 点击"发送认证数据到应用"按钮将提取的数据发送到您的应用

## 应用接收API格式

您的应用需要提供一个接收API，接受以下格式的POST请求：

```json
{
  "url": "https://www.tapd.cn/...",
  "cookies": {
    "cookie1": "value1",
    "cookie2": "value2",
    ...
  },
  "localStorage": {
    "key1": "value1",
    "key2": "value2",
    ...
  },
  "sessionStorage": {
    "key1": "value1",
    "key2": "value2",
    ...
  }
}
```

## 注意事项

- 插件需要访问TAPD网站的Cookie和存储数据，请确保您了解相关的隐私影响
- 插件仅在TAPD页面上有效
- 确保您的应用接收API支持CORS，允许来自Chrome扩展的请求

## 故障排除

- 如果提取失败，请检查您是否已登录TAPD
- 如果发送失败，请检查应用接收URL是否正确
- 查看浏览器控制台以获取更详细的错误信息
