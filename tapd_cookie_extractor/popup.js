// 当弹出窗口加载完成时执行
document.addEventListener('DOMContentLoaded', function() {
  // 获取DOM元素
  const statusMessage = document.getElementById('statusMessage');
  const extractCookiesButton = document.getElementById('extractCookiesButton');
  const extractLocalStorageButton = document.getElementById('extractLocalStorageButton');
  const extractSessionStorageButton = document.getElementById('extractSessionStorageButton');
  const cookiesData = document.getElementById('cookiesData');
  const localStorageData = document.getElementById('localStorageData');
  const sessionStorageData = document.getElementById('sessionStorageData');
  const appUrlInput = document.getElementById('appUrlInput');
  const saveUrlButton = document.getElementById('saveUrlButton');
  const sendDataButton = document.getElementById('sendDataButton');
  
  // 存储提取的数据
  let extractedData = {
    cookies: null,
    localStorage: null,
    sessionStorage: null
  };
  
  // 从存储中加载应用URL
  chrome.storage.sync.get(['appUrl'], function(result) {
    if (result.appUrl) {
      appUrlInput.value = result.appUrl;
    }
  });
  
  // 检查当前标签页是否是TAPD页面
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    const currentUrl = tabs[0].url;
    if (!currentUrl.includes('tapd.cn')) {
      updateStatus('warning', '当前页面不是TAPD页面，请在TAPD页面使用此插件');
      extractCookiesButton.disabled = true;
      extractLocalStorageButton.disabled = true;
      extractSessionStorageButton.disabled = true;
    }
  });
  
  // 提取Cookies按钮点击事件
  extractCookiesButton.addEventListener('click', function() {
    updateStatus('', '正在提取Cookies...');
    
    // 获取TAPD的Cookie
    chrome.cookies.getAll({domain: 'tapd.cn'}, function(cookies) {
      if (cookies && cookies.length > 0) {
        // 格式化Cookie数据
        const cookieData = {};
        cookies.forEach(cookie => {
          cookieData[cookie.name] = cookie.value;
        });
        
        // 存储提取的Cookie数据
        extractedData.cookies = cookieData;
        
        // 显示Cookie数据
        cookiesData.innerHTML = `<pre>${JSON.stringify(cookieData, null, 2)}</pre>`;
        
        // 更新状态
        updateStatus('success', `成功提取${cookies.length}个Cookies`);
        
        // 启用发送按钮
        updateSendButtonState();
      } else {
        cookiesData.innerHTML = `<pre>未找到TAPD Cookies，请确保您已登录TAPD</pre>`;
        updateStatus('error', '未找到TAPD Cookies');
      }
    });
  });
  
  // 提取Local Storage按钮点击事件
  extractLocalStorageButton.addEventListener('click', function() {
    updateStatus('', '正在提取Local Storage...');
    
    // 在当前标签页执行脚本提取Local Storage
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.scripting.executeScript({
        target: {tabId: tabs[0].id},
        function: getLocalStorage
      }, function(results) {
        if (chrome.runtime.lastError) {
          localStorageData.innerHTML = `<pre>提取失败: ${chrome.runtime.lastError.message}</pre>`;
          updateStatus('error', '提取Local Storage失败');
          return;
        }
        
        const data = results[0].result;
        if (data && Object.keys(data).length > 0) {
          // 存储提取的Local Storage数据
          extractedData.localStorage = data;
          
          // 显示Local Storage数据
          localStorageData.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
          
          // 更新状态
          updateStatus('success', `成功提取${Object.keys(data).length}个Local Storage项`);
          
          // 启用发送按钮
          updateSendButtonState();
        } else {
          localStorageData.innerHTML = `<pre>未找到TAPD相关的Local Storage数据</pre>`;
          updateStatus('warning', '未找到TAPD相关的Local Storage数据');
        }
      });
    });
  });
  
  // 提取Session Storage按钮点击事件
  extractSessionStorageButton.addEventListener('click', function() {
    updateStatus('', '正在提取Session Storage...');
    
    // 在当前标签页执行脚本提取Session Storage
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.scripting.executeScript({
        target: {tabId: tabs[0].id},
        function: getSessionStorage
      }, function(results) {
        if (chrome.runtime.lastError) {
          sessionStorageData.innerHTML = `<pre>提取失败: ${chrome.runtime.lastError.message}</pre>`;
          updateStatus('error', '提取Session Storage失败');
          return;
        }
        
        const data = results[0].result;
        if (data && Object.keys(data).length > 0) {
          // 存储提取的Session Storage数据
          extractedData.sessionStorage = data;
          
          // 显示Session Storage数据
          sessionStorageData.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
          
          // 更新状态
          updateStatus('success', `成功提取${Object.keys(data).length}个Session Storage项`);
          
          // 启用发送按钮
          updateSendButtonState();
        } else {
          sessionStorageData.innerHTML = `<pre>未找到TAPD相关的Session Storage数据</pre>`;
          updateStatus('warning', '未找到TAPD相关的Session Storage数据');
        }
      });
    });
  });
  
  // 保存URL按钮点击事件
  saveUrlButton.addEventListener('click', function() {
    const appUrl = appUrlInput.value.trim();
    if (!appUrl) {
      updateStatus('error', '请输入有效的应用URL');
      return;
    }
    
    // 保存到存储
    chrome.storage.sync.set({appUrl: appUrl}, function() {
      updateStatus('success', '应用URL已保存');
    });
  });
  
  // 发送数据按钮点击事件
  sendDataButton.addEventListener('click', function() {
    const appUrl = appUrlInput.value.trim();
    if (!appUrl) {
      updateStatus('error', '请先设置应用接收URL');
      return;
    }
    
    // 获取当前标签页URL
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      const currentUrl = tabs[0].url;
      
      // 准备发送的数据
      const data = {
        url: currentUrl,
        cookies: extractedData.cookies,
        localStorage: extractedData.localStorage,
        sessionStorage: extractedData.sessionStorage
      };
      
      // 发送数据到应用
      sendToApp(appUrl, data);
    });
  });
  
  // 标签切换功能
  const tabs = document.querySelectorAll('.tab');
  tabs.forEach(tab => {
    tab.addEventListener('click', function() {
      // 移除所有标签的active类
      tabs.forEach(t => t.classList.remove('active'));
      // 添加当前标签的active类
      this.classList.add('active');
      
      // 隐藏所有标签内容
      document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
      });
      
      // 显示当前标签内容
      const tabName = this.getAttribute('data-tab');
      document.getElementById(`${tabName}-tab`).classList.add('active');
    });
  });
  
  // 更新发送按钮状态
  function updateSendButtonState() {
    if (extractedData.cookies || extractedData.localStorage || extractedData.sessionStorage) {
      sendDataButton.disabled = false;
    } else {
      sendDataButton.disabled = true;
    }
  }
  
  // 更新状态消息
  function updateStatus(type, message) {
    statusMessage.className = 'status ' + type;
    statusMessage.textContent = message;
  }
  
  // 发送数据到应用
  function sendToApp(url, data) {
    updateStatus('', '正在发送数据...');
    
    fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    })
    .then(response => {
      if (response.ok) {
        return response.json();
      }
      throw new Error('请求失败，状态码: ' + response.status);
    })
    .then(data => {
      updateStatus('success', '认证数据已成功发送到应用');
      console.log('应用响应:', data);
    })
    .catch(error => {
      updateStatus('error', '发送失败: ' + error.message);
      console.error('发送错误:', error);
    });
  }
});

// 获取Local Storage的函数
function getLocalStorage() {
  const data = {};
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    try {
      // 尝试解析JSON
      const value = localStorage.getItem(key);
      try {
        data[key] = JSON.parse(value);
      } catch (e) {
        data[key] = value;
      }
    } catch (e) {
      data[key] = "无法读取";
    }
  }
  return data;
}

// 获取Session Storage的函数
function getSessionStorage() {
  const data = {};
  for (let i = 0; i < sessionStorage.length; i++) {
    const key = sessionStorage.key(i);
    try {
      // 尝试解析JSON
      const value = sessionStorage.getItem(key);
      try {
        data[key] = JSON.parse(value);
      } catch (e) {
        data[key] = value;
      }
    } catch (e) {
      data[key] = "无法读取";
    }
  }
  return data;
}
