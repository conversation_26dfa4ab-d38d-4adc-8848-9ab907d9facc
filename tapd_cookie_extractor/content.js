// 监听来自后台脚本的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.action === 'getLocalStorage') {
    // 提取Local Storage数据
    const data = {};
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      try {
        // 尝试解析JSON
        const value = localStorage.getItem(key);
        try {
          data[key] = JSON.parse(value);
        } catch (e) {
          data[key] = value;
        }
      } catch (e) {
        data[key] = "无法读取";
      }
    }
    sendResponse(data);
  }
  
  if (request.action === 'getSessionStorage') {
    // 提取Session Storage数据
    const data = {};
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      try {
        // 尝试解析JSON
        const value = sessionStorage.getItem(key);
        try {
          data[key] = JSON.parse(value);
        } catch (e) {
          data[key] = value;
        }
      } catch (e) {
        data[key] = "无法读取";
      }
    }
    sendResponse(data);
  }
  
  return true; // 保持消息通道开放以支持异步响应
});
