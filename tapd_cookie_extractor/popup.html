<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>TAPD <PERSON>ie Extractor</title>
  <style>
    body {
      width: 350px;
      padding: 15px;
      font-family: Arial, sans-serif;
    }
    .header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
    }
    .header img {
      width: 24px;
      height: 24px;
      margin-right: 10px;
    }
    .header h1 {
      font-size: 16px;
      margin: 0;
      color: #333;
    }
    .status {
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 15px;
      background-color: #f5f5f5;
      font-size: 14px;
    }
    .status.success {
      background-color: #e6f4ea;
      color: #137333;
    }
    .status.error {
      background-color: #fce8e6;
      color: #c5221f;
    }
    .status.warning {
      background-color: #fef7e0;
      color: #b06000;
    }
    button {
      background-color: #4285f4;
      color: white;
      border: none;
      padding: 10px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
      width: 100%;
      margin-bottom: 10px;
      font-size: 14px;
    }
    button:hover {
      background-color: #3367d6;
    }
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    .app-url {
      margin-top: 15px;
    }
    .app-url label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
      font-size: 14px;
    }
    .app-url input {
      width: 100%;
      padding: 8px;
      box-sizing: border-box;
      margin-bottom: 10px;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-size: 14px;
    }
    .data-container {
      margin-top: 15px;
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 10px;
      background-color: #f9f9f9;
    }
    .data-container pre {
      margin: 0;
      white-space: pre-wrap;
      word-break: break-all;
      font-size: 12px;
      font-family: monospace;
    }
    .footer {
      font-size: 12px;
      color: #666;
      margin-top: 15px;
      text-align: center;
    }
    .tabs {
      display: flex;
      margin-bottom: 10px;
    }
    .tab {
      padding: 8px 12px;
      cursor: pointer;
      border: 1px solid #ddd;
      border-bottom: none;
      border-radius: 4px 4px 0 0;
      background-color: #f5f5f5;
      margin-right: 5px;
    }
    .tab.active {
      background-color: #fff;
      border-bottom: 1px solid #fff;
      margin-bottom: -1px;
      position: relative;
      z-index: 1;
    }
    .tab-content {
      display: none;
      border: 1px solid #ddd;
      padding: 10px;
      border-radius: 0 4px 4px 4px;
    }
    .tab-content.active {
      display: block;
    }
  </style>
</head>
<body>
  <div class="header">
    <img src="images/icon48.png" alt="Logo">
    <h1>TAPD Cookie Extractor</h1>
  </div>
  
  <div id="statusMessage" class="status">
    准备就绪
  </div>
  
  <div class="tabs">
    <div class="tab active" data-tab="cookies">Cookies</div>
    <div class="tab" data-tab="localStorage">Local Storage</div>
    <div class="tab" data-tab="sessionStorage">Session Storage</div>
  </div>
  
  <div id="cookies-tab" class="tab-content active">
    <button id="extractCookiesButton">提取TAPD Cookies</button>
    <div id="cookiesData" class="data-container">
      <pre>尚未提取数据</pre>
    </div>
  </div>
  
  <div id="localStorage-tab" class="tab-content">
    <button id="extractLocalStorageButton">提取TAPD Local Storage</button>
    <div id="localStorageData" class="data-container">
      <pre>尚未提取数据</pre>
    </div>
  </div>
  
  <div id="sessionStorage-tab" class="tab-content">
    <button id="extractSessionStorageButton">提取TAPD Session Storage</button>
    <div id="sessionStorageData" class="data-container">
      <pre>尚未提取数据</pre>
    </div>
  </div>
  
  <div class="app-url">
    <label for="appUrlInput">应用接收URL:</label>
    <input type="text" id="appUrlInput" placeholder="http://localhost:9999/api/v1/requirement/tapd/auth">
    <button id="saveUrlButton">保存URL设置</button>
  </div>
  
  <button id="sendDataButton" disabled>发送认证数据到应用</button>
  
  <div class="footer">
    <p>此插件可提取TAPD认证信息并发送到您的应用</p>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
