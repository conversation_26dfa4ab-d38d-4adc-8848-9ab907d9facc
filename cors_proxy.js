/**
 * 简单的CORS代理服务器
 * 用于解决浏览器插件的跨域请求问题
 */

const http = require('http');
const https = require('https');
const url = require('url');

// 配置
const PORT = 8888;
const TARGET_HOST = '************';
const TARGET_PORT = 9999;
const TARGET_PATH = '/api/v1/reqAgent/tapd/parse';

// 创建HTTP服务器
const server = http.createServer((req, res) => {
  // 允许所有来源的跨域请求
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  // 处理OPTIONS预检请求
  if (req.method === 'OPTIONS') {
    res.writeHead(204);
    res.end();
    return;
  }
  
  // 只处理POST请求到/proxy路径
  if (req.method === 'POST' && req.url === '/proxy') {
    let body = '';
    
    // 接收请求数据
    req.on('data', chunk => {
      body += chunk.toString();
    });
    
    // 处理完整请求
    req.on('end', () => {
      console.log('收到代理请求');
      
      try {
        // 解析请求体
        const requestData = JSON.parse(body);
        console.log(`请求数据: ${JSON.stringify(requestData).substring(0, 100)}...`);
        
        // 准备转发到目标服务器的请求选项
        const options = {
          hostname: TARGET_HOST,
          port: TARGET_PORT,
          path: TARGET_PATH,
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        };
        
        // 创建请求到目标服务器
        const proxyReq = http.request(options, proxyRes => {
          console.log(`目标服务器响应状态码: ${proxyRes.statusCode}`);
          
          // 设置响应头
          res.writeHead(proxyRes.statusCode, proxyRes.headers);
          
          // 转发目标服务器的响应
          proxyRes.pipe(res);
        });
        
        // 处理请求错误
        proxyReq.on('error', error => {
          console.error(`代理请求错误: ${error.message}`);
          res.writeHead(500);
          res.end(JSON.stringify({ error: `代理请求失败: ${error.message}` }));
        });
        
        // 发送请求体到目标服务器
        proxyReq.write(JSON.stringify(requestData));
        proxyReq.end();
        
      } catch (error) {
        console.error(`处理请求失败: ${error.message}`);
        res.writeHead(400);
        res.end(JSON.stringify({ error: `请求处理失败: ${error.message}` }));
      }
    });
  } else {
    // 对于其他请求，返回简单的欢迎页面
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>CORS代理服务器</title>
          <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
            h1 { color: #333; }
            pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
          </style>
        </head>
        <body>
          <h1>CORS代理服务器</h1>
          <p>这是一个简单的CORS代理服务器，用于解决浏览器插件的跨域请求问题。</p>
          <p>使用方法:</p>
          <pre>
POST /proxy
Content-Type: application/json

{
  "url": "https://www.tapd.cn/...",
  "title": "需求标题",
  "content": "需求内容...",
  "token": "your-token"
}
          </pre>
        </body>
      </html>
    `);
  }
});

// 启动服务器
server.listen(PORT, () => {
  console.log(`CORS代理服务器运行在 http://localhost:${PORT}`);
  console.log(`代理目标: http://${TARGET_HOST}:${TARGET_PORT}${TARGET_PATH}`);
});
