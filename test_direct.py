"""
直接测试图片解析功能
"""
import os
import sys
import traceback

# 添加当前目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

try:
    # 导入需要的函数
    from app.api.v1.reqAgent.tapdAgent import html_to_markdown, parse_requirement

    # 设置环境变量，启用图片分析
    os.environ["ENABLE_IMAGE_ANALYSIS"] = "true"
    
    # 测试HTML内容
    html_content = """
    <div class="tapd-detail-content">
        <h1>需求标题：用户管理功能</h1>
        <div class="description">
            <p>本需求主要实现系统的用户管理功能</p>
            <p>包含用户的增删改查等基本操作</p>
        </div>
        <div class="requirement-details">
            <h2>功能点</h2>
            <ul>
                <li>用户注册</li>
                <li>用户登录</li>
                <li>用户信息修改</li>
                <li>用户权限管理</li>
            </ul>
            <h2>界面设计</h2>
            <p>界面需要简洁大方符合整体风格</p>
            <img src="https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7" alt="用户界面示例">
            <p>上图为用户界面参考设计</p>
            <h2>技术要求</h2>
            <p>前端使用Vue框架开发</p>
            <p>后端使用Spring Boot</p>
            <p>数据库使用MySQL</p>
        </div>
    </div>
    """
    
    print("="*50)
    print("直接测试图片解析功能")
    print("="*50)
    
    # 测试html_to_markdown函数
    print("\n1. 测试html_to_markdown函数")
    markdown_result = html_to_markdown(html_content)
    print("\nMarkdown转换结果:")
    print("-"*50)
    print(markdown_result)
    print("-"*50)
    
    # 检查是否包含图片解析内容
    if "<!-- 图片分析 -->" in markdown_result or "<!-- 示例图分析 -->" in markdown_result:
        print("\n✅ 成功: Markdown结果包含图片分析内容")
    else:
        print("\n❌ 失败: Markdown结果不包含图片分析内容")
    
    # 测试parse_requirement函数
    print("\n2. 测试parse_requirement函数")
    requirement = parse_requirement(
        url="https://www.tapd.cn/12345",
        title="测试需求标题",
        content=html_content
    )
    
    print("\n需求解析结果:")
    print("-"*50)
    print(f"ID: {requirement.id}")
    print(f"TAPD ID: {requirement.tapd_id}")
    print(f"名称: {requirement.name}")
    print(f"内容长度: {len(requirement.content)}")
    print(f"内容前200字符: {requirement.content[:200]}...")
    print("-"*50)
    
    # 检查是否包含图片解析内容
    if "<!-- 图片分析 -->" in requirement.content or "<!-- 示例图分析 -->" in requirement.content:
        print("\n✅ 成功: 需求内容包含图片分析内容")
    else:
        print("\n❌ 失败: 需求内容不包含图片分析内容")
    
    print("\n"+"="*50)
    print("测试完成")
    print("="*50)
    
except Exception as e:
    print(f"\n❌ 测试失败: {str(e)}")
    traceback.print_exc()
