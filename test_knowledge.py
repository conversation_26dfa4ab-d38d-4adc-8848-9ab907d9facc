import logging
import asyncio
from app.controllers.knowledge import knowledge_controller
from app.schemas.knowledge import KnowledgeItemCreate

logging.basicConfig(level=logging.DEBUG)

async def test():
    try:
        item = await knowledge_controller.create_with_creator(
            obj_in=KnowledgeItemCreate(
                title='测试知识条目',
                content='这是一个测试知识条目的内容',
                project_id=1
            ),
            creator_id=1
        )
        print(f'创建成功: {item}')
    except Exception as e:
        print(f'创建失败: {e}')

if __name__ == "__main__":
    asyncio.run(test())
