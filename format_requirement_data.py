import csv
import sys

# 读取原始数据
with open('requirement_data.csv', 'r') as f:
    data = f.readlines()

# 处理数据并写入新文件
with open('formatted_requirement_data.csv', 'w', newline='') as f:
    writer = csv.writer(f)
    for line in data:
        # 分割行，但保留字段内的管道符号
        fields = line.strip().split('|')
        if len(fields) >= 16:  # 确保有足够的字段
            writer.writerow(fields)

print("数据格式化完成，已保存到 formatted_requirement_data.csv")
