<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectInspectionProfilesVisibleTreeState">
    <entry key="Project Default">
      <profile-state>
        <expanded-state>
          <State />
          <State>
            <id>Python</id>
          </State>
        </expanded-state>
        <selected-state>
          <State>
            <id>Python</id>
          </State>
        </selected-state>
      </profile-state>
    </entry>
  </component>
  <component name="ProjectRootManager" version="2" project-jdk-name="needle_autoTest" project-jdk-type="Python SDK" />
</project>