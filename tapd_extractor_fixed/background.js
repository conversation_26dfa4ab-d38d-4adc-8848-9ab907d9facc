// 插件安装或更新时执行
chrome.runtime.onInstalled.addListener(function() {
  console.log('TAPD Content Extractor 已安装/更新');
});

// 监听来自弹出窗口或内容脚本的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  // 处理获取Cookie的请求
  if (request.action === 'getTapdCookies') {
    chrome.cookies.getAll({domain: 'tapd.cn'}, function(cookies) {
      sendResponse({cookies: cookies});
    });
    return true; // 保持消息通道开放以支持异步响应
  }

  // 处理获取图片的请求
  if (request.action === 'fetchImage') {
    fetchImageWithCookies(request.url)
      .then(base64 => sendResponse({success: true, data: base64}))
      .catch(error => {
        console.error('Background: 获取图片失败', error);
        sendResponse({success: false, error: error.message});
      });
    return true; // 保持消息通道开放以支持异步响应
  }

  // 处理提取内容的请求
  if (request.action === 'extractContent') {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      if (tabs.length === 0) {
        sendResponse({error: '没有活动标签页'});
        return;
      }

      const activeTab = tabs[0];
      chrome.tabs.sendMessage(activeTab.id, {action: 'extractContent'}, function(response) {
        sendResponse(response);
      });
    });
    return true; // 保持消息通道开放以支持异步响应
  }

  // 处理代理请求 - 新增功能
  if (request.action === 'proxyRequest') {
    console.log('Background: 收到代理请求');
    console.log('Background: 目标URL:', request.targetUrl);
    console.log('Background: 请求数据长度:', JSON.stringify(request.data).length);

    // 重试函数
    const fetchWithRetry = async (url, options, maxRetries = 3) => {
      let lastError;

      for (let i = 0; i < maxRetries; i++) {
        try {
          console.log(`Background: 尝试请求 (${i + 1}/${maxRetries})`);
          const response = await fetch(url, options);

          console.log('Background: 服务器响应状态:', response.status);
          if (!response.ok) {
            throw new Error('服务器响应错误: ' + response.status);
          }

          return await response.json();
        } catch (error) {
          console.warn(`Background: 请求失败 (${i + 1}/${maxRetries}):`, error.message);
          lastError = error;

          // 等待一段时间再重试
          if (i < maxRetries - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
          }
        }
      }

      throw lastError;
    };

    // 发送请求到目标服务器，带重试
    fetchWithRetry(
      request.targetUrl,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request.data)
      }
    )
    .then(data => {
      console.log('Background: 服务器响应数据长度:', JSON.stringify(data).length);
      sendResponse({success: true, data: data});
    })
    .catch(error => {
      console.error('Background: 代理请求最终失败:', error);

      // 尝试使用XMLHttpRequest作为备选方案
      console.log('Background: 尝试使用XMLHttpRequest作为备选方案');

      const xhr = new XMLHttpRequest();
      xhr.open('POST', request.targetUrl, true);
      xhr.setRequestHeader('Content-Type', 'application/json');

      xhr.onload = function() {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const data = JSON.parse(xhr.responseText);
            console.log('Background: XMLHttpRequest成功，响应数据长度:', xhr.responseText.length);
            sendResponse({success: true, data: data});
          } catch (e) {
            console.error('Background: 解析XMLHttpRequest响应失败:', e);
            sendResponse({success: false, error: '解析响应失败: ' + e.message});
          }
        } else {
          console.error('Background: XMLHttpRequest失败，状态码:', xhr.status);
          sendResponse({success: false, error: 'XMLHttpRequest失败: ' + xhr.status});
        }
      };

      xhr.onerror = function() {
        console.error('Background: XMLHttpRequest网络错误');
        sendResponse({success: false, error: 'XMLHttpRequest网络错误'});
      };

      xhr.send(JSON.stringify(request.data));
    });

    return true; // 保持消息通道开放以支持异步响应
  }
});

// 使用Cookie获取图片并转换为Base64
async function fetchImageWithCookies(url) {
  console.log('Background: 开始获取图片', url);

  try {
    // 处理TAPD特殊图片URL
    let processedUrl = url;

    // 处理compress图片URL，将其转换为原始图片URL
    if (url.includes('compress/compress_img') && url.includes('?src=')) {
      const srcMatch = url.match(/\?src=([^&]+)/);
      if (srcMatch && srcMatch[1]) {
        processedUrl = decodeURIComponent(srcMatch[1]);
        console.log(`Background: 处理compress图片URL，原始URL: ${processedUrl}`);
      }
    }

    // 获取所有TAPD相关的Cookie
    const cookies = await new Promise(resolve => {
      chrome.cookies.getAll({domain: 'tapd.cn'}, resolve);
    });

    // 构建Cookie字符串
    const cookieString = cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');
    console.log('Background: 获取到Cookie数量', cookies.length);

    // 检查是否有必要的cookie
    const hasTapdSession = cookieString.includes('tapdsession');
    const hasTU = cookieString.includes('t_u');
    console.log(`Background: 是否包含tapdsession: ${hasTapdSession}, 是否包含t_u: ${hasTU}`);

    if (!hasTapdSession || !hasTU) {
      console.warn('Background: 缺少必要的cookie，可能无法正确获取图片');
      // 尝试使用占位图片替代
      return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxHgA6+8BfVCwEQAAAABJRU5ErkJggg==';
    }

    // 发送请求
    const response = await fetch(processedUrl, {
      method: 'GET',
      headers: {
        'Cookie': cookieString,
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
        'Referer': 'https://www.tapd.cn/',
        'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
        'Origin': 'https://www.tapd.cn',
        'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'Sec-Fetch-Dest': 'image',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site'
      },
      credentials: 'include',
      redirect: 'follow' // 自动跟随重定向
    });

    if (!response.ok) {
      console.warn(`Background: HTTP响应不成功: ${response.status}`);
      // 尝试使用占位图片替代
      return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBgPAOvvAX1QsBEAAAAASUVORK5CYII=';
    }

    const blob = await response.blob();

    // 检查blob类型
    if (!blob.type.startsWith('image/')) {
      console.warn(`Background: 获取的内容不是图片，而是 ${blob.type}，大小: ${blob.size} 字节`);
      // 尝试使用占位图片替代
      return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBgPAOvvAX1QsBEAAAAASUVORK5CYII=';
    }

    console.log(`Background: 成功获取图片，类型: ${blob.type}，大小: ${blob.size} 字节`);

    // 转换为Base64
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.error('Background: 获取图片失败', error);
    // 出错时返回占位图片
    return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBgPAOvvAX1QsBEAAAAASUVORK5CYII=';
  }
}

// 当标签页更新时，检查是否是TAPD页面
chrome.tabs.onUpdated.addListener(function(tabId, changeInfo, tab) {
  if (changeInfo.status === 'complete' && tab.url && tab.url.includes('tapd.cn')) {
    // 更新图标状态，表示当前页面是TAPD
    chrome.action.setBadgeText({
      text: 'TAPD',
      tabId: tabId
    });
    chrome.action.setBadgeBackgroundColor({
      color: '#4285F4',
      tabId: tabId
    });
  } else if (changeInfo.status === 'complete') {
    // 清除图标状态
    chrome.action.setBadgeText({
      text: '',
      tabId: tabId
    });
  }
});
