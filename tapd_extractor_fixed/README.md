# TAPD Content Extractor Chrome插件

这个Chrome插件可以帮助您从TAPD页面提取内容，并将其发送到您的应用程序。

## 功能特点

- 提取TAPD需求、任务、缺陷等页面的内容
- 自动获取TAPD的Cookie信息
- 将提取的内容发送到您指定的应用接口
- 简单易用的用户界面

## 安装步骤

1. 下载插件文件夹
2. 打开Chrome浏览器，进入扩展程序页面：`chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择插件文件夹（tapd_extractor）
6. 插件将被安装到Chrome浏览器中

## 使用方法

1. 登录TAPD网站
2. 打开需要提取内容的TAPD页面（需求、任务或缺陷详情页）
3. 点击Chrome工具栏中的插件图标
4. 在弹出窗口中设置您的应用接收URL（例如：`http://localhost:9999/api/v1/reqAgent/tapd/parse`）
5. 点击"保存URL设置"按钮
6. 设置认证令牌（默认为`dev`，或者使用有效的JWT令牌）
7. 点击"保存令牌设置"按钮
8. 点击"提取当前TAPD页面内容"按钮
9. 插件将提取页面内容并发送到您的应用

## 应用接收API格式

您的应用需要提供一个接收API，接受以下格式的POST请求：

```json
{
  "url": "https://www.tapd.cn/...",
  "title": "需求标题",
  "content": "需求内容HTML",
  "cookies": "TAPD Cookie字符串",
  "user_id": 1,
  "project_id": 2
}
```

## 注意事项

- 插件需要访问TAPD网站的Cookie，请确保您了解相关的隐私影响
- 插件仅在TAPD页面上有效
- 确保您的应用接收API支持CORS，允许来自Chrome扩展的请求

## 故障排除

- 如果提取失败，请检查您是否已登录TAPD
- 如果发送失败，请检查应用接收URL是否正确
- 查看浏览器控制台以获取更详细的错误信息
