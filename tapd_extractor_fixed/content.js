// 监听来自后台脚本的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  console.log('收到消息:', request);

  if (request.action === 'extractContent') {
    // 提取页面内容
    const enableBase64 = request.settings && request.settings.enableBase64 !== undefined ?
                         request.settings.enableBase64 : true;

    console.log(`收到提取内容请求，启用图片Base64编码: ${enableBase64}`);

    // 异步提取内容
    extractPageContent(enableBase64).then(content => {
      console.log('内容提取成功:', content);
      // 添加success字段
      content.success = true;
      sendResponse(content);
    }).catch(error => {
      console.error('提取内容失败:', error);
      sendResponse({ error: error.message });
    });

    return true; // 保持消息通道开放以支持异步响应
  }
});

// 提取页面内容的函数
async function extractPageContent(enableBase64 = true) {
  // 检测页面类型并提取内容
  let pageType = 'unknown';
  let title = document.title;
  let content = '';

  // 需求详情页
  if (window.location.href.includes('/story/detail/')) {
    pageType = 'story';

    // 提取标题
    const titleElement = document.querySelector('.story-title');
    if (titleElement) {
      title = titleElement.textContent.trim();
    }

    // 提取内容
    const contentElement = document.querySelector('.story-content');
    if (contentElement) {
      content = contentElement.innerHTML;
    } else {
      // 尝试其他可能的内容选择器
      const descElement = document.querySelector('.description-content');
      if (descElement) {
        content = descElement.innerHTML;
      }
    }
  }
  // 任务详情页
  else if (window.location.href.includes('/task/detail/')) {
    pageType = 'task';

    // 提取标题
    const titleElement = document.querySelector('.task-title');
    if (titleElement) {
      title = titleElement.textContent.trim();
    }

    // 提取内容
    const contentElement = document.querySelector('.task-content');
    if (contentElement) {
      content = contentElement.innerHTML;
    } else {
      // 尝试其他可能的内容选择器
      const descElement = document.querySelector('.description-content');
      if (descElement) {
        content = descElement.innerHTML;
      }
    }
  }
  // 缺陷详情页
  else if (window.location.href.includes('/bug/detail/')) {
    pageType = 'bug';

    // 提取标题
    const titleElement = document.querySelector('.bug-title');
    if (titleElement) {
      title = titleElement.textContent.trim();
    }

    // 提取内容
    const contentElement = document.querySelector('.bug-content');
    if (contentElement) {
      content = contentElement.innerHTML;
    } else {
      // 尝试其他可能的内容选择器
      const descElement = document.querySelector('.description-content');
      if (descElement) {
        content = descElement.innerHTML;
      }
    }
  }

  // 如果没有找到内容，尝试通用方法
  if (!content) {
    // 尝试获取页面主要内容
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
      content = mainContent.innerHTML;
    } else {
      content = document.body.innerHTML;
    }
  }

  // 如果启用了Base64编码，将内容中的图片转换为Base64编码
  if (enableBase64) {
    console.log('启用图片Base64编码，开始处理图片...');
    try {
      const processedContent = await processImagesInContent(content);
      return {
        type: pageType,
        title: title,
        content: processedContent,
        success: true
      };
    } catch (error) {
      console.error('处理图片失败:', error);
      return {
        type: pageType,
        title: title,
        content: content,
        success: true,
        warning: '图片处理失败，返回原始内容'
      };
    }
  } else {
    console.log('图片Base64编码已禁用，跳过图片处理');
    return {
      type: pageType,
      title: title,
      content: content,
      success: true
    };
  }
}

/**
 * 将HTML内容中的图片转换为Base64编码
 * @param {string} htmlContent - HTML内容
 * @returns {Promise<string>} - 处理后的HTML内容
 */
async function processImagesInContent(htmlContent) {
  console.log('开始处理HTML内容中的图片');

  try {
    // 创建一个临时的DOM元素来解析HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;

    // 检测是否已经包含Base64编码的图片
    const hasBase64Images = htmlContent.includes('data:image');
    const hasHtmlBase64 = htmlContent.includes('data:text/html;base64');
    console.log(`原始HTML内容${hasBase64Images ? '包含' : '不包含'}Base64图片，${hasHtmlBase64 ? '包含' : '不包含'}HTML Base64数据`);

    // 获取当前页面的cookie
    const pageCookies = document.cookie;
    console.log(`当前页面cookie长度: ${pageCookies.length}`);

    // 检查是否有必要的cookie
    const hasTapdSession = pageCookies.includes('tapdsession');
    const hasTU = pageCookies.includes('t_u');
    console.log(`是否包含tapdsession: ${hasTapdSession}, 是否包含t_u: ${hasTU}`);

    if (!hasTapdSession || !hasTU) {
      console.warn('缺少必要的cookie，可能无法正确获取图片');
    }

    // 获取所有图片元素
    const images = tempDiv.querySelectorAll('img');
    console.log(`找到 ${images.length} 张图片`);

    // 如果没有图片，直接返回原始内容
    if (images.length === 0) {
      return htmlContent;
    }

    // 转换每个图片
    for (let i = 0; i < images.length; i++) {
      const img = images[i];
      try {
        // 检查是否是压缩图片库的图片
        const isCompressGallery = img.classList &&
                                (img.classList.contains('compress') ||
                                 img.classList.contains('gallery'));

        // 如果是压缩图片库的图片，优先处理
        if (isCompressGallery) {
          console.log(`发现压缩图片库图片: ${img.src}`);
          console.log(`原始图片URL: ${img.getAttribute('original_src')}`);
          console.log(`图片类名: ${img.className}`);

          // 先尝试使用src属性中的原始URL
          let realImageUrl = null;

          // 处理compress图片URL，将其转换为原始图片URL
          if (img.src.includes('compress/compress_img') && img.src.includes('?src=')) {
            const srcMatch = img.src.match(/\?src=([^&]+)/);
            if (srcMatch && srcMatch[1]) {
              realImageUrl = decodeURIComponent(srcMatch[1]);
              console.log(`从压缩URL提取原始URL: ${realImageUrl}`);
            }
          }

          // 如果从压缩URL无法提取，则使用original_src属性
          if (!realImageUrl) {
            realImageUrl = img.getAttribute('original_src');
            console.log(`使用original_src属性: ${realImageUrl}`);
          }

          // 如果还是没有，尝试使用href属性
          if (!realImageUrl) {
            realImageUrl = img.getAttribute('href');
            console.log(`使用href属性: ${realImageUrl}`);
          }

          if (realImageUrl) {
            try {
              // 使用background.js获取图片（带Cookie）
              const base64Data = await fetchImageWithBackground(realImageUrl);
              if (base64Data) {
                img.src = base64Data;
                console.log(`成功将压缩图片库图片替换为Base64: ${i+1}/${images.length}`);
                continue;
              }
            } catch (err) {
              console.error(`处理压缩图片库图片失败: ${err.message}`);
            }
          }
        }

        // 检查是否是TAPD特殊格式的图片
        if (img.src.startsWith('data:text/html;base64')) {
          console.log(`发现错误格式的图片: ${img.src.substring(0, 50)}...`);

          // 尝译从原始URL或href属性获取真实图片URL
          const originalSrc = img.getAttribute('original_src');
          const hrefAttr = img.getAttribute('href');

          let realImageUrl = null;

          // 棃选可能的图片URL
          if (originalSrc && (originalSrc.includes('.png') || originalSrc.includes('.jpg') ||
              originalSrc.includes('.jpeg') || originalSrc.includes('.gif'))) {
            realImageUrl = originalSrc;
            console.log(`使用original_src属性作为图片源: ${realImageUrl}`);
          } else if (hrefAttr && (hrefAttr.includes('.png') || hrefAttr.includes('.jpg') ||
                     hrefAttr.includes('.jpeg') || hrefAttr.includes('.gif'))) {
            realImageUrl = hrefAttr;
            console.log(`使用href属性作为图片源: ${realImageUrl}`);
          }

          if (realImageUrl) {
            try {
              // 创建一个新的Image对象来加载真实图片
              const tempImg = new Image();
              tempImg.crossOrigin = 'Anonymous';

              // 使用Promise待图片加载完成
              const imageData = await new Promise((resolve, reject) => {
                tempImg.onload = function() {
                  try {
                    const canvas = document.createElement('canvas');
                    canvas.width = tempImg.naturalWidth || tempImg.width;
                    canvas.height = tempImg.naturalHeight || tempImg.height;

                    if (canvas.width === 0 || canvas.height === 0) {
                      reject(new Error('图片尺寸为0'));
                      return;
                    }

                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(tempImg, 0, 0, canvas.width, canvas.height);

                    // 确保使用正确的MIME类型
                    const mimeType = realImageUrl.toLowerCase().endsWith('.png') ? 'image/png' : 'image/jpeg';
                    resolve(canvas.toDataURL(mimeType, 0.8));
                  } catch (err) {
                    reject(err);
                  }
                };

                tempImg.onerror = () => reject(new Error('图片加载失败'));

                // 添加缓存破坏参数
                tempImg.src = realImageUrl + '?t=' + new Date().getTime();
              });

              // 如果成功获取图片数据，则替换原始src
              img.src = imageData;
              console.log(`成功将HTML Base64替换为图片Base64: ${i+1}/${images.length}`);
            } catch (err) {
              console.error(`处理TAPD特殊图片失败: ${err.message}`);
              // 如果处理失败，使用alt文本代替
              const altText = img.getAttribute('alt') || '需求文档图片';
              img.outerHTML = `<span>[${altText}]</span>`;
            }
          } else {
            // 如果找不到真实图片URL，使用alt文本代替
            const altText = img.getAttribute('alt') || '需求文档图片';
            img.outerHTML = `<span>[${altText}]</span>`;
            console.warn(`无法获取图片的真实URL，使用文本替代: ${altText}`);
          }
          continue;
        }

        // 已经是正确的Base64图片格式，跳过
        if (img.src.startsWith('data:image')) {
          console.log(`图片已经是Base64格式，跳过: ${img.src.substring(0, 30)}...`);
          continue;
        }

        // 确保图片有alt属性
        if (!img.alt || img.alt.trim() === '') {
          img.alt = '需求文档图片';
        }

        // 记录原始URL
        console.log(`处理图片 ${i+1}/${images.length}: ${img.src}`);
        const originalSrc = img.src;

        // 转换图片
        const base64Data = await convertImageToBase64(img);

        // 检查是否成功转换为Base64
        if (base64Data && base64Data.startsWith('data:')) {
          // 替换原始src为Base64编码
          img.src = base64Data;
          console.log(`图片 ${i+1}/${images.length} 成功转换为Base64`);
        } else if (base64Data && base64Data.startsWith('[')) {
          // 如枞返回的是文本替代，则替换整个img标签
          img.outerHTML = `<span>${base64Data}</span>`;
          console.log(`图片 ${i+1}/${images.length} 替换为文本`);
        } else {
          console.warn(`图片 ${i+1}/${images.length} 转换失败，保留原始URL: ${originalSrc}`);
        }
      } catch (error) {
        console.error(`处理图片 ${i+1}/${images.length} 失败: ${error.message}`);
      }
    }

    console.log('所有图片处理完成');

    // 返回处理后的HTML内容
    const processedHtml = tempDiv.innerHTML;

    // 检查是否有图片被转换为Base64
    const hasBase64ImagesAfter = processedHtml.includes('data:image');
    const hasHtmlBase64After = processedHtml.includes('data:text/html;base64');
    console.log(`处理后的HTML内容${hasBase64ImagesAfter ? '包含' : '不包含'}Base64图片，${hasHtmlBase64After ? '还有' : '不再有'}HTML Base64数据`);

    return processedHtml;
  } catch (error) {
    console.error(`处理HTML内容中的图片失败: ${error.message}`);
    return htmlContent; // 出错时返回原始内容
  }
}

/**
 * 将图片转换为Base64编码
 * @param {HTMLImageElement} imgElement - 图片元素
 * @returns {Promise<string>} - Base64编码的图片
 */
async function convertImageToBase64(imgElement) {
  return new Promise(async (resolve) => {
    try {
      // 检查是否是TAPD的特殊图片URL
      const isTapdImage = imgElement.src.includes('tapd.cn') ||
                         imgElement.hasAttribute('original_src') &&
                         imgElement.getAttribute('original_src').includes('tapd.cn');

      // 检查是否是压缩图片库的图片
      const isCompressGallery = imgElement.classList &&
                               (imgElement.classList.contains('compress') ||
                                imgElement.classList.contains('gallery'));

      // 如果是压缩图片库的图片，打印详细信息
      if (isCompressGallery) {
        console.log(`发现压缩图片库图片: ${imgElement.src}`);
        console.log(`原始图片URL: ${imgElement.getAttribute('original_src')}`);
        console.log(`图片类名: ${imgElement.className}`);
      }

      // 如果是TAPD图片，优先使用original_src属性
      const originalSrc = imgElement.getAttribute('original_src');
      const actualSrc = originalSrc && originalSrc.includes('tapd.cn') ? originalSrc : imgElement.src;

      console.log(`处理图片: ${actualSrc}, 是否TAPD图片: ${isTapdImage}`);

      // 如果图片已经是Base64格式，直接返回
      if (actualSrc.startsWith('data:image')) {
        console.log('图片已经是Base64格式，直接返回');
        resolve(actualSrc);
        return;
      }

      // 如果是data:text/html;base64格式，这是错误的格式，需要修复
      if (actualSrc.startsWith('data:text/html;base64')) {
        console.warn('检测到错误的Base64格式(HTML而非图片)，尝试获取原始图片URL');

        // 尝试从 href 或 original_src 属性获取真实图片URL
        const hrefAttr = imgElement.getAttribute('href');
        const originalSrcAttr = imgElement.getAttribute('original_src');

        let realImageUrl = null;

        if (originalSrcAttr && (originalSrcAttr.includes('.png') || originalSrcAttr.includes('.jpg') ||
                              originalSrcAttr.includes('.jpeg') || originalSrcAttr.includes('.gif'))) {
          console.log(`使用original_src属性作为图片源: ${originalSrcAttr}`);
          realImageUrl = originalSrcAttr;
        } else if (hrefAttr && (hrefAttr.includes('.png') || hrefAttr.includes('.jpg') ||
                             hrefAttr.includes('.jpeg') || hrefAttr.includes('.gif'))) {
          console.log(`使用href属性作为图片源: ${hrefAttr}`);
          realImageUrl = hrefAttr;
        }

        if (realImageUrl) {
          // 使用background.js获取图片（带Cookie）
          const base64Data = await fetchImageWithBackground(realImageUrl);
          if (base64Data) {
            console.log(`成功获取图片，数据长度: ${base64Data.length}`);
            resolve(base64Data);
            return;
          }
        }

        console.warn('无法获取有效的图片URL，返回替代文本');
        // 返回一个包含alt文本的占位图片
        const altText = imgElement.getAttribute('alt') || '图片';
        resolve(`[${altText}]`);
        return;
      }

      // 如果是TAPD图片，使用background.js获取（带Cookie）
      if (isTapdImage) {
        console.log(`使用background.js获取TAPD图片: ${actualSrc}`);
        const base64Data = await fetchImageWithBackground(actualSrc);
        if (base64Data) {
          console.log(`成功获取TAPD图片，数据长度: ${base64Data.length}`);
          resolve(base64Data);
          return;
        }
      }

      // 如果使用background.js获取失败或不是TAPD图片，使用旧的方法
      console.log(`使用旧的方法获取图片: ${actualSrc}`);
      return fetchAndConvertImage(actualSrc, resolve);
    } catch (error) {
      console.error(`转换图片失败: ${error.message}`);
      resolve(imgElement.src); // 出错时返回原始src
    }
  });
}

// 辅助函数：获取并转换图片
function fetchAndConvertImage(url, resolve) {
  try {
    // 创建一个新的Image对象
    const img = new Image();
    img.crossOrigin = 'Anonymous'; // 关键！允许跨域加载

    // 处理加载完成事件
    img.onload = function() {
      try {
        // 创建Canvas元素
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // 设置Canvas大小
        canvas.width = img.naturalWidth || img.width;
        canvas.height = img.naturalHeight || img.height;

        // 检查图片尺寸
        if (canvas.width === 0 || canvas.height === 0) {
          console.warn(`图片尺寸为0: ${url}`);
          resolve(url);
          return;
        }

        // 在Canvas上绘制图片
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

        try {
          // 将Canvas内容转换为Base64，确保使用正确的MIME类型
          const dataURL = canvas.toDataURL('image/jpeg', 0.8);

          // 检查大小
          const maxSize = 2 * 1024 * 1024; // 2MB
          if (dataURL.length > maxSize) {
            console.warn(`图片太大，压缩处理: ${url}, 大小: ${dataURL.length} 字节`);
            // 尝试更高压缩率
            const compressedDataURL = canvas.toDataURL('image/jpeg', 0.5);
            if (compressedDataURL.length <= maxSize) {
              console.log(`压缩成功: ${compressedDataURL.length} 字节`);
              resolve(compressedDataURL);
              return;
            } else {
              console.warn(`压缩后仍然太大，返回原始URL`);
              resolve(url);
              return;
            }
          }

          console.log(`成功转换图片为Base64: ${url}, 大小: ${dataURL.length} 字节`);
          resolve(dataURL);
        } catch (canvasError) {
          // 如果图片被标记为"tainted"，则会抛出安全错误
          console.error(`Canvas安全错误: ${canvasError.message}`);
          // 尝试使用fetch API
          fetchImageAsBase64(url).then(base64 => {
            if (base64) {
              resolve(base64);
            } else {
              resolve(url);
            }
          }).catch(() => resolve(url));
        }
      } catch (error) {
        console.error(`处理图片时出错: ${error.message}`);
        resolve(url); // 出错时返回原始URL
      }
    };

    // 处理加载错误
    img.onerror = function() {
      console.warn(`图片加载失败: ${url}`);
      // 尝试使用fetch API
      fetchImageAsBase64(url).then(base64 => {
        if (base64) {
          resolve(base64);
        } else {
          resolve(url);
        }
      }).catch(() => resolve(url));
    };

    // 设置图片源，添加随机参数避免缓存
    const cacheBuster = '?t=' + new Date().getTime();
    img.src = url + cacheBuster;
  } catch (error) {
    console.error(`处理图片时出错: ${error.message}`);
    resolve(url); // 出错时返回原始URL
  }
}

// 使用fetch API获取图片并转换为Base64
async function fetchImageAsBase64(url) {
  try {
    console.log(`尝试使用fetch获取图片: ${url}`);

    // 构建请求头，包含所有必要的信息
    const headers = {
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
      'Accept-Language': 'zh-CN,zh;q=0.9',
      'Cache-Control': 'max-age=0',
      'Connection': 'keep-alive',
      'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
      'Referer': 'https://www.tapd.cn/',
      'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"macOS"',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'none',
      'Sec-Fetch-User': '?1',
      'Upgrade-Insecure-Requests': '1'
    };

    // 获取当前页面的cookie
    const cookies = document.cookie;
    console.log(`当前页面cookie长度: ${cookies.length}`);

    // 检查是否有必要的cookie
    const hasTapdSession = cookies.includes('tapdsession');
    const hasTU = cookies.includes('t_u');
    console.log(`是否包含tapdsession: ${hasTapdSession}, 是否包含t_u: ${hasTU}`);

    // 如果缺少必要的cookie，尝试使用图片元素直接获取
    if (!hasTapdSession || !hasTU) {
      console.warn('缺少必要的cookie，尝试使用图片元素直接获取');
      return await fetchImageWithImgElement(url);
    }

    // 发送请求获取图片
    const response = await fetch(url, {
      method: 'GET',
      headers: headers,
      credentials: 'include', // 关键！包含cookie
      cache: 'no-cache',
      mode: 'cors'
    });

    if (!response.ok) {
      console.warn(`HTTP响应不成功: ${response.status}, 尝试其他方法`);
      return await fetchImageWithImgElement(url);
    }

    const blob = await response.blob();

    // 检查blob类型，确保是图片
    if (!blob.type.startsWith('image/')) {
      console.warn(`获取的内容不是图片，而是 ${blob.type}，大小: ${blob.size} 字节`);

      // 如果不是图片，尝试使用图片元素直接获取
      return await fetchImageWithImgElement(url);
    }

    console.log(`成功获取图片，类型: ${blob.type}，大小: ${blob.size} 字节`);

    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.error(`使用fetch获取图片失败: ${error.message}`);
    // 尝试使用图片元素直接获取
    return await fetchImageWithImgElement(url);
  }
}

// 使用background.js获取图片（带Cookie）
async function fetchImageWithBackground(url) {
  return new Promise((resolve, reject) => {
    console.log(`尝试使用background.js获取图片: ${url}`);

    // 检查是否已经是Base64格式
    if (url.startsWith('data:')) {
      console.log('图片已经是Base64格式，直接返回');
      resolve(url);
      return;
    }

    // 处理TAPD特殊图片URL
    let processedUrl = url;

    // 处理compress图片URL，将其转换为原始图片URL
    if (url.includes('compress/compress_img') && url.includes('?src=')) {
      const srcMatch = url.match(/\?src=([^&]+)/);
      if (srcMatch && srcMatch[1]) {
        processedUrl = decodeURIComponent(srcMatch[1]);
        console.log(`处理compress图片URL，原始URL: ${processedUrl}`);
      }
    }

    // 发送消息给background.js
    chrome.runtime.sendMessage({action: 'fetchImage', url: processedUrl}, response => {
      if (response && response.success) {
        console.log(`成功使用background.js获取图片，数据长度: ${response.data.length}`);
        resolve(response.data);
      } else {
        console.error(`使用background.js获取图片失败: ${response ? response.error : '未知错误'}`);
        // 如果失败，尝试使用图片元素直接获取
        console.log('尝试备选方法获取图片...');

        // 尝试多种方法获取图片
        Promise.all([
          fetchImageWithImgElement(processedUrl).catch(() => null),
          fetchImageWithXHR(processedUrl).catch(() => null)
        ])
        .then(results => {
          // 使用第一个成功的结果
          const successResult = results.find(result => result !== null);
          if (successResult) {
            console.log('备选方法成功获取图片');
            resolve(successResult);
          } else {
            console.warn('所有获取图片的方法都失败，使用占位图片');
            // 使用占位图片
            resolve('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBgPAOvvAX1QsBEAAAAASUVORK5CYII=');
          }
        });
      }
    });
  });
}

// 使用图片元素直接获取图片（备选方法）
async function fetchImageWithImgElement(url) {
  return new Promise((resolve) => {
    console.log(`尝试使用图片元素直接获取图片: ${url}`);

    // 检查是否已经是Base64格式
    if (url.startsWith('data:')) {
      console.log('图片已经是Base64格式，直接返回');
      resolve(url);
      return;
    }

    // 创建一个新的图片元納
    const img = new Image();

    // 不设置跨域属性，让浏览器使用当前会话的Cookie
    // img.crossOrigin = 'anonymous'; // 移除这一行

    // 设置超时
    const timeoutId = setTimeout(() => {
      console.warn(`图片加载超时: ${url}`);
      resolve(null);
    }, 15000); // 增加超时时间到15秒

    // 图片加载成功
    img.onload = function() {
      clearTimeout(timeoutId);

      try {
        // 创建画布并绘制图片
        const canvas = document.createElement('canvas');
        canvas.width = img.naturalWidth || img.width;
        canvas.height = img.naturalHeight || img.height;

        if (canvas.width === 0 || canvas.height === 0) {
          console.warn(`图片尺寸为0: ${url}`);
          resolve(null);
          return;
        }

        const ctx = canvas.getContext('2d');

        try {
          // 尝试绘制图片
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

          // 尝试获取数据并转换为Base64
          const dataURL = canvas.toDataURL('image/png');
          console.log(`成功使用图片元素获取图片，尺寸: ${img.width}x${img.height}`);
          resolve(dataURL);
        } catch (securityError) {
          // 如果出现跨域安全错误，尝试使用其他方法
          console.error(`安全错误: ${securityError.message}`);
          resolve(null);
        }
      } catch (error) {
        console.error(`使用图片元納获取图片失败: ${error.message}`);
        resolve(null);
      }
    };

    // 图片加载失败
    img.onerror = function() {
      clearTimeout(timeoutId);
      console.error(`图片加载失败: ${url}`);
      resolve(null);
    };

    // 添加随机参数避免缓存
    const cacheBuster = `?t=${new Date().getTime()}`;
    img.src = url.includes('?') ? `${url}&_=${Date.now()}` : `${url}${cacheBuster}`;
  });
}

// 使用XMLHttpRequest获取图片（备选方案）
async function fetchImageWithXHR(url) {
  return new Promise((resolve, reject) => {
    console.log(`尝试使用XMLHttpRequest获取图片: ${url}`);

    const xhr = new XMLHttpRequest();
    xhr.open('GET', url, true);
    xhr.responseType = 'blob';
    xhr.withCredentials = true; // 包含cookie

    // 设置请求头
    xhr.setRequestHeader('Accept', 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7');
    xhr.setRequestHeader('Accept-Language', 'zh-CN,zh;q=0.9');
    xhr.setRequestHeader('Cache-Control', 'max-age=0');
    xhr.setRequestHeader('User-Agent', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36');
    xhr.setRequestHeader('Referer', 'https://www.tapd.cn/');

    xhr.onload = function() {
      if (xhr.status === 200) {
        const blob = xhr.response;

        // 检查blob类型
        if (!blob.type.startsWith('image/')) {
          console.warn(`XHR: 获取的内容不是图片，而是 ${blob.type}，大小: ${blob.size} 字节`);
          // 如果不是图片，返回null
          resolve(null);
          return;
        }

        console.log(`XHR: 成功获取图片，类型: ${blob.type}，大小: ${blob.size} 字节`);

        const reader = new FileReader();
        reader.onloadend = function() {
          resolve(reader.result);
        };
        reader.onerror = function() {
          reject(new Error('读取blob失败'));
        };
        reader.readAsDataURL(blob);
      } else {
        reject(new Error(`XHR error! status: ${xhr.status}`));
      }
    };

    xhr.onerror = function() {
      reject(new Error('XHR请求失败'));
    };

    xhr.send();
  }).catch(error => {
    console.error(`使用XMLHttpRequest获取图片失败: ${error.message}`);
    return null;
  });
}
