<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>TAPD Content Extractor</title>
  <style>
    body {
      width: 320px;
      padding: 15px;
      font-family: Arial, sans-serif;
    }
    .header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
    }
    .header img {
      width: 24px;
      height: 24px;
      margin-right: 10px;
    }
    .header h1 {
      font-size: 16px;
      margin: 0;
      color: #333;
    }
    .status {
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 15px;
      background-color: #f5f5f5;
      font-size: 14px;
    }
    .status.success {
      background-color: #e6f4ea;
      color: #137333;
    }
    .status.error {
      background-color: #fce8e6;
      color: #c5221f;
    }
    .status.warning {
      background-color: #fef7e0;
      color: #b06000;
    }
    button {
      background-color: #4285f4;
      color: white;
      border: none;
      padding: 10px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
      width: 100%;
      margin-bottom: 10px;
      font-size: 14px;
    }
    button:hover {
      background-color: #3367d6;
    }
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    .app-url {
      margin-top: 15px;
    }
    .app-url label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
      font-size: 14px;
    }
    .app-url input {
      width: 100%;
      padding: 8px;
      box-sizing: border-box;
      margin-bottom: 10px;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-size: 14px;
    }
    .footer {
      font-size: 12px;
      color: #666;
      margin-top: 15px;
      text-align: center;
    }
  </style>
</head>
<body>
  <div class="header">
    <img src="images/icon48.png" alt="Logo">
    <h1>TAPD Content Extractor</h1>
  </div>

  <div id="statusMessage" class="status">
    准备就绪
  </div>

  <button id="extractButton">提取当前TAPD页面内容</button>

  <div class="app-url">
    <label for="appUrlInput">应用接收URL:</label>
    <input type="text" id="appUrlInput" placeholder="http://localhost:9999/api/v1/reqAgent/tapd/parse">
    <button id="saveUrlButton">保存URL设置</button>
  </div>

  <div class="app-url">
    <label for="tokenInput">认证令牌:</label>
    <input type="text" id="tokenInput" placeholder="dev或有效的JWT令牌">
    <button id="saveTokenButton">保存令牌设置</button>
  </div>

  <div class="app-url">
    <label>
      <input type="checkbox" id="enableBase64" checked>
      启用图片Base64编码
    </label>
    <p style="font-size: 12px; color: #666; margin-top: 5px;">
      将页面中的图片转换为Base64编码，便于后端分析图片内容。
    </p>
  </div>

  <div class="footer">
    <p>此插件可提取TAPD页面内容并发送到您的应用</p>
  </div>

  <!-- 调试信息区域 -->
  <div id="debug-info" style="margin-top: 10px; padding: 10px; border: 1px solid #ccc; border-radius: 4px; max-height: 200px; overflow: auto; font-size: 12px; font-family: monospace;"></div>

  <script src="popup.js"></script>
</body>
</html>
