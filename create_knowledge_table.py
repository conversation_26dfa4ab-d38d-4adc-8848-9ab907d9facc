import asyncio
import logging
import os
from tortoise import Tortoise
from app.settings import TORTOISE_ORM

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def create_knowledge_table():
    """创建知识库表"""
    # 连接数据库
    await Tortoise.init(config=TORTOISE_ORM)

    # 创建知识库表的SQL
    create_table_sql = """
    -- 创建知识库条目表
    CREATE TABLE IF NOT EXISTS knowledge_items (
        id BIGSERIAL PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        item_type VARCHAR(50) NOT NULL DEFAULT '其他',
        tags VARCHAR(255),
        source VARCHAR(50) NOT NULL DEFAULT '手动添加',
        project_id INT NOT NULL,
        creator_id INT,
        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
    );

    -- 创建索引
    CREATE INDEX IF NOT EXISTS idx_knowledge_items_project_id ON knowledge_items(project_id);
    CREATE INDEX IF NOT EXISTS idx_knowledge_items_creator_id ON knowledge_items(creator_id);
    CREATE INDEX IF NOT EXISTS idx_knowledge_items_item_type ON knowledge_items(item_type);
    CREATE INDEX IF NOT EXISTS idx_knowledge_items_created_at ON knowledge_items(created_at);
    CREATE INDEX IF NOT EXISTS idx_knowledge_items_updated_at ON knowledge_items(updated_at);
    """

    # 添加API权限记录的SQL
    add_api_permissions_sql = """
    -- 添加API权限记录
    DO $$
    DECLARE
        next_id INT;
    BEGIN
        -- 获取下一个ID值
        SELECT COALESCE(MAX(id), 0) + 1 INTO next_id FROM api;

        -- 添加API权限记录
        INSERT INTO api (id, path, method, summary, tags, created_at, updated_at)
        SELECT next_id + row_number() OVER () - 1, t.path, t.method, t.summary, t.tags, NOW(), NOW()
        FROM (
            VALUES
                ('/knowledge/', 'GET', '获取知识条目列表', '知识库管理'),
                ('/knowledge/{item_id}', 'GET', '获取单个知识条目详情', '知识库管理'),
                ('/knowledge/', 'POST', '添加新的知识条目', '知识库管理'),
                ('/knowledge/{item_id}', 'PUT', '更新指定的知识条目', '知识库管理'),
                ('/knowledge/{item_id}', 'DELETE', '删除指定的知识条目', '知识库管理')
        ) AS t(path, method, summary, tags)
        WHERE NOT EXISTS (
            SELECT 1 FROM api WHERE path = t.path AND method = t.method
        );
    END $$;
    """

    # 执行SQL
    conn = Tortoise.get_connection("default")
    try:
        logger.info("创建知识库表...")
        await conn.execute_script(create_table_sql)
        logger.info("知识库表创建成功")

        logger.info("添加API权限记录...")
        await conn.execute_script(add_api_permissions_sql)
        logger.info("API权限记录添加成功")
    except Exception as e:
        logger.error(f"执行SQL失败: {str(e)}")

    # 关闭连接
    await Tortoise.close_connections()

async def create_knowledge_menu():
    """创建知识库菜单"""
    # 连接数据库
    await Tortoise.init(config=TORTOISE_ORM)

    # 创建知识库菜单的SQL
    create_menu_sql = """
    -- 查找测试模块的父菜单ID
    DO $$
    DECLARE
        testing_parent_id INT;
        max_order INT;
        knowledge_menu_exists INT;
    BEGIN
        -- 查找测试模块的父菜单ID
        SELECT id INTO testing_parent_id FROM menu WHERE name = '测试管理' AND parent_id = 0 LIMIT 1;

        -- 如果测试管理菜单不存在，则创建它
        IF testing_parent_id IS NULL THEN
            -- 查找最大的排序值
            SELECT COALESCE(MAX("order"), 0) INTO max_order FROM menu WHERE parent_id = 0;

            -- 创建测试管理父菜单
            INSERT INTO menu (
                name, menu_type, icon, path, "order", parent_id, is_hidden, component, keepalive, redirect, created_at, updated_at
            ) VALUES (
                '测试管理', 'catalog', 'carbon:test-tool', '/testing', max_order + 1, 0, 0, 'Layout', 0, '/testing/projects', NOW(), NOW()
            ) RETURNING id INTO testing_parent_id;

            RAISE NOTICE '创建了测试管理父菜单，ID: %', testing_parent_id;
        ELSE
            RAISE NOTICE '找到测试管理父菜单，ID: %', testing_parent_id;
        END IF;

        -- 检查知识库菜单是否已存在
        SELECT COUNT(*) INTO knowledge_menu_exists FROM menu WHERE name = '项目知识库' AND parent_id = testing_parent_id;

        -- 如果知识库菜单不存在，则创建它
        IF knowledge_menu_exists = 0 THEN
            -- 查找测试管理下最大的排序值
            SELECT COALESCE(MAX("order"), 0) INTO max_order FROM menu WHERE parent_id = testing_parent_id;

            -- 创建知识库菜单
            INSERT INTO menu (
                name, menu_type, icon, path, "order", parent_id, is_hidden, component, keepalive, redirect, created_at, updated_at
            ) VALUES (
                '项目知识库', 'menu', 'mdi:book-open-page-variant', 'knowledge', max_order + 1, testing_parent_id, 0, '/testing/knowledge', 1, NULL, NOW(), NOW()
            );

            RAISE NOTICE '创建了项目知识库菜单';
        ELSE
            RAISE NOTICE '项目知识库菜单已存在';
        END IF;
    END $$;
    """

    # 执行SQL
    conn = Tortoise.get_connection("default")
    try:
        logger.info("创建知识库菜单...")
        await conn.execute_script(create_menu_sql)
        logger.info("知识库菜单创建成功")
    except Exception as e:
        logger.error(f"执行SQL失败: {str(e)}")

    # 关闭连接
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(create_knowledge_table())
    asyncio.run(create_knowledge_menu())
