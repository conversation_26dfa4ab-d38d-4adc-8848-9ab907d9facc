#!/usr/bin/env python
"""
PostgreSQL数据库迁移工具

此脚本用于将一个PostgreSQL数据库的内容迁移到另一个PostgreSQL数据库。
它使用Python的psycopg2库直接连接数据库，不依赖pg_dump和pg_restore命令。

使用方法:
python db_migration.py --source-host localhost --source-port 5432 --source-user admin --source-password admin123 --source-db agent_testing --target-host target_host --target-port 5432 --target-user admin --target-password new_password --target-db agent_testing_new
"""

import argparse
import sys
import os
from datetime import datetime
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='PostgreSQL数据库迁移工具')

    # 源数据库参数
    parser.add_argument('--source-host', default='localhost', help='源数据库主机地址')
    parser.add_argument('--source-port', type=int, default=5432, help='源数据库端口')
    parser.add_argument('--source-user', default='admin', help='源数据库用户名')
    parser.add_argument('--source-password', default='admin123', help='源数据库密码')
    parser.add_argument('--source-db', default='agent_testing', help='源数据库名称')

    # 目标数据库参数
    parser.add_argument('--target-host', required=True, help='目标数据库主机地址')
    parser.add_argument('--target-port', type=int, default=5432, help='目标数据库端口')
    parser.add_argument('--target-user', required=True, help='目标数据库用户名')
    parser.add_argument('--target-password', required=True, help='目标数据库密码')
    parser.add_argument('--target-db', required=True, help='目标数据库名称')

    # 其他选项
    parser.add_argument('--create-db', action='store_true', help='如果目标数据库不存在，是否创建')
    parser.add_argument('--drop-tables', action='store_true', help='是否在迁移前删除目标数据库中的表')
    parser.add_argument('--only-schema', action='store_true', help='是否只迁移表结构，不迁移数据')
    parser.add_argument('--batch-size', type=int, default=1000, help='每批处理的数据行数')
    parser.add_argument('--exclude-tables', help='排除的表名，用逗号分隔，例如：audit_log,large_table')
    parser.add_argument('--include-tables', help='仅包含的表名，用逗号分隔，例如：user,project,requirement')
    parser.add_argument('--max-rows', type=int, default=0, help='每个表最多迁移的行数，0表示不限制')

    return parser.parse_args()

def connect_db(host, port, user, password, dbname=None):
    """连接到PostgreSQL数据库"""
    conn_params = {
        'host': host,
        'port': port,
        'user': user,
        'password': password
    }

    if dbname:
        conn_params['dbname'] = dbname

    print(f"尝试连接到数据库: {conn_params}")

    try:
        conn = psycopg2.connect(**conn_params)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        print(f"数据库连接成功: {host}:{port}")
        return conn
    except Exception as e:
        print(f"数据库连接错误: {str(e)}")
        import traceback
        print(traceback.format_exc())
        sys.exit(1)

def create_database(conn, dbname):
    """创建数据库"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"CREATE DATABASE {dbname};")
        cursor.close()
        print(f"数据库 {dbname} 创建成功")
    except Exception as e:
        print(f"创建数据库错误: {str(e)}")
        sys.exit(1)

def check_database_exists(conn, dbname):
    """检查数据库是否存在"""
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s;", (dbname,))
        exists = cursor.fetchone() is not None
        cursor.close()
        return exists
    except Exception as e:
        print(f"检查数据库错误: {str(e)}")
        sys.exit(1)

def get_tables(conn):
    """获取数据库中的所有表"""
    try:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public'
            ORDER BY table_name;
        """)
        tables = [row[0] for row in cursor.fetchall()]
        cursor.close()
        return tables
    except Exception as e:
        print(f"获取表列表错误: {str(e)}")
        sys.exit(1)

def get_table_schema(conn, table):
    """获取表结构"""
    try:
        cursor = conn.cursor()
        # 获取表的创建语句
        cursor.execute(f"""
            SELECT
                column_name,
                data_type,
                character_maximum_length,
                is_nullable,
                column_default
            FROM information_schema.columns
            WHERE table_name = %s
            ORDER BY ordinal_position;
        """, (table,))
        columns = cursor.fetchall()

        # 获取主键信息
        cursor.execute(f"""
            SELECT a.attname
            FROM pg_index i
            JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
            WHERE i.indrelid = %s::regclass AND i.indisprimary;
        """, (table,))
        primary_keys = [row[0] for row in cursor.fetchall()]

        cursor.close()
        return columns, primary_keys
    except Exception as e:
        print(f"获取表结构错误: {str(e)}")
        sys.exit(1)

def create_table(conn, table, columns, primary_keys):
    """创建表"""
    try:
        cursor = conn.cursor()

        # 检查是否需要创建序列
        needs_sequence = False
        sequence_name = f"{table}_id_seq"
        id_column = None

        for col in columns:
            name, data_type, max_length, is_nullable, default = col
            if name == "id" and data_type in ["bigint", "integer"] and default and "nextval" in default:
                needs_sequence = True
                id_column = col
                break

        # 如果需要，创建序列
        if needs_sequence:
            try:
                print(f"创建序列: {sequence_name}")
                cursor.execute(f'CREATE SEQUENCE IF NOT EXISTS "{sequence_name}" START 1;')
            except Exception as seq_error:
                print(f"创建序列错误: {str(seq_error)}")
                # 继续执行，不中断

        # 构建CREATE TABLE语句
        create_sql = f'CREATE TABLE IF NOT EXISTS "{table}" (\n'
        column_defs = []

        for col in columns:
            name, data_type, max_length, is_nullable, default = col

            # 处理数据类型
            if max_length is not None:
                type_str = f"{data_type}({max_length})"
            else:
                type_str = data_type

            # 处理NULL约束
            null_str = "NULL" if is_nullable == "YES" else "NOT NULL"

            # 处理默认值
            default_str = ""
            if default is not None:
                if name == "id" and needs_sequence:
                    default_str = f"DEFAULT nextval('{sequence_name}'::regclass)"
                else:
                    default_str = f"DEFAULT {default}"

            # 组合列定义
            column_def = f'    "{name}" {type_str} {null_str} {default_str}'.strip()
            column_defs.append(column_def)

        # 添加主键约束
        if primary_keys:
            pk_names = [f'"{pk}"' for pk in primary_keys]
            pk_constraint = f'    PRIMARY KEY ({", ".join(pk_names)})'
            column_defs.append(pk_constraint)

        create_sql += ',\n'.join(column_defs)
        create_sql += '\n);'

        # 执行创建表语句
        cursor.execute(create_sql)

        # 如果创建了序列，设置序列的所有者
        if needs_sequence:
            try:
                cursor.execute(f'ALTER SEQUENCE "{sequence_name}" OWNED BY "{table}".id;')
            except Exception as seq_error:
                print(f"设置序列所有者错误: {str(seq_error)}")
                # 继续执行，不中断

        cursor.close()
        print(f"表 {table} 创建成功")
    except Exception as e:
        print(f"创建表错误: {str(e)}")
        print(f"SQL: {create_sql}")
        import traceback
        print(traceback.format_exc())
        # 不退出程序，继续处理下一个表
        return False

    return True

def copy_table_data(source_conn, target_conn, table, batch_size=1000, max_rows=None):
    """复制表数据"""
    try:
        source_cursor = source_conn.cursor()
        target_cursor = target_conn.cursor()

        # 获取表的列名
        source_cursor.execute(f"""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = %s
            ORDER BY ordinal_position;
        """, (table,))
        columns = [row[0] for row in source_cursor.fetchall()]
        columns_str = ', '.join([f'"{col}"' for col in columns])

        # 获取表的总行数
        source_cursor.execute(f'SELECT COUNT(*) FROM "{table}";')
        total_rows = source_cursor.fetchone()[0]

        if total_rows == 0:
            print(f"表 {table} 没有数据，跳过")
            source_cursor.close()
            target_cursor.close()
            return

        # 如果设置了最大行数限制，调整总行数
        if max_rows is not None and max_rows < total_rows:
            print(f"表 {table} 有 {total_rows} 行，但根据限制只会迁移 {max_rows} 行")
            total_rows = max_rows

        # 分批获取和插入数据
        offset = 0
        rows_copied = 0
        while offset < total_rows:
            # 计算本批次应该获取的行数
            current_batch_size = min(batch_size, total_rows - offset)

            source_cursor.execute(f'SELECT * FROM "{table}" LIMIT %s OFFSET %s;', (current_batch_size, offset))
            rows = source_cursor.fetchall()

            if not rows:
                break

            # 构建批量插入语句
            for row in rows:
                # 为每一行创建占位符和值列表
                placeholders = []
                values = []

                for i, col in enumerate(columns):
                    placeholders.append('%s')
                    # 处理JSON和其他复杂类型
                    if isinstance(row[i], dict) or isinstance(row[i], list):
                        import json
                        values.append(json.dumps(row[i]))
                    else:
                        values.append(row[i])

                # 执行单行插入
                insert_sql = f'INSERT INTO "{table}" ({columns_str}) VALUES ({", ".join(placeholders)});'
                try:
                    target_cursor.execute(insert_sql, values)
                    rows_copied += 1
                except Exception as row_error:
                    print(f"插入行数据错误: {str(row_error)}")
                    print(f"表: {table}, 值: {values}")
                    # 继续处理下一行，而不是中止整个过程
                    continue

                # 如果达到最大行数限制，提前结束
                if max_rows is not None and rows_copied >= max_rows:
                    break

            # 如果达到最大行数限制，提前结束
            if max_rows is not None and rows_copied >= max_rows:
                break

            offset += current_batch_size
            print(f"表 {table}: 已复制 {min(rows_copied, total_rows)}/{total_rows} 行")

        source_cursor.close()
        target_cursor.close()
        print(f"表 {table} 数据复制完成，共复制 {rows_copied} 行")
    except Exception as e:
        print(f"复制表数据错误: {str(e)}")
        import traceback
        print(traceback.format_exc())
        # 不退出程序，继续处理下一个表
        return

def main():
    """主函数"""
    args = parse_args()

    print("=== PostgreSQL数据库迁移工具 ===")
    print(f"源数据库: {args.source_host}:{args.source_port}/{args.source_db}")
    print(f"目标数据库: {args.target_host}:{args.target_port}/{args.target_db}")

    # 处理表过滤选项
    exclude_tables = []
    if args.exclude_tables:
        exclude_tables = [t.strip() for t in args.exclude_tables.split(',')]
        print(f"将排除以下表: {', '.join(exclude_tables)}")

    include_tables = []
    if args.include_tables:
        include_tables = [t.strip() for t in args.include_tables.split(',')]
        print(f"仅迁移以下表: {', '.join(include_tables)}")

    if args.max_rows > 0:
        print(f"每个表最多迁移 {args.max_rows} 行数据")

    # 连接到源数据库
    print("\n连接到源数据库...")
    source_conn = connect_db(args.source_host, args.source_port, args.source_user, args.source_password, args.source_db)

    # 检查目标数据库是否存在
    print("\n检查目标数据库...")
    target_server_conn = connect_db(args.target_host, args.target_port, args.target_user, args.target_password)
    db_exists = check_database_exists(target_server_conn, args.target_db)

    if not db_exists:
        if args.create_db:
            print(f"目标数据库 {args.target_db} 不存在，正在创建...")
            create_database(target_server_conn, args.target_db)
        else:
            print(f"目标数据库 {args.target_db} 不存在，请先创建或使用 --create-db 选项")
            sys.exit(1)

    target_server_conn.close()

    # 连接到目标数据库
    print("\n连接到目标数据库...")
    target_conn = connect_db(args.target_host, args.target_port, args.target_user, args.target_password, args.target_db)

    # 获取源数据库中的所有表
    print("\n获取源数据库表结构...")
    all_tables = get_tables(source_conn)

    # 根据过滤条件筛选表
    tables = []
    for table in all_tables:
        if include_tables and table not in include_tables:
            print(f"表 {table} 不在包含列表中，跳过")
            continue
        if table in exclude_tables:
            print(f"表 {table} 在排除列表中，跳过")
            continue
        tables.append(table)

    print(f"将迁移 {len(tables)}/{len(all_tables)} 个表: {', '.join(tables)}")

    # 迁移表结构和数据
    print("\n开始迁移...")
    for table in tables:
        print(f"\n处理表: {table}")
        columns, primary_keys = get_table_schema(source_conn, table)
        table_created = create_table(target_conn, table, columns, primary_keys)

        if table_created and not args.only_schema:
            # 如果设置了最大行数限制，传递给copy_table_data函数
            max_rows = args.max_rows if args.max_rows > 0 else None
            copy_table_data(source_conn, target_conn, table, args.batch_size, max_rows)

    # 关闭连接
    source_conn.close()
    target_conn.close()

    print("\n=== 数据库迁移完成 ===")

if __name__ == "__main__":
    main()
