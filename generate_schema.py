#!/usr/bin/env python
# -*- coding: utf-8 -*-

import asyncio
from tortoise import Tortoise
from app.settings.config import settings
from app.models.knowledge_relation import RequirementKnowledgeRelation

async def generate_schema():
    # 初始化数据库连接
    print("正在初始化数据库连接...")
    await Tortoise.init(config=settings.TORTOISE_ORM)
    print("数据库连接初始化完成")

    # 生成 RequirementKnowledgeRelation 表的 schema
    print("正在生成 RequirementKnowledgeRelation 表的 schema...")
    # 使用 generate_schema 方法，不指定特定模型，让它生成所有模型的 schema
    await Tortoise.generate_schemas(safe=True)
    print("RequirementKnowledgeRelation 表的 schema 生成完成")

    # 关闭数据库连接
    await Tortoise.close_connections()
    print("数据库连接已关闭")

if __name__ == "__main__":
    asyncio.run(generate_schema())
