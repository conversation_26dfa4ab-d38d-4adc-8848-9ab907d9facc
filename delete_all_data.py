#!/usr/bin/env python
"""
删除数据库中的所有需求和测试用例
"""
import asyncio
import logging
from tortoise import Tortoise
from app.settings.config import settings
from app.models.admin import Requirement, TestCase, TestStep

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def init_db():
    """初始化数据库连接"""
    logger.info("正在连接数据库...")
    await Tortoise.init(config=settings.TORTOISE_ORM)
    logger.info("数据库连接成功")

async def close_db():
    """关闭数据库连接"""
    logger.info("正在关闭数据库连接...")
    await Tortoise.close_connections()
    logger.info("数据库连接已关闭")

async def delete_all_test_steps():
    """删除所有测试步骤"""
    logger.info("正在删除所有测试步骤...")
    count = await TestStep.all().count()
    if count > 0:
        deleted_count = await TestStep.all().delete()
        logger.info(f"已删除 {deleted_count} 条测试步骤记录")
    else:
        logger.info("没有测试步骤记录需要删除")
    return count

async def delete_all_test_cases():
    """删除所有测试用例"""
    logger.info("正在删除所有测试用例...")
    count = await TestCase.all().count()
    if count > 0:
        deleted_count = await TestCase.all().delete()
        logger.info(f"已删除 {deleted_count} 条测试用例记录")
    else:
        logger.info("没有测试用例记录需要删除")
    return count

async def delete_all_requirements():
    """删除所有需求"""
    logger.info("正在删除所有需求...")
    count = await Requirement.all().count()
    if count > 0:
        deleted_count = await Requirement.all().delete()
        logger.info(f"已删除 {deleted_count} 条需求记录")
    else:
        logger.info("没有需求记录需要删除")
    return count

async def main():
    """主函数"""
    try:
        await init_db()
        
        # 先删除测试步骤（因为它们依赖于测试用例）
        steps_count = await delete_all_test_steps()
        
        # 然后删除测试用例
        cases_count = await delete_all_test_cases()
        
        # 最后删除需求
        reqs_count = await delete_all_requirements()
        
        logger.info("数据删除完成")
        logger.info(f"总计删除: {steps_count} 条测试步骤, {cases_count} 条测试用例, {reqs_count} 条需求")
        
    except Exception as e:
        logger.error(f"删除数据时发生错误: {str(e)}", exc_info=True)
    finally:
        await close_db()

if __name__ == "__main__":
    asyncio.run(main())
