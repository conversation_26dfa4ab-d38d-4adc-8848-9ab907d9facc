import asyncio
from tortoise import Tortoise
from app.settings.config import TORTOISE_ORM

async def check_requirement_point():
    # Initialize Tortoise ORM
    await Tortoise.init(config=TORTOISE_ORM)
    
    # Get connection
    conn = Tortoise.get_connection("default")
    
    # 检查requirement_point表是否存在
    try:
        result = await conn.execute_query("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'requirement_point'
            );
        """)
        exists = result[1][0][0]
        if exists:
            print("requirement_point表存在")
            
            # 查询表结构
            result = await conn.execute_query("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_schema = 'public' 
                AND table_name = 'requirement_point';
            """)
            print("\n表结构:")
            for row in result[1]:
                print(f"- {row[0]}: {row[1]}")
                
            # 查询数据
            result = await conn.execute_query("SELECT COUNT(*) FROM requirement_point;")
            count = result[1][0][0]
            print(f"\n表中有 {count} 条记录")
            
            if count > 0:
                result = await conn.execute_query("SELECT * FROM requirement_point LIMIT 5;")
                print("\n示例数据:")
                for row in result[1]:
                    print(row)
        else:
            print("requirement_point表不存在")
            
            # 检查是否有其他与需求点相关的表
            result = await conn.execute_query("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name LIKE '%point%';
            """)
            if result[1]:
                print("\n找到可能与需求点相关的表:")
                for row in result[1]:
                    print(f"- {row[0]}")
            else:
                print("\n没有找到与需求点相关的表")
    except Exception as e:
        print(f"查询失败: {e}")
    
    # Close connections
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(check_requirement_point())
