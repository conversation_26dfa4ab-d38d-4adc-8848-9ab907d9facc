#!/usr/bin/env python
"""
为远程数据库的test_cases表添加is_adopted字段

此脚本连接到远程PostgreSQL数据库，并添加is_adopted字段到test_cases表。
"""

import asyncio
import logging
import sys
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 远程数据库连接信息
DB_CONFIG = {
    'host': '************',
    'port': 5432,
    'user': 'admin',
    'password': 'admin123',
    'database': 'agent_testing',
}

def connect_to_db():
    """连接到PostgreSQL数据库"""
    try:
        logger.info(f"正在连接到数据库: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}...")
        conn = psycopg2.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database=DB_CONFIG['database']
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        logger.info("数据库连接成功")
        return conn
    except Exception as e:
        logger.error(f"数据库连接失败: {str(e)}")
        sys.exit(1)

def check_column_exists(conn, table, column):
    """检查表中是否存在指定列"""
    try:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT EXISTS (
                SELECT 1
                FROM information_schema.columns
                WHERE table_name = %s AND column_name = %s
            );
        """, (table, column))
        exists = cursor.fetchone()[0]
        cursor.close()
        return exists
    except Exception as e:
        logger.error(f"检查列是否存在时出错: {str(e)}")
        return False

def add_is_adopted_field(conn):
    """添加is_adopted字段到test_cases表"""
    try:
        # 首先检查字段是否已存在
        if check_column_exists(conn, 'test_cases', 'is_adopted'):
            logger.info("is_adopted字段已存在，无需添加")
            return True

        # 添加字段
        cursor = conn.cursor()
        logger.info("正在添加is_adopted字段...")
        
        # 执行SQL语句添加字段
        cursor.execute("""
            ALTER TABLE test_cases ADD COLUMN is_adopted BOOLEAN NOT NULL DEFAULT TRUE;
        """)
        
        # 添加注释
        cursor.execute("""
            COMMENT ON COLUMN test_cases.is_adopted IS '测试用例是否被采纳。';
        """)
        
        cursor.close()
        logger.info("is_adopted字段添加成功")
        return True
    except Exception as e:
        logger.error(f"添加is_adopted字段失败: {str(e)}")
        return False

def verify_column_added(conn):
    """验证字段是否成功添加"""
    try:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns
            WHERE table_name = 'test_cases' AND column_name = 'is_adopted';
        """)
        column_info = cursor.fetchone()
        cursor.close()
        
        if column_info:
            logger.info(f"字段验证成功: {column_info}")
            return True
        else:
            logger.error("字段验证失败: 未找到is_adopted字段")
            return False
    except Exception as e:
        logger.error(f"字段验证失败: {str(e)}")
        return False

def main():
    """主函数"""
    try:
        # 连接数据库
        conn = connect_to_db()
        
        # 添加字段
        if add_is_adopted_field(conn):
            # 验证字段是否添加成功
            verify_column_added(conn)
        
        # 关闭连接
        conn.close()
        logger.info("数据库连接已关闭")
    except Exception as e:
        logger.error(f"执行过程中发生错误: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
