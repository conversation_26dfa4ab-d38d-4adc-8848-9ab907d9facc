<html lang="cn" style="--primary-color: #0EA5E9; --info-color: #2563EB; --success-color: #10B981; --warning-color: #F59E0B; --error-color: #EF4444; --primary-color-hover: #38BDF8; --error-color-suppl: #EF4444; --error-color-pressed: #DC2626; --error-color-hover: #F87171; --warning-color-suppl: #F59E0B; --warning-color-pressed: #D97706; --warning-color-hover: #FBBF24; --success-color-suppl: #10B981; --success-color-pressed: #059669; --success-color-hover: #34D399; --info-color-suppl: #2563EB; --info-color-pressed: #1D4ED8; --info-color-hover: #3B82F6; --primary-color-suppl: #0EA5E9; --primary-color-pressed: #0284C7;"><head>
    <script type="module" src="/@vite/client"></script>

    <meta charset="UTF-8">
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style cssr-id="n-popconfirm">.n-popconfirm .n-popconfirm__body {

 font-size: var(--n-font-size);
 display: flex;
 align-items: center;
 flex-wrap: nowrap;
 position: relative;
 
}

.n-popconfirm .n-popconfirm__body .n-popconfirm__icon {

 display: flex;
 font-size: var(--n-icon-size);
 color: var(--n-icon-color);
 transition: color .3s var(--n-bezier);
 margin: 0 8px 0 0;
 
}

.n-popconfirm .n-popconfirm__action {

 display: flex;
 justify-content: flex-end;
 
}

.n-popconfirm .n-popconfirm__action:not(:first-child) {
margin-top: 8px
}

.n-popconfirm .n-popconfirm__action .n-button:not(:last-child) {
margin-right: 8px;
}</style><style cssr-id="n-back-top">.n-back-top {

 position: fixed;
 right: 40px;
 bottom: 40px;
 cursor: pointer;
 display: flex;
 align-items: center;
 justify-content: center;
 color: var(--n-text-color);
 transition:
 color .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 border-radius: var(--n-border-radius);
 height: var(--n-height);
 min-width: var(--n-width);
 box-shadow: var(--n-box-shadow);
 background-color: var(--n-color);

}

.n-back-top.fade-in-scale-up-transition-leave-active {
  transform-origin: inherit;
  transition: opacity .2s cubic-bezier(.4, 0, 1, 1), transform .2s cubic-bezier(.4, 0, 1, 1) ;
}

.n-back-top.fade-in-scale-up-transition-enter-active {
  transform-origin: inherit;
  transition: opacity .2s cubic-bezier(0, 0, .2, 1), transform .2s cubic-bezier(0, 0, .2, 1) ;
}

.n-back-top.fade-in-scale-up-transition-enter-from, .n-back-top.fade-in-scale-up-transition-leave-to {
  opacity: 0;
  transform:  scale(.9);
}

.n-back-top.fade-in-scale-up-transition-leave-from, .n-back-top.fade-in-scale-up-transition-enter-to {
  opacity: 1;
  transform:  scale(1);
}

.n-back-top.n-back-top--transition-disabled {
  transition: none !important;
}

.n-back-top .n-base-icon {

 font-size: var(--n-icon-size);
 color: var(--n-icon-color);
 transition: color .3s var(--n-bezier);
 
}

.n-back-top svg {
  pointer-events: none;
}

.n-back-top:hover {
  box-shadow: var(--n-box-shadow-hover);
}

.n-back-top:hover .n-base-icon {
  color: var(--n-icon-color-hover);
}

.n-back-top:active {
  box-shadow: var(--n-box-shadow-pressed);
}

.n-back-top:active .n-base-icon {
  color: var(--n-icon-color-pressed);
}</style><style cssr-id="n-modal">.n-modal-container {

 position: fixed;
 left: 0;
 top: 0;
 height: 0;
 width: 0;
 display: flex;
 
}

.n-modal-mask {

 position: fixed;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 background-color: rgba(0, 0, 0, .4);
 
}

.n-modal-mask.fade-in-transition-enter-active {
  transition: all .25s var(--n-bezier-ease-out)!important;
}

.n-modal-mask.fade-in-transition-leave-active {
  transition: all .25s var(--n-bezier-ease-out)!important;
}

.n-modal-mask.fade-in-transition-enter-from, .n-modal-mask.fade-in-transition-leave-to {
  opacity: 0;
}

.n-modal-mask.fade-in-transition-leave-from, .n-modal-mask.fade-in-transition-enter-to {
  opacity: 1;
}

.n-modal-body-wrapper {

 position: fixed;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 overflow: visible;
 
}

.n-modal-body-wrapper .n-modal-scroll-content {

 min-height: 100%;
 display: flex;
 position: relative;
 
}

.n-modal {

 position: relative;
 align-self: center;
 color: var(--n-text-color);
 margin: auto;
 box-shadow: var(--n-box-shadow);
 
}

.n-modal.fade-in-scale-up-transition-leave-active {
  transform-origin: inherit;
  transition: opacity .25s cubic-bezier(.4, 0, 1, 1), transform .25s cubic-bezier(.4, 0, 1, 1) ;
}

.n-modal.fade-in-scale-up-transition-enter-active {
  transform-origin: inherit;
  transition: opacity .25s cubic-bezier(0, 0, .2, 1), transform .25s cubic-bezier(0, 0, .2, 1) ;
}

.n-modal.fade-in-scale-up-transition-enter-from, .n-modal.fade-in-scale-up-transition-leave-to {
  opacity: 0;
  transform:  scale(.5);
}

.n-modal.fade-in-scale-up-transition-leave-from, .n-modal.fade-in-scale-up-transition-enter-to {
  opacity: 1;
  transform:  scale(1);
}

.n-modal .n-draggable {

 cursor: move;
 user-select: none;
 
}</style><style cssr-id="n-select">.n-select {

 z-index: auto;
 outline: none;
 width: 100%;
 position: relative;
 font-weight: var(--n-font-weight);
 
}

.n-select-menu {

 margin: 4px 0;
 box-shadow: var(--n-menu-box-shadow);
 
}

.n-select-menu.fade-in-scale-up-transition-leave-active {
  transform-origin: inherit;
  transition: opacity .2s cubic-bezier(.4, 0, 1, 1), transform .2s cubic-bezier(.4, 0, 1, 1) ,background-color .3s var(--n-bezier), box-shadow .3s var(--n-bezier);
}

.n-select-menu.fade-in-scale-up-transition-enter-active {
  transform-origin: inherit;
  transition: opacity .2s cubic-bezier(0, 0, .2, 1), transform .2s cubic-bezier(0, 0, .2, 1) ,background-color .3s var(--n-bezier), box-shadow .3s var(--n-bezier);
}

.n-select-menu.fade-in-scale-up-transition-enter-from, .n-select-menu.fade-in-scale-up-transition-leave-to {
  opacity: 0;
  transform:  scale(.9);
}

.n-select-menu.fade-in-scale-up-transition-leave-from, .n-select-menu.fade-in-scale-up-transition-enter-to {
  opacity: 1;
  transform:  scale(1);
}</style><style cssr-id="n-pagination">.n-pagination {

 display: flex;
 vertical-align: middle;
 font-size: var(--n-item-font-size);
 flex-wrap: nowrap;

}

.n-pagination .n-pagination-prefix {

 display: flex;
 align-items: center;
 margin: var(--n-prefix-margin);
 
}

.n-pagination .n-pagination-suffix {

 display: flex;
 align-items: center;
 margin: var(--n-suffix-margin);
 
}

.n-pagination > *:not(:first-child) {

 margin: var(--n-item-margin);
 
}

.n-pagination .n-select {

 width: var(--n-select-width);
 
}

.n-pagination.transition-disabled .n-pagination-item {
transition: none!important;
}

.n-pagination .n-pagination-quick-jumper {

 white-space: nowrap;
 display: flex;
 color: var(--n-jumper-text-color);
 transition: color .3s var(--n-bezier);
 align-items: center;
 font-size: var(--n-jumper-font-size);
 
}

.n-pagination .n-pagination-quick-jumper .n-input {

 margin: var(--n-input-margin);
 width: var(--n-input-width);
 
}

.n-pagination .n-pagination-item {

 position: relative;
 cursor: pointer;
 user-select: none;
 -webkit-user-select: none;
 display: flex;
 align-items: center;
 justify-content: center;
 box-sizing: border-box;
 min-width: var(--n-item-size);
 height: var(--n-item-size);
 padding: var(--n-item-padding);
 background-color: var(--n-item-color);
 color: var(--n-item-text-color);
 border-radius: var(--n-item-border-radius);
 border: var(--n-item-border);
 fill: var(--n-button-icon-color);
 transition:
 color .3s var(--n-bezier),
 border-color .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 fill .3s var(--n-bezier);
 
}

.n-pagination .n-pagination-item.n-pagination-item--button {

 background: var(--n-button-color);
 color: var(--n-button-icon-color);
 border: var(--n-button-border);
 padding: 0;
 
}

.n-pagination .n-pagination-item.n-pagination-item--button .n-base-icon {

 font-size: var(--n-button-icon-size);
 
}

.n-pagination .n-pagination-item:not(.n-pagination-item--disabled).n-pagination-item--hover {

 background: var(--n-item-color-hover);
 color: var(--n-item-text-color-hover);
 border: var(--n-item-border-hover);

}

.n-pagination .n-pagination-item:not(.n-pagination-item--disabled).n-pagination-item--hover.n-pagination-item--button {

 background: var(--n-button-color-hover);
 border: var(--n-button-border-hover);
 color: var(--n-button-icon-color-hover);
 
}

.n-pagination .n-pagination-item:not(.n-pagination-item--disabled):hover {

 background: var(--n-item-color-hover);
 color: var(--n-item-text-color-hover);
 border: var(--n-item-border-hover);

}

.n-pagination .n-pagination-item:not(.n-pagination-item--disabled):hover.n-pagination-item--button {

 background: var(--n-button-color-hover);
 border: var(--n-button-border-hover);
 color: var(--n-button-icon-color-hover);
 
}

.n-pagination .n-pagination-item:not(.n-pagination-item--disabled):active {

 background: var(--n-item-color-pressed);
 color: var(--n-item-text-color-pressed);
 border: var(--n-item-border-pressed);
 
}

.n-pagination .n-pagination-item:not(.n-pagination-item--disabled):active.n-pagination-item--button {

 background: var(--n-button-color-pressed);
 border: var(--n-button-border-pressed);
 color: var(--n-button-icon-color-pressed);
 
}

.n-pagination .n-pagination-item:not(.n-pagination-item--disabled).n-pagination-item--active {

 background: var(--n-item-color-active);
 color: var(--n-item-text-color-active);
 border: var(--n-item-border-active);
 
}

.n-pagination .n-pagination-item:not(.n-pagination-item--disabled).n-pagination-item--active:hover {

 background: var(--n-item-color-active-hover);
 
}

.n-pagination .n-pagination-item.n-pagination-item--disabled {

 cursor: not-allowed;
 color: var(--n-item-text-color-disabled);
 
}

.n-pagination .n-pagination-item.n-pagination-item--disabled.n-pagination-item--active, .n-pagination .n-pagination-item.n-pagination-item--disabled.n-pagination-item--button {

 background-color: var(--n-item-color-disabled);
 border: var(--n-item-border-disabled);
 
}

.n-pagination.n-pagination--disabled {

 cursor: not-allowed;
 
}

.n-pagination.n-pagination--disabled .n-pagination-quick-jumper {

 color: var(--n-jumper-text-color-disabled);
 
}

.n-pagination.n-pagination--simple {

 display: flex;
 align-items: center;
 flex-wrap: nowrap;
 
}

.n-pagination.n-pagination--simple .n-pagination-quick-jumper .n-input {

 margin: 0;
 
}</style><style cssr-id="n-empty">.n-empty {

 display: flex;
 flex-direction: column;
 align-items: center;
 font-size: var(--n-font-size);

}

.n-empty .n-empty__icon {

 width: var(--n-icon-size);
 height: var(--n-icon-size);
 font-size: var(--n-icon-size);
 line-height: var(--n-icon-size);
 color: var(--n-icon-color);
 transition:
 color .3s var(--n-bezier);
 
}

.n-empty .n-empty__icon + .n-empty__description {

 margin-top: 8px;
 
}

.n-empty .n-empty__description {

 transition: color .3s var(--n-bezier);
 color: var(--n-text-color);
 
}

.n-empty .n-empty__extra {

 text-align: center;
 transition: color .3s var(--n-bezier);
 margin-top: 12px;
 color: var(--n-extra-text-color);
 
}</style><style cssr-id="n-ellipsis">.n-ellipsis {
  overflow: hidden;
}

.n-ellipsis:not(.n-ellipsis--line-clamp) {

 white-space: nowrap;
 display: inline-block;
 vertical-align: bottom;
 max-width: 100%;
 
}

.n-ellipsis.n-ellipsis--line-clamp {

 display: -webkit-inline-box;
 -webkit-box-orient: vertical;
 
}

.n-ellipsis.n-ellipsis--cursor-pointer {

 cursor: pointer;
 
}</style><style cssr-id="n-data-table">.n-data-table {

 width: 100%;
 font-size: var(--n-font-size);
 display: flex;
 flex-direction: column;
 position: relative;
 --n-merged-th-color: var(--n-th-color);
 --n-merged-td-color: var(--n-td-color);
 --n-merged-border-color: var(--n-border-color);
 --n-merged-th-color-sorting: var(--n-th-color-sorting);
 --n-merged-td-color-hover: var(--n-td-color-hover);
 --n-merged-td-color-sorting: var(--n-td-color-sorting);
 --n-merged-td-color-striped: var(--n-td-color-striped);
 
}

.n-data-table .n-data-table-wrapper {

 flex-grow: 1;
 display: flex;
 flex-direction: column;
 
}

.n-data-table.n-data-table--flex-height > .n-data-table-wrapper > .n-data-table-base-table {

 display: flex;
 flex-direction: column;
 flex-grow: 1;
 
}

.n-data-table.n-data-table--flex-height > .n-data-table-wrapper > .n-data-table-base-table > .n-data-table-base-table-body {
flex-basis: 0;
}

.n-data-table.n-data-table--flex-height > .n-data-table-wrapper > .n-data-table-base-table > .n-data-table-base-table-body:last-child {
flex-grow: 1;
}

.n-data-table > .n-data-table-loading-wrapper {

 color: var(--n-loading-color);
 font-size: var(--n-loading-size);
 position: absolute;
 left: 50%;
 top: 50%;
 transform: translateX(-50%) translateY(-50%);
 transition: color .3s var(--n-bezier);
 display: flex;
 align-items: center;
 justify-content: center;
 
}

.n-data-table > .n-data-table-loading-wrapper.fade-in-scale-up-transition-leave-active {
  transform-origin: inherit;
  transition: opacity .2s cubic-bezier(.4, 0, 1, 1), transform .2s cubic-bezier(.4, 0, 1, 1) ;
}

.n-data-table > .n-data-table-loading-wrapper.fade-in-scale-up-transition-enter-active {
  transform-origin: inherit;
  transition: opacity .2s cubic-bezier(0, 0, .2, 1), transform .2s cubic-bezier(0, 0, .2, 1) ;
}

.n-data-table > .n-data-table-loading-wrapper.fade-in-scale-up-transition-enter-from, .n-data-table > .n-data-table-loading-wrapper.fade-in-scale-up-transition-leave-to {
  opacity: 0;
  transform: translateX(-50%) translateY(-50%) scale(.9);
}

.n-data-table > .n-data-table-loading-wrapper.fade-in-scale-up-transition-leave-from, .n-data-table > .n-data-table-loading-wrapper.fade-in-scale-up-transition-enter-to {
  opacity: 1;
  transform: translateX(-50%) translateY(-50%) scale(1);
}

.n-data-table .n-data-table-expand-placeholder {

 margin-right: 8px;
 display: inline-block;
 width: 16px;
 height: 1px;
 
}

.n-data-table .n-data-table-indent {

 display: inline-block;
 height: 1px;
 
}

.n-data-table .n-data-table-expand-trigger {

 display: inline-flex;
 margin-right: 8px;
 cursor: pointer;
 font-size: 16px;
 vertical-align: -0.2em;
 position: relative;
 width: 16px;
 height: 16px;
 color: var(--n-td-text-color);
 transition: color .3s var(--n-bezier);
 
}

.n-data-table .n-data-table-expand-trigger.n-data-table-expand-trigger--expanded .n-icon {
transform: rotate(90deg);
}

.n-data-table .n-data-table-expand-trigger.n-data-table-expand-trigger--expanded .n-icon.icon-switch-transition-enter-from, .n-data-table .n-data-table-expand-trigger.n-data-table-expand-trigger--expanded .n-icon.icon-switch-transition-leave-to {
  transform: rotate(90deg) scale(0.75);
  left: 0;
  top: 0;
  opacity: 0;
}

.n-data-table .n-data-table-expand-trigger.n-data-table-expand-trigger--expanded .n-icon.icon-switch-transition-enter-to, .n-data-table .n-data-table-expand-trigger.n-data-table-expand-trigger--expanded .n-icon.icon-switch-transition-leave-from {
  transform: scale(1) rotate(90deg);
  left: 0;
  top: 0;
  opacity: 1;
}

.n-data-table .n-data-table-expand-trigger.n-data-table-expand-trigger--expanded .n-icon.icon-switch-transition-enter-active, .n-data-table .n-data-table-expand-trigger.n-data-table-expand-trigger--expanded .n-icon.icon-switch-transition-leave-active {
  transform-origin: center;
  position: absolute;
  left: 0;
  top: 0;
  transition: all .3s cubic-bezier(.4, 0, .2, 1) !important;
}

.n-data-table .n-data-table-expand-trigger.n-data-table-expand-trigger--expanded .n-base-icon {
transform: rotate(90deg);
}

.n-data-table .n-data-table-expand-trigger.n-data-table-expand-trigger--expanded .n-base-icon.icon-switch-transition-enter-from, .n-data-table .n-data-table-expand-trigger.n-data-table-expand-trigger--expanded .n-base-icon.icon-switch-transition-leave-to {
  transform: rotate(90deg) scale(0.75);
  left: 0;
  top: 0;
  opacity: 0;
}

.n-data-table .n-data-table-expand-trigger.n-data-table-expand-trigger--expanded .n-base-icon.icon-switch-transition-enter-to, .n-data-table .n-data-table-expand-trigger.n-data-table-expand-trigger--expanded .n-base-icon.icon-switch-transition-leave-from {
  transform: scale(1) rotate(90deg);
  left: 0;
  top: 0;
  opacity: 1;
}

.n-data-table .n-data-table-expand-trigger.n-data-table-expand-trigger--expanded .n-base-icon.icon-switch-transition-enter-active, .n-data-table .n-data-table-expand-trigger.n-data-table-expand-trigger--expanded .n-base-icon.icon-switch-transition-leave-active {
  transform-origin: center;
  position: absolute;
  left: 0;
  top: 0;
  transition: all .3s cubic-bezier(.4, 0, .2, 1) !important;
}

.n-data-table .n-data-table-expand-trigger .n-base-loading {

 color: var(--n-loading-color);
 transition: color .3s var(--n-bezier);
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 
}

.n-data-table .n-data-table-expand-trigger .n-base-loading.icon-switch-transition-enter-from, .n-data-table .n-data-table-expand-trigger .n-base-loading.icon-switch-transition-leave-to {
  transform:  scale(0.75);
  left: 0;
  top: 0;
  opacity: 0;
}

.n-data-table .n-data-table-expand-trigger .n-base-loading.icon-switch-transition-enter-to, .n-data-table .n-data-table-expand-trigger .n-base-loading.icon-switch-transition-leave-from {
  transform: scale(1) ;
  left: 0;
  top: 0;
  opacity: 1;
}

.n-data-table .n-data-table-expand-trigger .n-base-loading.icon-switch-transition-enter-active, .n-data-table .n-data-table-expand-trigger .n-base-loading.icon-switch-transition-leave-active {
  transform-origin: center;
  position: absolute;
  left: 0;
  top: 0;
  transition: all .3s cubic-bezier(.4, 0, .2, 1) !important;
}

.n-data-table .n-data-table-expand-trigger .n-icon {

 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 
}

.n-data-table .n-data-table-expand-trigger .n-icon.icon-switch-transition-enter-from, .n-data-table .n-data-table-expand-trigger .n-icon.icon-switch-transition-leave-to {
  transform:  scale(0.75);
  left: 0;
  top: 0;
  opacity: 0;
}

.n-data-table .n-data-table-expand-trigger .n-icon.icon-switch-transition-enter-to, .n-data-table .n-data-table-expand-trigger .n-icon.icon-switch-transition-leave-from {
  transform: scale(1) ;
  left: 0;
  top: 0;
  opacity: 1;
}

.n-data-table .n-data-table-expand-trigger .n-icon.icon-switch-transition-enter-active, .n-data-table .n-data-table-expand-trigger .n-icon.icon-switch-transition-leave-active {
  transform-origin: center;
  position: absolute;
  left: 0;
  top: 0;
  transition: all .3s cubic-bezier(.4, 0, .2, 1) !important;
}

.n-data-table .n-data-table-expand-trigger .n-base-icon {

 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 
}

.n-data-table .n-data-table-expand-trigger .n-base-icon.icon-switch-transition-enter-from, .n-data-table .n-data-table-expand-trigger .n-base-icon.icon-switch-transition-leave-to {
  transform:  scale(0.75);
  left: 0;
  top: 0;
  opacity: 0;
}

.n-data-table .n-data-table-expand-trigger .n-base-icon.icon-switch-transition-enter-to, .n-data-table .n-data-table-expand-trigger .n-base-icon.icon-switch-transition-leave-from {
  transform: scale(1) ;
  left: 0;
  top: 0;
  opacity: 1;
}

.n-data-table .n-data-table-expand-trigger .n-base-icon.icon-switch-transition-enter-active, .n-data-table .n-data-table-expand-trigger .n-base-icon.icon-switch-transition-leave-active {
  transform-origin: center;
  position: absolute;
  left: 0;
  top: 0;
  transition: all .3s cubic-bezier(.4, 0, .2, 1) !important;
}

.n-data-table .n-data-table-thead {

 transition: background-color .3s var(--n-bezier);
 background-color: var(--n-merged-th-color);
 
}

.n-data-table .n-data-table-tr {

 position: relative;
 box-sizing: border-box;
 background-clip: padding-box;
 transition: background-color .3s var(--n-bezier);
 
}

.n-data-table .n-data-table-tr .n-data-table-expand {

 position: sticky;
 left: 0;
 overflow: hidden;
 margin: calc(var(--n-th-padding) * -1);
 padding: var(--n-th-padding);
 box-sizing: border-box;
 
}

.n-data-table .n-data-table-tr.n-data-table-tr--striped {
background-color: var(--n-merged-td-color-striped);
}

.n-data-table .n-data-table-tr.n-data-table-tr--striped .n-data-table-td {
background-color: var(--n-merged-td-color-striped);
}

.n-data-table .n-data-table-tr:not(.n-data-table-tr--summary):hover {
background-color: var(--n-merged-td-color-hover);
}

.n-data-table .n-data-table-tr:not(.n-data-table-tr--summary):hover > .n-data-table-td {
background-color: var(--n-merged-td-color-hover);
}

.n-data-table .n-data-table-th {

 padding: var(--n-th-padding);
 position: relative;
 text-align: start;
 box-sizing: border-box;
 background-color: var(--n-merged-th-color);
 border-color: var(--n-merged-border-color);
 border-bottom: 1px solid var(--n-merged-border-color);
 color: var(--n-th-text-color);
 transition:
 border-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 font-weight: var(--n-th-font-weight);
 
}

.n-data-table .n-data-table-th.n-data-table-th--filterable {

 padding-right: 36px;
 
}

.n-data-table .n-data-table-th.n-data-table-th--filterable.n-data-table-th--sortable {

 padding-right: calc(var(--n-th-padding) + 36px);
 
}

.n-data-table .n-data-table-th.n-data-table-th--fixed-left {

 left: 0;
 position: sticky;
 z-index: 2;
 
}

.n-data-table .n-data-table-th.n-data-table-th--fixed-left::after {

 pointer-events: none;
 content: "";
 width: 36px;
 display: inline-block;
 position: absolute;
 top: 0;
 bottom: -1px;
 transition: box-shadow .2s var(--n-bezier);
 right: -36px;
 
}

.n-data-table .n-data-table-th.n-data-table-th--fixed-right {

 right: 0;
 position: sticky;
 z-index: 1;
 
}

.n-data-table .n-data-table-th.n-data-table-th--fixed-right::before {

 pointer-events: none;
 content: "";
 width: 36px;
 display: inline-block;
 position: absolute;
 top: 0;
 bottom: -1px;
 transition: box-shadow .2s var(--n-bezier);
 left: -36px;
 
}

.n-data-table .n-data-table-th.n-data-table-th--selection {

 padding: 0;
 text-align: center;
 line-height: 0;
 z-index: 3;
 
}

.n-data-table .n-data-table-th .n-data-table-th__title-wrapper {

 display: flex;
 align-items: center;
 flex-wrap: nowrap;
 max-width: 100%;
 
}

.n-data-table .n-data-table-th .n-data-table-th__title-wrapper .n-data-table-th__title {

 flex: 1;
 min-width: 0;
 
}

.n-data-table .n-data-table-th .n-data-table-th__ellipsis {

 display: inline-block;
 vertical-align: bottom;
 text-overflow: ellipsis;
 overflow: hidden;
 white-space: nowrap;
 max-width: 100%;
 
}

.n-data-table .n-data-table-th.n-data-table-th--hover {

 background-color: var(--n-merged-th-color-hover);
 
}

.n-data-table .n-data-table-th.n-data-table-th--sorting {

 background-color: var(--n-merged-th-color-sorting);
 
}

.n-data-table .n-data-table-th.n-data-table-th--sortable {

 cursor: pointer;
 
}

.n-data-table .n-data-table-th.n-data-table-th--sortable .n-data-table-th__ellipsis {

 max-width: calc(100% - 18px);
 
}

.n-data-table .n-data-table-th.n-data-table-th--sortable:hover {

 background-color: var(--n-merged-th-color-hover);
 
}

.n-data-table .n-data-table-th .n-data-table-sorter {

 height: var(--n-sorter-size);
 width: var(--n-sorter-size);
 margin-left: 4px;
 position: relative;
 display: inline-flex;
 align-items: center;
 justify-content: center;
 vertical-align: -0.2em;
 color: var(--n-th-icon-color);
 transition: color .3s var(--n-bezier);
 
}

.n-data-table .n-data-table-th .n-data-table-sorter .n-base-icon {
transition: transform .3s var(--n-bezier)
}

.n-data-table .n-data-table-th .n-data-table-sorter.n-data-table-sorter--desc .n-base-icon {

 transform: rotate(0deg);
 
}

.n-data-table .n-data-table-th .n-data-table-sorter.n-data-table-sorter--asc .n-base-icon {

 transform: rotate(-180deg);
 
}

.n-data-table .n-data-table-th .n-data-table-sorter.n-data-table-sorter--asc, .n-data-table .n-data-table-th .n-data-table-sorter.n-data-table-sorter--desc {

 color: var(--n-th-icon-color-active);
 
}

.n-data-table .n-data-table-th .n-data-table-resize-button {

 width: var(--n-resizable-container-size);
 position: absolute;
 top: 0;
 right: calc(var(--n-resizable-container-size) / 2);
 bottom: 0;
 cursor: col-resize;
 user-select: none;
 
}

.n-data-table .n-data-table-th .n-data-table-resize-button::after {

 width: var(--n-resizable-size);
 height: 50%;
 position: absolute;
 top: 50%;
 left: calc(var(--n-resizable-container-size) / 2);
 bottom: 0;
 background-color: var(--n-merged-border-color);
 transform: translateY(-50%);
 transition: background-color .3s var(--n-bezier);
 z-index: 1;
 content: '';
 
}

.n-data-table .n-data-table-th .n-data-table-resize-button.n-data-table-resize-button--active::after {
 
 background-color: var(--n-th-icon-color-active);
 
}

.n-data-table .n-data-table-th .n-data-table-resize-button:hover::after {

 background-color: var(--n-th-icon-color-active);
 
}

.n-data-table .n-data-table-th .n-data-table-filter {

 position: absolute;
 z-index: auto;
 right: 0;
 width: 36px;
 top: 0;
 bottom: 0;
 cursor: pointer;
 display: flex;
 justify-content: center;
 align-items: center;
 transition:
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 font-size: var(--n-filter-size);
 color: var(--n-th-icon-color);
 
}

.n-data-table .n-data-table-th .n-data-table-filter:hover {

 background-color: var(--n-th-button-color-hover);
 
}

.n-data-table .n-data-table-th .n-data-table-filter.n-data-table-filter--show {

 background-color: var(--n-th-button-color-hover);
 
}

.n-data-table .n-data-table-th .n-data-table-filter.n-data-table-filter--active {

 background-color: var(--n-th-button-color-hover);
 color: var(--n-th-icon-color-active);
 
}

.n-data-table .n-data-table-td {

 padding: var(--n-td-padding);
 text-align: start;
 box-sizing: border-box;
 border: none;
 background-color: var(--n-merged-td-color);
 color: var(--n-td-text-color);
 border-bottom: 1px solid var(--n-merged-border-color);
 transition:
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 border-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 
}

.n-data-table .n-data-table-td.n-data-table-td--expand .n-data-table-expand-trigger {

 margin-right: 0;
 
}

.n-data-table .n-data-table-td.n-data-table-td--last-row {

 border-bottom: 0 solid var(--n-merged-border-color);
 
}

.n-data-table .n-data-table-td.n-data-table-td--last-row::after {

 bottom: 0 !important;
 
}

.n-data-table .n-data-table-td.n-data-table-td--last-row::before {

 bottom: 0 !important;
 
}

.n-data-table .n-data-table-td.n-data-table-td--summary {

 background-color: var(--n-merged-th-color);
 
}

.n-data-table .n-data-table-td.n-data-table-td--hover {

 background-color: var(--n-merged-td-color-hover);
 
}

.n-data-table .n-data-table-td.n-data-table-td--sorting {

 background-color: var(--n-merged-td-color-sorting);
 
}

.n-data-table .n-data-table-td .n-data-table-td__ellipsis {

 display: inline-block;
 text-overflow: ellipsis;
 overflow: hidden;
 white-space: nowrap;
 max-width: 100%;
 vertical-align: bottom;
 max-width: calc(100% - var(--indent-offset, -1.5) * 16px - 24px);
 
}

.n-data-table .n-data-table-td.n-data-table-td--selection, .n-data-table .n-data-table-td.n-data-table-td--expand {

 text-align: center;
 padding: 0;
 line-height: 0;
 
}

.n-data-table .n-data-table-td.n-data-table-td--fixed-left {

 left: 0;
 position: sticky;
 z-index: 2;
 
}

.n-data-table .n-data-table-td.n-data-table-td--fixed-left::after {

 pointer-events: none;
 content: "";
 width: 36px;
 display: inline-block;
 position: absolute;
 top: 0;
 bottom: -1px;
 transition: box-shadow .2s var(--n-bezier);
 right: -36px;
 
}

.n-data-table .n-data-table-td.n-data-table-td--fixed-right {

 right: 0;
 position: sticky;
 z-index: 1;
 
}

.n-data-table .n-data-table-td.n-data-table-td--fixed-right::before {

 pointer-events: none;
 content: "";
 width: 36px;
 display: inline-block;
 position: absolute;
 top: 0;
 bottom: -1px;
 transition: box-shadow .2s var(--n-bezier);
 left: -36px;
 
}

.n-data-table .n-data-table-empty {

 box-sizing: border-box;
 padding: var(--n-empty-padding);
 flex-grow: 1;
 flex-shrink: 0;
 opacity: 1;
 display: flex;
 align-items: center;
 justify-content: center;
 transition: opacity .3s var(--n-bezier);
 
}

.n-data-table .n-data-table-empty.n-data-table-empty--hide {

 opacity: 0;
 
}

.n-data-table .n-data-table__pagination {

 margin: var(--n-pagination-margin);
 display: flex;
 justify-content: flex-end;
 
}

.n-data-table .n-data-table-wrapper {

 position: relative;
 opacity: 1;
 transition: opacity .3s var(--n-bezier), border-color .3s var(--n-bezier);
 border-top-left-radius: var(--n-border-radius);
 border-top-right-radius: var(--n-border-radius);
 line-height: var(--n-line-height);
 
}

.n-data-table.n-data-table--loading .n-data-table-wrapper {

 opacity: var(--n-opacity-loading);
 pointer-events: none;
 
}

.n-data-table.n-data-table--single-column .n-data-table-td {

 border-bottom: 0 solid var(--n-merged-border-color);
 
}

.n-data-table.n-data-table--single-column .n-data-table-td::after, .n-data-table.n-data-table--single-column .n-data-table-td::before {

 bottom: 0 !important;
 
}

.n-data-table:not(.n-data-table--single-line) .n-data-table-th {

 border-right: 1px solid var(--n-merged-border-color);
 
}

.n-data-table:not(.n-data-table--single-line) .n-data-table-th.n-data-table-th--last {

 border-right: 0 solid var(--n-merged-border-color);
 
}

.n-data-table:not(.n-data-table--single-line) .n-data-table-td {

 border-right: 1px solid var(--n-merged-border-color);
 
}

.n-data-table:not(.n-data-table--single-line) .n-data-table-td.n-data-table-td--last-col {

 border-right: 0 solid var(--n-merged-border-color);
 
}

.n-data-table.n-data-table--bordered .n-data-table-wrapper {

 border: 1px solid var(--n-merged-border-color);
 border-bottom-left-radius: var(--n-border-radius);
 border-bottom-right-radius: var(--n-border-radius);
 overflow: hidden;
 
}

.n-data-table .n-data-table-base-table.n-data-table-base-table--transition-disabled .n-data-table-th::after, .n-data-table .n-data-table-base-table.n-data-table-base-table--transition-disabled .n-data-table-th::before {
transition: none;
}

.n-data-table .n-data-table-base-table.n-data-table-base-table--transition-disabled .n-data-table-td::after, .n-data-table .n-data-table-base-table.n-data-table-base-table--transition-disabled .n-data-table-td::before {
transition: none;
}

.n-data-table.n-data-table--bottom-bordered .n-data-table-td.n-data-table-td--last-row {

 border-bottom: 1px solid var(--n-merged-border-color);
 
}

.n-data-table .n-data-table-table {

 font-variant-numeric: tabular-nums;
 width: 100%;
 word-break: break-word;
 transition: background-color .3s var(--n-bezier);
 border-collapse: separate;
 border-spacing: 0;
 background-color: var(--n-merged-td-color);
 
}

.n-data-table .n-data-table-base-table-header {

 border-top-left-radius: calc(var(--n-border-radius) - 1px);
 border-top-right-radius: calc(var(--n-border-radius) - 1px);
 z-index: 3;
 overflow: scroll;
 flex-shrink: 0;
 transition: border-color .3s var(--n-bezier);
 scrollbar-width: none;
 
}

.n-data-table .n-data-table-base-table-header::-webkit-scrollbar, .n-data-table .n-data-table-base-table-header::-webkit-scrollbar-track-piece, .n-data-table .n-data-table-base-table-header::-webkit-scrollbar-thumb {

 display: none;
 width: 0;
 height: 0;
 
}

.n-data-table .n-data-table-check-extra {

 transition: color .3s var(--n-bezier);
 color: var(--n-th-icon-color);
 position: absolute;
 font-size: 14px;
 right: -4px;
 top: 50%;
 transform: translateY(-50%);
 z-index: 1;
 
}

.n-data-table-filter-menu .n-scrollbar {

 max-height: 240px;
 
}

.n-data-table-filter-menu .n-data-table-filter-menu__group {

 display: flex;
 flex-direction: column;
 padding: 12px 12px 0 12px;
 
}

.n-data-table-filter-menu .n-data-table-filter-menu__group .n-checkbox {

 margin-bottom: 12px;
 margin-right: 0;
 
}

.n-data-table-filter-menu .n-data-table-filter-menu__group .n-radio {

 margin-bottom: 12px;
 margin-right: 0;
 
}

.n-data-table-filter-menu .n-data-table-filter-menu__action {

 padding: var(--n-action-padding);
 display: flex;
 flex-wrap: nowrap;
 justify-content: space-evenly;
 border-top: 1px solid var(--n-action-divider-color);
 
}

.n-data-table-filter-menu .n-data-table-filter-menu__action .n-button:not(:last-child) {

 margin: var(--n-action-button-margin);
 
}

.n-data-table-filter-menu .n-data-table-filter-menu__action .n-button:last-child {

 margin-right: 0;
 
}

.n-data-table-filter-menu .n-divider {

 margin: 0 !important;
 
}

.n-modal .n-data-table, .n-drawer .n-data-table {

 --n-merged-th-color: var(--n-th-color-modal);
 --n-merged-td-color: var(--n-td-color-modal);
 --n-merged-border-color: var(--n-border-color-modal);
 --n-merged-th-color-hover: var(--n-th-color-hover-modal);
 --n-merged-td-color-hover: var(--n-td-color-hover-modal);
 --n-merged-th-color-sorting: var(--n-th-color-hover-modal);
 --n-merged-td-color-sorting: var(--n-td-color-hover-modal);
 --n-merged-td-color-striped: var(--n-td-color-striped-modal);
 
}

.n-popover .n-data-table {

 --n-merged-th-color: var(--n-th-color-popover);
 --n-merged-td-color: var(--n-td-color-popover);
 --n-merged-border-color: var(--n-border-color-popover);
 --n-merged-th-color-hover: var(--n-th-color-hover-popover);
 --n-merged-td-color-hover: var(--n-td-color-hover-popover);
 --n-merged-th-color-sorting: var(--n-th-color-hover-popover);
 --n-merged-td-color-sorting: var(--n-td-color-hover-popover);
 --n-merged-td-color-striped: var(--n-td-color-striped-popover);
 
}</style><style cssr-id="n-input">.n-input {

 max-width: 100%;
 cursor: text;
 line-height: 1.5;
 z-index: auto;
 outline: none;
 box-sizing: border-box;
 position: relative;
 display: inline-flex;
 border-radius: var(--n-border-radius);
 background-color: var(--n-color);
 transition: background-color .3s var(--n-bezier);
 font-size: var(--n-font-size);
 font-weight: var(--n-font-weight);
 --n-padding-vertical: calc((var(--n-height) - 1.5 * var(--n-font-size)) / 2);

}

.n-input .n-input__input, .n-input .n-input__textarea {

 overflow: hidden;
 flex-grow: 1;
 position: relative;
 
}

.n-input .n-input__input-el, .n-input .n-input__textarea-el, .n-input .n-input__input-mirror, .n-input .n-input__textarea-mirror, .n-input .n-input__separator, .n-input .n-input__placeholder {

 box-sizing: border-box;
 font-size: inherit;
 line-height: 1.5;
 font-family: inherit;
 border: none;
 outline: none;
 background-color: #0000;
 text-align: inherit;
 transition:
 -webkit-text-fill-color .3s var(--n-bezier),
 caret-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 text-decoration-color .3s var(--n-bezier);
 
}

.n-input .n-input__input-el, .n-input .n-input__textarea-el {

 -webkit-appearance: none;
 scrollbar-width: none;
 width: 100%;
 min-width: 0;
 text-decoration-color: var(--n-text-decoration-color);
 color: var(--n-text-color);
 caret-color: var(--n-caret-color);
 background-color: transparent;
 
}

.n-input .n-input__input-el::-webkit-scrollbar, .n-input .n-input__textarea-el::-webkit-scrollbar, .n-input .n-input__input-el::-webkit-scrollbar-track-piece, .n-input .n-input__textarea-el::-webkit-scrollbar-track-piece, .n-input .n-input__input-el::-webkit-scrollbar-thumb, .n-input .n-input__textarea-el::-webkit-scrollbar-thumb {

 width: 0;
 height: 0;
 display: none;
 
}

.n-input .n-input__input-el::placeholder, .n-input .n-input__textarea-el::placeholder {

 color: #0000;
 -webkit-text-fill-color: transparent !important;
 
}

.n-input .n-input__input-el:-webkit-autofill ~ .n-input__placeholder, .n-input .n-input__textarea-el:-webkit-autofill ~ .n-input__placeholder {
display: none;
}

.n-input.n-input--round:not(.n-input--textarea) {
border-radius: calc(var(--n-height) / 2);
}

.n-input .n-input__placeholder {

 pointer-events: none;
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 overflow: hidden;
 color: var(--n-placeholder-color);
 
}

.n-input .n-input__placeholder span {

 width: 100%;
 display: inline-block;
 
}

.n-input.n-input--textarea .n-input__placeholder {
overflow: visible;
}

.n-input:not(.n-input--autosize) {
width: 100%;
}

.n-input.n-input--autosize .n-input__textarea-el, .n-input.n-input--autosize .n-input__input-el {

 position: absolute;
 top: 0;
 left: 0;
 height: 100%;
 
}

.n-input .n-input-wrapper {

 overflow: hidden;
 display: inline-flex;
 flex-grow: 1;
 position: relative;
 padding-left: var(--n-padding-left);
 padding-right: var(--n-padding-right);
 
}

.n-input .n-input__input-mirror {

 padding: 0;
 height: var(--n-height);
 line-height: var(--n-height);
 overflow: hidden;
 visibility: hidden;
 position: static;
 white-space: pre;
 pointer-events: none;
 
}

.n-input .n-input__input-el {

 padding: 0;
 height: var(--n-height);
 line-height: var(--n-height);
 
}

.n-input .n-input__input-el[type=password]::-ms-reveal {
display: none;
}

.n-input .n-input__input-el + .n-input__placeholder {

 display: flex;
 align-items: center; 
 
}

.n-input:not(.n-input--textarea) .n-input__placeholder {
white-space: nowrap;
}

.n-input .n-input__eye {

 display: flex;
 align-items: center;
 justify-content: center;
 transition: color .3s var(--n-bezier);
 
}

.n-input.n-input--textarea {
width: 100%;
}

.n-input.n-input--textarea .n-input-word-count {

 position: absolute;
 right: var(--n-padding-right);
 bottom: var(--n-padding-vertical);
 
}

.n-input.n-input--textarea.n-input--resizable .n-input-wrapper {

 resize: vertical;
 min-height: var(--n-height);
 
}

.n-input.n-input--textarea .n-input__textarea-el, .n-input.n-input--textarea .n-input__textarea-mirror, .n-input.n-input--textarea .n-input__placeholder {

 height: 100%;
 padding-left: 0;
 padding-right: 0;
 padding-top: var(--n-padding-vertical);
 padding-bottom: var(--n-padding-vertical);
 word-break: break-word;
 display: inline-block;
 vertical-align: bottom;
 box-sizing: border-box;
 line-height: var(--n-line-height-textarea);
 margin: 0;
 resize: none;
 white-space: pre-wrap;
 scroll-padding-block-end: var(--n-padding-vertical);
 
}

.n-input.n-input--textarea .n-input__textarea-mirror {

 width: 100%;
 pointer-events: none;
 overflow: hidden;
 visibility: hidden;
 position: static;
 white-space: pre-wrap;
 overflow-wrap: break-word;
 
}

.n-input.n-input--pair .n-input__input-el, .n-input.n-input--pair .n-input__placeholder {
text-align: center;
}

.n-input.n-input--pair .n-input__separator {

 display: flex;
 align-items: center;
 transition: color .3s var(--n-bezier);
 color: var(--n-text-color);
 white-space: nowrap;
 
}

.n-input.n-input--pair .n-input__separator .n-icon {

 color: var(--n-icon-color);
 
}

.n-input.n-input--pair .n-input__separator .n-base-icon {

 color: var(--n-icon-color);
 
}

.n-input.n-input--disabled {

 cursor: not-allowed;
 background-color: var(--n-color-disabled);
 
}

.n-input.n-input--disabled .n-input__border {
border: var(--n-border-disabled);
}

.n-input.n-input--disabled .n-input__input-el, .n-input.n-input--disabled .n-input__textarea-el {

 cursor: not-allowed;
 color: var(--n-text-color-disabled);
 text-decoration-color: var(--n-text-color-disabled);
 
}

.n-input.n-input--disabled .n-input__placeholder {
color: var(--n-placeholder-color-disabled);
}

.n-input.n-input--disabled .n-input__separator {
color: var(--n-text-color-disabled);
}

.n-input.n-input--disabled .n-input__separator .n-icon {

 color: var(--n-icon-color-disabled);
 
}

.n-input.n-input--disabled .n-input__separator .n-base-icon {

 color: var(--n-icon-color-disabled);
 
}

.n-input.n-input--disabled .n-input-word-count {

 color: var(--n-count-text-color-disabled);
 
}

.n-input.n-input--disabled .n-input__suffix, .n-input.n-input--disabled .n-input__prefix {
color: var(--n-text-color-disabled);
}

.n-input.n-input--disabled .n-input__suffix .n-icon, .n-input.n-input--disabled .n-input__prefix .n-icon {

 color: var(--n-icon-color-disabled);
 
}

.n-input.n-input--disabled .n-input__suffix .n-internal-icon, .n-input.n-input--disabled .n-input__prefix .n-internal-icon {

 color: var(--n-icon-color-disabled);
 
}

.n-input:not(.n-input--disabled) .n-input__eye {

 color: var(--n-icon-color);
 cursor: pointer;
 
}

.n-input:not(.n-input--disabled) .n-input__eye:hover {

 color: var(--n-icon-color-hover);
 
}

.n-input:not(.n-input--disabled) .n-input__eye:active {

 color: var(--n-icon-color-pressed);
 
}

.n-input:not(.n-input--disabled):hover .n-input__state-border {
border: var(--n-border-hover);
}

.n-input:not(.n-input--disabled).n-input--focus {
background-color: var(--n-color-focus);
}

.n-input:not(.n-input--disabled).n-input--focus .n-input__state-border {

 border: var(--n-border-focus);
 box-shadow: var(--n-box-shadow-focus);
 
}

.n-input .n-input__border, .n-input .n-input__state-border {

 box-sizing: border-box;
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 pointer-events: none;
 border-radius: inherit;
 border: var(--n-border);
 transition:
 box-shadow .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 
}

.n-input .n-input__state-border {

 border-color: #0000;
 z-index: 1;
 
}

.n-input .n-input__prefix {
margin-right: 4px;
}

.n-input .n-input__suffix {

 margin-left: 4px;
 
}

.n-input .n-input__suffix, .n-input .n-input__prefix {

 transition: color .3s var(--n-bezier);
 flex-wrap: nowrap;
 flex-shrink: 0;
 line-height: var(--n-height);
 white-space: nowrap;
 display: inline-flex;
 align-items: center;
 justify-content: center;
 color: var(--n-suffix-text-color);
 
}

.n-input .n-input__suffix .n-base-loading, .n-input .n-input__prefix .n-base-loading {

 font-size: var(--n-icon-size);
 margin: 0 2px;
 color: var(--n-loading-color);
 
}

.n-input .n-input__suffix .n-base-clear, .n-input .n-input__prefix .n-base-clear {

 font-size: var(--n-icon-size);
 
}

.n-input .n-input__suffix .n-base-clear .n-base-clear__placeholder .n-base-icon, .n-input .n-input__prefix .n-base-clear .n-base-clear__placeholder .n-base-icon {

 transition: color .3s var(--n-bezier);
 color: var(--n-icon-color);
 font-size: var(--n-icon-size);
 
}

.n-input .n-input__suffix > .n-icon, .n-input .n-input__prefix > .n-icon {

 transition: color .3s var(--n-bezier);
 color: var(--n-icon-color);
 font-size: var(--n-icon-size);
 
}

.n-input .n-input__suffix .n-base-icon, .n-input .n-input__prefix .n-base-icon {

 font-size: var(--n-icon-size);
 
}

.n-input .n-input-word-count {

 pointer-events: none;
 line-height: 1.5;
 font-size: .85em;
 color: var(--n-count-text-color);
 transition: color .3s var(--n-bezier);
 margin-left: 4px;
 font-variant: tabular-nums;
 
}

.n-input.n-input--warning-status:not(.n-input--disabled) .n-base-loading {

 color: var(--n-loading-color-warning)
 
}

.n-input.n-input--warning-status:not(.n-input--disabled) .n-input__input-el, .n-input.n-input--warning-status:not(.n-input--disabled) .n-input__textarea-el {

 caret-color: var(--n-caret-color-warning);
 
}

.n-input.n-input--warning-status:not(.n-input--disabled) .n-input__state-border {

 border: var(--n-border-warning);
 
}

.n-input.n-input--warning-status:not(.n-input--disabled):hover .n-input__state-border {

 border: var(--n-border-hover-warning);
 
}

.n-input.n-input--warning-status:not(.n-input--disabled):focus {

 background-color: var(--n-color-focus-warning);
 
}

.n-input.n-input--warning-status:not(.n-input--disabled):focus .n-input__state-border {

 box-shadow: var(--n-box-shadow-focus-warning);
 border: var(--n-border-focus-warning);
 
}

.n-input.n-input--warning-status:not(.n-input--disabled).n-input--focus {

 background-color: var(--n-color-focus-warning);
 
}

.n-input.n-input--warning-status:not(.n-input--disabled).n-input--focus .n-input__state-border {

 box-shadow: var(--n-box-shadow-focus-warning);
 border: var(--n-border-focus-warning);
 
}

.n-input.n-input--error-status:not(.n-input--disabled) .n-base-loading {

 color: var(--n-loading-color-error)
 
}

.n-input.n-input--error-status:not(.n-input--disabled) .n-input__input-el, .n-input.n-input--error-status:not(.n-input--disabled) .n-input__textarea-el {

 caret-color: var(--n-caret-color-error);
 
}

.n-input.n-input--error-status:not(.n-input--disabled) .n-input__state-border {

 border: var(--n-border-error);
 
}

.n-input.n-input--error-status:not(.n-input--disabled):hover .n-input__state-border {

 border: var(--n-border-hover-error);
 
}

.n-input.n-input--error-status:not(.n-input--disabled):focus {

 background-color: var(--n-color-focus-error);
 
}

.n-input.n-input--error-status:not(.n-input--disabled):focus .n-input__state-border {

 box-shadow: var(--n-box-shadow-focus-error);
 border: var(--n-border-focus-error);
 
}

.n-input.n-input--error-status:not(.n-input--disabled).n-input--focus {

 background-color: var(--n-color-focus-error);
 
}

.n-input.n-input--error-status:not(.n-input--disabled).n-input--focus .n-input__state-border {

 box-shadow: var(--n-box-shadow-focus-error);
 border: var(--n-border-focus-error);
 
}</style><style cssr-id="n-base-clear">.n-base-clear {

 flex-shrink: 0;
 height: 1em;
 width: 1em;
 position: relative;

}

.n-base-clear > .n-base-clear__clear {

 font-size: var(--n-clear-size);
 height: 1em;
 width: 1em;
 cursor: pointer;
 color: var(--n-clear-color);
 transition: color .3s var(--n-bezier);
 display: flex;
 
}

.n-base-clear > .n-base-clear__clear:hover {

 color: var(--n-clear-color-hover)!important;
 
}

.n-base-clear > .n-base-clear__clear:active {

 color: var(--n-clear-color-pressed)!important;
 
}

.n-base-clear > .n-base-clear__placeholder {

 display: flex;
 
}

.n-base-clear > .n-base-clear__clear, .n-base-clear > .n-base-clear__placeholder {

 position: absolute;
 left: 50%;
 top: 50%;
 transform: translateX(-50%) translateY(-50%);
 
}

.n-base-clear > .n-base-clear__clear.icon-switch-transition-enter-from, .n-base-clear > .n-base-clear__placeholder.icon-switch-transition-enter-from, .n-base-clear > .n-base-clear__clear.icon-switch-transition-leave-to, .n-base-clear > .n-base-clear__placeholder.icon-switch-transition-leave-to {
  transform: translateX(-50%) translateY(-50%) scale(0.75);
  left: 50%;
  top: 50%;
  opacity: 0;
}

.n-base-clear > .n-base-clear__clear.icon-switch-transition-enter-to, .n-base-clear > .n-base-clear__placeholder.icon-switch-transition-enter-to, .n-base-clear > .n-base-clear__clear.icon-switch-transition-leave-from, .n-base-clear > .n-base-clear__placeholder.icon-switch-transition-leave-from {
  transform: scale(1) translateX(-50%) translateY(-50%);
  left: 50%;
  top: 50%;
  opacity: 1;
}

.n-base-clear > .n-base-clear__clear.icon-switch-transition-enter-active, .n-base-clear > .n-base-clear__placeholder.icon-switch-transition-enter-active, .n-base-clear > .n-base-clear__clear.icon-switch-transition-leave-active, .n-base-clear > .n-base-clear__placeholder.icon-switch-transition-leave-active {
  transform-origin: center;
  position: absolute;
  left: 50%;
  top: 50%;
  transition: all .3s cubic-bezier(.4, 0, .2, 1) !important;
}</style><style cssr-id="n-base-loading">@keyframes rotator {

 0% {
 -webkit-transform: rotate(0deg);
 transform: rotate(0deg);
 }
 100% {
 -webkit-transform: rotate(360deg);
 transform: rotate(360deg);
 }
}

.n-base-loading {

 position: relative;
 line-height: 0;
 width: 1em;
 height: 1em;
 
}

.n-base-loading .n-base-loading__transition-wrapper {

 position: absolute;
 width: 100%;
 height: 100%;
 
}

.n-base-loading .n-base-loading__transition-wrapper.icon-switch-transition-enter-from, .n-base-loading .n-base-loading__transition-wrapper.icon-switch-transition-leave-to {
  transform:  scale(0.75);
  left: 0;
  top: 0;
  opacity: 0;
}

.n-base-loading .n-base-loading__transition-wrapper.icon-switch-transition-enter-to, .n-base-loading .n-base-loading__transition-wrapper.icon-switch-transition-leave-from {
  transform: scale(1) ;
  left: 0;
  top: 0;
  opacity: 1;
}

.n-base-loading .n-base-loading__transition-wrapper.icon-switch-transition-enter-active, .n-base-loading .n-base-loading__transition-wrapper.icon-switch-transition-leave-active {
  transform-origin: center;
  position: absolute;
  left: 0;
  top: 0;
  transition: all .3s cubic-bezier(.4, 0, .2, 1) !important;
}

.n-base-loading .n-base-loading__placeholder {

 position: absolute;
 left: 50%;
 top: 50%;
 transform: translateX(-50%) translateY(-50%);
 
}

.n-base-loading .n-base-loading__placeholder.icon-switch-transition-enter-from, .n-base-loading .n-base-loading__placeholder.icon-switch-transition-leave-to {
  transform: translateX(-50%) translateY(-50%) scale(0.75);
  left: 50%;
  top: 50%;
  opacity: 0;
}

.n-base-loading .n-base-loading__placeholder.icon-switch-transition-enter-to, .n-base-loading .n-base-loading__placeholder.icon-switch-transition-leave-from {
  transform: scale(1) translateX(-50%) translateY(-50%);
  left: 50%;
  top: 50%;
  opacity: 1;
}

.n-base-loading .n-base-loading__placeholder.icon-switch-transition-enter-active, .n-base-loading .n-base-loading__placeholder.icon-switch-transition-leave-active {
  transform-origin: center;
  position: absolute;
  left: 50%;
  top: 50%;
  transition: all .3s cubic-bezier(.4, 0, .2, 1) !important;
}

.n-base-loading .n-base-loading__container {

 animation: rotator 3s linear infinite both;
 
}

.n-base-loading .n-base-loading__container .n-base-loading__icon {

 height: 1em;
 width: 1em;
 
}</style><style cssr-id="n-internal-selection">.n-base-selection {

 --n-padding-single: var(--n-padding-single-top) var(--n-padding-single-right) var(--n-padding-single-bottom) var(--n-padding-single-left);
 --n-padding-multiple: var(--n-padding-multiple-top) var(--n-padding-multiple-right) var(--n-padding-multiple-bottom) var(--n-padding-multiple-left);
 position: relative;
 z-index: auto;
 box-shadow: none;
 width: 100%;
 max-width: 100%;
 display: inline-block;
 vertical-align: bottom;
 border-radius: var(--n-border-radius);
 min-height: var(--n-height);
 line-height: 1.5;
 font-size: var(--n-font-size);
 
}

.n-base-selection .n-base-loading {

 color: var(--n-loading-color);
 
}

.n-base-selection .n-base-selection-tags {
min-height: var(--n-height);
}

.n-base-selection .n-base-selection__border, .n-base-selection .n-base-selection__state-border {

 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 pointer-events: none;
 border: var(--n-border);
 border-radius: inherit;
 transition:
 box-shadow .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 
}

.n-base-selection .n-base-selection__state-border {

 z-index: 1;
 border-color: #0000;
 
}

.n-base-selection .n-base-suffix {

 cursor: pointer;
 position: absolute;
 top: 50%;
 transform: translateY(-50%);
 right: 10px;
 
}

.n-base-selection .n-base-suffix .n-base-suffix__arrow {

 font-size: var(--n-arrow-size);
 color: var(--n-arrow-color);
 transition: color .3s var(--n-bezier);
 
}

.n-base-selection .n-base-selection-overlay {

 display: flex;
 align-items: center;
 white-space: nowrap;
 pointer-events: none;
 position: absolute;
 top: 0;
 right: 0;
 bottom: 0;
 left: 0;
 padding: var(--n-padding-single);
 transition: color .3s var(--n-bezier);
 
}

.n-base-selection .n-base-selection-overlay .n-base-selection-overlay__wrapper {

 flex-basis: 0;
 flex-grow: 1;
 overflow: hidden;
 text-overflow: ellipsis;
 
}

.n-base-selection .n-base-selection-placeholder {

 color: var(--n-placeholder-color);
 
}

.n-base-selection .n-base-selection-placeholder .n-base-selection-placeholder__inner {

 max-width: 100%;
 overflow: hidden;
 
}

.n-base-selection .n-base-selection-tags {

 cursor: pointer;
 outline: none;
 box-sizing: border-box;
 position: relative;
 z-index: auto;
 display: flex;
 padding: var(--n-padding-multiple);
 flex-wrap: wrap;
 align-items: center;
 width: 100%;
 vertical-align: bottom;
 background-color: var(--n-color);
 border-radius: inherit;
 transition:
 color .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 
}

.n-base-selection .n-base-selection-label {

 height: var(--n-height);
 display: inline-flex;
 width: 100%;
 vertical-align: bottom;
 cursor: pointer;
 outline: none;
 z-index: auto;
 box-sizing: border-box;
 position: relative;
 transition:
 color .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 border-radius: inherit;
 background-color: var(--n-color);
 align-items: center;
 
}

.n-base-selection .n-base-selection-label .n-base-selection-input {

 font-size: inherit;
 line-height: inherit;
 outline: none;
 cursor: pointer;
 box-sizing: border-box;
 border:none;
 width: 100%;
 padding: var(--n-padding-single);
 background-color: #0000;
 color: var(--n-text-color);
 transition: color .3s var(--n-bezier);
 caret-color: var(--n-caret-color);
 
}

.n-base-selection .n-base-selection-label .n-base-selection-input .n-base-selection-input__content {

 text-overflow: ellipsis;
 overflow: hidden;
 white-space: nowrap; 
 
}

.n-base-selection .n-base-selection-label .n-base-selection-label__render-label {

 color: var(--n-text-color);
 
}

.n-base-selection:not(.n-base-selection--disabled):hover .n-base-selection__state-border {

 box-shadow: var(--n-box-shadow-hover);
 border: var(--n-border-hover);
 
}

.n-base-selection:not(.n-base-selection--disabled).n-base-selection--focus .n-base-selection__state-border {

 box-shadow: var(--n-box-shadow-focus);
 border: var(--n-border-focus);
 
}

.n-base-selection:not(.n-base-selection--disabled).n-base-selection--active .n-base-selection__state-border {

 box-shadow: var(--n-box-shadow-active);
 border: var(--n-border-active);
 
}

.n-base-selection:not(.n-base-selection--disabled).n-base-selection--active .n-base-selection-label {
background-color: var(--n-color-active);
}

.n-base-selection:not(.n-base-selection--disabled).n-base-selection--active .n-base-selection-tags {
background-color: var(--n-color-active);
}

.n-base-selection.n-base-selection--disabled {
cursor: not-allowed;
}

.n-base-selection.n-base-selection--disabled .n-base-selection__arrow {

 color: var(--n-arrow-color-disabled);
 
}

.n-base-selection.n-base-selection--disabled .n-base-selection-label {

 cursor: not-allowed;
 background-color: var(--n-color-disabled);
 
}

.n-base-selection.n-base-selection--disabled .n-base-selection-label .n-base-selection-input {

 cursor: not-allowed;
 color: var(--n-text-color-disabled);
 
}

.n-base-selection.n-base-selection--disabled .n-base-selection-label .n-base-selection-label__render-label {

 color: var(--n-text-color-disabled);
 
}

.n-base-selection.n-base-selection--disabled .n-base-selection-tags {

 cursor: not-allowed;
 background-color: var(--n-color-disabled);
 
}

.n-base-selection.n-base-selection--disabled .n-base-selection-placeholder {

 cursor: not-allowed;
 color: var(--n-placeholder-color-disabled);
 
}

.n-base-selection .n-base-selection-input-tag {

 height: calc(var(--n-height) - 6px);
 line-height: calc(var(--n-height) - 6px);
 outline: none;
 display: none;
 position: relative;
 margin-bottom: 3px;
 max-width: 100%;
 vertical-align: bottom;
 
}

.n-base-selection .n-base-selection-input-tag .n-base-selection-input-tag__input {

 font-size: inherit;
 font-family: inherit;
 min-width: 1px;
 padding: 0;
 background-color: #0000;
 outline: none;
 border: none;
 max-width: 100%;
 overflow: hidden;
 width: 1em;
 line-height: inherit;
 cursor: pointer;
 color: var(--n-text-color);
 caret-color: var(--n-caret-color);
 
}

.n-base-selection .n-base-selection-input-tag .n-base-selection-input-tag__mirror {

 position: absolute;
 left: 0;
 top: 0;
 white-space: pre;
 visibility: hidden;
 user-select: none;
 -webkit-user-select: none;
 opacity: 0;
 
}

.n-base-selection.n-base-selection--warning-status .n-base-selection__state-border {
border: var(--n-border-warning);
}

.n-base-selection.n-base-selection--warning-status:not(.n-base-selection--disabled):hover .n-base-selection__state-border {

 box-shadow: var(--n-box-shadow-hover-warning);
 border: var(--n-border-hover-warning);
 
}

.n-base-selection.n-base-selection--warning-status:not(.n-base-selection--disabled).n-base-selection--active .n-base-selection__state-border {

 box-shadow: var(--n-box-shadow-active-warning);
 border: var(--n-border-active-warning);
 
}

.n-base-selection.n-base-selection--warning-status:not(.n-base-selection--disabled).n-base-selection--active .n-base-selection-label {
background-color: var(--n-color-active-warning);
}

.n-base-selection.n-base-selection--warning-status:not(.n-base-selection--disabled).n-base-selection--active .n-base-selection-tags {
background-color: var(--n-color-active-warning);
}

.n-base-selection.n-base-selection--warning-status:not(.n-base-selection--disabled).n-base-selection--focus .n-base-selection__state-border {

 box-shadow: var(--n-box-shadow-focus-warning);
 border: var(--n-border-focus-warning);
 
}

.n-base-selection.n-base-selection--error-status .n-base-selection__state-border {
border: var(--n-border-error);
}

.n-base-selection.n-base-selection--error-status:not(.n-base-selection--disabled):hover .n-base-selection__state-border {

 box-shadow: var(--n-box-shadow-hover-error);
 border: var(--n-border-hover-error);
 
}

.n-base-selection.n-base-selection--error-status:not(.n-base-selection--disabled).n-base-selection--active .n-base-selection__state-border {

 box-shadow: var(--n-box-shadow-active-error);
 border: var(--n-border-active-error);
 
}

.n-base-selection.n-base-selection--error-status:not(.n-base-selection--disabled).n-base-selection--active .n-base-selection-label {
background-color: var(--n-color-active-error);
}

.n-base-selection.n-base-selection--error-status:not(.n-base-selection--disabled).n-base-selection--active .n-base-selection-tags {
background-color: var(--n-color-active-error);
}

.n-base-selection.n-base-selection--error-status:not(.n-base-selection--disabled).n-base-selection--focus .n-base-selection__state-border {

 box-shadow: var(--n-box-shadow-focus-error);
 border: var(--n-border-focus-error);
 
}

.n-base-selection-popover {

 margin-bottom: -3px;
 display: flex;
 flex-wrap: wrap;
 margin-right: -8px;
 
}

.n-base-selection-tag-wrapper {

 max-width: 100%;
 display: inline-flex;
 padding: 0 7px 3px 0;
 
}

.n-base-selection-tag-wrapper:last-child {
padding-right: 0;
}

.n-base-selection-tag-wrapper .n-tag {

 font-size: 14px;
 max-width: 100%;
 
}

.n-base-selection-tag-wrapper .n-tag .n-tag__content {

 line-height: 1.25;
 text-overflow: ellipsis;
 overflow: hidden;
 
}</style><style cssr-id="n-tree-select">.n-tree-select {

 z-index: auto;
 outline: none;
 width: 100%;
 position: relative;
 
}

.n-tree-select-menu {

 position: relative;
 overflow: hidden;
 margin: 4px 0;
 transition: box-shadow .3s var(--n-bezier), background-color .3s var(--n-bezier);
 border-radius: var(--n-menu-border-radius);
 box-shadow: var(--n-menu-box-shadow);
 background-color: var(--n-menu-color);
 outline: none;
 
}

.n-tree-select-menu .n-tree {
max-height: var(--n-menu-height);
}

.n-tree-select-menu .n-tree-select-menu__empty {

 display: flex;
 padding: 12px 32px;
 flex: 1;
 justify-content: center;
 
}

.n-tree-select-menu .n-tree-select-menu__header {

 padding: var(--n-header-padding);
 transition: 
 color .3s var(--n-bezier);
 border-color .3s var(--n-bezier);
 border-bottom: 1px solid var(--n-header-divider-color);
 color: var(--n-header-text-color);
 
}

.n-tree-select-menu .n-tree-select-menu__action {

 padding: var(--n-action-padding);
 transition: 
 color .3s var(--n-bezier);
 border-color .3s var(--n-bezier);
 border-top: 1px solid var(--n-action-divider-color);
 color: var(--n-action-text-color);
 
}

.n-tree-select-menu.fade-in-scale-up-transition-leave-active {
  transform-origin: inherit;
  transition: opacity .2s cubic-bezier(.4, 0, 1, 1), transform .2s cubic-bezier(.4, 0, 1, 1) ;
}

.n-tree-select-menu.fade-in-scale-up-transition-enter-active {
  transform-origin: inherit;
  transition: opacity .2s cubic-bezier(0, 0, .2, 1), transform .2s cubic-bezier(0, 0, .2, 1) ;
}

.n-tree-select-menu.fade-in-scale-up-transition-enter-from, .n-tree-select-menu.fade-in-scale-up-transition-leave-to {
  opacity: 0;
  transform:  scale(.9);
}

.n-tree-select-menu.fade-in-scale-up-transition-leave-from, .n-tree-select-menu.fade-in-scale-up-transition-enter-to {
  opacity: 1;
  transform:  scale(1);
}</style><style cssr-id="n-card">.n-card {

 font-size: var(--n-font-size);
 line-height: var(--n-line-height);
 display: flex;
 flex-direction: column;
 width: 100%;
 box-sizing: border-box;
 position: relative;
 border-radius: var(--n-border-radius);
 background-color: var(--n-color);
 color: var(--n-text-color);
 word-break: break-word;
 transition: 
 color .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 
}

.n-card.n-modal {
  background: var(--n-color-modal);
}

.n-card.n-card--hoverable:hover {
box-shadow: var(--n-box-shadow);
}

.n-card.n-card--content-segmented > .n-card__content {
  padding-top: var(--n-padding-bottom);
}

.n-card.n-card--content-soft-segmented > .n-card__content {

 margin: 0 var(--n-padding-left);
 padding: var(--n-padding-bottom) 0;
 
}

.n-card.n-card--footer-segmented > .n-card__footer {
  padding-top: var(--n-padding-bottom);
}

.n-card.n-card--footer-soft-segmented > .n-card__footer {

 padding: var(--n-padding-bottom) 0;
 margin: 0 var(--n-padding-left);
 
}

.n-card > .n-card-header {

 box-sizing: border-box;
 display: flex;
 align-items: center;
 font-size: var(--n-title-font-size);
 padding:
 var(--n-padding-top)
 var(--n-padding-left)
 var(--n-padding-bottom)
 var(--n-padding-left);
 
}

.n-card > .n-card-header .n-card-header__main {

 font-weight: var(--n-title-font-weight);
 transition: color .3s var(--n-bezier);
 flex: 1;
 min-width: 0;
 color: var(--n-title-text-color);
 
}

.n-card > .n-card-header .n-card-header__extra {

 display: flex;
 align-items: center;
 font-size: var(--n-font-size);
 font-weight: 400;
 transition: color .3s var(--n-bezier);
 color: var(--n-text-color);
 
}

.n-card > .n-card-header .n-card-header__close {

 margin: 0 0 0 8px;
 transition:
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 
}

.n-card > .n-card__action {

 box-sizing: border-box;
 transition:
 background-color .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 background-clip: padding-box;
 background-color: var(--n-action-color);
 
}

.n-card > .n-card__content {
flex: 1; min-width: 0;
}

.n-card > .n-card__content, .n-card > .n-card__footer {

 box-sizing: border-box;
 padding: 0 var(--n-padding-left) var(--n-padding-bottom) var(--n-padding-left);
 font-size: var(--n-font-size);
 
}

.n-card > .n-card__content:first-child, .n-card > .n-card__footer:first-child {
  padding-top: var(--n-padding-bottom);
}

.n-card > .n-card__action {

 background-color: var(--n-action-color);
 padding: var(--n-padding-bottom) var(--n-padding-left);
 border-bottom-left-radius: var(--n-border-radius);
 border-bottom-right-radius: var(--n-border-radius);
 
}

.n-card .n-card-cover {

 overflow: hidden;
 width: 100%;
 border-radius: var(--n-border-radius) var(--n-border-radius) 0 0;
 
}

.n-card .n-card-cover img {

 display: block;
 width: 100%;
 
}

.n-card.n-card--bordered {

 border: 1px solid var(--n-border-color);
 
}

.n-card.n-card--bordered:target {
border-color: var(--n-color-target);
}

.n-card.n-card--action-segmented > .n-card__action:not(:first-child) {
  border-top: 1px solid var(--n-border-color);
}

.n-card.n-card--content-segmented > .n-card__content, .n-card.n-card--content-soft-segmented > .n-card__content {
  transition: border-color 0.3s var(--n-bezier);
}

.n-card.n-card--content-segmented > .n-card__content:not(:first-child), .n-card.n-card--content-soft-segmented > .n-card__content:not(:first-child) {
  border-top: 1px solid var(--n-border-color);
}

.n-card.n-card--footer-segmented > .n-card__footer, .n-card.n-card--footer-soft-segmented > .n-card__footer {
  transition: border-color 0.3s var(--n-bezier);
}

.n-card.n-card--footer-segmented > .n-card__footer:not(:first-child), .n-card.n-card--footer-soft-segmented > .n-card__footer:not(:first-child) {
  border-top: 1px solid var(--n-border-color);
}

.n-card.n-card--embedded {

 background-color: var(--n-color-embedded);
 
}

.n-modal .n-card, .n-drawer .n-card {

 background: var(--n-color-modal);
 
}

.n-modal .n-card.n-card--embedded, .n-drawer .n-card.n-card--embedded {

 background-color: var(--n-color-embedded-modal);
 
}

.n-popover .n-card {

 background: var(--n-color-popover);
 
}

.n-popover .n-card.n-card--embedded {

 background-color: var(--n-color-embedded-popover);
 
}</style><style cssr-id="n-base-wave">.n-base-wave {

 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 border-radius: inherit;

}</style><style cssr-id="n-button">.n-button {

 margin: 0;
 font-weight: var(--n-font-weight);
 line-height: 1;
 font-family: inherit;
 padding: var(--n-padding);
 height: var(--n-height);
 font-size: var(--n-font-size);
 border-radius: var(--n-border-radius);
 color: var(--n-text-color);
 background-color: var(--n-color);
 width: var(--n-width);
 white-space: nowrap;
 outline: none;
 position: relative;
 z-index: auto;
 border: none;
 display: inline-flex;
 flex-wrap: nowrap;
 flex-shrink: 0;
 align-items: center;
 justify-content: center;
 user-select: none;
 -webkit-user-select: none;
 text-align: center;
 cursor: pointer;
 text-decoration: none;
 transition:
 color .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 opacity .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 
}

.n-button.n-button--color .n-button__border {
  border-color: var(--n-border-color);
}

.n-button.n-button--color.n-button--disabled .n-button__border {
  border-color: var(--n-border-color-disabled);
}

.n-button.n-button--color:not(.n-button--disabled):focus .n-button__state-border {
  border-color: var(--n-border-color-focus);
}

.n-button.n-button--color:not(.n-button--disabled):hover .n-button__state-border {
  border-color: var(--n-border-color-hover);
}

.n-button.n-button--color:not(.n-button--disabled):active .n-button__state-border {
  border-color: var(--n-border-color-pressed);
}

.n-button.n-button--color:not(.n-button--disabled).n-button--pressed .n-button__state-border {
  border-color: var(--n-border-color-pressed);
}

.n-button.n-button--disabled {
  background-color: var(--n-color-disabled);
  color: var(--n-text-color-disabled);
}

.n-button.n-button--disabled .n-button__border {
  border: var(--n-border-disabled);
}

.n-button:not(.n-button--disabled):focus {
  background-color: var(--n-color-focus);
  color: var(--n-text-color-focus);
}

.n-button:not(.n-button--disabled):focus .n-button__state-border {
  border: var(--n-border-focus);
}

.n-button:not(.n-button--disabled):hover {
  background-color: var(--n-color-hover);
  color: var(--n-text-color-hover);
}

.n-button:not(.n-button--disabled):hover .n-button__state-border {
  border: var(--n-border-hover);
}

.n-button:not(.n-button--disabled):active {
  background-color: var(--n-color-pressed);
  color: var(--n-text-color-pressed);
}

.n-button:not(.n-button--disabled):active .n-button__state-border {
  border: var(--n-border-pressed);
}

.n-button:not(.n-button--disabled).n-button--pressed {
  background-color: var(--n-color-pressed);
  color: var(--n-text-color-pressed);
}

.n-button:not(.n-button--disabled).n-button--pressed .n-button__state-border {
  border: var(--n-border-pressed);
}

.n-button.n-button--loading {
cursor: wait;
}

.n-button .n-base-wave {

 pointer-events: none;
 top: 0;
 right: 0;
 bottom: 0;
 left: 0;
 animation-iteration-count: 1;
 animation-duration: var(--n-ripple-duration);
 animation-timing-function: var(--n-bezier-ease-out), var(--n-bezier-ease-out);
 
}

.n-button .n-base-wave.n-base-wave--active {
  z-index: 1;
  animation-name: button-wave-spread, button-wave-opacity;
}

.n-button .n-button__border, .n-button .n-button__state-border {

 position: absolute;
 left: 0;
 top: 0;
 right: 0;
 bottom: 0;
 border-radius: inherit;
 transition: border-color .3s var(--n-bezier);
 pointer-events: none;
 
}

.n-button .n-button__border {
  border: var(--n-border);
}

.n-button .n-button__state-border {
  border: var(--n-border);
  border-color: #0000;
  z-index: 1;
}

.n-button .n-button__icon {

 margin: var(--n-icon-margin);
 margin-left: 0;
 height: var(--n-icon-size);
 width: var(--n-icon-size);
 max-width: var(--n-icon-size);
 font-size: var(--n-icon-size);
 position: relative;
 flex-shrink: 0;
 
}

.n-button .n-button__icon .n-icon-slot {

 height: var(--n-icon-size);
 width: var(--n-icon-size);
 position: absolute;
 left: 0;
 top: 50%;
 transform: translateY(-50%);
 display: flex;
 align-items: center;
 justify-content: center;
 
}

.n-button .n-button__icon .n-icon-slot.icon-switch-transition-enter-from, .n-button .n-button__icon .n-icon-slot.icon-switch-transition-leave-to {
  transform: translateY(-50%) scale(0.75);
  left: 0;
  top: 50%;
  opacity: 0;
}

.n-button .n-button__icon .n-icon-slot.icon-switch-transition-enter-to, .n-button .n-button__icon .n-icon-slot.icon-switch-transition-leave-from {
  transform: scale(1) translateY(-50%);
  left: 0;
  top: 50%;
  opacity: 1;
}

.n-button .n-button__icon .n-icon-slot.icon-switch-transition-enter-active, .n-button .n-button__icon .n-icon-slot.icon-switch-transition-leave-active {
  transform-origin: center;
  position: absolute;
  left: 0;
  top: 50%;
  transition: all .3s cubic-bezier(.4, 0, .2, 1) !important;
}

.n-button .n-button__icon.fade-in-width-expand-transition-leave-from, .n-button .n-button__icon.fade-in-width-expand-transition-enter-to {
  opacity: 1;
}

.n-button .n-button__icon.fade-in-width-expand-transition-leave-to, .n-button .n-button__icon.fade-in-width-expand-transition-enter-from {

 opacity: 0!important;
 margin-left: 0!important;
 margin-right: 0!important;
 
}

.n-button .n-button__icon.fade-in-width-expand-transition-leave-active {

 overflow: hidden;
 transition:
 opacity .2s cubic-bezier(.4, 0, .2, 1),
 max-width .2s cubic-bezier(.4, 0, .2, 1) .1s,
 margin-left .2s cubic-bezier(.4, 0, .2, 1) .1s,
 margin-right .2s cubic-bezier(.4, 0, .2, 1) .1s;
 
}

.n-button .n-button__icon.fade-in-width-expand-transition-enter-active {

 overflow: hidden;
 transition:
 opacity .2s cubic-bezier(.4, 0, .2, 1) .1s,
 max-width .2s cubic-bezier(.4, 0, .2, 1),
 margin-left .2s cubic-bezier(.4, 0, .2, 1),
 margin-right .2s cubic-bezier(.4, 0, .2, 1);
 
}

.n-button .n-button__content {

 display: flex;
 align-items: center;
 flex-wrap: nowrap;
 min-width: 0;
 
}

.n-button .n-button__content ~ .n-button__icon {
  margin: var(--n-icon-margin);
  margin-right: 0;
}

.n-button.n-button--block {

 display: flex;
 width: 100%;
 
}

.n-button.n-button--dashed .n-button__border, .n-button.n-button--dashed .n-button__state-border {
  border-style: dashed !important;
}

.n-button.n-button--disabled {
  cursor: not-allowed;
  opacity: var(--n-opacity-disabled);
}

@keyframes button-wave-spread {
  from {
    box-shadow: 0 0 0.5px 0 var(--n-ripple-color);
  }
  to {
    box-shadow: 0 0 0.5px 4.5px var(--n-ripple-color);
  }
}

@keyframes button-wave-opacity {
  from {
    opacity: var(--n-wave-opacity);
  }
  to {
    opacity: 0;
  }
}</style><style cssr-id="n-tag">.n-tag {

 --n-close-margin: var(--n-close-margin-top) var(--n-close-margin-right) var(--n-close-margin-bottom) var(--n-close-margin-left);
 white-space: nowrap;
 position: relative;
 box-sizing: border-box;
 cursor: default;
 display: inline-flex;
 align-items: center;
 flex-wrap: nowrap;
 padding: var(--n-padding);
 border-radius: var(--n-border-radius);
 color: var(--n-text-color);
 background-color: var(--n-color);
 transition: 
 border-color .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier),
 opacity .3s var(--n-bezier);
 line-height: 1;
 height: var(--n-height);
 font-size: var(--n-font-size);

}

.n-tag.n-tag--strong {

 font-weight: var(--n-font-weight-strong);
 
}

.n-tag .n-tag__border {

 pointer-events: none;
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 border-radius: inherit;
 border: var(--n-border);
 transition: border-color .3s var(--n-bezier);
 
}

.n-tag .n-tag__icon {

 display: flex;
 margin: 0 4px 0 0;
 color: var(--n-text-color);
 transition: color .3s var(--n-bezier);
 font-size: var(--n-avatar-size-override);
 
}

.n-tag .n-tag__avatar {

 display: flex;
 margin: 0 6px 0 0;
 
}

.n-tag .n-tag__close {

 margin: var(--n-close-margin);
 transition:
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 
}

.n-tag.n-tag--round {

 padding: 0 calc(var(--n-height) / 3);
 border-radius: calc(var(--n-height) / 2);
 
}

.n-tag.n-tag--round .n-tag__icon {

 margin: 0 4px 0 calc((var(--n-height) - 8px) / -2);
 
}

.n-tag.n-tag--round .n-tag__avatar {

 margin: 0 6px 0 calc((var(--n-height) - 8px) / -2);
 
}

.n-tag.n-tag--round.n-tag--closable {

 padding: 0 calc(var(--n-height) / 4) 0 calc(var(--n-height) / 3);
 
}

.n-tag.n-tag--icon.n-tag--round, .n-tag.n-tag--avatar.n-tag--round {

 padding: 0 calc(var(--n-height) / 3) 0 calc(var(--n-height) / 2);
 
}

.n-tag.n-tag--disabled {

 cursor: not-allowed !important;
 opacity: var(--n-opacity-disabled);
 
}

.n-tag.n-tag--checkable {

 cursor: pointer;
 box-shadow: none;
 color: var(--n-text-color-checkable);
 background-color: var(--n-color-checkable);
 
}

.n-tag.n-tag--checkable:not(.n-tag--disabled):hover {
background-color: var(--n-color-hover-checkable);
}

.n-tag.n-tag--checkable:not(.n-tag--disabled):hover:not(.n-tag--checked) {
color: var(--n-text-color-hover-checkable);
}

.n-tag.n-tag--checkable:not(.n-tag--disabled):active {
background-color: var(--n-color-pressed-checkable);
}

.n-tag.n-tag--checkable:not(.n-tag--disabled):active:not(.n-tag--checked) {
color: var(--n-text-color-pressed-checkable);
}

.n-tag.n-tag--checkable.n-tag--checked {

 color: var(--n-text-color-checked);
 background-color: var(--n-color-checked);
 
}

.n-tag.n-tag--checkable.n-tag--checked:not(.n-tag--disabled):hover {
background-color: var(--n-color-checked-hover);
}

.n-tag.n-tag--checkable.n-tag--checked:not(.n-tag--disabled):active {
background-color: var(--n-color-checked-pressed);
}</style><style cssr-id="n-breadcrumb">.n-breadcrumb {

 white-space: nowrap;
 cursor: default;
 line-height: var(--n-item-line-height);

}

.n-breadcrumb ul {

 list-style: none;
 padding: 0;
 margin: 0;
 
}

.n-breadcrumb a {

 color: inherit;
 text-decoration: inherit;
 
}

.n-breadcrumb .n-breadcrumb-item {

 font-size: var(--n-font-size);
 transition: color .3s var(--n-bezier);
 display: inline-flex;
 align-items: center;
 
}

.n-breadcrumb .n-breadcrumb-item .n-icon {

 font-size: 18px;
 vertical-align: -.2em;
 transition: color .3s var(--n-bezier);
 color: var(--n-item-text-color);
 
}

.n-breadcrumb .n-breadcrumb-item:not(:last-child).n-breadcrumb-item--clickable .n-breadcrumb-item__link {

 cursor: pointer;
 
}

.n-breadcrumb .n-breadcrumb-item:not(:last-child).n-breadcrumb-item--clickable .n-breadcrumb-item__link:hover {

 background-color: var(--n-item-color-hover);
 
}

.n-breadcrumb .n-breadcrumb-item:not(:last-child).n-breadcrumb-item--clickable .n-breadcrumb-item__link:active {

 background-color: var(--n-item-color-pressed); 
 
}

.n-breadcrumb .n-breadcrumb-item .n-breadcrumb-item__link {

 padding: 4px;
 border-radius: var(--n-item-border-radius);
 transition:
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 color: var(--n-item-text-color);
 position: relative;
 
}

.n-breadcrumb .n-breadcrumb-item .n-breadcrumb-item__link:hover {

 color: var(--n-item-text-color-hover);
 
}

.n-breadcrumb .n-breadcrumb-item .n-breadcrumb-item__link:hover .n-icon {

 color: var(--n-item-text-color-hover);
 
}

.n-breadcrumb .n-breadcrumb-item .n-breadcrumb-item__link:active {

 color: var(--n-item-text-color-pressed);
 
}

.n-breadcrumb .n-breadcrumb-item .n-breadcrumb-item__link:active .n-icon {

 color: var(--n-item-text-color-pressed);
 
}

.n-breadcrumb .n-breadcrumb-item .n-breadcrumb-item__separator {

 margin: 0 8px;
 color: var(--n-separator-color);
 transition: color .3s var(--n-bezier);
 user-select: none;
 -webkit-user-select: none;
 
}

.n-breadcrumb .n-breadcrumb-item:last-child .n-breadcrumb-item__link {

 font-weight: var(--n-font-weight-active);
 cursor: unset;
 color: var(--n-item-text-color-active);
 
}

.n-breadcrumb .n-breadcrumb-item:last-child .n-breadcrumb-item__link .n-icon {

 color: var(--n-item-text-color-active);
 
}

.n-breadcrumb .n-breadcrumb-item:last-child .n-breadcrumb-item__separator {

 display: none;
 
}</style><style cssr-id="n-base-icon">.n-base-icon {

 height: 1em;
 width: 1em;
 line-height: 1em;
 text-align: center;
 display: inline-block;
 position: relative;
 fill: currentColor;
 transform: translateZ(0);

}

.n-base-icon svg {

 height: 1em;
 width: 1em;
 
}</style><style cssr-id="n-dropdown">.n-dropdown-menu {

 transform-origin: var(--v-transform-origin);
 background-color: var(--n-color);
 border-radius: var(--n-border-radius);
 box-shadow: var(--n-box-shadow);
 position: relative;
 transition:
 background-color .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier);

}

.n-dropdown-menu.fade-in-scale-up-transition-leave-active {
  transform-origin: inherit;
  transition: opacity .2s cubic-bezier(.4, 0, 1, 1), transform .2s cubic-bezier(.4, 0, 1, 1) ;
}

.n-dropdown-menu.fade-in-scale-up-transition-enter-active {
  transform-origin: inherit;
  transition: opacity .2s cubic-bezier(0, 0, .2, 1), transform .2s cubic-bezier(0, 0, .2, 1) ;
}

.n-dropdown-menu.fade-in-scale-up-transition-enter-from, .n-dropdown-menu.fade-in-scale-up-transition-leave-to {
  opacity: 0;
  transform:  scale(.9);
}

.n-dropdown-menu.fade-in-scale-up-transition-leave-from, .n-dropdown-menu.fade-in-scale-up-transition-enter-to {
  opacity: 1;
  transform:  scale(1);
}

.n-dropdown-menu .n-dropdown-option {

 position: relative;
 
}

.n-dropdown-menu .n-dropdown-option a {

 text-decoration: none;
 color: inherit;
 outline: none;
 
}

.n-dropdown-menu .n-dropdown-option a::before {

 content: "";
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body {

 display: flex;
 cursor: pointer;
 position: relative;
 height: var(--n-option-height);
 line-height: var(--n-option-height);
 font-size: var(--n-font-size);
 color: var(--n-option-text-color);
 transition: color .3s var(--n-bezier);
 
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body::before {

 content: "";
 position: absolute;
 top: 0;
 bottom: 0;
 left: 4px;
 right: 4px;
 transition: background-color .3s var(--n-bezier);
 border-radius: var(--n-border-radius);
 
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body:not(.n-dropdown-option-body--disabled).n-dropdown-option-body--pending {

 color: var(--n-option-text-color-hover);
 
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body:not(.n-dropdown-option-body--disabled).n-dropdown-option-body--pending .n-dropdown-option-body__prefix, .n-dropdown-menu .n-dropdown-option .n-dropdown-option-body:not(.n-dropdown-option-body--disabled).n-dropdown-option-body--pending .n-dropdown-option-body__suffix {

 color: var(--n-option-text-color-hover);
 
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body:not(.n-dropdown-option-body--disabled).n-dropdown-option-body--pending::before {
background-color: var(--n-option-color-hover);
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body:not(.n-dropdown-option-body--disabled).n-dropdown-option-body--active {

 color: var(--n-option-text-color-active);
 
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body:not(.n-dropdown-option-body--disabled).n-dropdown-option-body--active .n-dropdown-option-body__prefix, .n-dropdown-menu .n-dropdown-option .n-dropdown-option-body:not(.n-dropdown-option-body--disabled).n-dropdown-option-body--active .n-dropdown-option-body__suffix {

 color: var(--n-option-text-color-active);
 
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body:not(.n-dropdown-option-body--disabled).n-dropdown-option-body--active::before {
background-color: var(--n-option-color-active);
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body:not(.n-dropdown-option-body--disabled).n-dropdown-option-body--child-active {

 color: var(--n-option-text-color-child-active);
 
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body:not(.n-dropdown-option-body--disabled).n-dropdown-option-body--child-active .n-dropdown-option-body__prefix, .n-dropdown-menu .n-dropdown-option .n-dropdown-option-body:not(.n-dropdown-option-body--disabled).n-dropdown-option-body--child-active .n-dropdown-option-body__suffix {

 color: var(--n-option-text-color-child-active);
 
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body.n-dropdown-option-body--disabled {

 cursor: not-allowed;
 opacity: var(--n-option-opacity-disabled);
 
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body.n-dropdown-option-body--group {

 font-size: calc(var(--n-font-size) - 1px);
 color: var(--n-group-header-text-color);
 
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body.n-dropdown-option-body--group .n-dropdown-option-body__prefix {

 width: calc(var(--n-option-prefix-width) / 2);
 
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body.n-dropdown-option-body--group .n-dropdown-option-body__prefix.n-dropdown-option-body__prefix--show-icon {

 width: calc(var(--n-option-icon-prefix-width) / 2);
 
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body .n-dropdown-option-body__prefix {

 width: var(--n-option-prefix-width);
 display: flex;
 justify-content: center;
 align-items: center;
 color: var(--n-prefix-color);
 transition: color .3s var(--n-bezier);
 z-index: 1;
 
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body .n-dropdown-option-body__prefix.n-dropdown-option-body__prefix--show-icon {

 width: var(--n-option-icon-prefix-width);
 
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body .n-dropdown-option-body__prefix .n-icon {

 font-size: var(--n-option-icon-size);
 
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body .n-dropdown-option-body__label {

 white-space: nowrap;
 flex: 1;
 z-index: 1;
 
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body .n-dropdown-option-body__suffix {

 box-sizing: border-box;
 flex-grow: 0;
 flex-shrink: 0;
 display: flex;
 justify-content: flex-end;
 align-items: center;
 min-width: var(--n-option-suffix-width);
 padding: 0 8px;
 transition: color .3s var(--n-bezier);
 color: var(--n-suffix-color);
 z-index: 1;
 
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body .n-dropdown-option-body__suffix.n-dropdown-option-body__suffix--has-submenu {

 width: var(--n-option-icon-suffix-width);
 
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body .n-dropdown-option-body__suffix .n-icon {

 font-size: var(--n-option-icon-size);
 
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body .n-dropdown-menu {
pointer-events: all;
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-offset-container {

 pointer-events: none;
 position: absolute;
 left: 0;
 right: 0;
 top: -4px;
 bottom: -4px;
 
}

.n-dropdown-menu .n-dropdown-divider {

 transition: background-color .3s var(--n-bezier);
 background-color: var(--n-divider-color);
 height: 1px;
 margin: 4px 0;
 
}

.n-dropdown-menu .n-dropdown-menu-wrapper {

 transform-origin: var(--v-transform-origin);
 width: fit-content;
 
}

.n-dropdown-menu > .n-scrollbar {

 height: inherit;
 max-height: inherit;
 
}

.n-dropdown-menu:not(.n-dropdown-menu--scrollable) {

 padding: var(--n-padding);
 
}

.n-dropdown-menu.n-dropdown-menu--scrollable .n-dropdown-menu__content {

 padding: var(--n-padding);
 
}</style><style cssr-id="vueuc/binder">.v-binder-follower-container {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  height: 0;
  pointer-events: none;
  z-index: auto;
}

.v-binder-follower-content {
  position: absolute;
  z-index: auto;
}

.v-binder-follower-content > * {
  pointer-events: all;
}</style><style cssr-id="n-popover">.n-popover {

 transition:
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 position: relative;
 font-size: var(--n-font-size);
 color: var(--n-text-color);
 box-shadow: var(--n-box-shadow);
 word-break: break-word;
 
}

.n-popover > .n-scrollbar {

 height: inherit;
 max-height: inherit;
 
}

.n-popover:not(.n-popover--raw) {

 background-color: var(--n-color);
 border-radius: var(--n-border-radius);
 
}

.n-popover:not(.n-popover--raw):not(.n-popover--scrollable):not(.n-popover--show-header-or-footer) {
padding: var(--n-padding);
}

.n-popover .n-popover__header {

 padding: var(--n-padding);
 border-bottom: 1px solid var(--n-divider-color);
 transition: border-color .3s var(--n-bezier);
 
}

.n-popover .n-popover__footer {

 padding: var(--n-padding);
 border-top: 1px solid var(--n-divider-color);
 transition: border-color .3s var(--n-bezier);
 
}

.n-popover.n-popover--scrollable .n-popover__content, .n-popover.n-popover--show-header-or-footer .n-popover__content {

 padding: var(--n-padding);
 
}

.n-popover-shared {

 transform-origin: inherit;
 
}

.n-popover-shared .n-popover-arrow-wrapper {

 position: absolute;
 overflow: hidden;
 pointer-events: none;
 
}

.n-popover-shared .n-popover-arrow-wrapper .n-popover-arrow {

 transition: background-color .3s var(--n-bezier);
 position: absolute;
 display: block;
 width: calc(var(--n-arrow-height) * 1.414);
 height: calc(var(--n-arrow-height) * 1.414);
 box-shadow: 0 0 8px 0 rgba(0, 0, 0, .12);
 transform: rotate(45deg);
 background-color: var(--n-color);
 pointer-events: all;
 
}

.n-popover-shared.popover-transition-enter-from, .n-popover-shared.popover-transition-leave-to {

 opacity: 0;
 transform: scale(.85);
 
}

.n-popover-shared.popover-transition-enter-to, .n-popover-shared.popover-transition-leave-from {

 transform: scale(1);
 opacity: 1;
 
}

.n-popover-shared.popover-transition-enter-active {

 transition:
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 opacity .15s var(--n-bezier-ease-out),
 transform .15s var(--n-bezier-ease-out);
 
}

.n-popover-shared.popover-transition-leave-active {

 transition:
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 opacity .15s var(--n-bezier-ease-in),
 transform .15s var(--n-bezier-ease-in);
 
}

[v-placement="top-start"] > .n-popover-shared {

 margin-bottom: var(--n-space);
 
}

[v-placement="top-start"] > .n-popover-shared.n-popover-shared--show-arrow {

 margin-bottom: var(--n-space-arrow);
 
}

[v-placement="top-start"] > .n-popover-shared.n-popover-shared--overlap {

 margin: 0;
 
}

[v-placement="top-start"] > .n-popover-shared > .n-popover-arrow-wrapper {

 right: 0;
 left: 0;
 top: 0;
 bottom: 0;
 top: 100%;
 bottom: auto;
 height: var(--n-space-arrow);
 
}

[v-placement="top-start"] > .n-popover-shared > .n-popover-arrow-wrapper .n-popover-arrow {

 top: calc(var(--n-arrow-height) * 1.414 / -2);
 left: calc(var(--n-arrow-offset) - var(--v-offset-left));
 
}

[v-placement="top"] > .n-popover-shared {

 margin-bottom: var(--n-space);
 
}

[v-placement="top"] > .n-popover-shared.n-popover-shared--show-arrow {

 margin-bottom: var(--n-space-arrow);
 
}

[v-placement="top"] > .n-popover-shared.n-popover-shared--overlap {

 margin: 0;
 
}

[v-placement="top"] > .n-popover-shared > .n-popover-arrow-wrapper {

 right: 0;
 left: 0;
 top: 0;
 bottom: 0;
 top: 100%;
 bottom: auto;
 height: var(--n-space-arrow);
 
}

[v-placement="top"] > .n-popover-shared > .n-popover-arrow-wrapper .n-popover-arrow {

 top: calc(var(--n-arrow-height) * 1.414 / -2);
 transform: translateX(calc(var(--n-arrow-height) * 1.414 / -2)) rotate(45deg);
 left: 50%;
 
}

[v-placement="top-end"] > .n-popover-shared {

 margin-bottom: var(--n-space);
 
}

[v-placement="top-end"] > .n-popover-shared.n-popover-shared--show-arrow {

 margin-bottom: var(--n-space-arrow);
 
}

[v-placement="top-end"] > .n-popover-shared.n-popover-shared--overlap {

 margin: 0;
 
}

[v-placement="top-end"] > .n-popover-shared > .n-popover-arrow-wrapper {

 right: 0;
 left: 0;
 top: 0;
 bottom: 0;
 top: 100%;
 bottom: auto;
 height: var(--n-space-arrow);
 
}

[v-placement="top-end"] > .n-popover-shared > .n-popover-arrow-wrapper .n-popover-arrow {

 top: calc(var(--n-arrow-height) * 1.414 / -2);
 right: calc(var(--n-arrow-offset) + var(--v-offset-left));
 
}

[v-placement="bottom-start"] > .n-popover-shared {

 margin-top: var(--n-space);
 
}

[v-placement="bottom-start"] > .n-popover-shared.n-popover-shared--show-arrow {

 margin-top: var(--n-space-arrow);
 
}

[v-placement="bottom-start"] > .n-popover-shared.n-popover-shared--overlap {

 margin: 0;
 
}

[v-placement="bottom-start"] > .n-popover-shared > .n-popover-arrow-wrapper {

 right: 0;
 left: 0;
 top: 0;
 bottom: 0;
 bottom: 100%;
 top: auto;
 height: var(--n-space-arrow);
 
}

[v-placement="bottom-start"] > .n-popover-shared > .n-popover-arrow-wrapper .n-popover-arrow {

 bottom: calc(var(--n-arrow-height) * 1.414 / -2);
 left: calc(var(--n-arrow-offset) - var(--v-offset-left));
 
}

[v-placement="bottom"] > .n-popover-shared {

 margin-top: var(--n-space);
 
}

[v-placement="bottom"] > .n-popover-shared.n-popover-shared--show-arrow {

 margin-top: var(--n-space-arrow);
 
}

[v-placement="bottom"] > .n-popover-shared.n-popover-shared--overlap {

 margin: 0;
 
}

[v-placement="bottom"] > .n-popover-shared > .n-popover-arrow-wrapper {

 right: 0;
 left: 0;
 top: 0;
 bottom: 0;
 bottom: 100%;
 top: auto;
 height: var(--n-space-arrow);
 
}

[v-placement="bottom"] > .n-popover-shared > .n-popover-arrow-wrapper .n-popover-arrow {

 bottom: calc(var(--n-arrow-height) * 1.414 / -2);
 transform: translateX(calc(var(--n-arrow-height) * 1.414 / -2)) rotate(45deg);
 left: 50%;
 
}

[v-placement="bottom-end"] > .n-popover-shared {

 margin-top: var(--n-space);
 
}

[v-placement="bottom-end"] > .n-popover-shared.n-popover-shared--show-arrow {

 margin-top: var(--n-space-arrow);
 
}

[v-placement="bottom-end"] > .n-popover-shared.n-popover-shared--overlap {

 margin: 0;
 
}

[v-placement="bottom-end"] > .n-popover-shared > .n-popover-arrow-wrapper {

 right: 0;
 left: 0;
 top: 0;
 bottom: 0;
 bottom: 100%;
 top: auto;
 height: var(--n-space-arrow);
 
}

[v-placement="bottom-end"] > .n-popover-shared > .n-popover-arrow-wrapper .n-popover-arrow {

 bottom: calc(var(--n-arrow-height) * 1.414 / -2);
 right: calc(var(--n-arrow-offset) + var(--v-offset-left));
 
}

[v-placement="left-start"] > .n-popover-shared {

 margin-right: var(--n-space);
 
}

[v-placement="left-start"] > .n-popover-shared.n-popover-shared--show-arrow {

 margin-right: var(--n-space-arrow);
 
}

[v-placement="left-start"] > .n-popover-shared.n-popover-shared--overlap {

 margin: 0;
 
}

[v-placement="left-start"] > .n-popover-shared > .n-popover-arrow-wrapper {

 right: 0;
 left: 0;
 top: 0;
 bottom: 0;
 left: 100%;
 right: auto;
 width: var(--n-space-arrow);
 
}

[v-placement="left-start"] > .n-popover-shared > .n-popover-arrow-wrapper .n-popover-arrow {

 left: calc(var(--n-arrow-height) * 1.414 / -2);
 top: calc(var(--n-arrow-offset-vertical) - var(--v-offset-top));
 
}

[v-placement="left"] > .n-popover-shared {

 margin-right: var(--n-space);
 
}

[v-placement="left"] > .n-popover-shared.n-popover-shared--show-arrow {

 margin-right: var(--n-space-arrow);
 
}

[v-placement="left"] > .n-popover-shared.n-popover-shared--overlap {

 margin: 0;
 
}

[v-placement="left"] > .n-popover-shared > .n-popover-arrow-wrapper {

 right: 0;
 left: 0;
 top: 0;
 bottom: 0;
 left: 100%;
 right: auto;
 width: var(--n-space-arrow);
 
}

[v-placement="left"] > .n-popover-shared > .n-popover-arrow-wrapper .n-popover-arrow {

 left: calc(var(--n-arrow-height) * 1.414 / -2);
 transform: translateY(calc(var(--n-arrow-height) * 1.414 / -2)) rotate(45deg);
 top: 50%;
 
}

[v-placement="left-end"] > .n-popover-shared {

 margin-right: var(--n-space);
 
}

[v-placement="left-end"] > .n-popover-shared.n-popover-shared--show-arrow {

 margin-right: var(--n-space-arrow);
 
}

[v-placement="left-end"] > .n-popover-shared.n-popover-shared--overlap {

 margin: 0;
 
}

[v-placement="left-end"] > .n-popover-shared > .n-popover-arrow-wrapper {

 right: 0;
 left: 0;
 top: 0;
 bottom: 0;
 left: 100%;
 right: auto;
 width: var(--n-space-arrow);
 
}

[v-placement="left-end"] > .n-popover-shared > .n-popover-arrow-wrapper .n-popover-arrow {

 left: calc(var(--n-arrow-height) * 1.414 / -2);
 bottom: calc(var(--n-arrow-offset-vertical) + var(--v-offset-top));
 
}

[v-placement="right-start"] > .n-popover-shared {

 margin-left: var(--n-space);
 
}

[v-placement="right-start"] > .n-popover-shared.n-popover-shared--show-arrow {

 margin-left: var(--n-space-arrow);
 
}

[v-placement="right-start"] > .n-popover-shared.n-popover-shared--overlap {

 margin: 0;
 
}

[v-placement="right-start"] > .n-popover-shared > .n-popover-arrow-wrapper {

 right: 0;
 left: 0;
 top: 0;
 bottom: 0;
 right: 100%;
 left: auto;
 width: var(--n-space-arrow);
 
}

[v-placement="right-start"] > .n-popover-shared > .n-popover-arrow-wrapper .n-popover-arrow {

 right: calc(var(--n-arrow-height) * 1.414 / -2);
 top: calc(var(--n-arrow-offset-vertical) - var(--v-offset-top));
 
}

[v-placement="right"] > .n-popover-shared {

 margin-left: var(--n-space);
 
}

[v-placement="right"] > .n-popover-shared.n-popover-shared--show-arrow {

 margin-left: var(--n-space-arrow);
 
}

[v-placement="right"] > .n-popover-shared.n-popover-shared--overlap {

 margin: 0;
 
}

[v-placement="right"] > .n-popover-shared > .n-popover-arrow-wrapper {

 right: 0;
 left: 0;
 top: 0;
 bottom: 0;
 right: 100%;
 left: auto;
 width: var(--n-space-arrow);
 
}

[v-placement="right"] > .n-popover-shared > .n-popover-arrow-wrapper .n-popover-arrow {

 right: calc(var(--n-arrow-height) * 1.414 / -2);
 transform: translateY(calc(var(--n-arrow-height) * 1.414 / -2)) rotate(45deg);
 top: 50%;
 
}

[v-placement="right-end"] > .n-popover-shared {

 margin-left: var(--n-space);
 
}

[v-placement="right-end"] > .n-popover-shared.n-popover-shared--show-arrow {

 margin-left: var(--n-space-arrow);
 
}

[v-placement="right-end"] > .n-popover-shared.n-popover-shared--overlap {

 margin: 0;
 
}

[v-placement="right-end"] > .n-popover-shared > .n-popover-arrow-wrapper {

 right: 0;
 left: 0;
 top: 0;
 bottom: 0;
 right: 100%;
 left: auto;
 width: var(--n-space-arrow);
 
}

[v-placement="right-end"] > .n-popover-shared > .n-popover-arrow-wrapper .n-popover-arrow {

 right: calc(var(--n-arrow-height) * 1.414 / -2);
 bottom: calc(var(--n-arrow-offset-vertical) + var(--v-offset-top));
 
}

[v-placement="right-start"] > .n-popover-shared.n-popover-shared--center-arrow .n-popover-arrow {
top: calc(max(calc((var(--v-target-height, 0px) - var(--n-arrow-height) * 1.414) / 2), var(--n-arrow-offset-vertical)) - var(--v-offset-top));
}

[v-placement="left-start"] > .n-popover-shared.n-popover-shared--center-arrow .n-popover-arrow {
top: calc(max(calc((var(--v-target-height, 0px) - var(--n-arrow-height) * 1.414) / 2), var(--n-arrow-offset-vertical)) - var(--v-offset-top));
}

[v-placement="top-end"] > .n-popover-shared.n-popover-shared--center-arrow .n-popover-arrow {
right: calc(max(calc((var(--v-target-width, 0px) - var(--n-arrow-height) * 1.414) / 2), var(--n-arrow-offset)) + var(--v-offset-left));
}

[v-placement="bottom-end"] > .n-popover-shared.n-popover-shared--center-arrow .n-popover-arrow {
right: calc(max(calc((var(--v-target-width, 0px) - var(--n-arrow-height) * 1.414) / 2), var(--n-arrow-offset)) + var(--v-offset-left));
}

[v-placement="right-end"] > .n-popover-shared.n-popover-shared--center-arrow .n-popover-arrow {
bottom: calc(max(calc((var(--v-target-height, 0px) - var(--n-arrow-height) * 1.414) / 2), var(--n-arrow-offset-vertical)) + var(--v-offset-top));
}

[v-placement="left-end"] > .n-popover-shared.n-popover-shared--center-arrow .n-popover-arrow {
bottom: calc(max(calc((var(--v-target-height, 0px) - var(--n-arrow-height) * 1.414) / 2), var(--n-arrow-offset-vertical)) + var(--v-offset-top));
}

[v-placement="top-start"] > .n-popover-shared.n-popover-shared--center-arrow .n-popover-arrow {
left: calc(max(calc((var(--v-target-width, 0px) - var(--n-arrow-height) * 1.414) / 2), var(--n-arrow-offset)) - var(--v-offset-left));
}

[v-placement="bottom-start"] > .n-popover-shared.n-popover-shared--center-arrow .n-popover-arrow {
left: calc(max(calc((var(--v-target-width, 0px) - var(--n-arrow-height) * 1.414) / 2), var(--n-arrow-offset)) - var(--v-offset-left));
}</style><style cssr-id="n-icon">.n-icon {

 height: 1em;
 width: 1em;
 line-height: 1em;
 text-align: center;
 display: inline-block;
 position: relative;
 fill: currentColor;
 transform: translateZ(0);

}

.n-icon.n-icon--color-transition {
  transition: color .3s var(--n-bezier);
}

.n-icon.n-icon--depth {
  color: var(--n-color);
}

.n-icon.n-icon--depth svg {
  opacity: var(--n-opacity);
  transition: opacity .3s var(--n-bezier);
}

.n-icon svg {
  height: 1em;
  width: 1em;
}</style><style cssr-id="n-menu">.n-menu {

 background-color: var(--n-color);
 color: var(--n-item-text-color);
 overflow: hidden;
 transition: background-color .3s var(--n-bezier);
 box-sizing: border-box;
 font-size: var(--n-font-size);
 padding-bottom: 6px;
 
}

.n-menu.n-menu--horizontal {

 max-width: 100%;
 width: 100%;
 display: flex;
 overflow: hidden;
 padding-bottom: 0;
 
}

.n-menu.n-menu--horizontal .n-submenu {
margin: 0;
}

.n-menu.n-menu--horizontal .n-menu-item {
margin: 0;
}

.n-menu.n-menu--horizontal .n-menu-item-content {

 padding: 0 20px;
 border-bottom: 2px solid #0000;
 
}

.n-menu.n-menu--horizontal .n-menu-item-content::before {
display: none;
}

.n-menu.n-menu--horizontal .n-menu-item-content.n-menu-item-content--selected {
border-bottom: 2px solid var(--n-border-color-horizontal)
}

.n-menu.n-menu--horizontal .n-menu-item-content.n-menu-item-content--selected .n-menu-item-content__icon {
color: var(--n-item-icon-color-active-horizontal);
}

.n-menu.n-menu--horizontal .n-menu-item-content.n-menu-item-content--selected .n-menu-item-content-header {

 color: var(--n-item-text-color-active-horizontal);
 
}

.n-menu.n-menu--horizontal .n-menu-item-content.n-menu-item-content--selected .n-menu-item-content-header a {
color: var(--n-item-text-color-active-horizontal);
}

.n-menu.n-menu--horizontal .n-menu-item-content.n-menu-item-content--selected .n-menu-item-content-header .n-menu-item-content-header__extra {
color: var(--n-item-text-color-active-horizontal);
}

.n-menu.n-menu--horizontal .n-menu-item-content.n-menu-item-content--child-active {

 border-bottom: 2px solid var(--n-border-color-horizontal);
 
}

.n-menu.n-menu--horizontal .n-menu-item-content.n-menu-item-content--child-active .n-menu-item-content-header {

 color: var(--n-item-text-color-child-active-horizontal);
 
}

.n-menu.n-menu--horizontal .n-menu-item-content.n-menu-item-content--child-active .n-menu-item-content-header a {

 color: var(--n-item-text-color-child-active-horizontal);
 
}

.n-menu.n-menu--horizontal .n-menu-item-content.n-menu-item-content--child-active .n-menu-item-content-header .n-menu-item-content-header__extra {

 color: var(--n-item-text-color-child-active-horizontal);
 
}

.n-menu.n-menu--horizontal .n-menu-item-content.n-menu-item-content--child-active .n-menu-item-content__icon {

 color: var(--n-item-icon-color-child-active-horizontal);
 
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled):not(.n-menu-item-content--selected, child-active):focus-within .n-menu-item-content__icon {

 color: var(--n-item-icon-color-hover-horizontal);
 
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled):not(.n-menu-item-content--selected, child-active):focus-within .n-menu-item-content-header {

 color: var(--n-item-text-color-hover-horizontal);
 
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled):not(.n-menu-item-content--selected, child-active):focus-within .n-menu-item-content-header a {

 color: var(--n-item-text-color-hover-horizontal);
 
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled):not(.n-menu-item-content--selected, child-active):focus-within .n-menu-item-content-header .n-menu-item-content-header__extra {

 color: var(--n-item-text-color-hover-horizontal);
 
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--selected.n-menu-item-content--hover .n-menu-item-content__icon {
color: var(--n-item-icon-color-active-hover-horizontal);
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--selected.n-menu-item-content--hover .n-menu-item-content-header {

 color: var(--n-item-text-color-active-hover-horizontal);
 
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--selected.n-menu-item-content--hover .n-menu-item-content-header a {
color: var(--n-item-text-color-active-hover-horizontal);
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--selected.n-menu-item-content--hover .n-menu-item-content-header .n-menu-item-content-header__extra {
color: var(--n-item-text-color-active-hover-horizontal);
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--selected:hover .n-menu-item-content__icon {
color: var(--n-item-icon-color-active-hover-horizontal);
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--selected:hover .n-menu-item-content-header {

 color: var(--n-item-text-color-active-hover-horizontal);
 
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--selected:hover .n-menu-item-content-header a {
color: var(--n-item-text-color-active-hover-horizontal);
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--selected:hover .n-menu-item-content-header .n-menu-item-content-header__extra {
color: var(--n-item-text-color-active-hover-horizontal);
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--child-active.n-menu-item-content--hover .n-menu-item-content__icon {
color: var(--n-item-icon-color-child-active-hover-horizontal);
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--child-active.n-menu-item-content--hover .n-menu-item-content-header {

 color: var(--n-item-text-color-child-active-hover-horizontal);
 
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--child-active.n-menu-item-content--hover .n-menu-item-content-header a {
color: var(--n-item-text-color-child-active-hover-horizontal);
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--child-active.n-menu-item-content--hover .n-menu-item-content-header .n-menu-item-content-header__extra {
color: var(--n-item-text-color-child-active-hover-horizontal);
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--child-active:hover .n-menu-item-content__icon {
color: var(--n-item-icon-color-child-active-hover-horizontal);
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--child-active:hover .n-menu-item-content-header {

 color: var(--n-item-text-color-child-active-hover-horizontal);
 
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--child-active:hover .n-menu-item-content-header a {
color: var(--n-item-text-color-child-active-hover-horizontal);
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--child-active:hover .n-menu-item-content-header .n-menu-item-content-header__extra {
color: var(--n-item-text-color-child-active-hover-horizontal);
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--hover {
border-bottom: 2px solid var(--n-border-color-horizontal);
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--hover .n-menu-item-content__icon {

 color: var(--n-item-icon-color-hover-horizontal);
 
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--hover .n-menu-item-content-header {

 color: var(--n-item-text-color-hover-horizontal);
 
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--hover .n-menu-item-content-header a {

 color: var(--n-item-text-color-hover-horizontal);
 
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--hover .n-menu-item-content-header .n-menu-item-content-header__extra {

 color: var(--n-item-text-color-hover-horizontal);
 
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled):hover {
border-bottom: 2px solid var(--n-border-color-horizontal);
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled):hover .n-menu-item-content__icon {

 color: var(--n-item-icon-color-hover-horizontal);
 
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled):hover .n-menu-item-content-header {

 color: var(--n-item-text-color-hover-horizontal);
 
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled):hover .n-menu-item-content-header a {

 color: var(--n-item-text-color-hover-horizontal);
 
}

.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled):hover .n-menu-item-content-header .n-menu-item-content-header__extra {

 color: var(--n-item-text-color-hover-horizontal);
 
}

.n-menu.n-menu--horizontal .n-menu-item-content .n-menu-item-content-header a {
color: var(--n-item-text-color-horizontal);
}

.n-menu:not(.n-menu--responsive) .n-menu-item-content-header {

 overflow: hidden;
 text-overflow: ellipsis;
 
}

.n-menu.n-menu--collapsed .n-menu-item-content.n-menu-item-content--selected::before {

 background-color: var(--n-item-color-active-collapsed) !important;
 
}

.n-menu.n-menu--collapsed .n-menu-item-content .n-menu-item-content-header {
opacity: 0;
}

.n-menu.n-menu--collapsed .n-menu-item-content .n-menu-item-content__arrow {
opacity: 0;
}

.n-menu.n-menu--collapsed .n-menu-item-content .n-menu-item-content__icon {
color: var(--n-item-icon-color-collapsed);
}

.n-menu .n-menu-item {

 height: var(--n-item-height);
 margin-top: 6px;
 position: relative;
 
}

.n-menu .n-menu-item-content {

 box-sizing: border-box;
 line-height: 1.75;
 height: 100%;
 display: grid;
 grid-template-areas: "icon content arrow";
 grid-template-columns: auto 1fr auto;
 align-items: center;
 cursor: pointer;
 position: relative;
 padding-right: 18px;
 transition:
 background-color .3s var(--n-bezier),
 padding-left .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 
}

.n-menu .n-menu-item-content > * {
z-index: 1;
}

.n-menu .n-menu-item-content::before {

 z-index: auto;
 content: "";
 background-color: #0000;
 position: absolute;
 left: 8px;
 right: 8px;
 top: 0;
 bottom: 0;
 pointer-events: none;
 border-radius: var(--n-border-radius);
 transition: background-color .3s var(--n-bezier);
 
}

.n-menu .n-menu-item-content.n-menu-item-content--disabled {

 opacity: .45;
 cursor: not-allowed;
 
}

.n-menu .n-menu-item-content.n-menu-item-content--collapsed .n-menu-item-content__arrow {
transform: rotate(0);
}

.n-menu .n-menu-item-content.n-menu-item-content--selected::before {
background-color: var(--n-item-color-active);
}

.n-menu .n-menu-item-content.n-menu-item-content--selected .n-menu-item-content__arrow {
color: var(--n-arrow-color-active);
}

.n-menu .n-menu-item-content.n-menu-item-content--selected .n-menu-item-content__icon {
color: var(--n-item-icon-color-active);
}

.n-menu .n-menu-item-content.n-menu-item-content--selected .n-menu-item-content-header {

 color: var(--n-item-text-color-active);
 
}

.n-menu .n-menu-item-content.n-menu-item-content--selected .n-menu-item-content-header a {
color: var(--n-item-text-color-active);
}

.n-menu .n-menu-item-content.n-menu-item-content--selected .n-menu-item-content-header .n-menu-item-content-header__extra {
color: var(--n-item-text-color-active);
}

.n-menu .n-menu-item-content.n-menu-item-content--child-active .n-menu-item-content-header {

 color: var(--n-item-text-color-child-active);
 
}

.n-menu .n-menu-item-content.n-menu-item-content--child-active .n-menu-item-content-header a {

 color: var(--n-item-text-color-child-active);
 
}

.n-menu .n-menu-item-content.n-menu-item-content--child-active .n-menu-item-content-header .n-menu-item-content-header__extra {

 color: var(--n-item-text-color-child-active);
 
}

.n-menu .n-menu-item-content.n-menu-item-content--child-active .n-menu-item-content__arrow {

 color: var(--n-arrow-color-child-active);
 
}

.n-menu .n-menu-item-content.n-menu-item-content--child-active .n-menu-item-content__icon {

 color: var(--n-item-icon-color-child-active);
 
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled):not(.n-menu-item-content--selected, child-active):focus-within::before {
background-color: var(--n-item-color-hover);
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled):not(.n-menu-item-content--selected, child-active):focus-within .n-menu-item-content__arrow {

 color: var(--n-arrow-color-hover);
 
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled):not(.n-menu-item-content--selected, child-active):focus-within .n-menu-item-content__icon {

 color: var(--n-item-icon-color-hover);
 
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled):not(.n-menu-item-content--selected, child-active):focus-within .n-menu-item-content-header {

 color: var(--n-item-text-color-hover);
 
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled):not(.n-menu-item-content--selected, child-active):focus-within .n-menu-item-content-header a {

 color: var(--n-item-text-color-hover);
 
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled):not(.n-menu-item-content--selected, child-active):focus-within .n-menu-item-content-header .n-menu-item-content-header__extra {

 color: var(--n-item-text-color-hover);
 
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--selected.n-menu-item-content--hover .n-menu-item-content__arrow {
color: var(--n-arrow-color-active-hover);
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--selected.n-menu-item-content--hover .n-menu-item-content__icon {
color: var(--n-item-icon-color-active-hover);
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--selected.n-menu-item-content--hover .n-menu-item-content-header {

 color: var(--n-item-text-color-active-hover);
 
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--selected.n-menu-item-content--hover .n-menu-item-content-header a {
color: var(--n-item-text-color-active-hover);
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--selected.n-menu-item-content--hover .n-menu-item-content-header .n-menu-item-content-header__extra {
color: var(--n-item-text-color-active-hover);
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--selected:hover .n-menu-item-content__arrow {
color: var(--n-arrow-color-active-hover);
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--selected:hover .n-menu-item-content__icon {
color: var(--n-item-icon-color-active-hover);
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--selected:hover .n-menu-item-content-header {

 color: var(--n-item-text-color-active-hover);
 
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--selected:hover .n-menu-item-content-header a {
color: var(--n-item-text-color-active-hover);
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--selected:hover .n-menu-item-content-header .n-menu-item-content-header__extra {
color: var(--n-item-text-color-active-hover);
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--child-active.n-menu-item-content--hover .n-menu-item-content__arrow {
color: var(--n-arrow-color-child-active-hover);
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--child-active.n-menu-item-content--hover .n-menu-item-content__icon {
color: var(--n-item-icon-color-child-active-hover);
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--child-active.n-menu-item-content--hover .n-menu-item-content-header {

 color: var(--n-item-text-color-child-active-hover);
 
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--child-active.n-menu-item-content--hover .n-menu-item-content-header a {
color: var(--n-item-text-color-child-active-hover);
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--child-active.n-menu-item-content--hover .n-menu-item-content-header .n-menu-item-content-header__extra {
color: var(--n-item-text-color-child-active-hover);
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--child-active:hover .n-menu-item-content__arrow {
color: var(--n-arrow-color-child-active-hover);
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--child-active:hover .n-menu-item-content__icon {
color: var(--n-item-icon-color-child-active-hover);
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--child-active:hover .n-menu-item-content-header {

 color: var(--n-item-text-color-child-active-hover);
 
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--child-active:hover .n-menu-item-content-header a {
color: var(--n-item-text-color-child-active-hover);
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--child-active:hover .n-menu-item-content-header .n-menu-item-content-header__extra {
color: var(--n-item-text-color-child-active-hover);
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--selected.n-menu-item-content--hover::before {
background-color: var(--n-item-color-active-hover);
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--selected:hover::before {
background-color: var(--n-item-color-active-hover);
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--hover::before {
background-color: var(--n-item-color-hover);
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--hover .n-menu-item-content__arrow {

 color: var(--n-arrow-color-hover);
 
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--hover .n-menu-item-content__icon {

 color: var(--n-item-icon-color-hover);
 
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--hover .n-menu-item-content-header {

 color: var(--n-item-text-color-hover);
 
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--hover .n-menu-item-content-header a {

 color: var(--n-item-text-color-hover);
 
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--hover .n-menu-item-content-header .n-menu-item-content-header__extra {

 color: var(--n-item-text-color-hover);
 
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled):hover::before {
background-color: var(--n-item-color-hover);
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled):hover .n-menu-item-content__arrow {

 color: var(--n-arrow-color-hover);
 
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled):hover .n-menu-item-content__icon {

 color: var(--n-item-icon-color-hover);
 
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled):hover .n-menu-item-content-header {

 color: var(--n-item-text-color-hover);
 
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled):hover .n-menu-item-content-header a {

 color: var(--n-item-text-color-hover);
 
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled):hover .n-menu-item-content-header .n-menu-item-content-header__extra {

 color: var(--n-item-text-color-hover);
 
}

.n-menu .n-menu-item-content .n-menu-item-content__icon {

 grid-area: icon;
 color: var(--n-item-icon-color);
 transition:
 color .3s var(--n-bezier),
 font-size .3s var(--n-bezier),
 margin-right .3s var(--n-bezier);
 box-sizing: content-box;
 display: inline-flex;
 align-items: center;
 justify-content: center;
 
}

.n-menu .n-menu-item-content .n-menu-item-content__arrow {

 grid-area: arrow;
 font-size: 16px;
 color: var(--n-arrow-color);
 transform: rotate(180deg);
 opacity: 1;
 transition:
 color .3s var(--n-bezier),
 transform 0.2s var(--n-bezier),
 opacity 0.2s var(--n-bezier);
 
}

.n-menu .n-menu-item-content .n-menu-item-content-header {

 grid-area: content;
 transition:
 color .3s var(--n-bezier),
 opacity .3s var(--n-bezier);
 opacity: 1;
 white-space: nowrap;
 color: var(--n-item-text-color);
 
}

.n-menu .n-menu-item-content .n-menu-item-content-header a {

 outline: none;
 text-decoration: none;
 transition: color .3s var(--n-bezier);
 color: var(--n-item-text-color);
 
}

.n-menu .n-menu-item-content .n-menu-item-content-header a::before {

 content: "";
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 
}

.n-menu .n-menu-item-content .n-menu-item-content-header .n-menu-item-content-header__extra {

 font-size: .93em;
 color: var(--n-group-text-color);
 transition: color .3s var(--n-bezier);
 
}

.n-menu .n-submenu {

 cursor: pointer;
 position: relative;
 margin-top: 6px;
 
}

.n-menu .n-submenu .n-menu-item-content {

 height: var(--n-item-height);
 
}

.n-menu .n-submenu .n-submenu-children {

 overflow: hidden;
 padding: 0;
 
}

.n-menu .n-submenu .n-submenu-children.fade-in-height-expand-transition-leave-from, .n-menu .n-submenu .n-submenu-children.fade-in-height-expand-transition-enter-to {
  opacity: 1;
}

.n-menu .n-submenu .n-submenu-children.fade-in-height-expand-transition-leave-to, .n-menu .n-submenu .n-submenu-children.fade-in-height-expand-transition-enter-from {
  opacity: 0;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.n-menu .n-submenu .n-submenu-children.fade-in-height-expand-transition-leave-active {

 overflow: hidden;
 transition:
 max-height .2s cubic-bezier(.4, 0, .2, 1) 0s,
 opacity .2s cubic-bezier(0, 0, .2, 1) 0s,
 margin-top .2s cubic-bezier(.4, 0, .2, 1) 0s,
 margin-bottom .2s cubic-bezier(.4, 0, .2, 1) 0s,
 padding-top .2s cubic-bezier(.4, 0, .2, 1) 0s,
 padding-bottom .2s cubic-bezier(.4, 0, .2, 1) 0s
 
 
}

.n-menu .n-submenu .n-submenu-children.fade-in-height-expand-transition-enter-active {

 overflow: hidden;
 transition:
 max-height .2s cubic-bezier(.4, 0, .2, 1),
 opacity .2s cubic-bezier(.4, 0, 1, 1),
 margin-top .2s cubic-bezier(.4, 0, .2, 1),
 margin-bottom .2s cubic-bezier(.4, 0, .2, 1),
 padding-top .2s cubic-bezier(.4, 0, .2, 1),
 padding-bottom .2s cubic-bezier(.4, 0, .2, 1)
 
 
}

.n-menu .n-menu-item-group .n-menu-item-group-title {

 margin-top: 6px;
 color: var(--n-group-text-color);
 cursor: default;
 font-size: .93em;
 height: 36px;
 display: flex;
 align-items: center;
 transition:
 padding-left .3s var(--n-bezier),
 color .3s var(--n-bezier);
 
}

.n-menu-tooltip a {

 color: inherit;
 text-decoration: none;
 
}

.n-menu-divider {

 transition: background-color .3s var(--n-bezier);
 background-color: var(--n-divider-color);
 height: 1px;
 margin: 6px 18px;
 
}</style><style cssr-id="n-scrollbar">.n-scrollbar {

 overflow: hidden;
 position: relative;
 z-index: auto;
 height: 100%;
 width: 100%;

}

.n-scrollbar > .n-scrollbar-container {

 width: 100%;
 overflow: scroll;
 height: 100%;
 min-height: inherit;
 max-height: inherit;
 scrollbar-width: none;
 
}

.n-scrollbar > .n-scrollbar-container::-webkit-scrollbar, .n-scrollbar > .n-scrollbar-container::-webkit-scrollbar-track-piece, .n-scrollbar > .n-scrollbar-container::-webkit-scrollbar-thumb {

 width: 0;
 height: 0;
 display: none;
 
}

.n-scrollbar > .n-scrollbar-container > .n-scrollbar-content {

 box-sizing: border-box;
 min-width: 100%;
 
}

.n-scrollbar > .n-scrollbar-rail, .n-scrollbar + .n-scrollbar-rail {

 position: absolute;
 pointer-events: none;
 user-select: none;
 background: var(--n-scrollbar-rail-color);
 -webkit-user-select: none;
 
}

.n-scrollbar > .n-scrollbar-rail.n-scrollbar-rail--horizontal, .n-scrollbar + .n-scrollbar-rail.n-scrollbar-rail--horizontal {

 height: var(--n-scrollbar-height);
 
}

.n-scrollbar > .n-scrollbar-rail.n-scrollbar-rail--horizontal > .n-scrollbar-rail__scrollbar, .n-scrollbar + .n-scrollbar-rail.n-scrollbar-rail--horizontal > .n-scrollbar-rail__scrollbar {

 height: var(--n-scrollbar-height);
 border-radius: var(--n-scrollbar-border-radius);
 right: 0;
 
}

.n-scrollbar > .n-scrollbar-rail.n-scrollbar-rail--horizontal--top, .n-scrollbar + .n-scrollbar-rail.n-scrollbar-rail--horizontal--top {

 top: var(--n-scrollbar-rail-top-horizontal-top); 
 right: var(--n-scrollbar-rail-right-horizontal-top); 
 bottom: var(--n-scrollbar-rail-bottom-horizontal-top); 
 left: var(--n-scrollbar-rail-left-horizontal-top); 
 
}

.n-scrollbar > .n-scrollbar-rail.n-scrollbar-rail--horizontal--bottom, .n-scrollbar + .n-scrollbar-rail.n-scrollbar-rail--horizontal--bottom {

 top: var(--n-scrollbar-rail-top-horizontal-bottom); 
 right: var(--n-scrollbar-rail-right-horizontal-bottom); 
 bottom: var(--n-scrollbar-rail-bottom-horizontal-bottom); 
 left: var(--n-scrollbar-rail-left-horizontal-bottom); 
 
}

.n-scrollbar > .n-scrollbar-rail.n-scrollbar-rail--vertical, .n-scrollbar + .n-scrollbar-rail.n-scrollbar-rail--vertical {

 width: var(--n-scrollbar-width);
 
}

.n-scrollbar > .n-scrollbar-rail.n-scrollbar-rail--vertical > .n-scrollbar-rail__scrollbar, .n-scrollbar + .n-scrollbar-rail.n-scrollbar-rail--vertical > .n-scrollbar-rail__scrollbar {

 width: var(--n-scrollbar-width);
 border-radius: var(--n-scrollbar-border-radius);
 bottom: 0;
 
}

.n-scrollbar > .n-scrollbar-rail.n-scrollbar-rail--vertical--left, .n-scrollbar + .n-scrollbar-rail.n-scrollbar-rail--vertical--left {

 top: var(--n-scrollbar-rail-top-vertical-left); 
 right: var(--n-scrollbar-rail-right-vertical-left); 
 bottom: var(--n-scrollbar-rail-bottom-vertical-left); 
 left: var(--n-scrollbar-rail-left-vertical-left); 
 
}

.n-scrollbar > .n-scrollbar-rail.n-scrollbar-rail--vertical--right, .n-scrollbar + .n-scrollbar-rail.n-scrollbar-rail--vertical--right {

 top: var(--n-scrollbar-rail-top-vertical-right); 
 right: var(--n-scrollbar-rail-right-vertical-right); 
 bottom: var(--n-scrollbar-rail-bottom-vertical-right); 
 left: var(--n-scrollbar-rail-left-vertical-right); 
 
}

.n-scrollbar > .n-scrollbar-rail.n-scrollbar-rail--disabled > .n-scrollbar-rail__scrollbar, .n-scrollbar + .n-scrollbar-rail.n-scrollbar-rail--disabled > .n-scrollbar-rail__scrollbar {
pointer-events: none;
}

.n-scrollbar > .n-scrollbar-rail > .n-scrollbar-rail__scrollbar, .n-scrollbar + .n-scrollbar-rail > .n-scrollbar-rail__scrollbar {

 z-index: 1;
 position: absolute;
 cursor: pointer;
 pointer-events: all;
 background-color: var(--n-scrollbar-color);
 transition: background-color .2s var(--n-scrollbar-bezier);
 
}

.n-scrollbar > .n-scrollbar-rail > .n-scrollbar-rail__scrollbar.fade-in-transition-enter-active, .n-scrollbar + .n-scrollbar-rail > .n-scrollbar-rail__scrollbar.fade-in-transition-enter-active {
  transition: all 0.2s cubic-bezier(.4, 0, .2, 1)!important;
}

.n-scrollbar > .n-scrollbar-rail > .n-scrollbar-rail__scrollbar.fade-in-transition-leave-active, .n-scrollbar + .n-scrollbar-rail > .n-scrollbar-rail__scrollbar.fade-in-transition-leave-active {
  transition: all 0.2s cubic-bezier(.4, 0, .2, 1)!important;
}

.n-scrollbar > .n-scrollbar-rail > .n-scrollbar-rail__scrollbar.fade-in-transition-enter-from, .n-scrollbar + .n-scrollbar-rail > .n-scrollbar-rail__scrollbar.fade-in-transition-enter-from, .n-scrollbar > .n-scrollbar-rail > .n-scrollbar-rail__scrollbar.fade-in-transition-leave-to, .n-scrollbar + .n-scrollbar-rail > .n-scrollbar-rail__scrollbar.fade-in-transition-leave-to {
  opacity: 0;
}

.n-scrollbar > .n-scrollbar-rail > .n-scrollbar-rail__scrollbar.fade-in-transition-leave-from, .n-scrollbar + .n-scrollbar-rail > .n-scrollbar-rail__scrollbar.fade-in-transition-leave-from, .n-scrollbar > .n-scrollbar-rail > .n-scrollbar-rail__scrollbar.fade-in-transition-enter-to, .n-scrollbar + .n-scrollbar-rail > .n-scrollbar-rail__scrollbar.fade-in-transition-enter-to {
  opacity: 1;
}

.n-scrollbar > .n-scrollbar-rail > .n-scrollbar-rail__scrollbar:hover, .n-scrollbar + .n-scrollbar-rail > .n-scrollbar-rail__scrollbar:hover {
background-color: var(--n-scrollbar-color-hover);
}</style><style cssr-id="n-layout-sider">.n-layout-sider {

 flex-shrink: 0;
 box-sizing: border-box;
 position: relative;
 z-index: 1;
 color: var(--n-text-color);
 transition:
 color .3s var(--n-bezier),
 border-color .3s var(--n-bezier),
 min-width .3s var(--n-bezier),
 max-width .3s var(--n-bezier),
 transform .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 background-color: var(--n-color);
 display: flex;
 justify-content: flex-end;

}

.n-layout-sider.n-layout-sider--bordered .n-layout-sider__border {

 content: "";
 position: absolute;
 top: 0;
 bottom: 0;
 width: 1px;
 background-color: var(--n-border-color);
 transition: background-color .3s var(--n-bezier);
 
}

.n-layout-sider .n-layout-sider__left-placement.n-layout-sider__left-placement--bordered .n-layout-sider__border {

 right: 0;
 
}

.n-layout-sider.n-layout-sider--right-placement {

 justify-content: flex-start;
 
}

.n-layout-sider.n-layout-sider--right-placement.n-layout-sider--bordered .n-layout-sider__border {

 left: 0;
 
}

.n-layout-sider.n-layout-sider--right-placement.n-layout-sider--collapsed .n-layout-toggle-button .n-base-icon {

 transform: rotate(180deg);
 
}

.n-layout-sider.n-layout-sider--right-placement.n-layout-sider--collapsed .n-layout-toggle-bar:hover .n-layout-toggle-bar__top {
  transform: rotate(-12deg) scale(1.15) translateY(-2px);
}

.n-layout-sider.n-layout-sider--right-placement.n-layout-sider--collapsed .n-layout-toggle-bar:hover .n-layout-toggle-bar__bottom {
  transform: rotate(12deg) scale(1.15) translateY(2px);
}

.n-layout-sider.n-layout-sider--right-placement .n-layout-toggle-button {

 left: 0;
 transform: translateX(-50%) translateY(-50%);
 
}

.n-layout-sider.n-layout-sider--right-placement .n-layout-toggle-button .n-base-icon {

 transform: rotate(0);
 
}

.n-layout-sider.n-layout-sider--right-placement .n-layout-toggle-bar {

 left: -28px;
 transform: rotate(180deg);
 
}

.n-layout-sider.n-layout-sider--right-placement .n-layout-toggle-bar:hover .n-layout-toggle-bar__top {
  transform: rotate(12deg) scale(1.15) translateY(-2px);
}

.n-layout-sider.n-layout-sider--right-placement .n-layout-toggle-bar:hover .n-layout-toggle-bar__bottom {
  transform: rotate(-12deg) scale(1.15) translateY(2px);
}

.n-layout-sider.n-layout-sider--collapsed .n-layout-toggle-bar:hover .n-layout-toggle-bar__top {
  transform: rotate(-12deg) scale(1.15) translateY(-2px);
}

.n-layout-sider.n-layout-sider--collapsed .n-layout-toggle-bar:hover .n-layout-toggle-bar__bottom {
  transform: rotate(12deg) scale(1.15) translateY(2px);
}

.n-layout-sider.n-layout-sider--collapsed .n-layout-toggle-button .n-base-icon {

 transform: rotate(0);
 
}

.n-layout-sider .n-layout-toggle-button {

 transition:
 color .3s var(--n-bezier),
 right .3s var(--n-bezier),
 left .3s var(--n-bezier),
 border-color .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 cursor: pointer;
 width: 24px;
 height: 24px;
 position: absolute;
 top: 50%;
 right: 0;
 border-radius: 50%;
 display: flex;
 align-items: center;
 justify-content: center;
 font-size: 18px;
 color: var(--n-toggle-button-icon-color);
 border: var(--n-toggle-button-border);
 background-color: var(--n-toggle-button-color);
 box-shadow: 0 2px 4px 0px rgba(0, 0, 0, .06);
 transform: translateX(50%) translateY(-50%);
 z-index: 1;
 
}

.n-layout-sider .n-layout-toggle-button .n-base-icon {

 transition: transform .3s var(--n-bezier);
 transform: rotate(180deg);
 
}

.n-layout-sider .n-layout-toggle-bar {

 cursor: pointer;
 height: 72px;
 width: 32px;
 position: absolute;
 top: calc(50% - 36px);
 right: -28px;
 
}

.n-layout-sider .n-layout-toggle-bar .n-layout-toggle-bar__top, .n-layout-sider .n-layout-toggle-bar .n-layout-toggle-bar__bottom {

 position: absolute;
 width: 4px;
 border-radius: 2px;
 height: 38px;
 left: 14px;
 transition: 
 background-color .3s var(--n-bezier),
 transform .3s var(--n-bezier);
 
}

.n-layout-sider .n-layout-toggle-bar .n-layout-toggle-bar__bottom {

 position: absolute;
 top: 34px;
 
}

.n-layout-sider .n-layout-toggle-bar:hover .n-layout-toggle-bar__top {
  transform: rotate(12deg) scale(1.15) translateY(-2px);
}

.n-layout-sider .n-layout-toggle-bar:hover .n-layout-toggle-bar__bottom {
  transform: rotate(-12deg) scale(1.15) translateY(2px);
}

.n-layout-sider .n-layout-toggle-bar .n-layout-toggle-bar__top, .n-layout-sider .n-layout-toggle-bar .n-layout-toggle-bar__bottom {
  background-color: var(--n-toggle-bar-color);
}

.n-layout-sider .n-layout-toggle-bar:hover .n-layout-toggle-bar__top, .n-layout-sider .n-layout-toggle-bar:hover .n-layout-toggle-bar__bottom {
  background-color: var(--n-toggle-bar-color-hover);
}

.n-layout-sider .n-layout-sider__border {

 position: absolute;
 top: 0;
 right: 0;
 bottom: 0;
 width: 1px;
 transition: background-color .3s var(--n-bezier);
 
}

.n-layout-sider .n-layout-sider-scroll-container {

 flex-grow: 1;
 flex-shrink: 0;
 box-sizing: border-box;
 height: 100%;
 opacity: 0;
 transition: opacity .3s var(--n-bezier);
 max-width: 100%;
 
}

.n-layout-sider.n-layout-sider--show-content .n-layout-sider-scroll-container {
  opacity: 1;
}

.n-layout-sider.n-layout-sider--absolute-positioned {

 position: absolute;
 left: 0;
 top: 0;
 bottom: 0;
 
}</style><style cssr-id="n-layout">.n-layout {

 color: var(--n-text-color);
 background-color: var(--n-color);
 box-sizing: border-box;
 position: relative;
 z-index: auto;
 flex: auto;
 overflow: hidden;
 transition:
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);

}

.n-layout .n-layout-scroll-container {

 overflow-x: hidden;
 box-sizing: border-box;
 height: 100%;
 
}

.n-layout.n-layout--absolute-positioned {

 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 
}</style><style cssr-id="n-notification">.n-notification-container {

 z-index: 4000;
 position: fixed;
 overflow: visible;
 display: flex;
 flex-direction: column;
 align-items: flex-end;
 
}

.n-notification-container > .n-scrollbar {

 width: initial;
 overflow: visible;
 height: -moz-fit-content !important;
 height: fit-content !important;
 max-height: 100vh !important;
 
}

.n-notification-container > .n-scrollbar > .n-scrollbar-container {

 height: -moz-fit-content !important;
 height: fit-content !important;
 max-height: 100vh !important;
 
}

.n-notification-container > .n-scrollbar > .n-scrollbar-container .n-scrollbar-content {

 padding-top: 12px;
 padding-bottom: 33px;
 
}

.n-notification-container.n-notification-container--top, .n-notification-container.n-notification-container--top-right, .n-notification-container.n-notification-container--top-left {

 top: 12px;
 
}

.n-notification-container.n-notification-container--top.transitioning > .n-scrollbar > .n-scrollbar-container, .n-notification-container.n-notification-container--top-right.transitioning > .n-scrollbar > .n-scrollbar-container, .n-notification-container.n-notification-container--top-left.transitioning > .n-scrollbar > .n-scrollbar-container {

 min-height: 100vh !important;
 
}

.n-notification-container.n-notification-container--bottom, .n-notification-container.n-notification-container--bottom-right, .n-notification-container.n-notification-container--bottom-left {

 bottom: 12px;
 
}

.n-notification-container.n-notification-container--bottom > .n-scrollbar > .n-scrollbar-container .n-scrollbar-content, .n-notification-container.n-notification-container--bottom-right > .n-scrollbar > .n-scrollbar-container .n-scrollbar-content, .n-notification-container.n-notification-container--bottom-left > .n-scrollbar > .n-scrollbar-container .n-scrollbar-content {

 padding-bottom: 12px;
 
}

.n-notification-container.n-notification-container--bottom .n-notification-wrapper, .n-notification-container.n-notification-container--bottom-right .n-notification-wrapper, .n-notification-container.n-notification-container--bottom-left .n-notification-wrapper {

 display: flex;
 align-items: flex-end;
 margin-bottom: 0;
 margin-top: 12px;
 
}

.n-notification-container.n-notification-container--top, .n-notification-container.n-notification-container--bottom {

 left: 50%;
 transform: translateX(-50%);
 
}

.n-notification-container.n-notification-container--top .n-notification-wrapper.notification-transition-enter-from, .n-notification-container.n-notification-container--bottom .n-notification-wrapper.notification-transition-enter-from, .n-notification-container.n-notification-container--top .n-notification-wrapper.notification-transition-leave-to, .n-notification-container.n-notification-container--bottom .n-notification-wrapper.notification-transition-leave-to {

 transform: scale(0.85);
 
}

.n-notification-container.n-notification-container--top .n-notification-wrapper.notification-transition-leave-from, .n-notification-container.n-notification-container--bottom .n-notification-wrapper.notification-transition-leave-from, .n-notification-container.n-notification-container--top .n-notification-wrapper.notification-transition-enter-to, .n-notification-container.n-notification-container--bottom .n-notification-wrapper.notification-transition-enter-to {

 transform: scale(1);
 
}

.n-notification-container.n-notification-container--top .n-notification-wrapper {

 transform-origin: top center;
 
}

.n-notification-container.n-notification-container--bottom .n-notification-wrapper {

 transform-origin: bottom center;
 
}

.n-notification-container.n-notification-container--top-right .n-notification, .n-notification-container.n-notification-container--bottom-right .n-notification {

 margin-left: 28px;
 margin-right: 16px;
 
}

.n-notification-container.n-notification-container--top-left .n-notification, .n-notification-container.n-notification-container--bottom-left .n-notification {

 margin-left: 16px;
 margin-right: 28px;
 
}

.n-notification-container.n-notification-container--top-right {

 right: 0;
 
}

.n-notification-container.n-notification-container--top-right .n-notification-wrapper.notification-transition-enter-from, .n-notification-container.n-notification-container--top-right .n-notification-wrapper.notification-transition-leave-to {

 transform: translate(calc(100%), 0);
 
}

.n-notification-container.n-notification-container--top-right .n-notification-wrapper.notification-transition-leave-from, .n-notification-container.n-notification-container--top-right .n-notification-wrapper.notification-transition-enter-to {

 transform: translate(0, 0);
 
}

.n-notification-container.n-notification-container--top-left {

 left: 0;
 
}

.n-notification-container.n-notification-container--top-left .n-notification-wrapper.notification-transition-enter-from, .n-notification-container.n-notification-container--top-left .n-notification-wrapper.notification-transition-leave-to {

 transform: translate(calc(-100%), 0);
 
}

.n-notification-container.n-notification-container--top-left .n-notification-wrapper.notification-transition-leave-from, .n-notification-container.n-notification-container--top-left .n-notification-wrapper.notification-transition-enter-to {

 transform: translate(0, 0);
 
}

.n-notification-container.n-notification-container--bottom-right {

 right: 0;
 
}

.n-notification-container.n-notification-container--bottom-right .n-notification-wrapper.notification-transition-enter-from, .n-notification-container.n-notification-container--bottom-right .n-notification-wrapper.notification-transition-leave-to {

 transform: translate(calc(100%), 0);
 
}

.n-notification-container.n-notification-container--bottom-right .n-notification-wrapper.notification-transition-leave-from, .n-notification-container.n-notification-container--bottom-right .n-notification-wrapper.notification-transition-enter-to {

 transform: translate(0, 0);
 
}

.n-notification-container.n-notification-container--bottom-left {

 left: 0;
 
}

.n-notification-container.n-notification-container--bottom-left .n-notification-wrapper.notification-transition-enter-from, .n-notification-container.n-notification-container--bottom-left .n-notification-wrapper.notification-transition-leave-to {

 transform: translate(calc(-100%), 0);
 
}

.n-notification-container.n-notification-container--bottom-left .n-notification-wrapper.notification-transition-leave-from, .n-notification-container.n-notification-container--bottom-left .n-notification-wrapper.notification-transition-enter-to {

 transform: translate(0, 0);
 
}

.n-notification-container.n-notification-container--scrollable.n-notification-container--top-right {

 top: 0;
 
}

.n-notification-container.n-notification-container--scrollable.n-notification-container--top-left {

 top: 0;
 
}

.n-notification-container.n-notification-container--scrollable.n-notification-container--bottom-right {

 bottom: 0;
 
}

.n-notification-container.n-notification-container--scrollable.n-notification-container--bottom-left {

 bottom: 0;
 
}

.n-notification-container .n-notification-wrapper {

 margin-bottom: 12px;
 
}

.n-notification-container .n-notification-wrapper.notification-transition-enter-from, .n-notification-container .n-notification-wrapper.notification-transition-leave-to {

 opacity: 0;
 margin-top: 0 !important;
 margin-bottom: 0 !important;
 
}

.n-notification-container .n-notification-wrapper.notification-transition-leave-from, .n-notification-container .n-notification-wrapper.notification-transition-enter-to {

 opacity: 1;
 
}

.n-notification-container .n-notification-wrapper.notification-transition-leave-active {

 transition:
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 opacity .3s var(--n-bezier),
 transform .3s var(--n-bezier-ease-in),
 max-height .3s var(--n-bezier),
 margin-top .3s linear,
 margin-bottom .3s linear,
 box-shadow .3s var(--n-bezier);
 
}

.n-notification-container .n-notification-wrapper.notification-transition-enter-active {

 transition:
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 opacity .3s var(--n-bezier),
 transform .3s var(--n-bezier-ease-out),
 max-height .3s var(--n-bezier),
 margin-top .3s linear,
 margin-bottom .3s linear,
 box-shadow .3s var(--n-bezier);
 
}

.n-notification-container .n-notification {

 background-color: var(--n-color);
 color: var(--n-text-color);
 transition:
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 opacity .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier);
 font-family: inherit;
 font-size: var(--n-font-size);
 font-weight: 400;
 position: relative;
 display: flex;
 overflow: hidden;
 flex-shrink: 0;
 padding-left: var(--n-padding-left);
 padding-right: var(--n-padding-right);
 width: var(--n-width);
 max-width: calc(100vw - 16px - 16px);
 border-radius: var(--n-border-radius);
 box-shadow: var(--n-box-shadow);
 box-sizing: border-box;
 opacity: 1;
 
}

.n-notification-container .n-notification .n-notification__avatar .n-icon {

 color: var(--n-icon-color);
 
}

.n-notification-container .n-notification .n-notification__avatar .n-base-icon {

 color: var(--n-icon-color);
 
}

.n-notification-container .n-notification.n-notification--show-avatar .n-notification-main {

 margin-left: 40px;
 width: calc(100% - 40px); 
 
}

.n-notification-container .n-notification.n-notification--closable .n-notification-main > *:first-child {

 padding-right: 20px;
 
}

.n-notification-container .n-notification.n-notification--closable .n-notification__close {

 position: absolute;
 top: 0;
 right: 0;
 margin: var(--n-close-margin);
 transition:
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 
}

.n-notification-container .n-notification .n-notification__avatar {

 position: absolute;
 top: var(--n-padding-top);
 left: var(--n-padding-left);
 width: 28px;
 height: 28px;
 font-size: 28px;
 display: flex;
 align-items: center;
 justify-content: center;
 
}

.n-notification-container .n-notification .n-notification__avatar .n-icon {
transition: color .3s var(--n-bezier);
}

.n-notification-container .n-notification .n-notification-main {

 padding-top: var(--n-padding-top);
 padding-bottom: var(--n-padding-bottom);
 box-sizing: border-box;
 display: flex;
 flex-direction: column;
 margin-left: 8px;
 width: calc(100% - 8px);
 
}

.n-notification-container .n-notification .n-notification-main .n-notification-main-footer {

 display: flex;
 align-items: center;
 justify-content: space-between;
 margin-top: 12px;
 
}

.n-notification-container .n-notification .n-notification-main .n-notification-main-footer .n-notification-main-footer__meta {

 font-size: var(--n-meta-font-size);
 transition: color .3s var(--n-bezier-ease-out);
 color: var(--n-description-text-color);
 
}

.n-notification-container .n-notification .n-notification-main .n-notification-main-footer .n-notification-main-footer__action {

 cursor: pointer;
 transition: color .3s var(--n-bezier-ease-out);
 color: var(--n-action-text-color);
 
}

.n-notification-container .n-notification .n-notification-main .n-notification-main__header {

 font-weight: var(--n-title-font-weight);
 font-size: var(--n-title-font-size);
 transition: color .3s var(--n-bezier-ease-out);
 color: var(--n-title-text-color);
 
}

.n-notification-container .n-notification .n-notification-main .n-notification-main__description {

 margin-top: 8px;
 font-size: var(--n-description-font-size);
 white-space: pre-wrap;
 word-wrap: break-word;
 transition: color .3s var(--n-bezier-ease-out);
 color: var(--n-description-text-color);
 
}

.n-notification-container .n-notification .n-notification-main .n-notification-main__content {

 line-height: var(--n-line-height);
 margin: 12px 0 0 0;
 font-family: inherit;
 white-space: pre-wrap;
 word-wrap: break-word;
 transition: color .3s var(--n-bezier-ease-out);
 color: var(--n-text-color);
 
}

.n-notification-container .n-notification .n-notification-main .n-notification-main__content:first-child {
margin: 0;
}</style><style cssr-id="n-global">body {

 margin: 0;
 font-size: 14px;
 font-family: v-sans, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
 line-height: 1.6;
 -webkit-text-size-adjust: 100%;
 -webkit-tap-highlight-color: transparent;

}

body input {

 font-family: inherit;
 font-size: inherit;
 
}</style><style cssr-id="n-loading-bar">.n-loading-bar-container {

 z-index: 5999;
 position: fixed;
 top: 0;
 left: 0;
 right: 0;
 height: 2px;

}

.n-loading-bar-container.fade-in-transition-enter-active {
  transition: all 0.3s cubic-bezier(.4, 0, .2, 1)!important;
}

.n-loading-bar-container.fade-in-transition-leave-active {
  transition: all 0.8s cubic-bezier(.4, 0, .2, 1)!important;
}

.n-loading-bar-container.fade-in-transition-enter-from, .n-loading-bar-container.fade-in-transition-leave-to {
  opacity: 0;
}

.n-loading-bar-container.fade-in-transition-leave-from, .n-loading-bar-container.fade-in-transition-enter-to {
  opacity: 1;
}

.n-loading-bar-container .n-loading-bar {

 width: 100%;
 transition:
 max-width 4s linear,
 background .2s linear;
 height: var(--n-height);
 
}

.n-loading-bar-container .n-loading-bar.n-loading-bar--starting {

 background: var(--n-color-loading);
 
}

.n-loading-bar-container .n-loading-bar.n-loading-bar--finishing {

 background: var(--n-color-loading);
 transition:
 max-width .2s linear,
 background .2s linear;
 
}

.n-loading-bar-container .n-loading-bar.n-loading-bar--error {

 background: var(--n-color-error);
 transition:
 max-width .2s linear,
 background .2s linear;
 
}</style><link rel="icon" href="/favicon.svg">
    <link rel="stylesheet" href="/resource/ai-loading.css">

    <title>用例管理 | AItest管理系统</title>
    <!-- 添加Monaco编辑器CDN -->
    <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.43.0/min/vs/loader.js"></script>
    <script>
      require.config({
        paths: {
          'vs': 'https://cdn.jsdelivr.net/npm/monaco-editor@0.43.0/min/vs'
        }
      });
      window.MonacoEnvironment = {
        getWorkerUrl: function (workerId, label) {
          return `data:text/javascript;charset=utf-8,${encodeURIComponent(`
            self.MonacoEnvironment = {
              baseUrl: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.43.0/min/'
            };
            importScripts('https://cdn.jsdelivr.net/npm/monaco-editor@0.43.0/min/vs/base/worker/workerMain.js');
          `)}`;
        }
      };
    </script>
    <!-- 字体加载已移除，因为字体文件不存在 -->
  <style type="text/css" data-vite-dev-id="/Users/<USER>/test/danwen/testing2/AItestPlatform/web/src/styles/reset.css">html {
  box-sizing: border-box;
}

*,
::before,
::after {
  margin: 0;
  padding: 0;
  box-sizing: inherit;
}

a {
  text-decoration: none;
  color: inherit;
}

a:hover,
a:link,
a:visited,
a:active {
  text-decoration: none;
}

ol,
ul {
  list-style: none;
}

input,
textarea {
  outline: none;
  border: none;
  resize: none;
}
</style><style type="text/css" data-vite-dev-id="/__uno.css">__uno_hash_f8dfc8{--:'';}/* layer: preflights */
*,::before,::after{--un-rotate:0;--un-rotate-x:0;--un-rotate-y:0;--un-rotate-z:0;--un-scale-x:1;--un-scale-y:1;--un-scale-z:1;--un-skew-x:0;--un-skew-y:0;--un-translate-x:0;--un-translate-y:0;--un-translate-z:0;--un-pan-x: ;--un-pan-y: ;--un-pinch-zoom: ;--un-scroll-snap-strictness:proximity;--un-ordinal: ;--un-slashed-zero: ;--un-numeric-figure: ;--un-numeric-spacing: ;--un-numeric-fraction: ;--un-border-spacing-x:0;--un-border-spacing-y:0;--un-ring-offset-shadow:0 0 rgba(0,0,0,0);--un-ring-shadow:0 0 rgba(0,0,0,0);--un-shadow-inset: ;--un-shadow:0 0 rgba(0,0,0,0);--un-ring-inset: ;--un-ring-offset-width:0px;--un-ring-offset-color:#fff;--un-ring-width:0px;--un-ring-color:rgba(147,197,253,0.5);--un-blur: ;--un-brightness: ;--un-contrast: ;--un-drop-shadow: ;--un-grayscale: ;--un-hue-rotate: ;--un-invert: ;--un-saturate: ;--un-sepia: ;--un-backdrop-blur: ;--un-backdrop-brightness: ;--un-backdrop-contrast: ;--un-backdrop-grayscale: ;--un-backdrop-hue-rotate: ;--un-backdrop-invert: ;--un-backdrop-opacity: ;--un-backdrop-saturate: ;--un-backdrop-sepia: ;}::backdrop{--un-rotate:0;--un-rotate-x:0;--un-rotate-y:0;--un-rotate-z:0;--un-scale-x:1;--un-scale-y:1;--un-scale-z:1;--un-skew-x:0;--un-skew-y:0;--un-translate-x:0;--un-translate-y:0;--un-translate-z:0;--un-pan-x: ;--un-pan-y: ;--un-pinch-zoom: ;--un-scroll-snap-strictness:proximity;--un-ordinal: ;--un-slashed-zero: ;--un-numeric-figure: ;--un-numeric-spacing: ;--un-numeric-fraction: ;--un-border-spacing-x:0;--un-border-spacing-y:0;--un-ring-offset-shadow:0 0 rgba(0,0,0,0);--un-ring-shadow:0 0 rgba(0,0,0,0);--un-shadow-inset: ;--un-shadow:0 0 rgba(0,0,0,0);--un-ring-inset: ;--un-ring-offset-width:0px;--un-ring-offset-color:#fff;--un-ring-width:0px;--un-ring-color:rgba(147,197,253,0.5);--un-blur: ;--un-brightness: ;--un-contrast: ;--un-drop-shadow: ;--un-grayscale: ;--un-hue-rotate: ;--un-invert: ;--un-saturate: ;--un-sepia: ;--un-backdrop-blur: ;--un-backdrop-brightness: ;--un-backdrop-contrast: ;--un-backdrop-grayscale: ;--un-backdrop-hue-rotate: ;--un-backdrop-invert: ;--un-backdrop-opacity: ;--un-backdrop-saturate: ;--un-backdrop-sepia: ;}
/* layer: shortcuts */
.container{width:100%;}
.wh-full,
[wh-full=""]{width:100%;height:100%;}
.f-c-c,
[f-c-c=""]{display:flex;align-items:center;justify-content:center;}
.flex-col,
[flex-col=""]{display:flex;flex-direction:column;}
@media (min-width: 640px){
.container{max-width:640px;}
}
@media (min-width: 768px){
.container{max-width:768px;}
}
@media (min-width: 1024px){
.container{max-width:1024px;}
}
@media (min-width: 1280px){
.container{max-width:1280px;}
}
@media (min-width: 1536px){
.container{max-width:1536px;}
}
/* layer: default */
.visible{visibility:visible;}
.absolute{position:absolute;}
.fixed{position:fixed;}
.relative{position:relative;}
.static{position:static;}
[bottom~="\32 0"]{bottom:5rem;}
.float-right{float:right;}
.mx-5{margin-left:1.25rem;margin-right:1.25rem;}
.mb-15,
[mb-15=""]{margin-bottom:3.75rem;}
.mb-30,
[mb-30=""]{margin-bottom:7.5rem;}
.ml-15,
[ml-15=""]{margin-left:3.75rem;}
.ml-2,
[ml-2=""]{margin-left:0.5rem;}
.ml-20,
[ml-20=""]{margin-left:5rem;}
.ml-auto,
[ml-auto=""]{margin-left:auto;}
.mr-15{margin-right:3.75rem;}
.mr-20,
.mr20,
[mr-20=""],
[mr20=""]{margin-right:5rem;}
.mr-5{margin-right:1.25rem;}
.mr-8,
[mr-8=""]{margin-right:2rem;}
.mr10,
[mr10=""]{margin-right:2.5rem;}
.mt-15,
[mt-15=""]{margin-top:3.75rem;}
.block{display:block;}
.inline-block{display:inline-block;}
.hidden,
[hidden=""]{display:none;}
.h-35,
[h-35=""]{height:8.75rem;}
.h-60,
[h-60=""]{height:15rem;}
.max-w-150,
[max-w-150=""]{max-width:37.5rem;}
.min-h-45,
[min-h-45=""]{min-height:11.25rem;}
.min-h-60,
[min-h-60=""]{min-height:15rem;}
.w-35,
[w-35=""]{width:8.75rem;}
.flex,
[flex=""]{display:flex;}
.flex-1,
[flex-1=""]{flex:1 1 0%;}
.flex-shrink{flex-shrink:1;}
.flex-shrink-0,
[flex-shrink-0=""]{flex-shrink:0;}
.flex-wrap{flex-wrap:wrap;}
.transform{transform:translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));}
.cursor-pointer,
[cursor-pointer=""]{cursor:pointer;}
.resize{resize:both;}
.items-start,
[items-start=""]{align-items:flex-start;}
.items-center,
[items-center=""]{align-items:center;}
[justify-end=""]{justify-content:flex-end;}
.justify-between,
[justify-between=""]{justify-content:space-between;}
.overflow-auto{overflow:auto;}
[overflow-hidden=""]{overflow:hidden;}
.break-all{word-break:break-all;}
.b-1,
.border,
[b-1=""]{border-width:1px;}
[rounded-16=""]{border-radius:4rem;}
.rounded-4{border-radius:1rem;}
.rounded-8,
[rounded-8=""]{border-radius:2rem;}
.rounded-full,
[rounded-full=""]{border-radius:9999px;}
.bg-\[\#f5f6fb\]{--un-bg-opacity:1;background-color:rgba(245,246,251,var(--un-bg-opacity));}
[bg~="\#fafafc"]{--un-bg-opacity:1;background-color:rgba(250,250,252,var(--un-bg-opacity));}
.dark .dark\:bg-black,
.dark [dark\:bg-black=""]{--un-bg-opacity:1;background-color:rgba(0,0,0,var(--un-bg-opacity));}
.dark .dark\:bg-dark\!{--un-bg-opacity:1 !important;background-color:rgba(10,35,24,var(--un-bg-opacity)) !important;}
.dark .dark\:bg-hex-121212{--un-bg-opacity:1;background-color:rgba(18,18,18,var(--un-bg-opacity));}
.bg-white{--un-bg-opacity:1;background-color:rgba(255,255,255,var(--un-bg-opacity));}
[stroke-width~="\31 \.5"]{stroke-width:1.5px;}
[stroke-width~="\32 "]{stroke-width:2px;}
.p-15,
[p-15=""]{padding:3.75rem;}
.px{padding-left:1rem;padding-right:1rem;}
.px-15,
[px-15=""]{padding-left:3.75rem;padding-right:3.75rem;}
[indent~="\31 8"]{text-indent:4.5rem;}
.text-14,
[text-14=""]{font-size:3.5rem;}
.text-16,
[text-16=""]{font-size:4rem;}
.text-22,
[text-22=""]{font-size:5.5rem;}
.text-36,
[text-36=""]{font-size:9rem;}
.font-bold,
[font-bold=""]{font-weight:700;}
.font-normal,
[font-normal=""]{font-weight:400;}
[color~="\#6a6a6a"]{--un-text-opacity:1;color:rgba(106,106,106,var(--un-text-opacity));}
.color-primary,
[color-primary=""]{color:var(--primary-color);}
.hover\:color-primary:hover{color:var(--primary-color);}
.text-hex-333,
[text-hex-333=""]{--un-text-opacity:1;color:rgba(51,51,51,var(--un-text-opacity));}
.dark [dark\:text-hex-ccc=""]{--un-text-opacity:1;color:rgba(204,204,204,var(--un-text-opacity));}
.dark .dark\:text-hex-ccc\>{color:#ccc>;}
.underline{text-decoration-line:underline;}
.blur{--un-blur:blur(8px);filter:var(--un-blur) var(--un-brightness) var(--un-contrast) var(--un-drop-shadow) var(--un-grayscale) var(--un-hue-rotate) var(--un-invert) var(--un-saturate) var(--un-sepia);}
.backdrop-filter{-webkit-backdrop-filter:var(--un-backdrop-blur) var(--un-backdrop-brightness) var(--un-backdrop-contrast) var(--un-backdrop-grayscale) var(--un-backdrop-hue-rotate) var(--un-backdrop-invert) var(--un-backdrop-opacity) var(--un-backdrop-saturate) var(--un-backdrop-sepia);backdrop-filter:var(--un-backdrop-blur) var(--un-backdrop-brightness) var(--un-backdrop-contrast) var(--un-backdrop-grayscale) var(--un-backdrop-hue-rotate) var(--un-backdrop-invert) var(--un-backdrop-opacity) var(--un-backdrop-saturate) var(--un-backdrop-sepia);}
.transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms;}
.ease{transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);}
@keyframes __un_qm{0%{box-shadow:inset 4px 4px #ff1e90, inset -4px -4px #ff1e90}100%{box-shadow:inset 8px 8px #3399ff, inset -8px -8px #3399ff}}
.\?{animation:__un_qm 0.5s ease-in-out alternate infinite;}
.bc-ccc,
[bc-ccc=""]{border-color:#ccc;}
@media (min-width: 640px){
.sm\:block,
[sm\:block=""]{display:block;}
}</style><style type="text/css" data-vite-dev-id="/Users/<USER>/test/danwen/testing2/AItestPlatform/web/src/styles/global.scss">@charset "UTF-8";
html,
body {
  width: 100%;
  height: 100%;
  overflow: auto; /* 修改为auto，允许滚动 */
}

html {
  font-size: 4px;
}

body {
  font-size: 16px;
  background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
  color: #1E293B;
  transition: background-color 0.3s ease;
  position: relative;
}
body::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(rgba(109, 40, 217, 0.05) 1px, transparent 1px), linear-gradient(90deg, rgba(109, 40, 217, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
  z-index: 0;
}

#app {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
}

/* transition fade-slide */
.fade-slide-leave-active,
.fade-slide-enter-active {
  transition: all 0.3s;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* 自定义滚动条样式 */
.cus-scroll {
  overflow: auto;
}
.cus-scroll::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.cus-scroll-x {
  overflow-x: auto;
}
.cus-scroll-x::-webkit-scrollbar {
  width: 0;
  height: 6px;
}

.cus-scroll-y {
  overflow-y: auto;
}
.cus-scroll-y::-webkit-scrollbar {
  width: 6px;
  height: 0;
}

.cus-scroll::-webkit-scrollbar-thumb,
.cus-scroll-x::-webkit-scrollbar-thumb,
.cus-scroll-y::-webkit-scrollbar-thumb {
  background-color: rgba(109, 40, 217, 0.2);
  border-radius: 3px;
}
.cus-scroll:hover::-webkit-scrollbar-thumb,
.cus-scroll-x:hover::-webkit-scrollbar-thumb,
.cus-scroll-y:hover::-webkit-scrollbar-thumb {
  background: rgba(109, 40, 217, 0.4);
}
.cus-scroll:hover::-webkit-scrollbar-thumb:hover,
.cus-scroll-x:hover::-webkit-scrollbar-thumb:hover,
.cus-scroll-y:hover::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}

/* 卡片样式 */
.n-card {
  background: #FFFFFF !important;
  backdrop-filter: blur(12px);
  border: 1px solid rgba(109, 40, 217, 0.08);
  box-shadow: 0 4px 20px rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}
.n-card .n-card__content {
  background: #F8FAFC !important;
  border-radius: 0 0 12px 12px;
  padding: 20px !important;
}
.n-card .n-card-header {
  padding: 20px !important;
  background: #FFFFFF !important;
  border-bottom: 1px solid rgba(109, 40, 217, 0.08);
  color: #1E293B !important;
  font-weight: 600;
  border-radius: 12px 12px 0 0;
}
.n-card:hover {
  border-color: rgba(109, 40, 217, 0.2);
  box-shadow: 0 8px 30px rgba(148, 163, 184, 0.15);
}

/* 表格样式 */
.n-data-table {
  background: #FFFFFF !important;
  border: 1px solid rgba(109, 40, 217, 0.08);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.03);
}
.n-data-table .n-data-table-thead {
  background: #F8FAFC !important;
}
.n-data-table .n-data-table-thead th {
  color: #475569 !important;
  font-weight: 600;
  font-size: 14px;
  padding: 16px !important;
  border-bottom: 1px solid rgba(109, 40, 217, 0.12) !important;
  background: #F8FAFC !important;
  transition: background-color 0.3s ease;
}
.n-data-table .n-data-table-thead th:hover {
  background: #F1F5F9 !important;
}
.n-data-table .n-data-table-tr td {
  padding: 14px 16px !important;
  color: #1E293B !important;
  font-size: 14px;
  border-bottom: 1px solid rgba(109, 40, 217, 0.06) !important;
  background: #FFFFFF;
  transition: all 0.3s ease;
}
.n-data-table .n-data-table-tr:hover td {
  background: rgba(109, 40, 217, 0.02) !important;
}
.n-data-table .n-data-table-tr:last-child td {
  border-bottom: none !important;
}
.n-data-table .n-data-table-tr.n-data-table-tr--checked td {
  background: rgba(109, 40, 217, 0.04) !important;
}
.n-data-table .n-data-table-td__ellipsis {
  color: #6D28D9 !important;
}
.n-data-table .n-data-table-td__ellipsis:hover {
  color: #7C3AED !important;
  text-decoration: underline;
}
.n-data-table .n-pagination {
  margin-top: 16px;
  padding: 12px 16px;
  background: #F8FAFC;
  border-top: 1px solid rgba(109, 40, 217, 0.08);
}
.n-data-table .n-pagination .n-pagination-item {
  background: transparent;
  color: #475569;
  border: 1px solid rgba(109, 40, 217, 0.1);
}
.n-data-table .n-pagination .n-pagination-item:hover {
  color: #6D28D9;
  border-color: #6D28D9;
}
.n-data-table .n-pagination .n-pagination-item.n-pagination-item--active {
  background: #6D28D9;
  color: white;
  border-color: #6D28D9;
}
.n-data-table .n-data-table-empty {
  padding: 32px;
  color: #94A3B8;
  font-size: 14px;
}
.n-data-table .n-data-table-loading {
  background: rgba(255, 255, 255, 0.8);
}
.n-data-table .n-data-table-loading .n-spin-body {
  color: #6D28D9;
}

.table-action-buttons {
  display: flex;
  gap: 8px;
}
.table-action-buttons .n-button {
  padding: 4px 12px;
  font-size: 13px;
  height: 28px;
}
.table-action-buttons .n-button:hover {
  transform: translateY(-1px);
}

/* 按钮样式 */
.n-button {
  background: linear-gradient(45deg, var(--primary-color) 0%, var(--info-color) 100%);
  border: none;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  color: white !important;
}
.n-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(109, 40, 217, 0.3);
}

/* 输入框样式 */
.n-input {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(109, 40, 217, 0.1);
  backdrop-filter: blur(5px);
}
.n-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(109, 40, 217, 0.2);
}

html, body, #app {
  height: 100%;
  margin: 0;
  overflow-x: hidden;
  overflow-y: auto; /* 允许垂直滚动 */
  position: relative;
  z-index: 1;
}

/* 添加页面容器样式，确保可以滚动 */
.page-container {
  height: 100%;
  overflow: auto !important;
  position: relative;
}

/* 确保CommonPage组件可以滚动 */
.common-page {
  height: 100%;
  overflow: auto !important;
  display: flex;
  flex-direction: column;
}

.n-card-header {
  padding: 20px 40px 0 40px !important;
  background: rgba(248, 250, 252, 0.9) !important;
  border-bottom: 1px solid rgba(109, 40, 217, 0.1);
  color: #1E293B !important;
}</style><style type="text/css" data-vite-dev-id="/Users/<USER>/test/danwen/testing2/AItestPlatform/web/src/styles/loading-override.css">/* 隐藏原始的Naive UI loading进度条 */
.n-loading-bar {
  display: none !important;
}

/* 确保我们的自定义加载组件正常显示 */
.ai-test-loading-container,
.ai-loading-container {
  z-index: 9999 !important;
}

/* 确保初始加载动画和路由加载动画样式一致 */
.ai-loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 暗黑模式下的背景颜色 */
:root.dark .ai-loading-container {
  background-color: rgba(18, 18, 18, 0.95);
}
</style><style type="text/css" data-vite-dev-id="/Users/<USER>/test/danwen/testing2/AItestPlatform/web/src/layout/components/sidebar/components/SideMenu.vue?vue&amp;type=style&amp;index=0&amp;lang.scss">.side-menu:not(.n-menu--collapsed) .n-menu-item-content::before {
  left: 5px;
  right: 5px;
}
.side-menu:not(.n-menu--collapsed) .n-menu-item-content.n-menu-item-content--selected::before, .side-menu:not(.n-menu--collapsed) .n-menu-item-content:hover::before {
  border-left: 4px solid var(--primary-color);
}</style><style type="text/css" data-vite-dev-id="/Users/<USER>/test/danwen/testing2/AItestPlatform/web/src/layout/components/AppMain.vue?vue&amp;type=style&amp;index=0&amp;lang.scss">@charset "UTF-8";
/* 页面过渡动画 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
}
.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(30px);
}
.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}
.n-card {
  box-shadow: 0 4px 20px rgba(148, 163, 184, 0.1);
  transition: all 0.3s ease;
  background: #FFFFFF !important;
  border: 1px solid rgba(109, 40, 217, 0.08);
  border-radius: 12px;
}
.n-card:hover {
  box-shadow: 0 8px 30px rgba(148, 163, 184, 0.15);
  border-color: rgba(109, 40, 217, 0.2);
}
.n-card > .n-card__content {
  background: #F8FAFC !important;
  border-radius: 0 0 12px 12px;
  padding: 20px !important;
}
.n-card-header {
  background: #FFFFFF !important;
  border-bottom: 1px solid rgba(109, 40, 217, 0.08);
  color: #1E293B !important;
  font-weight: 600;
  border-radius: 12px 12px 0 0;
  padding: 20px !important;
}
.n-card__footer {
  background: #F8FAFC !important;
  border-top: 1px solid rgba(109, 40, 217, 0.08);
  padding: 16px 20px !important;
}
.dark .dark\:bg-black, .dark [dark\:bg-black=""] {
  background-color: #F8FAFC;
}</style><style type="text/css" data-vite-dev-id="/Users/<USER>/test/danwen/testing2/AItestPlatform/web/src/components/common/ScrollX.vue?vue&amp;type=style&amp;index=0&amp;scoped=eec37558&amp;lang.scss">.wrapper[data-v-eec37558] {
  display: flex;
  background-color: #fff;
  z-index: 9;
  overflow: hidden;
  position: relative;
}
.wrapper .content[data-v-eec37558] {
  padding: 0 10px;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  transition: transform 0.5s;
}
.wrapper .content.overflow[data-v-eec37558] {
  padding-left: 30px;
  padding-right: 30px;
}
.wrapper .left[data-v-eec37558],
.wrapper .right[data-v-eec37558] {
  background-color: #fff;
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  width: 20px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  border: 1px solid #e0e0e6;
  border-radius: 2px;
  z-index: 2;
  cursor: pointer;
}
.wrapper .left[data-v-eec37558] {
  left: 0;
}
.wrapper .right[data-v-eec37558] {
  right: 0;
}</style><style type="text/css" data-vite-dev-id="/Users/<USER>/test/danwen/testing2/AItestPlatform/web/src/layout/components/tags/index.vue?vue&amp;type=style&amp;index=0&amp;lang.css">
.n-tag__close {
  box-sizing: content-box;
  border-radius: 50%;
  font-size: 12px;
  padding: 2px;
  transform: scale(0.9);
  transform: translateX(5px);
  transition: all 0.3s;
}
</style><style type="text/css" data-vite-dev-id="/Users/<USER>/test/danwen/testing2/AItestPlatform/web/src/layout/index.vue?vue&amp;type=style&amp;index=0&amp;lang.scss">.layout-sider {
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(12px);
  border-right: 1px solid rgba(109, 40, 217, 0.1);
}
.layout-header {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(12px);
  border-bottom: 1px solid rgba(109, 40, 217, 0.1);
}
.layout-tags {
  background: rgba(255, 255, 255, 0.8);
  border-bottom: 1px solid rgba(109, 40, 217, 0.1);
}
.layout-main {
  background: rgba(248, 250, 252, 0.6);
}
.n-card {
  border-radius: 12px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(12px);
  border: 1px solid rgba(109, 40, 217, 0.1);
  transition: all 0.3s ease;
}
.n-card:hover {
  border-color: rgba(109, 40, 217, 0.2);
  box-shadow: 0 8px 30px rgba(148, 163, 184, 0.15);
}
.dark .dark\:bg-hex-121212 {
  background-color: rgba(255, 255, 255, 0.9);
}
.bg-\[\#f5f6fb\], [bg-hex-f5f6fb=""] {
  background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
}</style><style type="text/css" data-vite-dev-id="/Users/<USER>/test/danwen/testing2/AItestPlatform/web/src/components/common/AITestLoading.vue?vue&amp;type=style&amp;index=0&amp;scoped=7df3e268&amp;lang.css">
.ai-test-loading-container[data-v-7df3e268] {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.85);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(3px);
  pointer-events: none; /* 允许点击穿透，避免阻止用户交互 */
  transition: background-color 0.3s ease;
}

/* 暗黑模式下的背景颜色 */
:root.dark .ai-test-loading-container[data-v-7df3e268] {
  background-color: rgba(18, 18, 18, 0.85);
}
.ai-test-loading-content[data-v-7df3e268] {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.ai-test-loading-icon[data-v-7df3e268] {
  width: 120px;
  height: 120px;
}
.ai-test-loading-text[data-v-7df3e268] {
  margin-top: 16px;
  font-size: 16px;
  color: var(--primary-color);
  font-weight: 500;
}

/* 动画效果 */
.fade-enter-active[data-v-7df3e268],
.fade-leave-active[data-v-7df3e268] {
  transition: opacity 0.3s ease;
}
.fade-enter-from[data-v-7df3e268],
.fade-leave-to[data-v-7df3e268] {
  opacity: 0;
}

/* 脑部动画 */
.brain[data-v-7df3e268] {
  animation: pulse-7df3e268 2s infinite;
}
@keyframes pulse-7df3e268 {
0% {
    stroke-width: 2;
    opacity: 0.8;
}
50% {
    stroke-width: 3;
    opacity: 1;
}
100% {
    stroke-width: 2;
    opacity: 0.8;
}
}

/* 神经节点动画 */
.node[data-v-7df3e268] {
  animation: nodeGlow-7df3e268 2s infinite;
}
.node1[data-v-7df3e268] {
  animation-delay: 0s;
}
.node2[data-v-7df3e268] {
  animation-delay: 0.3s;
}
.node3[data-v-7df3e268] {
  animation-delay: 0.6s;
}
@keyframes nodeGlow-7df3e268 {
0% {
    r: 4;
    opacity: 0.7;
}
50% {
    r: 5;
    opacity: 1;
}
100% {
    r: 4;
    opacity: 0.7;
}
}

/* 测试元素动画 */
.test-case[data-v-7df3e268] {
  animation: testCasePulse-7df3e268 3s infinite;
}
@keyframes testCasePulse-7df3e268 {
0% {
    stroke-width: 1.5;
}
50% {
    stroke-width: 2;
}
100% {
    stroke-width: 1.5;
}
}
.checkmark[data-v-7df3e268] {
  animation: drawCheckmark-7df3e268 3s infinite;
  stroke-dasharray: 30;
  stroke-dashoffset: 30;
}
@keyframes drawCheckmark-7df3e268 {
0% {
    stroke-dashoffset: 30;
}
50% {
    stroke-dashoffset: 0;
}
100% {
    stroke-dashoffset: 0;
}
}
.testing[data-v-7df3e268] {
  animation: testingPulse-7df3e268 1s infinite;
}
@keyframes testingPulse-7df3e268 {
0% {
    r: 3;
    opacity: 0.7;
}
50% {
    r: 4;
    opacity: 1;
}
100% {
    r: 3;
    opacity: 0.7;
}
}
.error[data-v-7df3e268] {
  animation: errorFlash-7df3e268 2s infinite;
}
@keyframes errorFlash-7df3e268 {
0% {
    stroke-width: 2;
    opacity: 0.7;
}
50% {
    stroke-width: 2.5;
    opacity: 1;
}
100% {
    stroke-width: 2;
    opacity: 0.7;
}
}

/* 数据流动动画 */
.data-point[data-v-7df3e268] {
  animation: moveAlongPath-7df3e268 4s infinite linear;
}
.dp1[data-v-7df3e268] {
  animation-delay: 0s;
}
.dp2[data-v-7df3e268] {
  animation-delay: 1.3s;
}
.dp3[data-v-7df3e268] {
  animation-delay: 2.6s;
}
@keyframes moveAlongPath-7df3e268 {
0% {
    transform: translate(50px, 35px);
    opacity: 0;
}
10% {
    opacity: 1;
}
30% {
    transform: translate(35px, 40px);
}
50% {
    transform: translate(25px, 60px);
}
70% {
    transform: translate(25px, 75px);
}
90% {
    opacity: 1;
}
100% {
    transform: translate(25px, 85px);
    opacity: 0;
}
}
</style><style data-id="immersive-translate-input-injected-css">.immersive-translate-input {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 2147483647;
  display: flex;
  justify-content: center;
  align-items: center;
}
.immersive-translate-attach-loading::after {
  content: " ";

  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: immersiveTranslateShadowRolling 1.5s linear infinite;

  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-2000%, -50%);
  z-index: 100;
}

.immersive-translate-loading-spinner {
  vertical-align: middle !important;
  width: 10px !important;
  height: 10px !important;
  display: inline-block !important;
  margin: 0 4px !important;
  border: 2px rgba(221, 244, 255, 0.6) solid !important;
  border-top: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-left: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-radius: 50% !important;
  padding: 0 !important;
  -webkit-animation: immersive-translate-loading-animation 0.6s infinite linear !important;
  animation: immersive-translate-loading-animation 0.6s infinite linear !important;
}

@-webkit-keyframes immersive-translate-loading-animation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
  }
}

@keyframes immersive-translate-loading-animation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}

.immersive-translate-input-loading {
  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: immersiveTranslateShadowRolling 1.5s linear infinite;
}

@keyframes immersiveTranslateShadowRolling {
  0% {
    box-shadow: 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  12% {
    box-shadow: 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  25% {
    box-shadow: 110px 0 var(--loading-color), 100px 0 var(--loading-color),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  36% {
    box-shadow: 120px 0 var(--loading-color), 110px 0 var(--loading-color),
      100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0);
  }

  50% {
    box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color),
      110px 0 var(--loading-color), 100px 0 var(--loading-color);
  }

  62% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color),
      120px 0 var(--loading-color), 110px 0 var(--loading-color);
  }

  75% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      130px 0 var(--loading-color), 120px 0 var(--loading-color);
  }

  87% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color);
  }

  100% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0);
  }
}

.immersive-translate-toast {
  display: flex;
  position: fixed;
  z-index: 2147483647;
  left: 0;
  right: 0;
  top: 1%;
  width: fit-content;
  padding: 12px 20px;
  margin: auto;
  overflow: auto;
  background: #fef6f9;
  box-shadow: 0px 4px 10px 0px rgba(0, 10, 30, 0.06);
  font-size: 15px;
  border-radius: 8px;
  color: #333;
}

.immersive-translate-toast-content {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.immersive-translate-toast-hidden {
  margin: 0 20px 0 72px;
  text-decoration: underline;
  cursor: pointer;
}

.immersive-translate-toast-close {
  color: #666666;
  font-size: 20px;
  font-weight: bold;
  padding: 0 10px;
  cursor: pointer;
}

@media screen and (max-width: 768px) {
  .immersive-translate-toast {
    top: 0;
    padding: 12px 0px 0 10px;
  }
  .immersive-translate-toast-content {
    flex-direction: column;
    text-align: center;
  }
  .immersive-translate-toast-hidden {
    margin: 10px auto;
  }
}

.immersive-translate-modal {
  display: none;
  position: fixed;
  z-index: 2147483647;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgb(0, 0, 0);
  background-color: rgba(0, 0, 0, 0.4);
  font-size: 15px;
}

.immersive-translate-modal-content {
  background-color: #fefefe;
  margin: 10% auto;
  padding: 40px 24px 24px;
  border: 1px solid #888;
  border-radius: 10px;
  width: 80%;
  max-width: 270px;
  font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  position: relative;
}

@media screen and (max-width: 768px) {
  .immersive-translate-modal-content {
    margin: 50% auto !important;
  }
}

.immersive-translate-modal .immersive-translate-modal-content-in-input {
  max-width: 500px;
}
.immersive-translate-modal-content-in-input .immersive-translate-modal-body {
  text-align: left;
  max-height: unset;
}

.immersive-translate-modal-title {
  text-align: center;
  font-size: 16px;
  font-weight: 700;
  color: #333333;
}

.immersive-translate-modal-body {
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  word-break: break-all;
  margin-top: 24px;
}

@media screen and (max-width: 768px) {
  .immersive-translate-modal-body {
    max-height: 250px;
    overflow-y: auto;
  }
}

.immersive-translate-close {
  color: #666666;
  position: absolute;
  right: 16px;
  top: 16px;
  font-size: 20px;
  font-weight: bold;
}

.immersive-translate-close:hover,
.immersive-translate-close:focus {
  color: black;
  text-decoration: none;
  cursor: pointer;
}

.immersive-translate-modal-footer {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 24px;
}

.immersive-translate-btn {
  width: fit-content;
  color: #fff;
  background-color: #ea4c89;
  border: none;
  font-size: 16px;
  margin: 0 8px;
  padding: 9px 30px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.immersive-translate-btn:hover {
  background-color: #f082ac;
}
.immersive-translate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.immersive-translate-btn:disabled:hover {
  background-color: #ea4c89;
}

.immersive-translate-cancel-btn {
  /* gray color */
  background-color: rgb(89, 107, 120);
}

.immersive-translate-cancel-btn:hover {
  background-color: hsl(205, 20%, 32%);
}

.immersive-translate-action-btn {
  background-color: transparent;
  color: #ea4c89;
  border: 1px solid #ea4c89;
}

.immersive-translate-btn svg {
  margin-right: 5px;
}

.immersive-translate-link {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  text-decoration: none;
  color: #007bff;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.immersive-translate-primary-link {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  text-decoration: none;
  color: #ea4c89;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.immersive-translate-modal input[type="radio"] {
  margin: 0 6px;
  cursor: pointer;
}

.immersive-translate-modal label {
  cursor: pointer;
}

.immersive-translate-close-action {
  position: absolute;
  top: 2px;
  right: 0px;
  cursor: pointer;
}

.imt-image-status {
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 16px !important;
}
.imt-image-status img,
.imt-image-status svg,
.imt-img-loading {
  width: 28px !important;
  height: 28px !important;
  margin: 0 0 8px 0 !important;
  min-height: 28px !important;
  min-width: 28px !important;
  position: relative !important;
}
.imt-img-loading {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAMAAACfWMssAAAAtFBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////oK74hAAAAPHRSTlMABBMIDyQXHwyBfFdDMSw+OjXCb+5RG51IvV/k0rOqlGRM6KKMhdvNyZBz9MaupmxpWyj437iYd/yJVNZeuUC7AAACt0lEQVRIx53T2XKiUBCA4QYOiyCbiAsuuGBcYtxiYtT3f6/pbqoYHVFO5r+iivpo6DpAWYpqeoFfr9f90DsYAuRSWkFnPO50OgR9PwiCUFcl2GEcx+N/YBh6pvKaefHlUgZd1zVe0NbYcQjGBfzrPE8Xz8aF+71D8gG6DHFPpc4a7xFiCDuhaWgKgGIJQ3d5IMGDrpS4S5KgpIm+en9f6PlAhKby4JwEIxlYJV9h5k5nee9GoxHJ2IDSNB0dwdad1NAxDJ/uXDHYmebdk4PdbkS58CIVHdYSUHTYYRWOJblWSyu2lmy3KNFVJNBhxcuGW4YBVCbYGRZwIooipHsNqjM4FbgOQqQqSKQQU9V8xmi1QlgHqQQ6DDBvRUVCDirs+EzGDGOQTCATgtYTnbCVLgsVgRE0T1QE0qHCFAht2z6dLvJQs3Lo2FQoDxWNUiBhaP4eRgwNkI+dAjVOA/kUrIDwf3CG8NfNOE0eiFotSuo+rBiq8tD9oY4Qzc6YJw99hl1wzpQvD7ef2M8QgnOGJfJw+EltQc+oX2yn907QB22WZcvlUpd143dqQu+8pCJZuGE4xCuPXJqqcs5sNpsI93Rmzym1k4Npk+oD1SH3/a3LOK/JpUBpWfqNySxWzCfNCUITuDG5dtuphrUJ1myeIE9bIsPiKrfqTai5WZxbhtNphYx6GEIHihyGFTI69lje/rxajdh0s0msZ0zYxyPLhYCb1CyHm9Qsd2H37Y3lugVwL9kNh8Ot8cha6fUNQ8nuXi5z9/ExsAO4zQrb/ev1yrCB7lGyQzgYDGuxq1toDN/JGvN+HyWNHKB7zEoK+PX11e12G431erGYzwmytAWU56fkMHY5JJnDRR2eZji3AwtIcrEV8Cojat/BdQ7XOwGV1e1hDjGGjXbdArm8uJZtCH5MbcctVX8A1WpqumJHwckAAAAASUVORK5CYII=");
  background-size: 28px 28px;
  animation: image-loading-rotate 1s linear infinite !important;
}

.imt-image-status span {
  color: var(--bg-2, #fff) !important;
  font-size: 14px !important;
  line-height: 14px !important;
  font-weight: 500 !important;
  font-family: "PingFang SC", Arial, sans-serif !important;
}

@keyframes image-loading-rotate {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}
</style><style type="text/css" data-vite-dev-id="/Users/<USER>/test/danwen/testing2/AItestPlatform/web/src/components/query-bar/QueryBarItem.vue?vue&amp;type=style&amp;index=0&amp;scoped=a5b41f3d&amp;lang.css">
.query-bar-item[data-v-a5b41f3d] {
  display: flex;
  align-items: center;
  margin-right: 15px;
  margin-bottom: 10px;
}
.query-bar-item-label[data-v-a5b41f3d] {
  font-weight: 500;
  margin-right: 8px;
  white-space: nowrap;
}
.query-bar-item-content[data-v-a5b41f3d] {
  flex: 1;
  min-width: 200px;
}
</style><style type="text/css" data-vite-dev-id="/Users/<USER>/test/danwen/testing2/AItestPlatform/web/src/components/query-bar/QueryBar.vue?vue&amp;type=style&amp;index=0&amp;scoped=d24e232f&amp;lang.css">
.query-bar-container[data-v-d24e232f] {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  margin-bottom: 20px;
}
.query-bar-container[data-v-d24e232f] .n-space {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}
.query-bar-container[data-v-d24e232f] .n-space-item {
  margin-bottom: 10px;
  min-width: 200px;
}
</style><style type="text/css" data-vite-dev-id="/Users/<USER>/test/danwen/testing2/AItestPlatform/web/src/components/table/CrudTable.vue?vue&amp;type=style&amp;index=0&amp;lang.scss">.crud-table-container {
  width: 100%;
}
.crud-table-query-bar {
  margin-bottom: 20px;
  width: 100%;
  display: block;
}
.n-data-table .n-data-table-td {
  background-color: #0a2318;
}
.n-data-table .n-data-table-th {
  background-color: #18181c;
}</style><style type="text/css" data-vite-dev-id="/Users/<USER>/test/danwen/testing2/AItestPlatform/web/src/views/testing/testcases/index.vue?vue&amp;type=style&amp;index=0&amp;scoped=c7cd6387&amp;lang.css">
.table-container[data-v-c7cd6387] {
  position: relative;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  padding-bottom: 20px;
  padding-top: 20px;
}

/* 查询栏中的分页控件容器 */
.query-pagination-container[data-v-c7cd6387] {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-left: auto;
  min-width: 300px;
  flex-shrink: 0;
}
.query-pagination-container[data-v-c7cd6387] .query-bar-item-content {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
}

/* 确保查询栏能够容纳分页控件 */
.top-pagination-table[data-v-c7cd6387] .query-bar-container {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.top-pagination-table[data-v-c7cd6387] .query-bar-container .n-space {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  width: 100%;
  justify-content: flex-start;
  gap: 16px;
}

/* 分页控件样式优化 */
.query-pagination-container[data-v-c7cd6387] .n-data-table-pagination {
  position: static !important;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 6px 10px;
  margin: 0;
  display: flex;
  align-items: center;
  max-width: 100%;
  overflow: visible;
  font-size: 13px;
}
.table-container[data-v-c7cd6387] .n-data-table {
  max-height: calc(100% - 20px);
  display: flex;
  flex-direction: column;
}
.table-container[data-v-c7cd6387] .n-data-table-base-table-body {
  overflow: auto !important;
  max-height: calc(100vh - 320px);
}

/* 查询栏分页控件内部元素样式优化 */
.query-pagination-container[data-v-c7cd6387] .n-pagination {
  font-size: 13px;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  background: transparent;
  border: none;
  box-shadow: none;
}
.query-pagination-container[data-v-c7cd6387] .n-pagination-item {
  min-width: 26px;
  height: 26px;
  margin: 0 1px;
  font-size: 12px;
  line-height: 26px;
  border-radius: 4px;
}
.query-pagination-container[data-v-c7cd6387] .n-pagination .n-pagination-quick-jumper {
  margin-left: 6px;
  font-size: 12px;
}
.query-pagination-container[data-v-c7cd6387] .n-pagination .n-pagination-quick-jumper .n-input {
  width: 36px;
  height: 26px;
  margin: 0 3px;
}
.query-pagination-container[data-v-c7cd6387] .n-pagination .n-pagination-quick-jumper .n-input__input {
  font-size: 12px;
  height: 26px;
  padding: 0 4px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
.top-pagination-table[data-v-c7cd6387] .n-data-table-pagination {
    top: -55px !important;
    right: 15px !important;
    max-width: 350px !important;
    font-size: 12px !important;
}
}
@media (max-width: 768px) {
.top-pagination-table[data-v-c7cd6387] .n-data-table-pagination {
    top: -50px !important;
    right: 10px !important;
    max-width: 300px !important;
    font-size: 11px !important;
    padding: 4px 8px !important;
}
.top-pagination-table[data-v-c7cd6387] .n-pagination .n-pagination-size-picker {
    display: none !important;
}
.top-pagination-table[data-v-c7cd6387] .n-pagination .n-pagination-quick-jumper {
    display: none !important;
}
}
@media (max-width: 576px) {
.top-pagination-table[data-v-c7cd6387] .n-data-table-pagination {
    top: -45px !important;
    right: 5px !important;
    max-width: 250px !important;
    font-size: 10px !important;
    padding: 3px 6px !important;
}
.top-pagination-table[data-v-c7cd6387] .n-pagination-item {
    min-width: 20px !important;
    height: 20px !important;
    font-size: 10px !important;
    line-height: 20px !important;
    margin: 0 1px !important;
}
.top-pagination-table[data-v-c7cd6387] .query-bar-container {
    min-height: 50px !important;
}
}
.action-buttons[data-v-c7cd6387] {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 确保水平和垂直滚动条都可见 */
.table-container[data-v-c7cd6387] .n-scrollbar {
  overflow: auto !important;
}

/* 设置滚动条样式 */
.table-container[data-v-c7cd6387] .n-scrollbar-rail {
  z-index: 10;
}

/* 表格内容区域滚动 */
.table-container[data-v-c7cd6387] .n-data-table-base-table-body {
  max-height: calc(100vh - 350px);
  overflow: auto !important;
}

/* 重新定位分页控件到查询栏 */
.top-pagination-table[data-v-c7cd6387] .n-data-table-pagination {
  position: absolute !important;
  top: -60px !important;
  right: 20px !important;
  z-index: 1000 !important;
  background: white !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 6px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  padding: 6px 10px !important;
  margin: 0 !important;
  font-size: 13px !important;
  width: auto !important;
  max-width: 400px !important;
}

/* 确保查询栏有足够的空间容纳分页控件 */
.top-pagination-table[data-v-c7cd6387] .query-bar-container {
  position: relative !important;
  padding-bottom: 10px !important;
  min-height: 60px !important;
}

/* 确保表格内容区域有足够的空间 */
.table-container[data-v-c7cd6387] .n-data-table {
  display: flex;
  flex-direction: column;
}

/* 调整表格底部的间距 */
.top-pagination-table[data-v-c7cd6387] .n-data-table-wrapper {
  padding-bottom: 0;
}



/* 列宽调整样式 */
.table-container[data-v-c7cd6387] .n-data-table-th {
  position: relative;
  transition: background-color 0.3s;
}

/* 调整列宽时的拖动手柄样式 */
.table-container[data-v-c7cd6387] .n-data-table-resize-button {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 5px;
  cursor: col-resize;
  z-index: 1;
}

/* 拖动时的视觉反馈 */
.table-container[data-v-c7cd6387] .n-data-table-resize-button:hover,
.table-container[data-v-c7cd6387] .n-data-table-resize-button:active {
  background-color: #6D28D9;
  opacity: 0.5;
}

/* 固定的滚动按钮 - 底部 */
.scroll-to-bottom-btn[data-v-c7cd6387] {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #2080f0;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
}
.scroll-to-bottom-btn[data-v-c7cd6387]:hover {
  bottom: 30px; /* 固定在底部 */
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

/* 固定的滚动按钮 - 顶部 */
.scroll-to-top-btn[data-v-c7cd6387] {
  position: fixed;
  bottom: 100px; /* 位于底部按钮上方 */
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #18a058; /* 使用不同的颜色区分 */
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
}
.scroll-to-top-btn[data-v-c7cd6387]:hover {
  bottom: 90px;
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

/* 用例ID链接样式 */
.table-container[data-v-c7cd6387] .n-data-table-td a {
  display: inline-block;
  padding: 2px 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}
.table-container[data-v-c7cd6387] .n-data-table-td a:hover {
  background-color: rgba(32, 128, 240, 0.1);
  transform: scale(1.05);
}

/* 测试用例编辑表单样式优化 */
.testcase-edit-form[data-v-c7cd6387] {
  max-width: 100%;
  padding: 0 20px;
}
.testcase-edit-form[data-v-c7cd6387] .n-form-item {
  margin-bottom: 24px;
}
.testcase-edit-form[data-v-c7cd6387] .n-form-item-label {
  font-weight: 600;
  color: #374151;
  min-width: 120px;
  padding-right: 16px;
}
.testcase-edit-form[data-v-c7cd6387] .n-input {
  width: 100%;
  font-size: 14px;
  line-height: 1.5;
}
.testcase-edit-form[data-v-c7cd6387] .n-input__textarea {
  min-height: 40px;
  resize: vertical;
  font-family: inherit;
  line-height: 1.6;
  word-wrap: break-word;
  word-break: break-all;
}
.testcase-edit-form[data-v-c7cd6387] .n-tree-select,
.testcase-edit-form[data-v-c7cd6387] .n-select {
  width: 100%;
}

/* 测试步骤容器优化 */
.testcase-edit-form .test-steps-container[data-v-c7cd6387] {
  margin-bottom: 32px;
}
.testcase-edit-form .test-steps-container[data-v-c7cd6387] .n-form-item-blank {
  width: 100%;
}

/* 测试步骤行布局优化 */
.testcase-edit-form .test-step-row[data-v-c7cd6387] {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #f3f4f6;
  margin-bottom: 16px;
}
.testcase-edit-form .test-step-row[data-v-c7cd6387]:last-child {
  border-bottom: none;
  margin-bottom: 0;
}
.testcase-edit-form .step-number[data-v-c7cd6387] {
  width: 40px;
  font-weight: 600;
  color: #6366f1;
  flex-shrink: 0;
  text-align: center;
  padding-top: 8px;
  font-size: 16px;
}
.testcase-edit-form .step-operation[data-v-c7cd6387] {
  flex: 1;
  min-width: 0;
}
.testcase-edit-form .step-connector[data-v-c7cd6387] {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6366f1;
  width: 40px;
  flex-shrink: 0;
  padding-top: 8px;
}
.testcase-edit-form .step-expected[data-v-c7cd6387] {
  flex: 1;
  min-width: 0;
}
.testcase-edit-form .step-actions[data-v-c7cd6387] {
  width: 40px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  padding-top: 8px;
}
.testcase-edit-form .add-step-row[data-v-c7cd6387] {
  display: flex;
  justify-content: center;
  padding: 16px 0;
  margin-top: 16px;
  border-top: 2px dashed #e5e7eb;
}
.testcase-edit-form .add-step-btn[data-v-c7cd6387] {
  background: linear-gradient(45deg, #6366f1 0%, #8b5cf6 100%);
  border: none;
  color: white;
  font-weight: 500;
  padding: 8px 24px;
  border-radius: 8px;
  transition: all 0.3s ease;
}
.testcase-edit-form .add-step-btn[data-v-c7cd6387]:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

/* 强制移动分页控件到查询栏右侧 - 使用多个选择器确保生效 */
.top-pagination-table[data-v-c7cd6387] .n-data-table-pagination,
.table-container .top-pagination-table[data-v-c7cd6387] .n-data-table-pagination,
.n-data-table-pagination[data-v-c7cd6387] {
  position: absolute !important;
  top: -60px !important;
  right: 20px !important;
  z-index: 9999 !important;
  background: white !important;
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  padding: 8px 12px !important;
  margin: 0 !important;
  font-size: 13px !important;
  width: auto !important;
  max-width: 450px !important;
  min-width: 200px !important;
  transform: none !important;
}

/* 确保查询栏容器有足够空间 */
.top-pagination-table[data-v-c7cd6387] .query-bar-container {
  position: relative !important;
  padding-bottom: 15px !important;
  min-height: 70px !important;
}

/* 分页控件内部元素样式 */
.top-pagination-table[data-v-c7cd6387] .n-pagination {
  margin: 0 !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-wrap: nowrap !important;
}
.top-pagination-table[data-v-c7cd6387] .n-pagination-item {
  margin: 0 2px !important;
  min-width: 28px !important;
  height: 28px !important;
  font-size: 12px !important;
  line-height: 28px !important;
}

/* 响应式设计 */
@media (max-width: 1400px) {
.testcase-edit-form[data-v-c7cd6387] {
    padding: 0 16px;
}
.testcase-edit-form .test-step-row[data-v-c7cd6387] {
    flex-direction: column;
    gap: 12px;
}
.testcase-edit-form .step-number[data-v-c7cd6387],
  .testcase-edit-form .step-connector[data-v-c7cd6387],
  .testcase-edit-form .step-actions[data-v-c7cd6387] {
    width: 100%;
    text-align: left;
    padding-top: 0;
}
.testcase-edit-form .step-operation[data-v-c7cd6387],
  .testcase-edit-form .step-expected[data-v-c7cd6387] {
    width: 100%;
}

  /* 分页控件响应式调整 */
.top-pagination-table[data-v-c7cd6387] .n-data-table-pagination {
    top: -55px !important;
    right: 15px !important;
    max-width: 350px !important;
    font-size: 12px !important;
}
}
@media (max-width: 768px) {
.testcase-edit-form[data-v-c7cd6387] .n-form-item-label {
    min-width: 100px;
    padding-right: 12px;
}

  /* 小屏幕分页控件调整 */
.top-pagination-table[data-v-c7cd6387] .n-data-table-pagination {
    top: -50px !important;
    right: 10px !important;
    max-width: 280px !important;
    font-size: 11px !important;
    padding: 6px 8px !important;
}
.top-pagination-table[data-v-c7cd6387] .n-pagination .n-pagination-size-picker {
    display: none !important;
}
.top-pagination-table[data-v-c7cd6387] .n-pagination .n-pagination-quick-jumper {
    display: none !important;
}
}
@media (max-width: 576px) {
  /* 超小屏幕分页控件调整 */
.top-pagination-table[data-v-c7cd6387] .n-data-table-pagination {
    top: -45px !important;
    right: 5px !important;
    max-width: 220px !important;
    font-size: 10px !important;
    padding: 4px 6px !important;
}
.top-pagination-table[data-v-c7cd6387] .n-pagination-item {
    min-width: 24px !important;
    height: 24px !important;
    font-size: 10px !important;
    line-height: 24px !important;
    margin: 0 1px !important;
}
}
</style></head>

  <body>
    <div id="app" data-v-app=""><div class="n-config-provider" wh-full=""><!--teleport start--><!--teleport end--><div class="n-layout n-layout--static-positioned" wh-full="" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-color: #fff; --n-text-color: rgb(51, 54, 57);"><div class="n-layout-scroll-container" style="display: flex; flex-flow: row; width: 100%;"><aside class="n-layout-sider n-layout-sider--static-positioned n-layout-sider--left-placement n-layout-sider--bordered n-layout-sider--show-content layout-sider" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-toggle-button-color: #FFF; --n-toggle-button-border: 1px solid rgb(239, 239, 245); --n-toggle-bar-color: rgba(191, 191, 191, 1); --n-toggle-bar-color-hover: rgba(153, 153, 153, 1); --n-color: #fff; --n-text-color: rgb(51, 54, 57); --n-border-color: rgb(239, 239, 245); --n-toggle-button-icon-color: rgb(51, 54, 57); max-width: 220px; width: 220px;"><div role="none" class="n-scrollbar" style="--n-scrollbar-bezier: cubic-bezier(.4, 0, .2, 1); --n-scrollbar-color: rgba(0, 0, 0, 0.25); --n-scrollbar-color-hover: rgba(0, 0, 0, 0.4); --n-scrollbar-border-radius: 5px; --n-scrollbar-width: 5px; --n-scrollbar-height: 5px; --n-scrollbar-rail-top-horizontal-top: 4px; --n-scrollbar-rail-right-horizontal-top: 2px; --n-scrollbar-rail-bottom-horizontal-top: auto; --n-scrollbar-rail-left-horizontal-top: 2px; --n-scrollbar-rail-top-horizontal-bottom: auto; --n-scrollbar-rail-right-horizontal-bottom: 2px; --n-scrollbar-rail-bottom-horizontal-bottom: 4px; --n-scrollbar-rail-left-horizontal-bottom: 2px; --n-scrollbar-rail-top-vertical-right: 2px; --n-scrollbar-rail-right-vertical-right: 4px; --n-scrollbar-rail-bottom-vertical-right: 2px; --n-scrollbar-rail-left-vertical-right: auto; --n-scrollbar-rail-top-vertical-left: 2px; --n-scrollbar-rail-right-vertical-left: auto; --n-scrollbar-rail-bottom-vertical-left: 2px; --n-scrollbar-rail-left-vertical-left: 4px; --n-scrollbar-rail-color: transparent;"><div role="none" class="n-scrollbar-container"><div role="none" class="n-scrollbar-content"><a href="/" class="" h-60="" f-c-c=""><svg class="inline-block" width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg" text-36="" color-primary=""><!-- 背景圆形 --><circle cx="256" cy="256" r="256" fill="#0EA5E9"></circle><!-- 大脑图案 --><path d="M256 120C200 120 155 165 155 220C155 275 200 320 256 320C311 320 356 275 356 220C356 165 311 120 256 120ZM256 300C211 300 175 264 175 220C175 176 211 140 256 140C300 140 336 176 336 220C336 264 300 300 256 300Z" fill="white"></path><!-- 脑神经连接 --><path d="M256 140C211 140 175 176 175 220C175 264 211 300 256 300C300 300 336 264 336 220C336 176 300 140 256 140ZM256 280C222 280 195 253 195 220C195 187 222 160 256 160C289 160 316 187 316 220C316 253 289 280 256 280Z" fill="#0EA5E9"></path><!-- 中心圆点 --><circle cx="256" cy="220" r="40" fill="white"></circle><!-- 电路板线条 --><path d="M155 220H120" stroke="white" stroke-width="10" stroke-linecap="round"></path><path d="M356 220H390" stroke="white" stroke-width="10" stroke-linecap="round"></path><path d="M256 120V90" stroke="white" stroke-width="10" stroke-linecap="round"></path><path d="M256 320V350" stroke="white" stroke-width="10" stroke-linecap="round"></path><!-- 代码符号 --><path d="M200 380L170 410L200 440" stroke="white" stroke-width="10" stroke-linecap="round" stroke-linejoin="round"></path><path d="M310 380L340 410L310 440" stroke="white" stroke-width="10" stroke-linecap="round" stroke-linejoin="round"></path><path d="M240 370L270 450" stroke="white" stroke-width="10" stroke-linecap="round"></path><!-- 测试图标 --><circle cx="120" cy="410" r="30" stroke="white" stroke-width="10" fill="none"></circle><path d="M120 390V410H140" stroke="white" stroke-width="10" stroke-linecap="round"></path><!-- 齿轮图标 --><circle cx="390" cy="410" r="30" stroke="white" stroke-width="10" fill="none"></circle><path d="M390 380V390M390 430V440M360 410H370M410 410H420M368 388L378 398M368 432L378 422M412 388L402 398M412 432L402 422" stroke="white" stroke-width="10" stroke-linecap="round"></path></svg><h2 ml-2="" mr-8="" max-w-150="" flex-shrink-0="" text-16="" font-bold="" color-primary="">AItest管理系统</h2></a><div class="side-menu n-menu n-menu--vertical" role="menu" style="--n-divider-color: rgb(239, 239, 245); --n-bezier: cubic-bezier(.4, 0, .2, 1); --n-font-size: 14px; --n-border-color-horizontal: #0000; --n-border-radius: 3px; --n-item-height: 42px; --n-group-text-color: rgb(118, 124, 130); --n-color: #0000; --n-item-text-color: rgb(51, 54, 57); --n-item-text-color-hover: rgb(51, 54, 57); --n-item-text-color-active: #0EA5E9; --n-item-text-color-child-active: #0EA5E9; --n-item-text-color-child-active-hover: #0EA5E9; --n-item-text-color-active-hover: #0EA5E9; --n-item-icon-color: rgb(31, 34, 37); --n-item-icon-color-hover: rgb(31, 34, 37); --n-item-icon-color-active: #0EA5E9; --n-item-icon-color-active-hover: #0EA5E9; --n-item-icon-color-child-active: #0EA5E9; --n-item-icon-color-child-active-hover: #0EA5E9; --n-item-icon-color-collapsed: rgb(31, 34, 37); --n-item-text-color-horizontal: rgb(51, 54, 57); --n-item-text-color-hover-horizontal: #38BDF8; --n-item-text-color-active-horizontal: #0EA5E9; --n-item-text-color-child-active-horizontal: #0EA5E9; --n-item-text-color-child-active-hover-horizontal: #0EA5E9; --n-item-text-color-active-hover-horizontal: #0EA5E9; --n-item-icon-color-horizontal: rgb(31, 34, 37); --n-item-icon-color-hover-horizontal: #38BDF8; --n-item-icon-color-active-horizontal: #0EA5E9; --n-item-icon-color-active-hover-horizontal: #0EA5E9; --n-item-icon-color-child-active-horizontal: #0EA5E9; --n-item-icon-color-child-active-hover-horizontal: #0EA5E9; --n-arrow-color: rgb(51, 54, 57); --n-arrow-color-hover: rgb(51, 54, 57); --n-arrow-color-active: #0EA5E9; --n-arrow-color-active-hover: #0EA5E9; --n-arrow-color-child-active: #0EA5E9; --n-arrow-color-child-active-hover: #0EA5E9; --n-item-color-hover: rgb(243, 243, 245); --n-item-color-active: rgba(14, 165, 233, 0.1); --n-item-color-active-hover: rgba(14, 165, 233, 0.1); --n-item-color-active-collapsed: rgba(14, 165, 233, 0.1);"><div role="menuitem" class="n-menu-item"><!----><div role="none" class="n-menu-item-content" style="padding-left: 18px;"><div class="n-menu-item-content__icon" role="none" style="width: 22px; height: 22px; font-size: 20px; margin-right: 8px;"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 18px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 48 48" class="iconify iconify--icon-park-outline"><g fill="none" stroke="currentColor" stroke-linejoin="round" stroke-width="4"><path d="M12 33H4V7h40v26z"></path><path stroke-linecap="round" d="M16 22v4m8 7v6m0-21v8m8-12v12M12 41h24"></path></g></svg></i></div><div class="n-menu-item-content-header" role="none">工作台<!----></div><!----></div><!----></div><!----><div class="n-submenu" role="menu" aria-expanded="true"><div class="n-menu-item" role="menuitem"><div role="none" class="n-menu-item-content n-menu-item-content--child-active" style="padding-left: 18px;"><div class="n-menu-item-content__icon" role="none" style="width: 22px; height: 22px; font-size: 20px; margin-right: 8px;"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 18px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="M6 13h9v-2H6zm0-3h9V8H6zM4 20q-.825 0-1.412-.587T2 18V6q0-.825.588-1.412T4 4h16q.825 0 1.413.588T22 6v12q0 .825-.587 1.413T20 20zm0-2h16V6H4zm0 0V6z"></path></svg></i></div><div class="n-menu-item-content-header" role="none">测试管理<!----></div><i class="n-base-icon n-menu-item-content__arrow" aria-hidden="true"><svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.20041 5.73966C3.48226 5.43613 3.95681 5.41856 4.26034 5.70041L8 9.22652L11.7397 5.70041C12.0432 5.41856 12.5177 5.43613 12.7996 5.73966C13.0815 6.0432 13.0639 6.51775 12.7603 6.7996L8.51034 10.7996C8.22258 11.0668 7.77743 11.0668 7.48967 10.7996L3.23966 6.7996C2.93613 6.51775 2.91856 6.0432 3.20041 5.73966Z" fill="currentColor"></path></svg></i></div></div><div class="n-submenu-children" role="menu"><div role="menuitem" class="n-menu-item"><!----><div role="none" class="n-menu-item-content" style="padding-left: 36px;"><div class="n-menu-item-content__icon" role="none" style="width: 22px; height: 22px; font-size: 20px; margin-right: 8px;"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 18px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--mdi"><path fill="currentColor" d="M19 18.31V20a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-3.7c-.46-.18-1.05-.3-2-.3a1 1 0 0 1-1-1a1 1 0 0 1 1-1c.82 0 1.47.08 2 .21V12.3c-.46-.18-1.05-.3-2-.3a1 1 0 0 1-1-1a1 1 0 0 1 1-1c.82 0 1.47.08 2 .21V8.3C4.54 8.12 3.95 8 3 8a1 1 0 0 1-1-1a1 1 0 0 1 1-1c.82 0 1.47.08 2 .21V4a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v2.16c1.78.31 2.54.97 2.71 1.13c.39.39.39 1.03 0 1.42s-.91.38-1.42 0c0 0-1.04-.71-3.29-.71c-1.26 0-2.09.41-3.05.9c-1.04.51-2.21 1.1-3.95 1.1c-.36 0-.69 0-1-.04V7.95c.3.05.63.05 1 .05c1.26 0 2.09-.41 3.05-.89C14.09 6.59 15.27 6 17 6V4H7v16h10v-2c1.5 0 1.97.29 2 .31M17 10c-1.73 0-2.91.59-3.95 1.11c-.96.48-1.79.89-3.05.89c-.37 0-.7 0-1-.05v2.01c.31.04.64.04 1 .04c1.74 0 2.91-.59 3.95-1.1c.96-.48 1.79-.9 3.05-.9c2.25 0 3.29.71 3.29.71c.51.39 1.03.39 1.42 0s.39-1.02 0-1.42C21.5 11.08 20.25 10 17 10m0 4c-1.73 0-2.91.59-3.95 1.11c-.96.48-1.79.89-3.05.89c-.37 0-.7 0-1-.05v2.01c.31.04.64.04 1 .04c1.74 0 2.91-.59 3.95-1.1c.96-.48 1.79-.9 3.05-.9c2.25 0 3.29.71 3.29.71c.51.39 1.03.39 1.42 0s.39-1.02 0-1.42C21.5 15.08 20.25 14 17 14"></path></svg></i></div><div class="n-menu-item-content-header" role="none">项目管理<!----></div><!----></div><!----></div><div role="menuitem" class="n-menu-item"><!----><div role="none" class="n-menu-item-content" style="padding-left: 36px;"><div class="n-menu-item-content__icon" role="none" style="width: 22px; height: 22px; font-size: 20px; margin-right: 8px;"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 18px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--mdi"><path fill="currentColor" d="M22 2v2c-1.74 0-3 4.58-4.04 8.27c-1.39 5-2.7 9.73-5.96 9.73s-4.57-4.73-5.96-9.73C5 8.58 3.74 4 2 4V2c3.26 0 4.57 4.73 5.96 9.73C9 15.42 10.26 20 12 20s3-4.58 4.04-8.27C17.43 6.73 18.74 2 22 2"></path></svg></i></div><div class="n-menu-item-content-header" role="none">需求分析<!----></div><!----></div><!----></div><div role="menuitem" class="n-menu-item"><!----><div role="none" class="n-menu-item-content" style="padding-left: 36px;"><div class="n-menu-item-content__icon" role="none" style="width: 22px; height: 22px; font-size: 20px; margin-right: 8px;"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 18px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--mdi"><path fill="currentColor" d="M20.84 22.73L18 19.9l-.62.63L16 17.89l-3.65-3.65L9.6 17l.36 2.47l-1.07 1.06l-1.76-3.18l-3.19-1.77L5 14.5l2.5.37l2.73-2.75L6.59 8.5L3.94 7.09l.63-.63L1.11 3l1.28-1.27l19.72 19.73zM16.67 9.92l3.89-3.89c.59-.58.59-1.53 0-2.12s-1.56-.58-2.12 0L14.55 7.8L9.94 6.74l7.8 7.8z"></path></svg></i></div><div class="n-menu-item-content-header" role="none">需求管理<!----></div><!----></div><!----></div><div role="menuitem" class="n-menu-item"><!----><div role="none" class="n-menu-item-content" style="padding-left: 36px;"><div class="n-menu-item-content__icon" role="none" style="width: 22px; height: 22px; font-size: 20px; margin-right: 8px;"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 18px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--mdi"><path fill="currentColor" d="M2.5 19h19v2h-19zm19.57-9.36c-.21-.8-1.04-1.28-1.84-1.06L14.92 10L8 3.57l-1.91.51l4.14 7.17l-4.97 1.33l-1.97-1.54l-1.45.39l1.82 3.16l.77 1.33l1.6-.42l5.31-1.43l4.35-1.16L21 11.5c.81-.24 1.28-1.06 1.07-1.86"></path></svg></i></div><div class="n-menu-item-content-header" role="none">用例生成<!----></div><!----></div><!----></div><div role="menuitem" class="n-menu-item"><!----><div role="none" class="n-menu-item-content n-menu-item-content--selected" style="padding-left: 36px;"><div class="n-menu-item-content__icon" role="none" style="width: 22px; height: 22px; font-size: 20px; margin-right: 8px;"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 18px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--mdi"><path fill="currentColor" d="M11 23a2 2 0 0 1-2-2v-2h6v2a2 2 0 0 1-2 2zm1-22c.71 0 1.39.09 2.05.26C15.22 2.83 16 5.71 16 9c0 2.28-.38 4.37-1 7a2 2 0 0 1-2 2h-2a2 2 0 0 1-2-2c-.62-2.63-1-4.72-1-7c0-3.29.78-6.17 1.95-7.74C10.61 1.09 11.29 1 12 1m8 7c0 3.18-1.85 7.92-4.54 9.21C16.41 15.39 17 11.83 17 9s-.59-5.39-1.54-7.21C18.15 3.08 20 4.82 20 8M4 8c0-3.18 1.85-4.92 4.54-6.21C7.59 3.61 7 6.17 7 9s.59 6.39 1.54 8.21C5.85 15.92 4 11.18 4 8"></path></svg></i></div><div class="n-menu-item-content-header" role="none">用例管理<!----></div><!----></div><!----></div><div role="menuitem" class="n-menu-item"><!----><div role="none" class="n-menu-item-content" style="padding-left: 36px;"><div class="n-menu-item-content__icon" role="none" style="width: 22px; height: 22px; font-size: 20px; margin-right: 8px;"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 18px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--mdi"><path fill="currentColor" d="m19 2l-5 4.5v11l5-4.5zM6.5 5C4.55 5 2.45 5.4 1 6.5v14.66c0 .25.25.5.5.5c.1 0 .15-.07.25-.07c1.35-.65 3.3-1.09 4.75-1.09c1.95 0 4.05.4 5.5 1.5c1.35-.85 3.8-1.5 5.5-1.5c1.65 0 3.35.31 4.75 1.06c.1.05.15.03.25.03c.25 0 .5-.25.5-.5V6.5c-.6-.45-1.25-.75-2-1V19c-1.1-.35-2.3-.5-3.5-.5c-1.7 0-4.15.65-5.5 1.5V6.5C10.55 5.4 8.45 5 6.5 5"></path></svg></i></div><div class="n-menu-item-content-header" role="none">项目知识库<!----></div><!----></div><!----></div></div></div><!----><!----><div class="n-submenu" role="menu" aria-expanded="false"><div class="n-menu-item" role="menuitem"><div role="none" class="n-menu-item-content n-menu-item-content--collapsed" style="padding-left: 18px;"><div class="n-menu-item-content__icon" role="none" style="width: 22px; height: 22px; font-size: 20px; margin-right: 8px;"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 18px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 32 32" class="iconify iconify--carbon"><path fill="currentColor" d="M30 24v-2h-2.101a5 5 0 0 0-.732-1.753l1.49-1.49l-1.414-1.414l-1.49 1.49A5 5 0 0 0 24 18.101V16h-2v2.101a5 5 0 0 0-1.753.732l-1.49-1.49l-1.414 1.414l1.49 1.49A5 5 0 0 0 18.101 22H16v2h2.101a5 5 0 0 0 .732 1.753l-1.49 1.49l1.414 1.414l1.49-1.49a5 5 0 0 0 1.753.732V30h2v-2.101a5 5 0 0 0 1.753-.732l1.49 1.49l1.414-1.414l-1.49-1.49A5 5 0 0 0 27.899 24Zm-7 2a3 3 0 1 1 3-3a3.003 3.003 0 0 1-3 3"></path><path fill="currentColor" d="M28 4H4a2 2 0 0 0-2 2v20a2 2 0 0 0 2 2h10v-2H4V12h24v3h2V6a2 2 0 0 0-2-2m0 6H4V6h24Z"></path><circle cx="20" cy="8" r="1" fill="currentColor"></circle><circle cx="23" cy="8" r="1" fill="currentColor"></circle><circle cx="26" cy="8" r="1" fill="currentColor"></circle></svg></i></div><div class="n-menu-item-content-header" role="none">系统配置<!----></div><i class="n-base-icon n-menu-item-content__arrow" aria-hidden="true"><svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.20041 5.73966C3.48226 5.43613 3.95681 5.41856 4.26034 5.70041L8 9.22652L11.7397 5.70041C12.0432 5.41856 12.5177 5.43613 12.7996 5.73966C13.0815 6.0432 13.0639 6.51775 12.7603 6.7996L8.51034 10.7996C8.22258 11.0668 7.77743 11.0668 7.48967 10.7996L3.23966 6.7996C2.93613 6.51775 2.91856 6.0432 3.20041 5.73966Z" fill="currentColor"></path></svg></i></div></div><!----></div><!----></div></div></div><div class="n-scrollbar-rail n-scrollbar-rail--vertical n-scrollbar-rail--vertical--right n-scrollbar-rail--disabled" data-scrollbar-rail="true" aria-hidden="true"><!----></div><!----></div><!----><div class="n-layout-sider__border"></div></aside><article flex-col="" flex-1="" overflow-hidden=""><header class="flex items-center px-15 layout-header" style="height: 60px;"><div flex="" items-center=""><i cursor-pointer="" role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 20px;"><svg class="inline-block" viewBox="0 0 24 24" width="1em" height="1em"><path fill="currentColor" d="M11 13h10v-2H11m0-2h10V7H11M3 3v2h18V3M3 21h18v-2H3m0-7l4 4V8m4 9h10v-2H11z"></path></svg></i><nav class="n-breadcrumb" aria-label="Breadcrumb" ml-15="" hidden="" sm:block="" style="--n-font-size: 14px; --n-bezier: cubic-bezier(.4, 0, .2, 1); --n-item-text-color: rgb(118, 124, 130); --n-item-text-color-hover: rgb(51, 54, 57); --n-item-text-color-pressed: rgb(51, 54, 57); --n-item-text-color-active: rgb(51, 54, 57); --n-separator-color: rgb(118, 124, 130); --n-item-color-hover: rgba(46, 51, 56, .09); --n-item-color-pressed: rgba(46, 51, 56, .13); --n-item-border-radius: 3px; --n-font-weight-active: 400; --n-item-line-height: 1.25;"><ul><li class="n-breadcrumb-item n-breadcrumb-item--clickable"><span class="n-breadcrumb-item__link"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 18px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="M6 13h9v-2H6zm0-3h9V8H6zM4 20q-.825 0-1.412-.587T2 18V6q0-.825.588-1.412T4 4h16q.825 0 1.413.588T22 6v12q0 .825-.587 1.413T20 20zm0-2h16V6H4zm0 0V6z"></path></svg></i> 测试管理</span><span class="n-breadcrumb-item__separator" aria-hidden="true">/</span></li><li class="n-breadcrumb-item n-breadcrumb-item--clickable"><span class="n-breadcrumb-item__link"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 18px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--mdi"><path fill="currentColor" d="M11 23a2 2 0 0 1-2-2v-2h6v2a2 2 0 0 1-2 2zm1-22c.71 0 1.39.09 2.05.26C15.22 2.83 16 5.71 16 9c0 2.28-.38 4.37-1 7a2 2 0 0 1-2 2h-2a2 2 0 0 1-2-2c-.62-2.63-1-4.72-1-7c0-3.29.78-6.17 1.95-7.74C10.61 1.09 11.29 1 12 1m8 7c0 3.18-1.85 7.92-4.54 9.21C16.41 15.39 17 11.83 17 9s-.59-5.39-1.54-7.21C18.15 3.08 20 4.82 20 8M4 8c0-3.18 1.85-4.92 4.54-6.21C7.59 3.61 7 6.17 7 9s.59 6.39 1.54 8.21C5.85 15.92 4 11.18 4 8"></path></svg></i> 用例管理</span><span class="n-breadcrumb-item__separator" aria-hidden="true">/</span></li></ul></nav></div><div ml-auto="" flex="" items-center=""><!----><i mr-20="" role="img" class="n-icon" style="cursor: pointer; --n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 18px;"><svg class="inline-block" viewBox="0 0 24 24" width="1em" height="1em"><path fill="currentColor" d="M17.9 17.39c-.26-.8-1.01-1.39-1.9-1.39h-1v-3a1 1 0 0 0-1-1H8v-2h2a1 1 0 0 0 1-1V7h2a2 2 0 0 0 2-2v-.41a7.984 7.984 0 0 1 2.9 12.8M11 19.93c-3.95-.49-7-3.85-7-7.93c0-.62.08-1.22.21-1.79L9 15v1a2 2 0 0 0 2 2m1-16A10 10 0 0 0 2 12a10 10 0 0 0 10 10a10 10 0 0 0 10-10A10 10 0 0 0 12 2"></path></svg></i><!----><!-- <ThemeMode /> --><i mr-20="" role="img" class="n-icon" style="cursor: pointer; --n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 18px;"><svg class="inline-block" viewBox="0 0 24 24" width="1em" height="1em"><path fill="currentColor" d="M12 2A10 10 0 0 0 2 12c0 4.42 2.87 8.17 6.84 9.5c.5.08.66-.23.66-.5v-1.69c-2.77.6-3.36-1.34-3.36-1.34c-.46-1.16-1.11-1.47-1.11-1.47c-.91-.62.07-.6.07-.6c1 .07 1.53 1.03 1.53 1.03c.87 1.52 2.34 1.07 2.91.83c.09-.65.35-1.09.63-1.34c-2.22-.25-4.55-1.11-4.55-4.92c0-1.11.38-2 1.03-2.71c-.1-.25-.45-1.29.1-2.64c0 0 .84-.27 2.75 1.02c.79-.22 1.65-.33 2.5-.33s1.71.11 2.5.33c1.91-1.29 2.75-1.02 2.75-1.02c.55 1.35.2 2.39.1 2.64c.65.71 1.03 1.6 1.03 2.71c0 3.82-2.34 4.66-4.57 4.91c.36.31.69.92.69 1.85V21c0 .27.16.59.67.5C19.14 20.16 22 16.42 22 12A10 10 0 0 0 12 2"></path></svg></i><i mr20="" role="img" class="n-icon" style="cursor: pointer; --n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 18px;"><svg class="inline-block" viewBox="0 0 1024 1024" width="1em" height="1em"><path fill="currentColor" d="m290 236.4l43.9-43.9a8.01 8.01 0 0 0-4.7-13.6L169 160c-5.1-.6-9.5 3.7-8.9 8.9L179 329.1c.8 6.6 8.9 9.4 13.6 4.7l43.7-43.7L370 423.7c3.1 3.1 8.2 3.1 11.3 0l42.4-42.3c3.1-3.1 3.1-8.2 0-11.3zm352.7 187.3c3.1 3.1 8.2 3.1 11.3 0l133.7-133.6l43.7 43.7a8.01 8.01 0 0 0 13.6-4.7L863.9 169c.6-5.1-3.7-9.5-8.9-8.9L694.8 179c-6.6.8-9.4 8.9-4.7 13.6l43.9 43.9L600.3 370a8.03 8.03 0 0 0 0 11.3zM845 694.9c-.8-6.6-8.9-9.4-13.6-4.7l-43.7 43.7L654 600.3a8.03 8.03 0 0 0-11.3 0l-42.4 42.3a8.03 8.03 0 0 0 0 11.3L734 787.6l-43.9 43.9a8.01 8.01 0 0 0 4.7 13.6L855 864c5.1.6 9.5-3.7 8.9-8.9zm-463.7-94.6a8.03 8.03 0 0 0-11.3 0L236.3 733.9l-43.7-43.7a8.01 8.01 0 0 0-13.6 4.7L160.1 855c-.6 5.1 3.7 9.5 8.9 8.9L329.2 845c6.6-.8 9.4-8.9 4.7-13.6L290 787.6L423.7 654c3.1-3.1 3.1-8.2 0-11.3z"></path></svg></i><!----><div flex="" cursor-pointer="" items-center=""><img src="https://avatars.githubusercontent.com/u/54677442?v=4" mr10="" h-35="" w-35="" rounded-full=""><span>admin</span></div><!----></div></header><section class="layout-tags"><div data-v-eec37558="" class="wrapper bg-white dark:bg-dark!" style="height: 50px;"><!--v-if--><div data-v-eec37558="" class="content" style="transform: translateX(0px);"><div class="n-tag mx-5 cursor-pointer rounded-4 px-15 hover:color-primary" style="--n-font-weight-strong: 500; --n-avatar-size-override: calc(28px - 8px); --n-bezier: cubic-bezier(.4, 0, .2, 1); --n-border-radius: 2px; --n-border: 1px solid rgba(14, 165, 233, 0.3); --n-close-icon-size: 14px; --n-close-color-pressed: rgba(14, 165, 233, 0.18); --n-close-color-hover: rgba(14, 165, 233, 0.12); --n-close-border-radius: 2px; --n-close-icon-color: #0EA5E9; --n-close-icon-color-hover: #0EA5E9; --n-close-icon-color-pressed: #0EA5E9; --n-close-icon-color-disabled: #0EA5E9; --n-close-margin-top: 0; --n-close-margin-right: 0; --n-close-margin-bottom: 0; --n-close-margin-left: 4px; --n-close-size: 18px; --n-color: rgba(14, 165, 233, 0.1); --n-color-checkable: #0000; --n-color-checked: #0EA5E9; --n-color-checked-hover: #38BDF8; --n-color-checked-pressed: #0284C7; --n-color-hover-checkable: rgba(46, 51, 56, .09); --n-color-pressed-checkable: rgba(46, 51, 56, .13); --n-font-size: 14px; --n-height: 28px; --n-opacity-disabled: 0.5; --n-padding: 0 7px; --n-text-color: #0EA5E9; --n-text-color-checkable: rgb(51, 54, 57); --n-text-color-checked: #FFF; --n-text-color-hover-checkable: rgb(51, 54, 57); --n-text-color-pressed-checkable: rgb(51, 54, 57);"><!----><span class="n-tag__content">用例管理</span><!----><div class="n-tag__border"></div></div><!--v-if--></div></div></section><section class="appSection layout-main" flex-1="" overflow-hidden=""><!-- 业务页面 --><section data-v-c7cd6387="" class="cus-scroll-y wh-full flex-col bg-[#f5f6fb] p-15 dark:bg-hex-121212 overflow-auto"><header mb-15="" min-h-45="" flex="" items-center="" justify-between="" px-15=""><h2 text-22="" font-normal="" text-hex-333="" dark:text-hex-ccc="">用例列表</h2><div data-v-c7cd6387="" class="action-buttons"><button data-v-c7cd6387="" class="n-button n-button--info-type n-button--medium-type float-right mr-15" tabindex="0" type="button" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: #2563EB; --n-color-hover: #3B82F6; --n-color-pressed: #1D4ED8; --n-color-focus: #3B82F6; --n-color-disabled: #2563EB; --n-ripple-color: #2563EB; --n-text-color: #FFF; --n-text-color-hover: #FFF; --n-text-color-pressed: #FFF; --n-text-color-focus: #FFF; --n-text-color-disabled: #FFF; --n-border: 1px solid #2563EB; --n-border-hover: 1px solid #3B82F6; --n-border-pressed: 1px solid #1D4ED8; --n-border-focus: 1px solid #3B82F6; --n-border-disabled: 1px solid #2563EB; --n-width: initial; --n-height: 34px; --n-font-size: 14px; --n-padding: 0 14px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px;"><!----><!----><span class="n-button__content"><i data-v-c7cd6387="" class="mr-5 n-icon" role="img" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 18px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="m12 16l-5-5l1.4-1.45l2.6 2.6V4h2v8.15l2.6-2.6L17 11zm-6 4q-.825 0-1.412-.587T4 18v-3h2v3h12v-3h2v3q0 .825-.587 1.413T18 20z"></path></svg></i>导出 </span><div aria-hidden="true" class="n-base-wave"></div><div aria-hidden="true" class="n-button__border"></div><div aria-hidden="true" class="n-button__state-border"></div></button><button data-v-c7cd6387="" class="n-button n-button--primary-type n-button--medium-type float-right mr-15" tabindex="0" type="button" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: #0EA5E9; --n-color-hover: #38BDF8; --n-color-pressed: #0284C7; --n-color-focus: #38BDF8; --n-color-disabled: #0EA5E9; --n-ripple-color: #0EA5E9; --n-text-color: #FFF; --n-text-color-hover: #FFF; --n-text-color-pressed: #FFF; --n-text-color-focus: #FFF; --n-text-color-disabled: #FFF; --n-border: 1px solid #0EA5E9; --n-border-hover: 1px solid #38BDF8; --n-border-pressed: 1px solid #0284C7; --n-border-focus: 1px solid #38BDF8; --n-border-disabled: 1px solid #0EA5E9; --n-width: initial; --n-height: 34px; --n-font-size: 14px; --n-padding: 0 14px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px;"><!----><!----><span class="n-button__content"><i data-v-c7cd6387="" class="mr-5 n-icon" role="img" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 18px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="M11 13H5v-2h6V5h2v6h6v2h-6v6h-2z"></path></svg></i>新增用例 </span><div aria-hidden="true" class="n-base-wave"></div><div aria-hidden="true" class="n-button__border"></div><div aria-hidden="true" class="n-button__state-border"></div></button></div></header><div class="n-card n-card--bordered" flex-1="" rounded-16="" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-border-radius: 3px; --n-color: #fff; --n-color-modal: #fff; --n-color-popover: #fff; --n-color-embedded: rgb(250, 250, 252); --n-color-embedded-modal: rgb(250, 250, 252); --n-color-embedded-popover: rgb(250, 250, 252); --n-color-target: #0EA5E9; --n-text-color: rgb(51, 54, 57); --n-line-height: 1.6; --n-action-color: rgb(250, 250, 252); --n-title-text-color: rgb(31, 34, 37); --n-title-font-weight: 500; --n-close-icon-color: rgba(102, 102, 102, 1); --n-close-icon-color-hover: rgba(102, 102, 102, 1); --n-close-icon-color-pressed: rgba(102, 102, 102, 1); --n-close-color-hover: rgba(0, 0, 0, .09); --n-close-color-pressed: rgba(0, 0, 0, .13); --n-border-color: rgb(239, 239, 245); --n-box-shadow: 0 1px 2px -2px rgba(0, 0, 0, .08), 0 3px 6px 0 rgba(0, 0, 0, .06), 0 5px 12px 4px rgba(0, 0, 0, .04); --n-padding-top: 19px; --n-padding-bottom: 20px; --n-padding-left: 24px; --n-font-size: 14px; --n-title-font-size: 18px; --n-close-size: 22px; --n-close-icon-size: 18px; --n-close-border-radius: 3px;"><!----><!----><div class="n-card__content" role="none"><div data-v-c7cd6387="" class="scroll-to-bottom-btn"><i data-v-c7cd6387="" role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 20px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--mdi"><path fill="currentColor" d="M16.59 5.59L18 7l-6 6l-6-6l1.41-1.41L12 10.17zm0 6L18 13l-6 6l-6-6l1.41-1.41L12 16.17z"></path></svg></i></div><div data-v-c7cd6387="" class="scroll-to-top-btn"><i data-v-c7cd6387="" role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 20px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--mdi"><path fill="currentColor" d="M7.41 18.41L6 17l6-6l6 6l-1.41 1.41L12 13.83zm0-6L6 11l6-6l6 6l-1.41 1.41L12 7.83z"></path></svg></i></div><div data-v-c7cd6387="" class="table-container" style="min-height: calc(-220px + 100vh); max-height: calc(-150px + 100vh); overflow: auto; position: relative;"><div data-v-c7cd6387="" class="top-pagination-table crud-table-container top-pagination-table" style="width: 100%; height: 100%;"><div data-v-d24e232f="" class="query-bar-container crud-table-query-bar" bg="#fafafc" min-h-60="" flex="" items-start="" justify-between="" b-1="" rounded-8="" p-15="" bc-ccc="" dark:bg-black="" mb-30="" style="position: relative !important; padding-bottom: 15px !important; min-height: 70px !important;"><div data-v-d24e232f="" role="none" class="n-space" style="display: flex; flex-flow: wrap; justify-content: flex-start; gap: 15px 35px; width: 100%;"><div role="none" style="max-width: 100%;"><div data-v-a5b41f3d="" data-v-c7cd6387="" class="query-bar-item" flex="" items-center=""><label data-v-a5b41f3d="" class="query-bar-item-label" flex-shrink-0="" style="width: 80px;">项目名称</label><div data-v-a5b41f3d="" class="query-bar-item-content" style="min-width: 220px;"><div data-v-c7cd6387="" class="n-tree-select" value-field="value" style="width: 200px;"><div class="n-base-selection" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-border: 1px solid rgb(224, 224, 230); --n-border-active: 1px solid #0EA5E9; --n-border-focus: 1px solid #38BDF8; --n-border-hover: 1px solid #38BDF8; --n-border-radius: 3px; --n-box-shadow-active: 0 0 0 2px rgba(14, 165, 233, 0.2); --n-box-shadow-focus: 0 0 0 2px rgba(14, 165, 233, 0.2); --n-box-shadow-hover: none; --n-caret-color: #0EA5E9; --n-color: rgba(255, 255, 255, 1); --n-color-active: rgba(255, 255, 255, 1); --n-color-disabled: rgb(250, 250, 252); --n-font-size: 14px; --n-height: 34px; --n-padding-single-top: 0; --n-padding-multiple-top: 3px; --n-padding-single-right: 26px; --n-padding-multiple-right: 26px; --n-padding-single-left: 12px; --n-padding-multiple-left: 12px; --n-padding-single-bottom: 0; --n-padding-multiple-bottom: 0; --n-placeholder-color: rgba(194, 194, 194, 1); --n-placeholder-color-disabled: rgba(209, 209, 209, 1); --n-text-color: rgb(51, 54, 57); --n-text-color-disabled: rgba(194, 194, 194, 1); --n-arrow-color: rgba(194, 194, 194, 1); --n-arrow-color-disabled: rgba(209, 209, 209, 1); --n-loading-color: #0EA5E9; --n-color-active-warning: rgba(255, 255, 255, 1); --n-box-shadow-focus-warning: 0 0 0 2px rgba(245, 158, 11, 0.2); --n-box-shadow-active-warning: 0 0 0 2px rgba(245, 158, 11, 0.2); --n-box-shadow-hover-warning: none; --n-border-warning: 1px solid #F59E0B; --n-border-focus-warning: 1px solid #FBBF24; --n-border-hover-warning: 1px solid #FBBF24; --n-border-active-warning: 1px solid #F59E0B; --n-color-active-error: rgba(255, 255, 255, 1); --n-box-shadow-focus-error: 0 0 0 2px rgba(239, 68, 68, 0.2); --n-box-shadow-active-error: 0 0 0 2px rgba(239, 68, 68, 0.2); --n-box-shadow-hover-error: none; --n-border-error: 1px solid #EF4444; --n-border-focus-error: 1px solid #F87171; --n-border-hover-error: 1px solid #F87171; --n-border-active-error: 1px solid #EF4444; --n-clear-size: 16px; --n-clear-color: rgba(194, 194, 194, 1); --n-clear-color-hover: rgba(146, 146, 146, 1); --n-clear-color-pressed: rgba(175, 175, 175, 1); --n-arrow-size: 16px; --n-font-weight: 400;"><div class="n-base-selection-label" tabindex="0"><div class="n-base-selection-placeholder n-base-selection-overlay"><div class="n-base-selection-placeholder__inner">请选择项目</div></div><div class="n-base-loading n-base-suffix" role="img" aria-label="loading"><div class="n-base-loading__placeholder"><div class="n-base-clear"><div class="n-base-clear__placeholder"><i class="n-base-icon n-base-suffix__arrow"><svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.14645 5.64645C3.34171 5.45118 3.65829 5.45118 3.85355 5.64645L8 9.79289L12.1464 5.64645C12.3417 5.45118 12.6583 5.45118 12.8536 5.64645C13.0488 5.84171 13.0488 6.15829 12.8536 6.35355L8.35355 10.8536C8.15829 11.0488 7.84171 11.0488 7.64645 10.8536L3.14645 6.35355C2.95118 6.15829 2.95118 5.84171 3.14645 5.64645Z" fill="currentColor"></path></svg></i></div></div></div></div></div><div class="n-base-selection__border"></div><div class="n-base-selection__state-border"></div></div><!----></div></div></div></div><div role="none" style="max-width: 100%;"><div data-v-a5b41f3d="" data-v-c7cd6387="" class="query-bar-item" flex="" items-center=""><label data-v-a5b41f3d="" class="query-bar-item-label" flex-shrink-0="" style="width: 80px;">需求名称</label><div data-v-a5b41f3d="" class="query-bar-item-content" style="min-width: 220px;"><div data-v-c7cd6387="" class="n-input n-input--resizable n-input--stateful" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-count-text-color: rgb(118, 124, 130); --n-count-text-color-disabled: rgba(194, 194, 194, 1); --n-color: rgba(255, 255, 255, 1); --n-font-size: 14px; --n-font-weight: 400; --n-border-radius: 3px; --n-height: 34px; --n-padding-left: 12px; --n-padding-right: 12px; --n-text-color: rgb(51, 54, 57); --n-caret-color: #0EA5E9; --n-text-decoration-color: rgb(51, 54, 57); --n-border: 1px solid rgb(224, 224, 230); --n-border-disabled: 1px solid rgb(224, 224, 230); --n-border-hover: 1px solid #38BDF8; --n-border-focus: 1px solid #38BDF8; --n-placeholder-color: rgba(194, 194, 194, 1); --n-placeholder-color-disabled: rgba(209, 209, 209, 1); --n-icon-size: 16px; --n-line-height-textarea: 1.6; --n-color-disabled: rgb(250, 250, 252); --n-color-focus: rgba(255, 255, 255, 1); --n-text-color-disabled: rgba(194, 194, 194, 1); --n-box-shadow-focus: 0 0 0 2px rgba(14, 165, 233, 0.2); --n-loading-color: #0EA5E9; --n-caret-color-warning: #F59E0B; --n-color-focus-warning: rgba(255, 255, 255, 1); --n-box-shadow-focus-warning: 0 0 0 2px rgba(245, 158, 11, 0.2); --n-border-warning: 1px solid #F59E0B; --n-border-focus-warning: 1px solid #FBBF24; --n-border-hover-warning: 1px solid #FBBF24; --n-loading-color-warning: #F59E0B; --n-caret-color-error: #EF4444; --n-color-focus-error: rgba(255, 255, 255, 1); --n-box-shadow-focus-error: 0 0 0 2px rgba(239, 68, 68, 0.2); --n-border-error: 1px solid #EF4444; --n-border-focus-error: 1px solid #F87171; --n-border-hover-error: 1px solid #F87171; --n-loading-color-error: #EF4444; --n-clear-color: rgba(194, 194, 194, 1); --n-clear-size: 16px; --n-clear-color-hover: rgba(146, 146, 146, 1); --n-clear-color-pressed: rgba(175, 175, 175, 1); --n-icon-color: rgba(194, 194, 194, 1); --n-icon-color-hover: rgba(146, 146, 146, 1); --n-icon-color-pressed: rgba(175, 175, 175, 1); --n-icon-color-disabled: rgba(209, 209, 209, 1); --n-suffix-text-color: rgb(51, 54, 57); width: 200px;"><div class="n-input-wrapper"><!----><div class="n-input__input"><input type="text" class="n-input__input-el" placeholder="请输入需求名称或TAPD链接" size="20"><div class="n-input__placeholder"><span>请输入需求名称或TAPD链接</span></div><!----></div><div class="n-input__suffix"><div class="n-base-clear"><div class="n-base-clear__placeholder"><!----></div></div><!----><!----><!----><!----><!----></div></div><!----><!----><div class="n-input__border"></div><div class="n-input__state-border"></div><!----></div></div></div></div><div role="none" style="max-width: 100%;"><div data-v-a5b41f3d="" data-v-c7cd6387="" class="query-bar-item" flex="" items-center=""><label data-v-a5b41f3d="" class="query-bar-item-label" flex-shrink-0="" style="width: 80px;">创建者</label><div data-v-a5b41f3d="" class="query-bar-item-content" style="min-width: 220px;"><div data-v-c7cd6387="" class="n-input n-input--resizable n-input--stateful" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-count-text-color: rgb(118, 124, 130); --n-count-text-color-disabled: rgba(194, 194, 194, 1); --n-color: rgba(255, 255, 255, 1); --n-font-size: 14px; --n-font-weight: 400; --n-border-radius: 3px; --n-height: 34px; --n-padding-left: 12px; --n-padding-right: 12px; --n-text-color: rgb(51, 54, 57); --n-caret-color: #0EA5E9; --n-text-decoration-color: rgb(51, 54, 57); --n-border: 1px solid rgb(224, 224, 230); --n-border-disabled: 1px solid rgb(224, 224, 230); --n-border-hover: 1px solid #38BDF8; --n-border-focus: 1px solid #38BDF8; --n-placeholder-color: rgba(194, 194, 194, 1); --n-placeholder-color-disabled: rgba(209, 209, 209, 1); --n-icon-size: 16px; --n-line-height-textarea: 1.6; --n-color-disabled: rgb(250, 250, 252); --n-color-focus: rgba(255, 255, 255, 1); --n-text-color-disabled: rgba(194, 194, 194, 1); --n-box-shadow-focus: 0 0 0 2px rgba(14, 165, 233, 0.2); --n-loading-color: #0EA5E9; --n-caret-color-warning: #F59E0B; --n-color-focus-warning: rgba(255, 255, 255, 1); --n-box-shadow-focus-warning: 0 0 0 2px rgba(245, 158, 11, 0.2); --n-border-warning: 1px solid #F59E0B; --n-border-focus-warning: 1px solid #FBBF24; --n-border-hover-warning: 1px solid #FBBF24; --n-loading-color-warning: #F59E0B; --n-caret-color-error: #EF4444; --n-color-focus-error: rgba(255, 255, 255, 1); --n-box-shadow-focus-error: 0 0 0 2px rgba(239, 68, 68, 0.2); --n-border-error: 1px solid #EF4444; --n-border-focus-error: 1px solid #F87171; --n-border-hover-error: 1px solid #F87171; --n-loading-color-error: #EF4444; --n-clear-color: rgba(194, 194, 194, 1); --n-clear-size: 16px; --n-clear-color-hover: rgba(146, 146, 146, 1); --n-clear-color-pressed: rgba(175, 175, 175, 1); --n-icon-color: rgba(194, 194, 194, 1); --n-icon-color-hover: rgba(146, 146, 146, 1); --n-icon-color-pressed: rgba(175, 175, 175, 1); --n-icon-color-disabled: rgba(209, 209, 209, 1); --n-suffix-text-color: rgb(51, 54, 57); width: 200px;"><div class="n-input-wrapper"><!----><div class="n-input__input"><input type="text" class="n-input__input-el" placeholder="请输入创建者名称" size="20"><div class="n-input__placeholder"><span>请输入创建者名称</span></div><!----></div><div class="n-input__suffix"><div class="n-base-clear"><div class="n-base-clear__placeholder"><!----></div></div><!----><!----><!----><!----><!----></div></div><!----><!----><div class="n-input__border"></div><div class="n-input__state-border"></div><!----></div></div></div></div><div role="none" style="max-width: 100%;"><div data-v-d24e232f=""><button data-v-d24e232f="" class="n-button n-button--primary-type n-button--medium-type n-button--secondary" tabindex="0" type="button" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: rgba(14, 165, 233, 0.16); --n-color-hover: rgba(14, 165, 233, 0.22); --n-color-pressed: rgba(14, 165, 233, 0.28); --n-color-focus: rgba(14, 165, 233, 0.22); --n-color-disabled: rgba(46, 51, 56, .05); --n-ripple-color: #0000; --n-text-color: #0EA5E9; --n-text-color-hover: #0EA5E9; --n-text-color-pressed: #0EA5E9; --n-text-color-focus: #0EA5E9; --n-text-color-disabled: #0EA5E9; --n-border: 1px solid #0EA5E9; --n-border-hover: 1px solid #38BDF8; --n-border-pressed: 1px solid #0284C7; --n-border-focus: 1px solid #38BDF8; --n-border-disabled: 1px solid #0EA5E9; --n-width: initial; --n-height: 34px; --n-font-size: 14px; --n-padding: 0 14px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px;"><!----><!----><span class="n-button__content">重置</span><div aria-hidden="true" class="n-base-wave"></div><!----><!----></button><button data-v-d24e232f="" class="n-button n-button--primary-type n-button--medium-type" tabindex="0" type="button" ml-20="" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: #0EA5E9; --n-color-hover: #38BDF8; --n-color-pressed: #0284C7; --n-color-focus: #38BDF8; --n-color-disabled: #0EA5E9; --n-ripple-color: #0EA5E9; --n-text-color: #FFF; --n-text-color-hover: #FFF; --n-text-color-pressed: #FFF; --n-text-color-focus: #FFF; --n-text-color-disabled: #FFF; --n-border: 1px solid #0EA5E9; --n-border-hover: 1px solid #38BDF8; --n-border-pressed: 1px solid #0284C7; --n-border-focus: 1px solid #38BDF8; --n-border-disabled: 1px solid #0EA5E9; --n-width: initial; --n-height: 34px; --n-font-size: 14px; --n-padding: 0 14px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px;"><!----><!----><span class="n-button__content">搜索</span><div aria-hidden="true" class="n-base-wave"></div><div aria-hidden="true" class="n-button__border"></div><div aria-hidden="true" class="n-button__state-border"></div></button></div></div></div></div><div class="n-data-table n-data-table--bordered n-data-table--single-line" scroll-y="800" style="--n-font-size: 14px; --n-th-padding: 12px; --n-td-padding: 12px; --n-bezier: cubic-bezier(.4, 0, .2, 1); --n-border-radius: 3px; --n-line-height: 1.6; --n-border-color: rgba(239, 239, 245, 1); --n-border-color-modal: rgba(239, 239, 245, 1); --n-border-color-popover: rgba(239, 239, 245, 1); --n-th-color: rgba(250, 250, 252, 1); --n-th-color-hover: rgba(243, 243, 247, 1); --n-th-color-modal: rgba(250, 250, 252, 1); --n-th-color-hover-modal: rgba(243, 243, 247, 1); --n-th-color-popover: rgba(250, 250, 252, 1); --n-th-color-hover-popover: rgba(243, 243, 247, 1); --n-td-color: #fff; --n-td-color-hover: rgba(247, 247, 250, 1); --n-td-color-modal: #fff; --n-td-color-hover-modal: rgba(247, 247, 250, 1); --n-td-color-popover: #fff; --n-td-color-hover-popover: rgba(247, 247, 250, 1); --n-th-text-color: rgb(31, 34, 37); --n-td-text-color: rgb(51, 54, 57); --n-th-font-weight: 500; --n-th-button-color-hover: rgba(0, 0, 100, 0.03); --n-th-icon-color: rgba(194, 194, 194, 1); --n-th-icon-color-active: #0EA5E9; --n-filter-size: 15px; --n-pagination-margin: 12px 0 0 0; --n-empty-padding: 48px 0; --n-box-shadow-before: inset -12px 0 8px -12px rgba(0, 0, 0, .18); --n-box-shadow-after: inset 12px 0 8px -12px rgba(0, 0, 0, .18); --n-sorter-size: 15px; --n-resizable-container-size: 8px; --n-resizable-size: 2px; --n-loading-size: 28px; --n-loading-color: #0EA5E9; --n-opacity-loading: 0.5; --n-td-color-striped: rgba(250, 250, 252, 1); --n-td-color-striped-modal: rgba(250, 250, 252, 1); --n-td-color-striped-popover: rgba(250, 250, 252, 1);"><div class="n-data-table-wrapper"><div class="n-data-table-base-table"><!----><div class="n-data-table-base-table-body n-scrollbar" role="none" style="--n-scrollbar-bezier: cubic-bezier(.4, 0, .2, 1); --n-scrollbar-color: rgba(0, 0, 0, 0.25); --n-scrollbar-color-hover: rgba(0, 0, 0, 0.4); --n-scrollbar-border-radius: 5px; --n-scrollbar-width: 5px; --n-scrollbar-height: 5px; --n-scrollbar-rail-top-horizontal-top: 4px; --n-scrollbar-rail-right-horizontal-top: 2px; --n-scrollbar-rail-bottom-horizontal-top: auto; --n-scrollbar-rail-left-horizontal-top: 2px; --n-scrollbar-rail-top-horizontal-bottom: auto; --n-scrollbar-rail-right-horizontal-bottom: 2px; --n-scrollbar-rail-bottom-horizontal-bottom: 4px; --n-scrollbar-rail-left-horizontal-bottom: 2px; --n-scrollbar-rail-top-vertical-right: 2px; --n-scrollbar-rail-right-vertical-right: 4px; --n-scrollbar-rail-bottom-vertical-right: 2px; --n-scrollbar-rail-left-vertical-right: auto; --n-scrollbar-rail-top-vertical-left: 2px; --n-scrollbar-rail-right-vertical-left: auto; --n-scrollbar-rail-bottom-vertical-left: 2px; --n-scrollbar-rail-left-vertical-left: 4px; --n-scrollbar-rail-color: transparent;"><div role="none" class="n-scrollbar-container"><div role="none" class="n-scrollbar-content" style="width: 100%; min-width: 1500px;"><table class="n-data-table-table" style="table-layout: fixed;"><colgroup><col style="width: auto; min-width: auto;"><col style="width: auto; min-width: auto;"><col style="width: auto; min-width: auto;"><col style="width: auto; min-width: auto;"><col style="width: auto; min-width: auto;"><col style="width: auto; min-width: auto;"><col style="width: auto; min-width: auto;"><col style="width: auto; min-width: auto;"><col style="width: auto; min-width: auto;"><col style="width: auto; min-width: auto;"><col style="width: auto; min-width: auto;"><col style="width: auto; min-width: auto;"><col style="width: auto; min-width: auto;"><col style="width: auto; min-width: auto;"><col style="width: auto; min-width: auto;"><col style="width: auto; min-width: auto;"></colgroup><thead class="n-data-table-thead" data-n-id="18d07d4e"><tr class="n-data-table-tr"><th colspan="1" rowspan="1" data-col-key="id" class="n-data-table-th" style="text-align: center;"><div class="n-data-table-th__title-wrapper"><div class="n-data-table-th__title"><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>用例ID</span></span><!----></div><!----></div><!----><span data-data-table-resizable="true" class="n-data-table-resize-button"></span></th><th colspan="1" rowspan="1" data-col-key="title" class="n-data-table-th" style="text-align: center;"><div class="n-data-table-th__title-wrapper"><div class="n-data-table-th__title"><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>用例标题</span></span><!----></div><!----></div><!----><span data-data-table-resizable="true" class="n-data-table-resize-button"></span></th><th colspan="1" rowspan="1" data-col-key="desc" class="n-data-table-th" style="text-align: center;"><div class="n-data-table-th__title-wrapper"><div class="n-data-table-th__title"><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>用例描述</span></span><!----></div><!----></div><!----><span data-data-table-resizable="true" class="n-data-table-resize-button"></span></th><th colspan="1" rowspan="1" data-col-key="preconditions" class="n-data-table-th" style="text-align: center;"><div class="n-data-table-th__title-wrapper"><div class="n-data-table-th__title"><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>前置条件</span></span><!----></div><!----></div><!----><span data-data-table-resizable="true" class="n-data-table-resize-button"></span></th><th colspan="1" rowspan="1" data-col-key="steps" class="n-data-table-th" style="text-align: center;"><div class="n-data-table-th__title-wrapper"><div class="n-data-table-th__title"><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>用例步骤</span></span><!----></div><!----></div><!----><span data-data-table-resizable="true" class="n-data-table-resize-button"></span></th><th colspan="1" rowspan="1" data-col-key="expected_results" class="n-data-table-th" style="text-align: center;"><div class="n-data-table-th__title-wrapper"><div class="n-data-table-th__title"><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>预期结果</span></span><!----></div><!----></div><!----><span data-data-table-resizable="true" class="n-data-table-resize-button"></span></th><th colspan="1" rowspan="1" data-col-key="postconditions" class="n-data-table-th" style="text-align: center;"><div class="n-data-table-th__title-wrapper"><div class="n-data-table-th__title"><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>后置条件</span></span><!----></div><!----></div><!----><span data-data-table-resizable="true" class="n-data-table-resize-button"></span></th><th colspan="1" rowspan="1" data-col-key="priority" class="n-data-table-th" style="text-align: center;"><div class="n-data-table-th__title-wrapper"><div class="n-data-table-th__title"><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>优先级</span></span><!----></div><!----></div><!----><span data-data-table-resizable="true" class="n-data-table-resize-button"></span></th><th colspan="1" rowspan="1" data-col-key="status" class="n-data-table-th" style="text-align: center;"><div class="n-data-table-th__title-wrapper"><div class="n-data-table-th__title"><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>用例状态</span></span><!----></div><!----></div><!----><span data-data-table-resizable="true" class="n-data-table-resize-button"></span></th><th colspan="1" rowspan="1" data-col-key="tags" class="n-data-table-th" style="text-align: center;"><div class="n-data-table-th__title-wrapper"><div class="n-data-table-th__title"><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>用例标签</span></span><!----></div><!----></div><!----><span data-data-table-resizable="true" class="n-data-table-resize-button"></span></th><th colspan="1" rowspan="1" data-col-key="requirement" class="n-data-table-th" style="text-align: center;"><div class="n-data-table-th__title-wrapper"><div class="n-data-table-th__title"><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>关联需求</span></span><!----></div><!----></div><!----><span data-data-table-resizable="true" class="n-data-table-resize-button"></span></th><th colspan="1" rowspan="1" data-col-key="tapd_url" class="n-data-table-th" style="text-align: center;"><div class="n-data-table-th__title-wrapper"><div class="n-data-table-th__title"><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>TAPD需求链接</span></span><!----></div><!----></div><!----><span data-data-table-resizable="true" class="n-data-table-resize-button"></span></th><th colspan="1" rowspan="1" data-col-key="project" class="n-data-table-th" style="text-align: center;"><div class="n-data-table-th__title-wrapper"><div class="n-data-table-th__title"><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>关联项目</span></span><!----></div><!----></div><!----><span data-data-table-resizable="true" class="n-data-table-resize-button"></span></th><th colspan="1" rowspan="1" data-col-key="creator" class="n-data-table-th" style="text-align: center;"><div class="n-data-table-th__title-wrapper"><div class="n-data-table-th__title"><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>创建者</span></span><!----></div><!----></div><!----><span data-data-table-resizable="true" class="n-data-table-resize-button"></span></th><th colspan="1" rowspan="1" data-col-key="created_at" class="n-data-table-th" style="text-align: center;"><div class="n-data-table-th__title-wrapper"><div class="n-data-table-th__title"><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>创建时间</span></span><!----></div><!----></div><!----><span data-data-table-resizable="true" class="n-data-table-resize-button"></span></th><th colspan="1" rowspan="1" data-col-key="actions" class="n-data-table-th n-data-table-th--fixed-right n-data-table-th--last" style="right: 0px; text-align: center;"><div class="n-data-table-th__title-wrapper"><div class="n-data-table-th__title">操作</div><!----></div><!----><span data-data-table-resizable="true" class="n-data-table-resize-button"></span></th></tr></thead><tbody data-n-id="18d07d4e" class="n-data-table-tbody"><tr class="n-data-table-tr"><td colspan="1" rowspan="1" data-col-key="id" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><a title="点击编辑用例" style="color: rgb(32, 128, 240); cursor: pointer; text-decoration: underline; font-weight: 500; transition: 0.2s;">1</a></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="title" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>验证高血压既往史下拉选项完整显示</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="desc" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>验证高血压病种入组时既往史下拉选项是否完整显示16个疾病选项+其他项</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="preconditions" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>已登录医生账号
已选择高血压病种入组
进入病历填写页面</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="steps" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div></div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="expected_results" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div></div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="postconditions" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>无</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="priority" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><span>高</span></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="status" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><span>进行中</span></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="tags" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>功能测试</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="requirement" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>高血压既往史下拉选项修改</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="tapd_url" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><a href="https://www.tapd.cn/tapd_fe/22012671/story/detail/1122012671001005124?menu_workitem_type_id=0" target="_blank" style="color: rgb(32, 128, 240); text-decoration: underline;">TAPD需求链接</a></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="project" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>共同照护web</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="creator" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>朱海华</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="created_at" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>2025-05-25 11:52:29</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="actions" class="n-data-table-td n-data-table-td--fixed-right n-data-table-td--center-align n-data-table-td--last-col" style="text-align: center; right: 0px;"><!----><button class="n-button n-button--primary-type n-button--small-type" tabindex="0" type="button" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: #0EA5E9; --n-color-hover: #38BDF8; --n-color-pressed: #0284C7; --n-color-focus: #38BDF8; --n-color-disabled: #0EA5E9; --n-ripple-color: #0EA5E9; --n-text-color: #FFF; --n-text-color-hover: #FFF; --n-text-color-pressed: #FFF; --n-text-color-focus: #FFF; --n-text-color-disabled: #FFF; --n-border: 1px solid #0EA5E9; --n-border-hover: 1px solid #38BDF8; --n-border-pressed: 1px solid #0284C7; --n-border-focus: 1px solid #38BDF8; --n-border-disabled: 1px solid #0EA5E9; --n-width: initial; --n-height: 28px; --n-font-size: 14px; --n-padding: 0 10px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px; margin-left: 8px;"><!----><span class="n-button__icon"><div class="n-icon-slot" role="none"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 16px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="M3 21v-4.25L16.2 3.575q.3-.275.663-.425t.762-.15t.775.15t.65.45L20.425 5q.3.275.438.65T21 6.4q0 .4-.137.763t-.438.662L7.25 21zM17.6 7.8L19 6.4L17.6 5l-1.4 1.4z"></path></svg></i></div></span><span class="n-button__content">编辑</span><div aria-hidden="true" class="n-base-wave"></div><div aria-hidden="true" class="n-button__border"></div><div aria-hidden="true" class="n-button__state-border"></div></button><!----><button class="n-button n-button--error-type n-button--small-type" tabindex="0" type="button" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: #EF4444; --n-color-hover: #F87171; --n-color-pressed: #DC2626; --n-color-focus: #F87171; --n-color-disabled: #EF4444; --n-ripple-color: #EF4444; --n-text-color: #FFF; --n-text-color-hover: #FFF; --n-text-color-pressed: #FFF; --n-text-color-focus: #FFF; --n-text-color-disabled: #FFF; --n-border: 1px solid #EF4444; --n-border-hover: 1px solid #F87171; --n-border-pressed: 1px solid #DC2626; --n-border-focus: 1px solid #F87171; --n-border-disabled: 1px solid #EF4444; --n-width: initial; --n-height: 28px; --n-font-size: 14px; --n-padding: 0 10px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px; margin-left: 8px;"><!----><span class="n-button__icon"><div class="n-icon-slot" role="none"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 16px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="M7 21q-.825 0-1.412-.587T5 19V6H4V4h5V3h6v1h5v2h-1v13q0 .825-.587 1.413T17 21zM17 6H7v13h10zM9 17h2V8H9zm4 0h2V8h-2zM7 6v13z"></path></svg></i></div></span><span class="n-button__content">删除</span><div aria-hidden="true" class="n-base-wave"></div><div aria-hidden="true" class="n-button__border"></div><div aria-hidden="true" class="n-button__state-border"></div></button><!----></td></tr><tr class="n-data-table-tr"><td colspan="1" rowspan="1" data-col-key="id" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><a title="点击编辑用例" style="color: rgb(32, 128, 240); cursor: pointer; text-decoration: underline; font-weight: 500; transition: 0.2s;">2</a></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="title" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>验证高血压既往史与现病史选项一致性</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="desc" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>验证高血压病种入组时既往史下拉选项与现病史选项完全一致</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="preconditions" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>已登录医生账号
已选择高血压病种入组
进入病历填写页面</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="steps" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div></div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="expected_results" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div></div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="postconditions" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>无</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="priority" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><span>高</span></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="status" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><span>通过</span></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="tags" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>功能测试</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="requirement" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>高血压既往史下拉选项修改</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="tapd_url" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><a href="https://www.tapd.cn/tapd_fe/22012671/story/detail/1122012671001005124?menu_workitem_type_id=0" target="_blank" style="color: rgb(32, 128, 240); text-decoration: underline;">TAPD需求链接</a></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="project" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>共同照护web</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="creator" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>朱海华</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="created_at" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>2025-05-25 11:52:29</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="actions" class="n-data-table-td n-data-table-td--fixed-right n-data-table-td--center-align n-data-table-td--last-col" style="text-align: center; right: 0px;"><!----><button class="n-button n-button--primary-type n-button--small-type" tabindex="0" type="button" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: #0EA5E9; --n-color-hover: #38BDF8; --n-color-pressed: #0284C7; --n-color-focus: #38BDF8; --n-color-disabled: #0EA5E9; --n-ripple-color: #0EA5E9; --n-text-color: #FFF; --n-text-color-hover: #FFF; --n-text-color-pressed: #FFF; --n-text-color-focus: #FFF; --n-text-color-disabled: #FFF; --n-border: 1px solid #0EA5E9; --n-border-hover: 1px solid #38BDF8; --n-border-pressed: 1px solid #0284C7; --n-border-focus: 1px solid #38BDF8; --n-border-disabled: 1px solid #0EA5E9; --n-width: initial; --n-height: 28px; --n-font-size: 14px; --n-padding: 0 10px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px; margin-left: 8px;"><!----><span class="n-button__icon"><div class="n-icon-slot" role="none"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 16px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="M3 21v-4.25L16.2 3.575q.3-.275.663-.425t.762-.15t.775.15t.65.45L20.425 5q.3.275.438.65T21 6.4q0 .4-.137.763t-.438.662L7.25 21zM17.6 7.8L19 6.4L17.6 5l-1.4 1.4z"></path></svg></i></div></span><span class="n-button__content">编辑</span><div aria-hidden="true" class="n-base-wave"></div><div aria-hidden="true" class="n-button__border"></div><div aria-hidden="true" class="n-button__state-border"></div></button><!----><button class="n-button n-button--error-type n-button--small-type" tabindex="0" type="button" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: #EF4444; --n-color-hover: #F87171; --n-color-pressed: #DC2626; --n-color-focus: #F87171; --n-color-disabled: #EF4444; --n-ripple-color: #EF4444; --n-text-color: #FFF; --n-text-color-hover: #FFF; --n-text-color-pressed: #FFF; --n-text-color-focus: #FFF; --n-text-color-disabled: #FFF; --n-border: 1px solid #EF4444; --n-border-hover: 1px solid #F87171; --n-border-pressed: 1px solid #DC2626; --n-border-focus: 1px solid #F87171; --n-border-disabled: 1px solid #EF4444; --n-width: initial; --n-height: 28px; --n-font-size: 14px; --n-padding: 0 10px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px; margin-left: 8px;"><!----><span class="n-button__icon"><div class="n-icon-slot" role="none"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 16px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="M7 21q-.825 0-1.412-.587T5 19V6H4V4h5V3h6v1h5v2h-1v13q0 .825-.587 1.413T17 21zM17 6H7v13h10zM9 17h2V8H9zm4 0h2V8h-2zM7 6v13z"></path></svg></i></div></span><span class="n-button__content">删除</span><div aria-hidden="true" class="n-base-wave"></div><div aria-hidden="true" class="n-button__border"></div><div aria-hidden="true" class="n-button__state-border"></div></button><!----></td></tr><tr class="n-data-table-tr"><td colspan="1" rowspan="1" data-col-key="id" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><a title="点击编辑用例" style="color: rgb(32, 128, 240); cursor: pointer; text-decoration: underline; font-weight: 500; transition: 0.2s;">3</a></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="title" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>验证高血压既往史多选项选择功能</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="desc" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>验证可以同时选择多个高血压既往史选项</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="preconditions" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>已登录医生账号
已选择高血压病种入组
进入病历填写页面</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="steps" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div></div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="expected_results" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div></div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="postconditions" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>无</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="priority" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><span>中</span></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="status" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><span>通过</span></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="tags" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>功能测试</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="requirement" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>高血压既往史下拉选项修改</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="tapd_url" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><a href="https://www.tapd.cn/tapd_fe/22012671/story/detail/1122012671001005124?menu_workitem_type_id=0" target="_blank" style="color: rgb(32, 128, 240); text-decoration: underline;">TAPD需求链接</a></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="project" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>共同照护web</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="creator" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>朱海华</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="created_at" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>2025-05-25 11:52:29</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="actions" class="n-data-table-td n-data-table-td--fixed-right n-data-table-td--center-align n-data-table-td--last-col" style="text-align: center; right: 0px;"><!----><button class="n-button n-button--primary-type n-button--small-type" tabindex="0" type="button" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: #0EA5E9; --n-color-hover: #38BDF8; --n-color-pressed: #0284C7; --n-color-focus: #38BDF8; --n-color-disabled: #0EA5E9; --n-ripple-color: #0EA5E9; --n-text-color: #FFF; --n-text-color-hover: #FFF; --n-text-color-pressed: #FFF; --n-text-color-focus: #FFF; --n-text-color-disabled: #FFF; --n-border: 1px solid #0EA5E9; --n-border-hover: 1px solid #38BDF8; --n-border-pressed: 1px solid #0284C7; --n-border-focus: 1px solid #38BDF8; --n-border-disabled: 1px solid #0EA5E9; --n-width: initial; --n-height: 28px; --n-font-size: 14px; --n-padding: 0 10px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px; margin-left: 8px;"><!----><span class="n-button__icon"><div class="n-icon-slot" role="none"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 16px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="M3 21v-4.25L16.2 3.575q.3-.275.663-.425t.762-.15t.775.15t.65.45L20.425 5q.3.275.438.65T21 6.4q0 .4-.137.763t-.438.662L7.25 21zM17.6 7.8L19 6.4L17.6 5l-1.4 1.4z"></path></svg></i></div></span><span class="n-button__content">编辑</span><div aria-hidden="true" class="n-base-wave"></div><div aria-hidden="true" class="n-button__border"></div><div aria-hidden="true" class="n-button__state-border"></div></button><!----><button class="n-button n-button--error-type n-button--small-type" tabindex="0" type="button" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: #EF4444; --n-color-hover: #F87171; --n-color-pressed: #DC2626; --n-color-focus: #F87171; --n-color-disabled: #EF4444; --n-ripple-color: #EF4444; --n-text-color: #FFF; --n-text-color-hover: #FFF; --n-text-color-pressed: #FFF; --n-text-color-focus: #FFF; --n-text-color-disabled: #FFF; --n-border: 1px solid #EF4444; --n-border-hover: 1px solid #F87171; --n-border-pressed: 1px solid #DC2626; --n-border-focus: 1px solid #F87171; --n-border-disabled: 1px solid #EF4444; --n-width: initial; --n-height: 28px; --n-font-size: 14px; --n-padding: 0 10px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px; margin-left: 8px;"><!----><span class="n-button__icon"><div class="n-icon-slot" role="none"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 16px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="M7 21q-.825 0-1.412-.587T5 19V6H4V4h5V3h6v1h5v2h-1v13q0 .825-.587 1.413T17 21zM17 6H7v13h10zM9 17h2V8H9zm4 0h2V8h-2zM7 6v13z"></path></svg></i></div></span><span class="n-button__content">删除</span><div aria-hidden="true" class="n-base-wave"></div><div aria-hidden="true" class="n-button__border"></div><div aria-hidden="true" class="n-button__state-border"></div></button><!----></td></tr><tr class="n-data-table-tr"><td colspan="1" rowspan="1" data-col-key="id" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><a title="点击编辑用例" style="color: rgb(32, 128, 240); cursor: pointer; text-decoration: underline; font-weight: 500; transition: 0.2s;">8</a></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="title" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>验证并发修改高血压既往史选项</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="desc" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>验证多终端同时修改既往史选项的数据一致性</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="preconditions" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>已登录两个医生账号
已选择高血压病种入组
进入病历填写页面</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="steps" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div></div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="expected_results" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div></div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="postconditions" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>无</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="priority" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><span>中</span></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="status" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><span>通过</span></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="tags" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>并发测试</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="requirement" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>高血压既往史下拉选项修改</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="tapd_url" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><a href="https://www.tapd.cn/tapd_fe/22012671/story/detail/1122012671001005124?menu_workitem_type_id=0" target="_blank" style="color: rgb(32, 128, 240); text-decoration: underline;">TAPD需求链接</a></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="project" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>共同照护web</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="creator" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>朱海华</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="created_at" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>2025-05-25 11:52:29</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="actions" class="n-data-table-td n-data-table-td--fixed-right n-data-table-td--center-align n-data-table-td--last-col" style="text-align: center; right: 0px;"><!----><button class="n-button n-button--primary-type n-button--small-type" tabindex="0" type="button" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: #0EA5E9; --n-color-hover: #38BDF8; --n-color-pressed: #0284C7; --n-color-focus: #38BDF8; --n-color-disabled: #0EA5E9; --n-ripple-color: #0EA5E9; --n-text-color: #FFF; --n-text-color-hover: #FFF; --n-text-color-pressed: #FFF; --n-text-color-focus: #FFF; --n-text-color-disabled: #FFF; --n-border: 1px solid #0EA5E9; --n-border-hover: 1px solid #38BDF8; --n-border-pressed: 1px solid #0284C7; --n-border-focus: 1px solid #38BDF8; --n-border-disabled: 1px solid #0EA5E9; --n-width: initial; --n-height: 28px; --n-font-size: 14px; --n-padding: 0 10px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px; margin-left: 8px;"><!----><span class="n-button__icon"><div class="n-icon-slot" role="none"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 16px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="M3 21v-4.25L16.2 3.575q.3-.275.663-.425t.762-.15t.775.15t.65.45L20.425 5q.3.275.438.65T21 6.4q0 .4-.137.763t-.438.662L7.25 21zM17.6 7.8L19 6.4L17.6 5l-1.4 1.4z"></path></svg></i></div></span><span class="n-button__content">编辑</span><div aria-hidden="true" class="n-base-wave"></div><div aria-hidden="true" class="n-button__border"></div><div aria-hidden="true" class="n-button__state-border"></div></button><!----><button class="n-button n-button--error-type n-button--small-type" tabindex="0" type="button" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: #EF4444; --n-color-hover: #F87171; --n-color-pressed: #DC2626; --n-color-focus: #F87171; --n-color-disabled: #EF4444; --n-ripple-color: #EF4444; --n-text-color: #FFF; --n-text-color-hover: #FFF; --n-text-color-pressed: #FFF; --n-text-color-focus: #FFF; --n-text-color-disabled: #FFF; --n-border: 1px solid #EF4444; --n-border-hover: 1px solid #F87171; --n-border-pressed: 1px solid #DC2626; --n-border-focus: 1px solid #F87171; --n-border-disabled: 1px solid #EF4444; --n-width: initial; --n-height: 28px; --n-font-size: 14px; --n-padding: 0 10px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px; margin-left: 8px;"><!----><span class="n-button__icon"><div class="n-icon-slot" role="none"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 16px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="M7 21q-.825 0-1.412-.587T5 19V6H4V4h5V3h6v1h5v2h-1v13q0 .825-.587 1.413T17 21zM17 6H7v13h10zM9 17h2V8H9zm4 0h2V8h-2zM7 6v13z"></path></svg></i></div></span><span class="n-button__content">删除</span><div aria-hidden="true" class="n-base-wave"></div><div aria-hidden="true" class="n-button__border"></div><div aria-hidden="true" class="n-button__state-border"></div></button><!----></td></tr><tr class="n-data-table-tr"><td colspan="1" rowspan="1" data-col-key="id" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><a title="点击编辑用例" style="color: rgb(32, 128, 240); cursor: pointer; text-decoration: underline; font-weight: 500; transition: 0.2s;">7</a></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="title" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>验证接口超时情况下既往史选项加载</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="desc" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>验证网络异常时既往史下拉选项的加载失败处理</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="preconditions" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>已登录医生账号
已选择高血压病种入组
进入病历填写页面
模拟网络延迟&gt;5s</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="steps" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div></div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="expected_results" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div></div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="postconditions" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>恢复网络正常</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="priority" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><span>高</span></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="status" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><span>进行中</span></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="tags" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>异常测试</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="requirement" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>高血压既往史下拉选项修改</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="tapd_url" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><a href="https://www.tapd.cn/tapd_fe/22012671/story/detail/1122012671001005124?menu_workitem_type_id=0" target="_blank" style="color: rgb(32, 128, 240); text-decoration: underline;">TAPD需求链接</a></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="project" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>共同照护web</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="creator" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>朱海华</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="created_at" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>2025-05-25 11:52:29</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="actions" class="n-data-table-td n-data-table-td--fixed-right n-data-table-td--center-align n-data-table-td--last-col" style="text-align: center; right: 0px;"><!----><button class="n-button n-button--primary-type n-button--small-type" tabindex="0" type="button" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: #0EA5E9; --n-color-hover: #38BDF8; --n-color-pressed: #0284C7; --n-color-focus: #38BDF8; --n-color-disabled: #0EA5E9; --n-ripple-color: #0EA5E9; --n-text-color: #FFF; --n-text-color-hover: #FFF; --n-text-color-pressed: #FFF; --n-text-color-focus: #FFF; --n-text-color-disabled: #FFF; --n-border: 1px solid #0EA5E9; --n-border-hover: 1px solid #38BDF8; --n-border-pressed: 1px solid #0284C7; --n-border-focus: 1px solid #38BDF8; --n-border-disabled: 1px solid #0EA5E9; --n-width: initial; --n-height: 28px; --n-font-size: 14px; --n-padding: 0 10px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px; margin-left: 8px;"><!----><span class="n-button__icon"><div class="n-icon-slot" role="none"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 16px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="M3 21v-4.25L16.2 3.575q.3-.275.663-.425t.762-.15t.775.15t.65.45L20.425 5q.3.275.438.65T21 6.4q0 .4-.137.763t-.438.662L7.25 21zM17.6 7.8L19 6.4L17.6 5l-1.4 1.4z"></path></svg></i></div></span><span class="n-button__content">编辑</span><div aria-hidden="true" class="n-base-wave"></div><div aria-hidden="true" class="n-button__border"></div><div aria-hidden="true" class="n-button__state-border"></div></button><!----><button class="n-button n-button--error-type n-button--small-type" tabindex="0" type="button" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: #EF4444; --n-color-hover: #F87171; --n-color-pressed: #DC2626; --n-color-focus: #F87171; --n-color-disabled: #EF4444; --n-ripple-color: #EF4444; --n-text-color: #FFF; --n-text-color-hover: #FFF; --n-text-color-pressed: #FFF; --n-text-color-focus: #FFF; --n-text-color-disabled: #FFF; --n-border: 1px solid #EF4444; --n-border-hover: 1px solid #F87171; --n-border-pressed: 1px solid #DC2626; --n-border-focus: 1px solid #F87171; --n-border-disabled: 1px solid #EF4444; --n-width: initial; --n-height: 28px; --n-font-size: 14px; --n-padding: 0 10px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px; margin-left: 8px;"><!----><span class="n-button__icon"><div class="n-icon-slot" role="none"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 16px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="M7 21q-.825 0-1.412-.587T5 19V6H4V4h5V3h6v1h5v2h-1v13q0 .825-.587 1.413T17 21zM17 6H7v13h10zM9 17h2V8H9zm4 0h2V8h-2zM7 6v13z"></path></svg></i></div></span><span class="n-button__content">删除</span><div aria-hidden="true" class="n-base-wave"></div><div aria-hidden="true" class="n-button__border"></div><div aria-hidden="true" class="n-button__state-border"></div></button><!----></td></tr><tr class="n-data-table-tr"><td colspan="1" rowspan="1" data-col-key="id" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><a title="点击编辑用例" style="color: rgb(32, 128, 240); cursor: pointer; text-decoration: underline; font-weight: 500; transition: 0.2s;">6</a></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="title" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>验证高血压既往史选项修改后对其他模块的影响</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="desc" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>验证修改高血压既往史选项后是否会影响健康目标模块的显示</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="preconditions" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>已登录医生账号
已选择高血压病种入组
进入病历填写页面</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="steps" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div></div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="expected_results" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div></div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="postconditions" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>无</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="priority" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><span>高</span></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="status" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><span>进行中</span></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="tags" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>集成测试</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="requirement" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>高血压既往史下拉选项修改</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="tapd_url" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><a href="https://www.tapd.cn/tapd_fe/22012671/story/detail/1122012671001005124?menu_workitem_type_id=0" target="_blank" style="color: rgb(32, 128, 240); text-decoration: underline;">TAPD需求链接</a></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="project" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>共同照护web</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="creator" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>朱海华</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="created_at" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>2025-05-25 11:52:29</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="actions" class="n-data-table-td n-data-table-td--fixed-right n-data-table-td--center-align n-data-table-td--last-col" style="text-align: center; right: 0px;"><!----><button class="n-button n-button--primary-type n-button--small-type" tabindex="0" type="button" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: #0EA5E9; --n-color-hover: #38BDF8; --n-color-pressed: #0284C7; --n-color-focus: #38BDF8; --n-color-disabled: #0EA5E9; --n-ripple-color: #0EA5E9; --n-text-color: #FFF; --n-text-color-hover: #FFF; --n-text-color-pressed: #FFF; --n-text-color-focus: #FFF; --n-text-color-disabled: #FFF; --n-border: 1px solid #0EA5E9; --n-border-hover: 1px solid #38BDF8; --n-border-pressed: 1px solid #0284C7; --n-border-focus: 1px solid #38BDF8; --n-border-disabled: 1px solid #0EA5E9; --n-width: initial; --n-height: 28px; --n-font-size: 14px; --n-padding: 0 10px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px; margin-left: 8px;"><!----><span class="n-button__icon"><div class="n-icon-slot" role="none"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 16px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="M3 21v-4.25L16.2 3.575q.3-.275.663-.425t.762-.15t.775.15t.65.45L20.425 5q.3.275.438.65T21 6.4q0 .4-.137.763t-.438.662L7.25 21zM17.6 7.8L19 6.4L17.6 5l-1.4 1.4z"></path></svg></i></div></span><span class="n-button__content">编辑</span><div aria-hidden="true" class="n-base-wave"></div><div aria-hidden="true" class="n-button__border"></div><div aria-hidden="true" class="n-button__state-border"></div></button><!----><button class="n-button n-button--error-type n-button--small-type" tabindex="0" type="button" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: #EF4444; --n-color-hover: #F87171; --n-color-pressed: #DC2626; --n-color-focus: #F87171; --n-color-disabled: #EF4444; --n-ripple-color: #EF4444; --n-text-color: #FFF; --n-text-color-hover: #FFF; --n-text-color-pressed: #FFF; --n-text-color-focus: #FFF; --n-text-color-disabled: #FFF; --n-border: 1px solid #EF4444; --n-border-hover: 1px solid #F87171; --n-border-pressed: 1px solid #DC2626; --n-border-focus: 1px solid #F87171; --n-border-disabled: 1px solid #EF4444; --n-width: initial; --n-height: 28px; --n-font-size: 14px; --n-padding: 0 10px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px; margin-left: 8px;"><!----><span class="n-button__icon"><div class="n-icon-slot" role="none"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 16px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="M7 21q-.825 0-1.412-.587T5 19V6H4V4h5V3h6v1h5v2h-1v13q0 .825-.587 1.413T17 21zM17 6H7v13h10zM9 17h2V8H9zm4 0h2V8h-2zM7 6v13z"></path></svg></i></div></span><span class="n-button__content">删除</span><div aria-hidden="true" class="n-base-wave"></div><div aria-hidden="true" class="n-button__border"></div><div aria-hidden="true" class="n-button__state-border"></div></button><!----></td></tr><tr class="n-data-table-tr"><td colspan="1" rowspan="1" data-col-key="id" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><a title="点击编辑用例" style="color: rgb(32, 128, 240); cursor: pointer; text-decoration: underline; font-weight: 500; transition: 0.2s;">5</a></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="title" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>验证高血压既往史选项边界值-不选择任何选项</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="desc" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>验证不选择任何高血压既往史选项时的处理逻辑</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="preconditions" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>已登录医生账号
已选择高血压病种入组
进入病历填写页面</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="steps" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div></div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="expected_results" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div></div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="postconditions" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>无</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="priority" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><span>中</span></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="status" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><span>进行中</span></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="tags" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>边界测试</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="requirement" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>高血压既往史下拉选项修改</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="tapd_url" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><a href="https://www.tapd.cn/tapd_fe/22012671/story/detail/1122012671001005124?menu_workitem_type_id=0" target="_blank" style="color: rgb(32, 128, 240); text-decoration: underline;">TAPD需求链接</a></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="project" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>共同照护web</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="creator" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>朱海华</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="created_at" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>2025-05-25 11:52:29</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="actions" class="n-data-table-td n-data-table-td--fixed-right n-data-table-td--center-align n-data-table-td--last-col" style="text-align: center; right: 0px;"><!----><button class="n-button n-button--primary-type n-button--small-type" tabindex="0" type="button" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: #0EA5E9; --n-color-hover: #38BDF8; --n-color-pressed: #0284C7; --n-color-focus: #38BDF8; --n-color-disabled: #0EA5E9; --n-ripple-color: #0EA5E9; --n-text-color: #FFF; --n-text-color-hover: #FFF; --n-text-color-pressed: #FFF; --n-text-color-focus: #FFF; --n-text-color-disabled: #FFF; --n-border: 1px solid #0EA5E9; --n-border-hover: 1px solid #38BDF8; --n-border-pressed: 1px solid #0284C7; --n-border-focus: 1px solid #38BDF8; --n-border-disabled: 1px solid #0EA5E9; --n-width: initial; --n-height: 28px; --n-font-size: 14px; --n-padding: 0 10px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px; margin-left: 8px;"><!----><span class="n-button__icon"><div class="n-icon-slot" role="none"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 16px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="M3 21v-4.25L16.2 3.575q.3-.275.663-.425t.762-.15t.775.15t.65.45L20.425 5q.3.275.438.65T21 6.4q0 .4-.137.763t-.438.662L7.25 21zM17.6 7.8L19 6.4L17.6 5l-1.4 1.4z"></path></svg></i></div></span><span class="n-button__content">编辑</span><div aria-hidden="true" class="n-base-wave"></div><div aria-hidden="true" class="n-button__border"></div><div aria-hidden="true" class="n-button__state-border"></div></button><!----><button class="n-button n-button--error-type n-button--small-type" tabindex="0" type="button" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: #EF4444; --n-color-hover: #F87171; --n-color-pressed: #DC2626; --n-color-focus: #F87171; --n-color-disabled: #EF4444; --n-ripple-color: #EF4444; --n-text-color: #FFF; --n-text-color-hover: #FFF; --n-text-color-pressed: #FFF; --n-text-color-focus: #FFF; --n-text-color-disabled: #FFF; --n-border: 1px solid #EF4444; --n-border-hover: 1px solid #F87171; --n-border-pressed: 1px solid #DC2626; --n-border-focus: 1px solid #F87171; --n-border-disabled: 1px solid #EF4444; --n-width: initial; --n-height: 28px; --n-font-size: 14px; --n-padding: 0 10px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px; margin-left: 8px;"><!----><span class="n-button__icon"><div class="n-icon-slot" role="none"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 16px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="M7 21q-.825 0-1.412-.587T5 19V6H4V4h5V3h6v1h5v2h-1v13q0 .825-.587 1.413T17 21zM17 6H7v13h10zM9 17h2V8H9zm4 0h2V8h-2zM7 6v13z"></path></svg></i></div></span><span class="n-button__content">删除</span><div aria-hidden="true" class="n-base-wave"></div><div aria-hidden="true" class="n-button__border"></div><div aria-hidden="true" class="n-button__state-border"></div></button><!----></td></tr><tr class="n-data-table-tr"><td colspan="1" rowspan="1" data-col-key="id" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><a title="点击编辑用例" style="color: rgb(32, 128, 240); cursor: pointer; text-decoration: underline; font-weight: 500; transition: 0.2s;">4</a></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="title" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>验证高血压既往史选项边界值-选择全部选项</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="desc" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>验证可以同时选择高血压既往史所有17个选项（16个疾病+其他）</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="preconditions" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>已登录医生账号
已选择高血压病种入组
进入病历填写页面</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="steps" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div></div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="expected_results" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div></div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="postconditions" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>无</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="priority" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><span>中</span></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="status" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><span>进行中</span></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="tags" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>边界测试</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="requirement" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>高血压既往史下拉选项修改</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="tapd_url" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><a href="https://www.tapd.cn/tapd_fe/22012671/story/detail/1122012671001005124?menu_workitem_type_id=0" target="_blank" style="color: rgb(32, 128, 240); text-decoration: underline;">TAPD需求链接</a></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="project" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>共同照护web</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="creator" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>朱海华</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="created_at" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>2025-05-25 11:52:29</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="actions" class="n-data-table-td n-data-table-td--fixed-right n-data-table-td--center-align n-data-table-td--last-col" style="text-align: center; right: 0px;"><!----><button class="n-button n-button--primary-type n-button--small-type" tabindex="0" type="button" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: #0EA5E9; --n-color-hover: #38BDF8; --n-color-pressed: #0284C7; --n-color-focus: #38BDF8; --n-color-disabled: #0EA5E9; --n-ripple-color: #0EA5E9; --n-text-color: #FFF; --n-text-color-hover: #FFF; --n-text-color-pressed: #FFF; --n-text-color-focus: #FFF; --n-text-color-disabled: #FFF; --n-border: 1px solid #0EA5E9; --n-border-hover: 1px solid #38BDF8; --n-border-pressed: 1px solid #0284C7; --n-border-focus: 1px solid #38BDF8; --n-border-disabled: 1px solid #0EA5E9; --n-width: initial; --n-height: 28px; --n-font-size: 14px; --n-padding: 0 10px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px; margin-left: 8px;"><!----><span class="n-button__icon"><div class="n-icon-slot" role="none"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 16px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="M3 21v-4.25L16.2 3.575q.3-.275.663-.425t.762-.15t.775.15t.65.45L20.425 5q.3.275.438.65T21 6.4q0 .4-.137.763t-.438.662L7.25 21zM17.6 7.8L19 6.4L17.6 5l-1.4 1.4z"></path></svg></i></div></span><span class="n-button__content">编辑</span><div aria-hidden="true" class="n-base-wave"></div><div aria-hidden="true" class="n-button__border"></div><div aria-hidden="true" class="n-button__state-border"></div></button><!----><button class="n-button n-button--error-type n-button--small-type" tabindex="0" type="button" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: #EF4444; --n-color-hover: #F87171; --n-color-pressed: #DC2626; --n-color-focus: #F87171; --n-color-disabled: #EF4444; --n-ripple-color: #EF4444; --n-text-color: #FFF; --n-text-color-hover: #FFF; --n-text-color-pressed: #FFF; --n-text-color-focus: #FFF; --n-text-color-disabled: #FFF; --n-border: 1px solid #EF4444; --n-border-hover: 1px solid #F87171; --n-border-pressed: 1px solid #DC2626; --n-border-focus: 1px solid #F87171; --n-border-disabled: 1px solid #EF4444; --n-width: initial; --n-height: 28px; --n-font-size: 14px; --n-padding: 0 10px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px; margin-left: 8px;"><!----><span class="n-button__icon"><div class="n-icon-slot" role="none"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 16px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="M7 21q-.825 0-1.412-.587T5 19V6H4V4h5V3h6v1h5v2h-1v13q0 .825-.587 1.413T17 21zM17 6H7v13h10zM9 17h2V8H9zm4 0h2V8h-2zM7 6v13z"></path></svg></i></div></span><span class="n-button__content">删除</span><div aria-hidden="true" class="n-base-wave"></div><div aria-hidden="true" class="n-button__border"></div><div aria-hidden="true" class="n-button__state-border"></div></button><!----></td></tr><tr class="n-data-table-tr"><td colspan="1" rowspan="1" data-col-key="id" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><a title="点击编辑用例" style="color: rgb(32, 128, 240); cursor: pointer; text-decoration: underline; font-weight: 500; transition: 0.2s;">12</a></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="title" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>修改高血压现病史选项后验证关联病历同步更新</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="desc" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>验证当系统管理员修改高血压病种现病史下拉选项后，所有关联病历记录中的现病史选项是否同步更新 [功能模块: 病历填写]</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="preconditions" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>已登录系统管理员账号
系统中存在至少3份高血压病种病历记录
病历记录中包含现病史选项"冠心病"、"脑卒中"、"心律失常"</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="steps" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div><p title="以系统管理员身份登录系统" style="cursor: pointer; margin-bottom: 5px;">以系统管理员身份登录系统</p><p title="导航至&quot;病历选项配置&quot;页面" style="cursor: pointer; margin-bottom: 5px;">导航至"病历选项配置"页面</p><p title="修改现病史选项，将&quot;冠心病&quot;改为&quot;冠状动脉疾病&quot;" style="cursor: pointer; margin-bottom: 5px;">修改现病史选项，将"冠心病"改为"冠状动...</p><p title="查询3份高血压病种病历记录" style="cursor: pointer; margin-bottom: 5px;">查询3份高血压病种病历记录</p><p title="检查病历修改日志" style="cursor: pointer; margin-bottom: 5px;">检查病历修改日志</p></div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="expected_results" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div><p title="成功进入系统管理员界面" style="cursor: pointer; margin-bottom: 5px;">成功进入系统管理员界面</p><p title="显示当前现病史选项列表" style="cursor: pointer; margin-bottom: 5px;">显示当前现病史选项列表</p><p title="选项修改成功保存" style="cursor: pointer; margin-bottom: 5px;">选项修改成功保存</p><p title="所有病历中的现病史选项&quot;冠心病&quot;已更新为&quot;冠状动脉疾病&quot;" style="cursor: pointer; margin-bottom: 5px;">所有病历中的现病史选项"冠心病"已更新为...</p><p title="显示选项修改操作及同步更新记录" style="cursor: pointer; margin-bottom: 5px;">显示选项修改操作及同步更新记录</p></div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="postconditions" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>恢复原始选项设置</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="priority" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><span>高</span></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="status" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><span>未开始</span></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="tags" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>功能测试</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="requirement" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>选项修改后关联病历记录的同步更新</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="tapd_url" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><a href="https://www.tapd.cn/tapd_fe/22012671/story/detail/1122012671001005124?menu_workitem_type_id=0" target="_blank" style="color: rgb(32, 128, 240); text-decoration: underline;">TAPD需求链接</a></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="project" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>共同照护web</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="creator" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>朱海华</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="created_at" class="n-data-table-td n-data-table-td--center-align" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>2025-05-27 05:41:15</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="actions" class="n-data-table-td n-data-table-td--fixed-right n-data-table-td--center-align n-data-table-td--last-col" style="text-align: center; right: 0px;"><!----><button class="n-button n-button--primary-type n-button--small-type" tabindex="0" type="button" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: #0EA5E9; --n-color-hover: #38BDF8; --n-color-pressed: #0284C7; --n-color-focus: #38BDF8; --n-color-disabled: #0EA5E9; --n-ripple-color: #0EA5E9; --n-text-color: #FFF; --n-text-color-hover: #FFF; --n-text-color-pressed: #FFF; --n-text-color-focus: #FFF; --n-text-color-disabled: #FFF; --n-border: 1px solid #0EA5E9; --n-border-hover: 1px solid #38BDF8; --n-border-pressed: 1px solid #0284C7; --n-border-focus: 1px solid #38BDF8; --n-border-disabled: 1px solid #0EA5E9; --n-width: initial; --n-height: 28px; --n-font-size: 14px; --n-padding: 0 10px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px; margin-left: 8px;"><!----><span class="n-button__icon"><div class="n-icon-slot" role="none"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 16px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="M3 21v-4.25L16.2 3.575q.3-.275.663-.425t.762-.15t.775.15t.65.45L20.425 5q.3.275.438.65T21 6.4q0 .4-.137.763t-.438.662L7.25 21zM17.6 7.8L19 6.4L17.6 5l-1.4 1.4z"></path></svg></i></div></span><span class="n-button__content">编辑</span><div aria-hidden="true" class="n-base-wave"></div><div aria-hidden="true" class="n-button__border"></div><div aria-hidden="true" class="n-button__state-border"></div></button><!----><button class="n-button n-button--error-type n-button--small-type" tabindex="0" type="button" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: #EF4444; --n-color-hover: #F87171; --n-color-pressed: #DC2626; --n-color-focus: #F87171; --n-color-disabled: #EF4444; --n-ripple-color: #EF4444; --n-text-color: #FFF; --n-text-color-hover: #FFF; --n-text-color-pressed: #FFF; --n-text-color-focus: #FFF; --n-text-color-disabled: #FFF; --n-border: 1px solid #EF4444; --n-border-hover: 1px solid #F87171; --n-border-pressed: 1px solid #DC2626; --n-border-focus: 1px solid #F87171; --n-border-disabled: 1px solid #EF4444; --n-width: initial; --n-height: 28px; --n-font-size: 14px; --n-padding: 0 10px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px; margin-left: 8px;"><!----><span class="n-button__icon"><div class="n-icon-slot" role="none"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 16px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="M7 21q-.825 0-1.412-.587T5 19V6H4V4h5V3h6v1h5v2h-1v13q0 .825-.587 1.413T17 21zM17 6H7v13h10zM9 17h2V8H9zm4 0h2V8h-2zM7 6v13z"></path></svg></i></div></span><span class="n-button__content">删除</span><div aria-hidden="true" class="n-base-wave"></div><div aria-hidden="true" class="n-button__border"></div><div aria-hidden="true" class="n-button__state-border"></div></button><!----></td></tr><tr class="n-data-table-tr"><td colspan="1" rowspan="1" data-col-key="id" class="n-data-table-td n-data-table-td--center-align n-data-table-td--last-row" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><a title="点击编辑用例" style="color: rgb(32, 128, 240); cursor: pointer; text-decoration: underline; font-weight: 500; transition: 0.2s;">13</a></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="title" class="n-data-table-td n-data-table-td--center-align n-data-table-td--last-row" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>批量修改既往史选项后验证多病历同步更新</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="desc" class="n-data-table-td n-data-table-td--center-align n-data-table-td--last-row" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>验证当批量修改高血压病种既往史下拉选项时，系统能否正确处理100份以上关联病历的同步更新 [功能模块: 病历填写]</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="preconditions" class="n-data-table-td n-data-table-td--center-align n-data-table-td--last-row" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>系统中存在150份高血压病种病历记录
病历记录中包含既往史选项"糖尿病"、"肾病"</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="steps" class="n-data-table-td n-data-table-td--center-align n-data-table-td--last-row" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div><p title="准备150份包含&quot;糖尿病&quot;既往史的高血压病历" style="cursor: pointer; margin-bottom: 5px;">准备150份包含"糖尿病"既往史的高血压...</p><p title="修改既往史选项，将&quot;糖尿病&quot;改为&quot;糖尿病(I型)&quot;" style="cursor: pointer; margin-bottom: 5px;">修改既往史选项，将"糖尿病"改为"糖尿病...</p><p title="执行批量查询150份病历" style="cursor: pointer; margin-bottom: 5px;">执行批量查询150份病历</p><p title="验证同步更新时间" style="cursor: pointer; margin-bottom: 5px;">验证同步更新时间</p><p title="检查系统资源占用情况" style="cursor: pointer; margin-bottom: 5px;">检查系统资源占用情况</p></div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="expected_results" class="n-data-table-td n-data-table-td--center-align n-data-table-td--last-row" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div><p title="测试数据准备完成" style="cursor: pointer; margin-bottom: 5px;">测试数据准备完成</p><p title="选项修改成功保存" style="cursor: pointer; margin-bottom: 5px;">选项修改成功保存</p><p title="所有病历中的&quot;糖尿病&quot;选项已更新为&quot;糖尿病(I型)&quot;" style="cursor: pointer; margin-bottom: 5px;">所有病历中的"糖尿病"选项已更新为"糖尿...</p><p title="150份病历更新完成时间≤5秒" style="cursor: pointer; margin-bottom: 5px;">150份病历更新完成时间≤5秒</p><p title="CPU使用率≤80%，内存使用≤2GB" style="cursor: pointer; margin-bottom: 5px;">CPU使用率≤80%，内存使用≤2GB</p></div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="postconditions" class="n-data-table-td n-data-table-td--center-align n-data-table-td--last-row" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>恢复原始选项设置</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="priority" class="n-data-table-td n-data-table-td--center-align n-data-table-td--last-row" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><span>高</span></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="status" class="n-data-table-td n-data-table-td--center-align n-data-table-td--last-row" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><span>未开始</span></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="tags" class="n-data-table-td n-data-table-td--center-align n-data-table-td--last-row" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>功能测试</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="requirement" class="n-data-table-td n-data-table-td--center-align n-data-table-td--last-row" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>选项修改后关联病历记录的同步更新</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="tapd_url" class="n-data-table-td n-data-table-td--center-align n-data-table-td--last-row" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><a href="https://www.tapd.cn/tapd_fe/22012671/story/detail/1122012671001005124?menu_workitem_type_id=0" target="_blank" style="color: rgb(32, 128, 240); text-decoration: underline;">TAPD需求链接</a></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="project" class="n-data-table-td n-data-table-td--center-align n-data-table-td--last-row" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>共同照护web</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="creator" class="n-data-table-td n-data-table-td--center-align n-data-table-td--last-row" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span>朱海华</span></span><!----></td><td colspan="1" rowspan="1" data-col-key="created_at" class="n-data-table-td n-data-table-td--center-align n-data-table-td--last-row" style="text-align: center;"><!----><!----><span class="n-ellipsis" style="text-overflow: ellipsis;"><span><div>2025-05-27 05:41:15</div></span></span><!----></td><td colspan="1" rowspan="1" data-col-key="actions" class="n-data-table-td n-data-table-td--fixed-right n-data-table-td--center-align n-data-table-td--last-col n-data-table-td--last-row" style="text-align: center; right: 0px;"><!----><button class="n-button n-button--primary-type n-button--small-type" tabindex="0" type="button" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: #0EA5E9; --n-color-hover: #38BDF8; --n-color-pressed: #0284C7; --n-color-focus: #38BDF8; --n-color-disabled: #0EA5E9; --n-ripple-color: #0EA5E9; --n-text-color: #FFF; --n-text-color-hover: #FFF; --n-text-color-pressed: #FFF; --n-text-color-focus: #FFF; --n-text-color-disabled: #FFF; --n-border: 1px solid #0EA5E9; --n-border-hover: 1px solid #38BDF8; --n-border-pressed: 1px solid #0284C7; --n-border-focus: 1px solid #38BDF8; --n-border-disabled: 1px solid #0EA5E9; --n-width: initial; --n-height: 28px; --n-font-size: 14px; --n-padding: 0 10px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px; margin-left: 8px;"><!----><span class="n-button__icon"><div class="n-icon-slot" role="none"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 16px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="M3 21v-4.25L16.2 3.575q.3-.275.663-.425t.762-.15t.775.15t.65.45L20.425 5q.3.275.438.65T21 6.4q0 .4-.137.763t-.438.662L7.25 21zM17.6 7.8L19 6.4L17.6 5l-1.4 1.4z"></path></svg></i></div></span><span class="n-button__content">编辑</span><div aria-hidden="true" class="n-base-wave"></div><div aria-hidden="true" class="n-button__border"></div><div aria-hidden="true" class="n-button__state-border"></div></button><!----><button class="n-button n-button--error-type n-button--small-type" tabindex="0" type="button" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-bezier-ease-out: cubic-bezier(0, 0, .2, 1); --n-ripple-duration: .6s; --n-opacity-disabled: 0.5; --n-wave-opacity: 0.6; --n-font-weight: 400; --n-color: #EF4444; --n-color-hover: #F87171; --n-color-pressed: #DC2626; --n-color-focus: #F87171; --n-color-disabled: #EF4444; --n-ripple-color: #EF4444; --n-text-color: #FFF; --n-text-color-hover: #FFF; --n-text-color-pressed: #FFF; --n-text-color-focus: #FFF; --n-text-color-disabled: #FFF; --n-border: 1px solid #EF4444; --n-border-hover: 1px solid #F87171; --n-border-pressed: 1px solid #DC2626; --n-border-focus: 1px solid #F87171; --n-border-disabled: 1px solid #EF4444; --n-width: initial; --n-height: 28px; --n-font-size: 14px; --n-padding: 0 10px; --n-icon-size: 18px; --n-icon-margin: 6px; --n-border-radius: 3px; margin-left: 8px;"><!----><span class="n-button__icon"><div class="n-icon-slot" role="none"><i role="img" class="n-icon" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); font-size: 16px;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24" class="iconify iconify--material-symbols"><path fill="currentColor" d="M7 21q-.825 0-1.412-.587T5 19V6H4V4h5V3h6v1h5v2h-1v13q0 .825-.587 1.413T17 21zM17 6H7v13h10zM9 17h2V8H9zm4 0h2V8h-2zM7 6v13z"></path></svg></i></div></span><span class="n-button__content">删除</span><div aria-hidden="true" class="n-base-wave"></div><div aria-hidden="true" class="n-button__border"></div><div aria-hidden="true" class="n-button__state-border"></div></button><!----></td></tr></tbody></table></div></div><div class="n-scrollbar-rail n-scrollbar-rail--vertical n-scrollbar-rail--vertical--right" data-scrollbar-rail="true" aria-hidden="true" style="z-index: 3;"><!----></div><div class="n-scrollbar-rail n-scrollbar-rail--horizontal n-scrollbar-rail--horizontal--bottom n-scrollbar-rail--disabled" data-scrollbar-rail="true" aria-hidden="true" style="z-index: 3;"><!----></div></div></div></div><div class="n-data-table__pagination"><div class="n-pagination" style="--n-prefix-margin: 0 8px 0 0; --n-suffix-margin: 0 0 0 8px; --n-item-font-size: 14px; --n-select-width: unset; --n-select-margin: 0 0 0 8px; --n-input-width: 60px; --n-input-margin: 0 0 0 8px; --n-input-margin-rtl: 0 8px 0 0; --n-item-size: 28px; --n-item-text-color: rgb(51, 54, 57); --n-item-text-color-disabled: rgba(194, 194, 194, 1); --n-item-text-color-hover: #38BDF8; --n-item-text-color-active: #0EA5E9; --n-item-text-color-pressed: #0284C7; --n-item-color: #0000; --n-item-color-hover: #0000; --n-item-color-disabled: rgb(250, 250, 252); --n-item-color-active: #0000; --n-item-color-active-hover: #0000; --n-item-color-pressed: #0000; --n-item-border: 1px solid #0000; --n-item-border-hover: 1px solid #0000; --n-item-border-disabled: 1px solid rgb(224, 224, 230); --n-item-border-active: 1px solid #0EA5E9; --n-item-border-pressed: 1px solid #0000; --n-item-padding: 0 4px; --n-item-border-radius: 3px; --n-bezier: cubic-bezier(.4, 0, .2, 1); --n-jumper-font-size: 14px; --n-jumper-text-color: rgb(51, 54, 57); --n-jumper-text-color-disabled: rgba(194, 194, 194, 1); --n-item-margin: 0 0 0 8px; --n-item-margin-rtl: 0 8px 0 0; --n-button-icon-size: 16px; --n-button-icon-color: rgb(51, 54, 57); --n-button-icon-color-hover: rgb(51, 54, 57); --n-button-icon-color-pressed: rgb(51, 54, 57); --n-button-color-hover: #0000; --n-button-color: #0000; --n-button-color-pressed: #0000; --n-button-border: 1px solid rgb(224, 224, 230); --n-button-border-hover: 1px solid rgb(224, 224, 230); --n-button-border-pressed: 1px solid rgb(224, 224, 230);"><div class="n-pagination-prefix">共 13 条</div><div class="n-pagination-item n-pagination-item--button n-pagination-item--disabled"><i class="n-base-icon"><svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12.2674 15.793C11.9675 16.0787 11.4927 16.0672 11.2071 15.7673L6.20572 10.5168C5.9298 10.2271 5.9298 9.7719 6.20572 9.48223L11.2071 4.23177C11.4927 3.93184 11.9675 3.92031 12.2674 4.206C12.5673 4.49169 12.5789 4.96642 12.2932 5.26634L7.78458 9.99952L12.2932 14.7327C12.5789 15.0326 12.5673 15.5074 12.2674 15.793Z" fill="currentColor"></path></svg></i></div><div class="n-pagination-item n-pagination-item--active n-pagination-item--clickable">1</div><!----><div class="n-pagination-item n-pagination-item--clickable">2</div><!----><div class="n-pagination-item n-pagination-item--button"><i class="n-base-icon"><svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M7.73271 4.20694C8.03263 3.92125 8.50737 3.93279 8.79306 4.23271L13.7944 9.48318C14.0703 9.77285 14.0703 10.2281 13.7944 10.5178L8.79306 15.7682C8.50737 16.0681 8.03263 16.0797 7.73271 15.794C7.43279 15.5083 7.42125 15.0336 7.70694 14.7336L12.2155 10.0005L7.70694 5.26729C7.42125 4.96737 7.43279 4.49264 7.73271 4.20694Z" fill="currentColor"></path></svg></i></div><div class="n-select"><div class="n-base-selection n-base-selection--selected" style="--n-bezier: cubic-bezier(.4, 0, .2, 1); --n-border: 1px solid rgb(224, 224, 230); --n-border-active: 1px solid #0EA5E9; --n-border-focus: 1px solid #38BDF8; --n-border-hover: 1px solid #38BDF8; --n-border-radius: 3px; --n-box-shadow-active: 0 0 0 2px rgba(14, 165, 233, 0.2); --n-box-shadow-focus: 0 0 0 2px rgba(14, 165, 233, 0.2); --n-box-shadow-hover: none; --n-caret-color: #0EA5E9; --n-color: rgba(255, 255, 255, 1); --n-color-active: rgba(255, 255, 255, 1); --n-color-disabled: rgb(250, 250, 252); --n-font-size: 14px; --n-height: 28px; --n-padding-single-top: 0; --n-padding-multiple-top: 3px; --n-padding-single-right: 26px; --n-padding-multiple-right: 26px; --n-padding-single-left: 12px; --n-padding-multiple-left: 12px; --n-padding-single-bottom: 0; --n-padding-multiple-bottom: 0; --n-placeholder-color: rgba(194, 194, 194, 1); --n-placeholder-color-disabled: rgba(209, 209, 209, 1); --n-text-color: rgb(51, 54, 57); --n-text-color-disabled: rgba(194, 194, 194, 1); --n-arrow-color: rgba(194, 194, 194, 1); --n-arrow-color-disabled: rgba(209, 209, 209, 1); --n-loading-color: #0EA5E9; --n-color-active-warning: rgba(255, 255, 255, 1); --n-box-shadow-focus-warning: 0 0 0 2px rgba(245, 158, 11, 0.2); --n-box-shadow-active-warning: 0 0 0 2px rgba(245, 158, 11, 0.2); --n-box-shadow-hover-warning: none; --n-border-warning: 1px solid #F59E0B; --n-border-focus-warning: 1px solid #FBBF24; --n-border-hover-warning: 1px solid #FBBF24; --n-border-active-warning: 1px solid #F59E0B; --n-color-active-error: rgba(255, 255, 255, 1); --n-box-shadow-focus-error: 0 0 0 2px rgba(239, 68, 68, 0.2); --n-box-shadow-active-error: 0 0 0 2px rgba(239, 68, 68, 0.2); --n-box-shadow-hover-error: none; --n-border-error: 1px solid #EF4444; --n-border-focus-error: 1px solid #F87171; --n-border-hover-error: 1px solid #F87171; --n-border-active-error: 1px solid #EF4444; --n-clear-size: 16px; --n-clear-color: rgba(194, 194, 194, 1); --n-clear-color-hover: rgba(146, 146, 146, 1); --n-clear-color-pressed: rgba(175, 175, 175, 1); --n-arrow-size: 16px; --n-font-weight: 400;"><div class="n-base-selection-label" tabindex="0"><div class="n-base-selection-input" title="10 / 页"><div class="n-base-selection-input__content">10 / 页</div></div><div class="n-base-loading n-base-suffix" role="img" aria-label="loading"><div class="n-base-loading__placeholder"><div class="n-base-clear"><div class="n-base-clear__placeholder"><i class="n-base-icon n-base-suffix__arrow"><svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.14645 5.64645C3.34171 5.45118 3.65829 5.45118 3.85355 5.64645L8 9.79289L12.1464 5.64645C12.3417 5.45118 12.6583 5.45118 12.8536 5.64645C13.0488 5.84171 13.0488 6.15829 12.8536 6.35355L8.35355 10.8536C8.15829 11.0488 7.84171 11.0488 7.64645 10.8536L3.14645 6.35355C2.95118 6.15829 2.95118 5.84171 3.14645 5.64645Z" fill="currentColor"></path></svg></i></div></div></div></div></div><div class="n-base-selection__border"></div><div class="n-base-selection__state-border"></div></div><!----></div><!----><!----></div></div><!----></div></div></div><!----></div><!----><!----></div><footer f-c-c="" flex-col="" text-14="" color="#6a6a6a" mt-15=""></footer><div class="n-back-top-placeholder" aria-hidden="true" style="display: none;"><!----></div></section></section></article></div></div><!-- AI测试加载组件 --><!--v-if--><div></div><!----><!----></div></div>
    <script src="/resource/ai-loading.js"></script>
    <script type="module" src="/src/main.js"></script>
  

<div id="mbGetImgModule" class="mb-getmaterial-box" style="display: block;">
		               <div class="wzy-dialog"></div>
		               <div class="btn-group">
		                  <div class="btn-group-body">
		                     <span>采集</span>
		                     <svg class="wzyiconxiala" width="200px" height="200.00px" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><path fill="#ffffff" d="M732.16000001 431.104L535.04 645.12c-6.656 6.656-13.312 9.728-23.04 9.728s-16.384-3.072-23.04-9.728L291.83999999 431.104c-9.728-9.728-13.312-26.112-6.65599999-39.424 6.656-13.31200001 16.384-23.04 29.696-23.04l394.752 0c13.312 0 26.112 9.728 29.696 23.04 6.14399999 13.312 3.072 29.696-7.16799999 39.424z"></path></svg>
		                  </div>
		                  <div class="mb-getmaterial-dropdown">
							<ul class="mb-getmaterial-medias"><li data-ghid="mb" data-ghidname="美编素材库" class="collect-active"><svg t="1667288501413" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2556" width="200" height="200"><path d="M219.952 512.576l210.432 210.432-45.248 45.256-210.432-210.432z" p-id="2557"></path><path d="M799.672 262.264l45.256 45.256-460.464 460.464-45.256-45.256z" p-id="2558"></path></svg>美编素材库</li></ul>
							<div>编辑</div>
							<ul class="mb-getmaterial-opts">
								<li class="mb-getmaterial-add-btn"><svg t="1667288501413" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2556" width="200" height="200"><path d="M219.952 512.576l210.432 210.432-45.248 45.256-210.432-210.432z" p-id="2557"></path><path d="M799.672 262.264l45.256 45.256-460.464 460.464-45.256-45.256z" p-id="2558"></path></svg>添加新的媒体</li>
								<li class="mb-getmaterial-hide-btn"><svg t="1667288501413" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2556" width="200" height="200"><path d="M219.952 512.576l210.432 210.432-45.248 45.256-210.432-210.432z" p-id="2557"></path><path d="M799.672 262.264l45.256 45.256-460.464 460.464-45.256-45.256z" p-id="2558"></path></svg>隐藏采集图片</li>
							</ul> 
						  </div>
		               </div>
					</div>
					<div id="mbCollectedMp">
						<div class="mb-collected-mp">
							<h3>选择采集的公众号</h3>
							<ul class="mb-collected-mps"><li data-ghid="mb" data-ghidname="美编素材库" class="collect-active"><span></span>美编素材库</li></ul>
							<div class="mb-collected-mp-footer">
								<div class="mb-collected-mp-footer-cancel">取 消</div>
								<div class="mb-collected-mp-footer-enter">确 定</div>
							</div>
						</div>
					</div>
				  <div id="glmos-main-content" class="glmos-main-content" data-version="1.0.27"></div><link rel="stylesheet" href="chrome-extension://mnpdbmgpebfihcndnpgdaihnkmloclkd/do-action-pointer.css"><!----></body><div id="immersive-translate-popup" style="all: initial"></div></html>