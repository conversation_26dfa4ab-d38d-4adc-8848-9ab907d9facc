#!/usr/bin/env python
# -*- coding: utf-8 -*-

import asyncio
import argparse
import os
import sys
import copy
from tortoise import Tortoise
from app.settings.config import settings

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 自定义数据库配置
def get_db_config(host=None, port=None, user=None, password=None, database=None):
    """根据参数生成数据库配置"""
    # 如果没有提供参数，使用默认配置
    if not any([host, port, user, password, database]):
        return settings.TORTOISE_ORM

    # 复制原始配置
    config = copy.deepcopy(settings.TORTOISE_ORM)

    # 更新数据库连接信息
    if host:
        config["connections"]["default"]["credentials"]["host"] = host
    if port:
        config["connections"]["default"]["credentials"]["port"] = int(port)
    if user:
        config["connections"]["default"]["credentials"]["user"] = user
    if password:
        config["connections"]["default"]["credentials"]["password"] = password
    if database:
        config["connections"]["default"]["credentials"]["database"] = database

    return config

async def create_relation_table(host=None, port=None, user=None, password=None, database=None):
    # 初始化数据库连接
    print("正在初始化数据库连接...")

    # 获取数据库配置
    db_config = get_db_config(host, port, user, password, database)

    # 打印连接信息（隐藏密码）
    conn_info = db_config["connections"]["default"]["credentials"].copy()
    if "password" in conn_info:
        conn_info["password"] = "******"  # 隐藏密码
    print(f"数据库连接信息: {conn_info}")

    await Tortoise.init(config=db_config)
    print("数据库连接初始化完成")

    # 获取数据库连接
    conn = Tortoise.get_connection("default")

    # 检查表是否已存在
    try:
        result = await conn.execute_query("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_name = 'requirement_knowledge_relation'
        );
        """)

        # 打印结果以便调试
        print(f"查询结果: {result}")

        # 根据返回结果的结构进行适当的访问
        if isinstance(result, tuple) and len(result) > 0:
            if isinstance(result[1], list) and len(result[1]) > 0:
                table_exists = result[1][0][0]
            else:
                table_exists = False
        else:
            table_exists = False
    except Exception as e:
        print(f"检查表是否存在时出错: {str(e)}")
        table_exists = False

    if table_exists:
        print("表 requirement_knowledge_relation 已存在")
    else:
        print("表 requirement_knowledge_relation 不存在，正在创建...")

        # 创建表
        await conn.execute_script("""
        CREATE TABLE requirement_knowledge_relation (
            id SERIAL PRIMARY KEY,
            requirement_id INTEGER NOT NULL,
            knowledge_id INTEGER NOT NULL,
            relevance_level VARCHAR(50) NOT NULL DEFAULT '直接相关',
            relevance_score FLOAT NOT NULL DEFAULT 0.0,
            source VARCHAR(50) NOT NULL DEFAULT '用例生成',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );

        CREATE INDEX idx_req_knowledge_relation_requirement_id ON requirement_knowledge_relation(requirement_id);
        CREATE INDEX idx_req_knowledge_relation_knowledge_id ON requirement_knowledge_relation(knowledge_id);
        CREATE INDEX idx_req_knowledge_relation_relevance_level ON requirement_knowledge_relation(relevance_level);
        """)

        print("表 requirement_knowledge_relation 创建成功")

    # 关闭数据库连接
    await Tortoise.close_connections()
    print("数据库连接已关闭")

def main():
    """主函数"""
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description="创建需求与知识点关联关系表")

    # 添加数据库连接参数
    parser.add_argument("--host", help="数据库主机地址，例如：localhost 或 *************")
    parser.add_argument("--port", help="数据库端口，例如：5432")
    parser.add_argument("--user", help="数据库用户名，例如：admin")
    parser.add_argument("--password", help="数据库密码")
    parser.add_argument("--database", help="数据库名称，例如：agent_testing")

    # 解析命令行参数
    args = parser.parse_args()

    # 构建数据库参数字典
    db_params = {}
    if args.host:
        db_params["host"] = args.host
    if args.port:
        db_params["port"] = args.port
    if args.user:
        db_params["user"] = args.user
    if args.password:
        db_params["password"] = args.password
    if args.database:
        db_params["database"] = args.database

    # 打印连接信息（隐藏密码）
    if db_params:
        conn_info = db_params.copy()
        if "password" in conn_info:
            conn_info["password"] = "******"  # 隐藏密码
        print(f"将连接到远程数据库: {conn_info}")

    try:
        # 执行创建表操作
        asyncio.run(create_relation_table(**db_params))
        print("操作完成！")
    except Exception as e:
        print(f"创建表时出错：{str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
