#!/usr/bin/env python
import asyncio
import sys
from tortoise import Tortoise
from app.settings.config import settings

async def init():
    # 初始化Tortoise ORM
    await Tortoise.init(config=settings.TORTOISE_ORM)

async def list_tables():
    # 获取所有表名
    conn = Tortoise.get_connection("default")
    result = await conn.execute_query("SELECT name FROM sqlite_master WHERE type='table';")
    tables = [row[0] for row in result[1]]
    # 过滤掉 sqlite_ 开头的系统表
    tables = [t for t in tables if not t.startswith('sqlite_')]
    print("数据库中的表:")
    for table in tables:
        print(f"- {table}")
    return tables

async def query_table(table_name, limit=10):
    # 查询指定表的数据
    conn = Tortoise.get_connection("default")
    try:
        # 先获取表的列名
        result = await conn.execute_query(f"PRAGMA table_info({table_name});")
        if not result[1]:
            print(f"表 {table_name} 不存在或没有列信息")
            return

        columns = [row[1] for row in result[1]]  # 列名在第二列

        # 查询数据
        result = await conn.execute_query(f"SELECT * FROM {table_name} LIMIT {limit};")
        rows = result[1]

        if not rows:
            print(f"表 {table_name} 中没有数据")
            return

        print(f"\n表 {table_name} 的数据:")
        print("=" * 80)
        print(" | ".join(columns))
        print("-" * 80)

        for row in rows:
            # 将每个值转换为字符串并限制长度
            row_str = [str(val)[:50] + ('...' if len(str(val)) > 50 else '') for val in row]
            print(" | ".join(row_str))
    except Exception as e:
        print(f"查询表 {table_name} 时出错: {e}")

async def main():
    await init()

    if len(sys.argv) > 1:
        # 如果提供了表名参数，则查询该表
        table_name = sys.argv[1]
        await query_table(table_name)
    else:
        # 否则列出所有表并查询每个表
        tables = await list_tables()
        for table in tables:
            await query_table(table)

    # 关闭连接
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(main())
