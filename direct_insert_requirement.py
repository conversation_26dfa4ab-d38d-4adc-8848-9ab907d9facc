#!/usr/bin/env python
import asyncio
import sys
import json
import pytz
from datetime import datetime
from tortoise import Tortoise, connections
from app.settings.config import settings

# 获取带时区的当前时间
def get_now_with_timezone():
    """获取带UTC时区的当前时间"""
    return datetime.now(pytz.UTC)

async def init():
    # 初始化Tortoise ORM
    await Tortoise.init(config=settings.TORTOISE_ORM)
    print("数据库连接初始化完成")

async def direct_insert_requirement():
    """直接使用SQL插入需求，避免ORM的时区处理"""
    # 获取数据库连接
    conn = connections.get("default")

    # 1. 修改PostgreSQL会话时区设置
    await conn.execute_query("SET timezone TO 'UTC';")
    print("已将当前会话时区设置为UTC")

    # 2. 检查requirement表的字段类型
    try:
        result = await conn.execute_query("""
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_name = 'requirement' AND column_name IN ('created_at', 'updated_at');
        """)

        column_info = {}
        for row in result[1]:
            column_info[row[0]] = {"type": row[1], "nullable": row[2]}

        print(f"requirement表字段信息: {column_info}")
    except Exception as e:
        print(f"获取表结构信息失败: {e}")
        column_info = {}

    # 3. 创建一个最小化的需求字典
    minimal_dict = {
        "name": f"测试需求-{get_now_with_timezone().strftime('%Y%m%d%H%M%S')}",
        "description": "这是一个测试需求，用于验证时区问题修复",
        "category": "功能",
        "level": "高",
        "estimate": 8,
        "project_id": 1,
        "reviewer": "测试人员"
    }

    # 4. 构建SQL插入语句
    fields = []
    values = []
    params = []

    for field_name, field_value in minimal_dict.items():
        if field_value is not None:
            fields.append(field_name)
            values.append(f"${len(params) + 1}")
            params.append(field_value)

    # 检查字段类型，仅用于日志记录
    has_timestamp_with_tz = True
    for column, info in column_info.items():
        if "timestamp" in info["type"].lower() and "without time zone" in info["type"].lower():
            has_timestamp_with_tz = False
            print(f"警告: {column}字段是timestamp without time zone类型")
            break

    if has_timestamp_with_tz:
        print("所有时间字段都是timestamp with time zone类型，这是理想的配置")

    # 添加created_at和updated_at字段
    fields.extend(["created_at", "updated_at"])
    # 使用简单的NOW()函数，PostgreSQL会根据字段类型自动处理时区
    values.extend(["NOW()", "NOW()"])

    # 构建完整的SQL语句
    sql = f"INSERT INTO requirement ({', '.join(fields)}) VALUES ({', '.join(values)}) RETURNING id"

    try:
        # 执行SQL语句
        print(f"执行SQL: {sql}")
        print(f"参数: {params}")
        result = await conn.execute_query(sql, params)
        requirement_id = result[1][0][0]
        print(f"需求插入成功! ID: {requirement_id}")

        # 查询插入的需求
        try:
            query_result = await conn.execute_query(f"SELECT * FROM requirement WHERE id = {requirement_id}")
            print("\n插入的需求详情:")

            # 检查查询结果的结构
            print(f"查询结果类型: {type(query_result)}")
            print(f"查询结果长度: {len(query_result)}")

            if len(query_result) >= 2 and isinstance(query_result[1], list) and len(query_result[1]) > 0:
                # 打印列名
                print("列名:", query_result[0])
                # 打印第一行数据
                print("数据:", query_result[1][0])

                # 尝试使用zip打印列名和值
                for column, value in zip(query_result[0], query_result[1][0]):
                    print(f"{column}: {value}")
            else:
                print(f"查询结果格式不符合预期: {query_result}")
        except Exception as e:
            print(f"查询需求详情失败: {e}")

        # 特别检查时间字段
        try:
            time_fields = await conn.execute_query(f"""
            SELECT
                created_at,
                updated_at
            FROM requirement WHERE id = {requirement_id}
            """)
            print("\n时间字段详情:")

            if len(time_fields) >= 2 and isinstance(time_fields[1], list) and len(time_fields[1]) > 0:
                print(f"created_at: {time_fields[1][0][0]}")
                print(f"updated_at: {time_fields[1][0][1]}")

                # 检查时区信息
                tz_check = await conn.execute_query(f"""
                SELECT
                    EXTRACT(TIMEZONE FROM created_at) as created_at_tz,
                    EXTRACT(TIMEZONE FROM updated_at) as updated_at_tz
                FROM requirement WHERE id = {requirement_id}
                """)

                if len(tz_check) >= 2 and isinstance(tz_check[1], list) and len(tz_check[1]) > 0:
                    print(f"created_at timezone offset (seconds): {tz_check[1][0][0]}")
                    print(f"updated_at timezone offset (seconds): {tz_check[1][0][1]}")
            else:
                print(f"时间字段查询结果格式不符合预期: {time_fields}")
        except Exception as e:
            print(f"查询时间字段失败: {e}")

    except Exception as e:
        print(f"需求插入失败: {e}")

    print("直接插入需求完成")

async def main():
    try:
        await init()
        await direct_insert_requirement()
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(main())
