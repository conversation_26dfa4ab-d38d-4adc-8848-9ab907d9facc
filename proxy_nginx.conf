worker_processes 1;
error_log /tmp/nginx_error.log;
pid /tmp/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /usr/local/etc/nginx/mime.types;
    default_type application/octet-stream;
    access_log /tmp/nginx_access.log;
    sendfile on;
    keepalive_timeout 65;

    server {
        listen 8080;
        server_name localhost;

        # 重写规则：处理重复的 /api/v1/api/v1/ 路径
        location ~ ^/api/v1/api/v1/(.*)$ {
            rewrite ^/api/v1/api/v1/(.*)$ /api/v1/$1 break;
            proxy_pass http://localhost:9999;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

        # 常规请求转发
        location / {
            proxy_pass http://localhost:9999;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}
