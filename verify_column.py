#!/usr/bin/env python
"""
验证远程数据库的test_cases表是否包含is_adopted字段
"""

import psycopg2
import sys
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

# 远程数据库连接信息
DB_CONFIG = {
    'host': '************',
    'port': 5432,
    'user': 'admin',
    'password': 'admin123',
    'database': 'agent_testing',
}

def main():
    """主函数"""
    try:
        # 连接到数据库
        logger.info(f"正在连接到数据库: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}...")
        conn = psycopg2.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database=DB_CONFIG['database']
        )
        logger.info("数据库连接成功")

        # 创建游标
        cursor = conn.cursor()

        # 查询test_cases表结构
        logger.info("查询test_cases表结构...")
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns
            WHERE table_name = 'test_cases'
            ORDER BY ordinal_position;
        """)
        columns = cursor.fetchall()

        # 打印表结构
        logger.info("test_cases表结构:")
        for column in columns:
            logger.info(f"列名: {column[0]}, 数据类型: {column[1]}, 可空: {column[2]}, 默认值: {column[3]}")

        # 检查is_adopted字段是否存在
        is_adopted_exists = any(column[0] == 'is_adopted' for column in columns)
        if is_adopted_exists:
            logger.info("is_adopted字段已存在于test_cases表中")
        else:
            logger.error("is_adopted字段不存在于test_cases表中")

        # 关闭连接
        cursor.close()
        conn.close()
        logger.info("数据库连接已关闭")

    except Exception as e:
        logger.error(f"执行过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
