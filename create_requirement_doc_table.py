import asyncio
import sys
from tortoise import Tortoise
from app.settings.config import TORTOISE_ORM
import logging  # 导入日志模块
logger = logging.getLogger(__name__)  # 定义 logger 对象

async def create_table():
    # 初始化 Tortoise ORM
    await Tortoise.init(config=TORTOISE_ORM)

    # 获取数据库连接
    conn = Tortoise.get_connection('default')

    try:
        # 检查数据库类型
        db_type = str(type(conn))
        print(f"数据库连接类型: {db_type}")
        logger.info(f"数据库连接类型: {db_type}")
        # 根据数据库类型选择不同的检查方式
        table_exists = False

        if 'asyncpg' in db_type.lower():
            # PostgreSQL
            try:
                result = await conn.execute_query(
                    "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'requirement_doc')"
                )
                table_exists = result[1][0][0] if result[1] else False
                print("使用 PostgreSQL 检查表是否存在")
            except Exception as e:
                print(f"PostgreSQL 检查失败: {e}")
                # 尝试通用方法
                try:
                    await conn.execute_query("SELECT 1 FROM requirement_doc LIMIT 1")
                    table_exists = True
                except Exception:
                    table_exists = False
        elif 'sqlite' in db_type.lower():
            # SQLite
            try:
                result = await conn.execute_query(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name='requirement_doc'"
                )
                table_exists = bool(result[1])
                print("使用 SQLite 检查表是否存在")
            except Exception as e:
                print(f"SQLite 检查失败: {e}")
                # 尝试通用方法
                try:
                    await conn.execute_query("SELECT 1 FROM requirement_doc LIMIT 1")
                    table_exists = True
                except Exception:
                    table_exists = False
        else:
            # 其他数据库，使用通用方法
            try:
                # 尝试查询表，如果表不存在会抛出异常
                await conn.execute_query("SELECT 1 FROM requirement_doc LIMIT 1")
                table_exists = True
            except Exception:
                table_exists = False
            print("使用通用方法检查表是否存在")

        if table_exists:
            print("表 requirement_doc 已存在，跳过创建")
        else:
            # 执行 SQL 创建表
            print("开始创建 requirement_doc 表...")

            # 根据数据库类型选择不同的创建表语句
            if 'asyncpg' in db_type.lower():
                # PostgreSQL 版本
                await conn.execute_script('''
                    CREATE TABLE IF NOT EXISTS "requirement_doc" (
                        "id" SERIAL PRIMARY KEY NOT NULL,
                        "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        "content" TEXT NOT NULL,
                        "name" VARCHAR(255),
                        "url" VARCHAR(500),
                        "handler" VARCHAR(100),
                        "developer" VARCHAR(100),
                        "tester" VARCHAR(100),
                        "project_id" INTEGER,
                        "user_id" INTEGER,
                        "source_type" VARCHAR(50),
                        "status" VARCHAR(20)
                    );
                    CREATE INDEX IF NOT EXISTS "idx_requirementdoc_project_id" ON "requirement_doc" ("project_id");
                    CREATE INDEX IF NOT EXISTS "idx_requirementdoc_user_id" ON "requirement_doc" ("user_id");
                    CREATE INDEX IF NOT EXISTS "idx_requirementdoc_created_at" ON "requirement_doc" ("created_at");
                    CREATE INDEX IF NOT EXISTS "idx_requirementdoc_name" ON "requirement_doc" ("name");
                    CREATE INDEX IF NOT EXISTS "idx_requirementdoc_url" ON "requirement_doc" ("url");
                ''')
                print("使用 PostgreSQL 语法创建表")
            else:
                # SQLite 版本
                await conn.execute_script('''
                    CREATE TABLE IF NOT EXISTS "requirement_doc" (
                        "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                        "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        "content" TEXT NOT NULL,
                        "name" VARCHAR(255),
                        "url" VARCHAR(500),
                        "handler" VARCHAR(100),
                        "developer" VARCHAR(100),
                        "tester" VARCHAR(100),
                        "project_id" INTEGER,
                        "user_id" INTEGER,
                        "source_type" VARCHAR(50),
                        "status" VARCHAR(20)
                    );
                    CREATE INDEX IF NOT EXISTS "idx_requirementdoc_project_id" ON "requirement_doc" ("project_id");
                    CREATE INDEX IF NOT EXISTS "idx_requirementdoc_user_id" ON "requirement_doc" ("user_id");
                    CREATE INDEX IF NOT EXISTS "idx_requirementdoc_created_at" ON "requirement_doc" ("created_at");
                    CREATE INDEX IF NOT EXISTS "idx_requirementdoc_name" ON "requirement_doc" ("name");
                    CREATE INDEX IF NOT EXISTS "idx_requirementdoc_url" ON "requirement_doc" ("url");
                ''')
                print("使用 SQLite 语法创建表")
            print("表 requirement_doc 创建成功！")
    except Exception as e:
        print(f"错误: {e}")
        print(f"错误类型: {type(e)}")
        import traceback
        traceback.print_exc()

    # 关闭连接
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(create_table())
