#!/usr/bin/env python
# -*- coding: utf-8 -*-

import asyncio
import requests
import json

async def test_tapd_search():
    """测试TAPD链接搜索功能"""
    
    # 测试URL
    base_url = "http://localhost:9999/api/v1/knowledge/"
    
    # 测试参数
    test_cases = [
        {
            "name": "普通搜索",
            "params": {"search": "高血压", "page": 1, "page_size": 10}
        },
        {
            "name": "TAPD链接搜索",
            "params": {"source_url": "https://www.tapd.cn/tapd_fe/22012671/story/detail/1122012671001005124", "page": 1, "page_size": 10}
        },
        {
            "name": "项目筛选",
            "params": {"project_id": 6, "page": 1, "page_size": 10}
        }
    ]
    
    for test_case in test_cases:
        print(f"\n=== 测试: {test_case['name']} ===")
        try:
            response = requests.get(base_url, params=test_case['params'])
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"返回数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
            else:
                print(f"错误响应: {response.text}")
                
        except Exception as e:
            print(f"请求失败: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_tapd_search())
