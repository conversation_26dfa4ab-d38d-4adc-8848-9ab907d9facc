import asyncio
from tortoise import Tortoise, connections
from app.settings.config import TORTOISE_ORM

async def check_db():
    # 初始化Tortoise ORM
    await Tortoise.init(config=TORTOISE_ORM)
    
    # 获取数据库连接
    conn = connections.get("default")
    
    # 查询remark字段信息
    result = await conn.execute_query("""
    SELECT column_name, data_type, is_nullable 
    FROM information_schema.columns 
    WHERE table_name = 'requirement' AND column_name = 'remark';
    """)
    
    print("Remark字段信息:")
    print(result)
    
    # 查询一条包含remark的记录
    result = await conn.execute_query("""
    SELECT id, name, remark 
    FROM requirement 
    WHERE remark IS NOT NULL 
    LIMIT 5;
    """)
    
    print("\n包含remark的记录:")
    print(result)
    
    # 关闭连接
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(check_db())
