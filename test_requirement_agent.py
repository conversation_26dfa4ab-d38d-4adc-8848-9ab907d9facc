import asyncio
import json
from app.api.v1.agent.requirement_agents import RequirementDatabaseAgent, TopicId, task_result_topic_type
from autogen_core import MessageContext, DefaultTopicId
from pydantic import BaseModel
from typing import List, Optional

class RequirementItem(BaseModel):
    name: str
    description: str
    category: str = "功能"
    parent: Optional[str] = None
    module: Optional[str] = None
    level: str = "高"
    reviewer: Optional[str] = None
    estimate: int = 8
    criteria: Optional[str] = None
    remark: Optional[str] = "[功能模块: 测试模块]"
    keywords: Optional[str] = None
    project_id: int = 1
    tapd_url: Optional[str] = None

class RequirementList(BaseModel):
    requirements: List[RequirementItem]

class TestMessage(BaseModel):
    source: str
    content: str
    project_id: int = 1
    auto_link_requirements: bool = False
    tapd_url: Optional[str] = None
    tapd_tester: Optional[str] = None

async def test_requirement_agent():
    # 创建测试需求
    requirements = [
        RequirementItem(
            name="测试需求1",
            description="这是一个测试需求描述",
            remark="这是一个测试备注 [功能模块: 测试模块]"
        ),
        RequirementItem(
            name="测试需求2",
            description="这是另一个测试需求描述",
            remark="这是另一个测试备注 [功能模块: 另一个测试模块]"
        )
    ]
    
    # 创建需求列表
    requirement_list = RequirementList(requirements=requirements)
    
    # 创建测试消息
    test_message = TestMessage(
        source="test",
        content=requirement_list.model_dump_json(),
        project_id=1
    )
    
    # 创建数据库智能体
    agent = RequirementDatabaseAgent()
    
    # 模拟消息上下文
    ctx = MessageContext(
        topic_id=DefaultTopicId(type="test"),
        sender_id=DefaultTopicId(type="test_sender")
    )
    
    # 添加发布消息的方法
    async def mock_publish(message, topic_id):
        print(f"发布消息到 {topic_id}:")
        print(json.dumps(message.model_dump(), indent=2, ensure_ascii=False))
    
    agent.publish_message = mock_publish
    
    # 处理消息
    await agent.handle_message(test_message, ctx)

if __name__ == "__main__":
    asyncio.run(test_requirement_agent())
