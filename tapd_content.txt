<div class="detail-item__content-wrapper">
 <div class="detail-item__parent">
  <div>
   <div class="detail-item__parent--header">
    <div class="parent-story-selector">
     <span>
      上级需求:
     </span>
     <a href="https://www.tapd.cn/tapd_fe/22012671/story/detail/1122012671001004842" target="_blank">
      Topic：售后
     </a>
    </div>
   </div>
   <div class="detail-item__parent--expand" parent-id="1122012671001004842">
    展开详情
    <i class="tapd-icon-arrow-down-v2">
    </i>
   </div>
  </div>
  <!-- -->
  <!-- -->
 </div>
 <div class="content-wrap">
  <div class="cherry-editor-content tex2jax_ignore" tabindex="-1">
   <h3>
    一、功能位置
   </h3>
   <p>
    共同照护web端 - 平台运营工作台 - 糖友商城 - 商品规格编码管理。
   </p>
   <p class="tox-clear-float">
    <img alt="需求文档图片" class="compress gallery" disable-lazyload="true" href="https://file.tapd.cn/tfl/captures/2025-04/tapd_22012671_base64_1744348664_417.png" original_src="https://file.tapd.cn//tfl/captures/2025-04/tapd_22012671_base64_1744348664_417.png" src="https://file.tapd.cn/compress/compress_img/1400/tapd_22012671_base64_1744348664_417.png?src=/tfl/captures/2025-04/tapd_22012671_base64_1744348664_417.png" style="max-width:600px;height:348px;" title="点击看原图"/>
   </p>
   <p class="tox-clear-float">
    <br/>
   </p>
   <h3 class="tox-clear-float">
    二、需求说明
   </h3>
   <h4 class="tox-clear-float">
    （一）状态联动
   </h4>
   <p>
    商品新增了“删除”功能，编码管理里的商品规格需保持同步，如果已经删除的商品，则同样不再提供编码管理的商品规格。
   </p>
   <h4 class="tox-clear-float">
    （二）增加字段
   </h4>
   <p>
    在〔弹窗修改〕、〔Excel导入〕和〔页面展示〕上，均增加〔SAP名称〕、〔成本价〕和〔二次成本价〕三个字段。
   </p>
   <h4 class="tox-clear-float">
    （三）字段说明
   </h4>
   <div class="tox-clear-float">
    【SAP名称】。
   </div>
   <div class="tox-clear-float">
    顺序排在〔SAP编码〕之后，〔云仓编码〕之前，且与〔发货名称〕规则保持一致，录入后，后期其它表格会引用该值。
   </div>
   <div class="tox-clear-float">
    <div class="scroll_table">
     <table border="1" style="border-collapse: collapse; border-width: 1px; border-color: #ced4d9; width: 659px;">
      <tbody>
       <tr>
        <td style="width: 96px; border-width: 1px; border-color: #ced4d9;">
         类型
        </td>
        <td style="width: 544px; border-width: 1px; border-color: #ced4d9;">
         截图
        </td>
       </tr>
       <tr>
        <td style="width: 96px; border-width: 1px; border-color: #ced4d9;">
         弹窗修改
        </td>
        <td style="width: 544px; border-width: 1px; border-color: #ced4d9;">
         <p class="tox-clear-float">
          <img alt="需求文档图片" class="compress gallery" disable-lazyload="true" href="https://file.tapd.cn/tfl/captures/2025-04/tapd_22012671_base64_1744348711_481.png" original_src="https://file.tapd.cn//tfl/captures/2025-04/tapd_22012671_base64_1744348711_481.png" src="https://file.tapd.cn/compress/compress_img/1400/tapd_22012671_base64_1744348711_481.png?src=/tfl/captures/2025-04/tapd_22012671_base64_1744348711_481.png" style="max-width:80%;" title="点击看原图"/>
         </p>
        </td>
       </tr>
       <tr>
        <td style="width: 96px; border-width: 1px; border-color: #ced4d9;">
         Excel导入
        </td>
        <td style="width: 544px; border-width: 1px; border-color: #ced4d9;">
         <p class="tox-clear-float">
          <img alt="需求文档图片" class="compress gallery" disable-lazyload="true" href="https://file.tapd.cn/tfl/captures/2025-04/tapd_22012671_base64_1744348909_650.png" original_src="https://file.tapd.cn//tfl/captures/2025-04/tapd_22012671_base64_1744348909_650.png" src="https://file.tapd.cn/compress/compress_img/1400/tapd_22012671_base64_1744348909_650.png?src=/tfl/captures/2025-04/tapd_22012671_base64_1744348909_650.png" style="max-width:80%;" title="点击看原图"/>
         </p>
         <p class="tox-clear-float">
          此处下载和导入的Excel均需支持。
         </p>
        </td>
       </tr>
       <tr>
        <td style="width: 96px; border-width: 1px; border-color: #ced4d9;">
         页面展示
        </td>
        <td style="width: 544px; border-width: 1px; border-color: #ced4d9;">
         <p class="tox-clear-float">
          <img alt="需求文档图片" class="compress gallery" disable-lazyload="true" href="https://file.tapd.cn/tfl/captures/2025-04/tapd_22012671_base64_1744357971_355.png" original_src="https://file.tapd.cn//tfl/captures/2025-04/tapd_22012671_base64_1744357971_355.png" src="https://file.tapd.cn/compress/compress_img/1400/tapd_22012671_base64_1744357971_355.png?src=/tfl/captures/2025-04/tapd_22012671_base64_1744357971_355.png" style="max-width:80%;" title="点击看原图"/>
         </p>
        </td>
       </tr>
      </tbody>
     </table>
    </div>
    <p>
     【〔成本价〕和〔二次成本价〕】。
    </p>
    <p>
     需要留版本记录，即〔在yyyy.mm.dd到yyyy.mm.dd时间范围内，是xx价格〕，影响的是，某个时间段内的商品成本价。
    </p>
    <p>
     配置时：
    </p>
    <p>
     1. 在yyyy.mm.dd hh:mm:ss时间点设置一个成本价为b，即认为，之前的该商品的成本价，均由a来计算，而之后的该商品的成本价，均由b来计算，直到出现下一个配置的时间点，则按照c来计算。
    </p>
    <p>
     2. 如果原本在①yyyy.mm.dd到②yyyy.mm.dd时间范围内，均为成本价a；在②yyyy.mm.dd到③yyyy.mm.dd时间范围内，均为成本价b；此时用户删除②这个时间点，则①yyyy.mm.dd到③yyyy.mm.dd时间范围，成本价为a。
    </p>
    <p>
     3. 因为成本价是一个连续的时间线，在没有成本价记录之前，sku商品的成本价以0元计算。
    </p>
    <p>
     4. 在功能上线之前就已经有“成本价”的商品sku，在功能上线之时，以商品sku的“创建时间”为“生效时间”，以商品sku的“成本价”为“成本价”，创建第一条数据。
    </p>
    <p>
     该问题有两个链路受影响。
    </p>
    <div class="scroll_table">
     <table border="1" style="border-collapse: collapse; border-width: 1px; border-color: #ced4d9; width: 993px;">
      <tbody>
       <tr>
        <td style="width: 88px; border-width: 1px; padding: 4px; border-color: #ced4d9;">
         功能位置
        </td>
        <td style="width: 389px; border-width: 1px; padding: 4px; border-color: #ced4d9;">
         线上截图
        </td>
        <td style="width: 239px; border-width: 1px; padding: 4px; border-color: #ced4d9;">
         产品逻辑
        </td>
        <td style="width: 240px; border-width: 1px; padding: 4px; border-color: #ced4d9;">
         字段说明
        </td>
       </tr>
       <tr>
        <td style="width: 88px; border-width: 1px; padding: 4px; border-color: #ced4d9;">
         商品配置
        </td>
        <td style="width: 389px; border-width: 1px; padding: 4px; border-color: #ced4d9;">
         <p class="tox-clear-float">
          <img alt="需求文档图片" class="compress gallery" disable-lazyload="true" href="https://file.tapd.cn/tfl/captures/2025-04/tapd_22012671_base64_1744604869_815.png" original_src="https://file.tapd.cn//tfl/captures/2025-04/tapd_22012671_base64_1744604869_815.png" src="https://file.tapd.cn/compress/compress_img/1400/tapd_22012671_base64_1744604869_815.png?src=/tfl/captures/2025-04/tapd_22012671_base64_1744604869_815.png" style="max-width:80%;" title="点击看原图"/>
         </p>
        </td>
        <td style="width: 239px; border-width: 1px; padding: 4px; border-color: #ced4d9;">
         <p>
          增加一个时间点选择控件，选择在某个时间点内，成本价为¥xx。
         </p>
         <p class="tox-clear-float">
          <img alt="需求文档图片" class="compress gallery" data-src="https://file.tapd.cn/compress/compress_img/1400/tapd_22012671_base64_1746786032_775.png?src=/tfl/captures/2025-05/tapd_22012671_base64_1746786032_775.png" href="https://file.tapd.cn/tfl/captures/2025-05/tapd_22012671_base64_1746786032_775.png" original_src="https://file.tapd.cn//tfl/captures/2025-05/tapd_22012671_base64_1746786032_775.png" src="https://file.tapd.cn/compress/compress_img/1400/tapd_22012671_base64_1746786032_775.png?src=/tfl/captures/2025-05/tapd_22012671_base64_1746786032_775.png" style="" title="点击看原图"/>
         </p>
        </td>
        <td rowspan="2" style="width: 240px; border-width: 1px; padding: 4px; border-color: #ced4d9;">
         <p>
          时间范围：
         </p>
         <p>
          <span data-metadata="&lt;!--(figmeta)eyJmaWxlS2V5IjoibmlvdDVuR0JXNTNWdEwya3A0MVZOdCIsInBhc3RlSUQiOjEwNjMyNDczMzAsImRhdGFUeXBlIjoic2NlbmUifQo=(/figmeta)--&gt;">
          </span>
          <span data-buffer="&lt;!--(figma)ZmlnLWtpd2lQAAAAKl4AALW9C5xkSVXgHffmo6q6+jU97wGG1/DwPcwMD9/mq6qyO1+TN6t6ZnyUWZW3upLOyizzZvV0s66LiIiIiIiIiIiIiIiIiIiIiIiIiIiIiIiIiCzLsnwsy7Isy7rf/5yI+8jqGvT7fb+d33TdiBMnTpw4ceLEiRNxb/55rhlGUf9C2LuyHxpz09l2vbUZ9ErdnuG/Vrta26yslVqrtYCstx7Uupm8r9i1VpV0LqivtkoNUvmgd2+jRqKgic2gJrSKiquUN4Nz9c5mt9Zol6TmQqvdq6/cuxmstdcb1c31zmq3VJX6iy65WW23JL8U57u1lW4tWAN0LKjUWrVNwJ21zbvXa917AS5ngd1apyHA49X6ygrPE5VGvdbqbZa7tF4pBcLbyQxvZ9vrXfpRE85OBb1urdS0JeRPu7zt8TX1Vq/WLVV69Q062ajDmBUNZWe6tUq71apV6GyGmZjDa48ujnm9Tvmhlc16q9KtNeG31KDU1QHjeh0Z+OqtB2mrN7i+dala62oXbixdHkaM1D2kjdD2StvbjDggulLdbLe0RaOZ8916T+h4rckg7Oz2oxA0GCj1lBZIzfaGJr3zw/FgOL7QPRgJTqvduq/WbVNg2lUtFwpWpe6ksAbIVNuVdekKSa9Sam2UAlL+are93iGRW+mWmoKXL7fbjVqptdnuIN1evd0CWNig3+0uqSKdludCo65kF2uNRr0TSHIJCfUQhSrfsW5tdb1R6m522o17V5XIMk0hmCrySfGO92r3CEsnGMGKAE4G9zbLbVHkU/UWjbUUytDXK+dEVNcEa6VObfN8vbe26eqecQOjDF5bkaEpN9qVc+SuO1+vruoEuB5aTenpDc1atV4iceNafXWtwT8pvimAgO3szS65ibC7jZI0esv5UrBW3+zRMrkHbZS69VJZ+X9wzyUeoonNCvIgd2uM4qbfQ+meTqqHlYKgHjCgm1Bur0vZw69W5FpDtY7CRySEhJsuhQAf2WxX17XV2yz+KgXkHmVz3fZ5Mo9mjnbarUCpKhOPUdFU2k3AlvpjRYKbnVJPJvNXaXFG6F+tgEa93C3ptPkaza/UteWv1QydqInUv67cXVeL8PXNUqu0SveYq/XWKpBv6HVLrWCl3W2Sub3SDDa79Uoydo9j0omCC5E7gt3+fnh+ONvthZdnVn1vDe5eL3VrlBok6zTNYwCbbbUCPs2oLmG4yOaSbLV9XgYzf5TSFTqlbqnRwAIy8ZvwY3WgOA9u1FYEulBrrW5WSwxvSRtflDyWZF0yS5JxEjmm6XaDuU9umW53ave1dWCOdzAHtRWmTHWz021XaoFMvhPoVK0h5SfjybkZIDSldioBNdcbvXpHgaeR7jq2qN7qqOpcs1a7p2Rn15nKWm2jq8lrO1Rz4OvadNsmZQYIZzd0GuvS/I2lLpoSd/Mmm4tlcXOw3mzCy+bZ9RaaqQRu0Qn2oKBTqzGA5fUyagngwaq/GG3GvN1l8IE9pDwKx4MmVkjYQec3e2uMxKqoCMtat6lLlVctdc/VhLTvOilqlhPTguUosxKQzVfajXaSK+iE1TrFANuoKTVG1Ki2mezkF22VOLskUwdNJXksaK/0UGlokFteK3WZiC6nSxR22yr/ido9FeRke35yTUf7VIDBT4ziaW2FxDWNdUTVDuo9aeJMpz8cO+2lOWYkQINGVesMC60Jq0C8BCRPlQfWmKSA0FSxnsByCQwkp/T5etOKucCKcLZOorjBxJcFYIHJLKNBcrHVrqvGLtX38DCC7f4otCOCi9Ct9So6GCt16buHDisHPavLudrOTrjterFQx7x2cRBKTCoKTbXb7qRZj7ldk3WEBbPcWBem/XKpcm4elLMTnVQeC9+s9dSkFOhdXZgtypSv6JJXbKOEdfQJsFnvsAzx9Brt85qAw55lMUCJGpuVUkeUOZ/mmIPdii6TBSFaDbcn0/5sOBlTJ14MYQyVYChIe0ijfq6WKqg/Xy2YXZFVNpeMZbWNiCXlnS9tSC/8RtiX5bg3He6RixuBmc21mtMur3WwtxVO18fDWQSxbklEZzr1e2qNgIRHN3FFBNOvTMbRbJpq0QLaBdxIucrAa5bEofBh3A1jLqjgNJHIr0CxumlrFFxGsYvBbDq5GJZGwwtjKiTEDBYf5SHhsR65pG+RK/19tD7uD/JR9fMSm+xboyGClE7kbLZ293q9ITZdhzDv9FbMpPXrCsgbBcdIJ6Bidi1eSFfbzceRX8zk7yC/lMnfSf5YJn8X+eVM/vHkj2fyTyB/olLvVrKtn7S9PTsZimSaeGFdoKZc26hJD7y44355MhmF/XF7P7SqQe/WW9YaIEaqietA2gvWy9h/Tfv3qJHIiVKp8Ncm0+FTJuNZf0R1Z30zY4vyqxT8s+s4PSt15TCtvRFOZ0OmssDaHYoyVcsoZrtJym9ODqKwcjCNJlPkwdJTwr5SYCrddsDMrXdJe7V7azKVUT1yPm6sNtVhRuKgr1eYE+TzrCY8Cjwq9QapYlOstlRZYIjZkpBaTMZPs0sNTL6Yo2MbWJHJtDmcToWTZP7p8PP0NIG5wwyzfKof4lf70a41VH6FJR+QSTXdU2NmJ0a+ox6GOdupydMLNuThd6qy08jVLu9PprPDkymHs8j6wUrrZoyJAbiK2r4XA5K56zf6VyYHs9XpcGCJ5O38yog+ZdC30y2X1un0Z7NwOqYIrHpHpwoLghoTTwf2YDbphtHwKZBORKTsqGQSPrwkpSaqNz0Ybzs99Kv1QNxEoWnYf7B0k/DUdgWh6ztj2A3azrL22Erx8CqomVWalS4zl1EWk5br1XATneOXj8kgzFmYSDIxiBhxXaJIevHShAXpb1+0w5iP+7SGab8P6SoHHqsyXr2mWfmUVE5rqaJT7SopW9H6ZbRObA5pW6EyOYCxqatXfKB6iN8NUq603hP9zGdIFZTU2YNoNty5QvYBqXRKFVz0jZrdu+VsvlzrnbfeSF7GvLI7HA0cP3nXmLEUvYRirCracGCHX002QHZ7Qf2+2mavjZ1Syc4B0Fa0o97ssG0iJyXgWKF3JtFQtIIVCVDceKnMeK3bDaainZ+KdWe1YuNZ6gA27mmLszJ14w52TO1wpz1Qxsx1mrXacSwec4wJ/gjeoeyZyXvrXR3xMi4Cz1yl0VYvJc8mZTPe7ZAvrHfwumubul3b7K63enXdoBaZntW6+GCqOQvZaptsjARnsV0+iyVm9qCOggpsqU4fpv0Mi6fZ42FglA1TWoHNTWmDVZC812wTbsHTJu3btC3IUWtNPErSeVuAHyRoBZvTjUgRLJx+9fOJsKgoFqt4xzyXKDtXuzeudozsRtvufpdJ2w6v6aAfT/LMafInbBOxAp20WfbrG1L7VG/aH9uxtz28hbWdXU9vk8WIVd5JwmAr0AWt4q0Q0eHp263jSredbHxyGVC8KOUzMLv8FDKQZP0pdtgNWpgjtpBCYlqLKciSWkoBCaVjEvKwMEdpOYXElI6nIEsJMcWAhNJJyyiDCFJM7NQcMKZ3eg5qSV4zB0uontGWHNQRvTYLi2lelwVaktdnQQnFGzCg9QqarONzI34tYTGryAK4iV1PG083hdxc60dMdTviJ4lcVdbL9QoFRkjHGY8dSibri9GzGwxqyFxMivKCNwcp2LpzsKJdN5L8QtBxG/7FVdSTuZgAlhxqAjhmUzpBmL12dizPA3vnxc4cPwRcY8cH+ESwPZ2MRtXh1JocmHZz7CssMUhYTb+ti72aiTUIB1i7WUh57Z4Oq601vhUoiP+mOW91ncXJ8yMCdzRGesF4owlOmCb9ymSEc+Plp2bJeBf442/xJ9fnT976P1S+TM67wh+/CwjsFHA/f3K7/MkrpWA22afCtqTNwHj7zpyDYJsShI3+*************************************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(/figma)--&gt;">
          </span>
          <span>
           yyyy.mm.dd。
          </span>
         </p>
         <p>
          <span>
           <br/>
          </span>
         </p>
         <p>
          <span>
           价格：
          </span>
         </p>
         <p>
          支持两位小数，数值不能大于该商品当前售价，且成本价可以为0。
         </p>
         <p>
          如果格式非两位小数，拦截提交，message提示“成本价仅支持两位小数”。
         </p>
         <p>
          如果数值大于当前售价，拦截提交，message提示“成本价需小于售价”。
         </p>
        </td>
       </tr>
       <tr>
        <td style="width: 88px; border-width: 1px; padding: 4px; border-color: #ced4d9;">
         编码管理
        </td>
        <td style="width: 389px; border-width: 1px; padding: 4px; border-color: #ced4d9;">
         <p class="tox-clear-float">
          <img alt="需求文档图片" class="compress gallery" data-src="https://file.tapd.cn/compress/compress_img/1400/tapd_22012671_base64_1744605733_792.png?src=/tfl/captures/2025-04/tapd_22012671_base64_1744605733_792.png" href="https://file.tapd.cn/tfl/captures/2025-04/tapd_22012671_base64_1744605733_792.png" original_src="https://file.tapd.cn//tfl/captures/2025-04/tapd_22012671_base64_1744605733_792.png" src="https://file.tapd.cn/compress/compress_img/1400/tapd_22012671_base64_1744605733_792.png?src=/tfl/captures/2025-04/tapd_22012671_base64_1744605733_792.png" style="max-width:80%;" title="点击看原图"/>
         </p>
        </td>
        <td style="width: 239px; border-width: 1px; padding: 4px; border-color: #ced4d9;">
         <p>
          增加成本价和二次成本价的录入功能。
         </p>
         <p>
          <br/>
         </p>
         <p>
          【成本价】
         </p>
         <p>
          成本价（只有“成本价”，“二次成本价”只有编码管理有）取〔商品配置中的“成本价格”〕的值展示，如果商品配置新增、更改或删除了成本价，则双方同步。
         </p>
         <p>
          （1）展示
         </p>
         <p class="tox-clear-float">
          <img alt="需求文档图片" class="compress gallery" data-src="https://file.tapd.cn/compress/compress_img/1400/tapd_22012671_base64_1746786039_851.png?src=/tfl/captures/2025-05/tapd_22012671_base64_1746786039_851.png" href="https://file.tapd.cn/tfl/captures/2025-05/tapd_22012671_base64_1746786039_851.png" original_src="https://file.tapd.cn//tfl/captures/2025-05/tapd_22012671_base64_1746786039_851.png" src="https://file.tapd.cn/compress/compress_img/1400/tapd_22012671_base64_1746786039_851.png?src=/tfl/captures/2025-05/tapd_22012671_base64_1746786039_851.png" style="" title="点击看原图"/>
         </p>
         <p class="tox-clear-float">
          （2）配置
         </p>
         <p class="tox-clear-float">
          无
         </p>
         <p class="tox-clear-float">
          <br/>
         </p>
         <p class="tox-clear-float">
          【二次成本价】。
         </p>
         <p class="tox-clear-float">
          “二次成本价”默认都为编码管理这边配置，需要提供“二次成本价生效时间”和“二次成本价”。
         </p>
         <p class="tox-clear-float">
          （1）展示
         </p>
         <p class="tox-clear-float">
          <img alt="需求文档图片" class="compress gallery" data-src="https://file.tapd.cn/compress/compress_img/1400/tapd_22012671_base64_1746786047_176.png?src=/tfl/captures/2025-05/tapd_22012671_base64_1746786047_176.png" href="https://file.tapd.cn/tfl/captures/2025-05/tapd_22012671_base64_1746786047_176.png" original_src="https://file.tapd.cn//tfl/captures/2025-05/tapd_22012671_base64_1746786047_176.png" src="https://file.tapd.cn/compress/compress_img/1400/tapd_22012671_base64_1746786047_176.png?src=/tfl/captures/2025-05/tapd_22012671_base64_1746786047_176.png" style="" title="点击看原图"/>
         </p>
         <p class="tox-clear-float">
          （2）配置
         </p>
         <p class="tox-clear-float">
          ① 弹窗配置。
         </p>
         <p class="tox-clear-float">
          <img alt="需求文档图片" class="compress gallery" data-src="https://file.tapd.cn/compress/compress_img/1400/tapd_22012671_base64_1744701133_123.png?src=/tfl/captures/2025-04/tapd_22012671_base64_1744701133_123.png" href="https://file.tapd.cn/tfl/captures/2025-04/tapd_22012671_base64_1744701133_123.png" original_src="https://file.tapd.cn//tfl/captures/2025-04/tapd_22012671_base64_1744701133_123.png" src="https://file.tapd.cn/compress/compress_img/1400/tapd_22012671_base64_1744701133_123.png?src=/tfl/captures/2025-04/tapd_22012671_base64_1744701133_123.png" style="max-width:240px;height:139px;" title="点击看原图"/>
         </p>
         <p class="tox-clear-float">
          ② excel配置。
         </p>
         <p>
          上传时，文件中需要提供两列，分别是“二次成本价生效时间”和“二次成本价”。
         </p>
         <p>
          录入即视为，在录入的“二次成本价生效时间”之后，都按照新的“二次成本价”来执行。
         </p>
         <p>
          “二次成本价”表格下载时“生效时间”和“二次成本价”均为空，录入时如果二者有一者为空，则不录入该条数据。
         </p>
        </td>
       </tr>
      </tbody>
     </table>
    </div>
    <p>
     <br/>
    </p>
   </div>
  </div>
  <!-- -->
 </div>
 <div class="translate-content-wrap" style="display: none;">
  <div class="cherry-editor-content tex2jax_ignore" tabindex="-1">
   <h3>
    翻译
   </h3>
   <br/>
   <h3>
    标题:
   </h3>
   <br/>
  </div>
  <!-- -->
 </div>
 <div class="view-operator-area-wrapper">
  <div class="view-operator-area">
   <ul>
    <li>
     <i class="tapd-icon-edit-v2" data-type="edit">
     </i>
    </li>
    <!-- -->
    <li>
     <i class="tapd-icon-page-fullscreen-v2">
     </i>
    </li>
   </ul>
   <span class="pack-up-button">
    <i class="tapd-icon-triangle-right-v2">
    </i>
   </span>
  </div>
 </div>
</div>
