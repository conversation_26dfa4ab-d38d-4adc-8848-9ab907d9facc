#!/usr/bin/env python
# -*- coding: utf-8 -*-

import asyncio
import logging
from tortoise import Tortoise
from app.settings.config import settings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def fix_test_steps_sequence():
    """修复test_steps表的序列问题"""
    try:
        # 初始化数据库连接
        logger.info("正在初始化数据库连接...")
        await Tortoise.init(config=settings.TORTOISE_ORM)
        logger.info("数据库连接初始化完成")

        # 获取数据库连接
        conn = Tortoise.get_connection("default")
        
        # 检查test_steps表的最大ID
        logger.info("检查test_steps表的最大ID...")
        result = await conn.execute_query('SELECT MAX(id) FROM test_steps;')
        max_id = result[1][0][0] if result[1] and result[1][0][0] else 0
        logger.info(f'test_steps表的最大ID: {max_id}')
        
        # 检查序列的当前值
        logger.info("检查test_steps_id_seq序列的当前值...")
        result = await conn.execute_query('SELECT last_value FROM test_steps_id_seq;')
        seq_value = result[1][0][0] if result[1] else 0
        logger.info(f'test_steps_id_seq序列的当前值: {seq_value}')
        
        # 如果序列值小于等于最大ID，需要重置序列
        if seq_value <= max_id:
            new_seq_value = max_id + 1
            logger.info(f"序列值需要更新，将设置为: {new_seq_value}")
            
            # 重置序列
            await conn.execute_query(f'SELECT setval(\'test_steps_id_seq\', {new_seq_value});')
            logger.info("序列重置完成")
            
            # 验证序列重置结果
            result = await conn.execute_query('SELECT last_value FROM test_steps_id_seq;')
            new_seq_value_check = result[1][0][0] if result[1] else 0
            logger.info(f'序列重置后的值: {new_seq_value_check}')
        else:
            logger.info("序列值正常，无需修复")
        
        logger.info("修复完成！")
        
    except Exception as e:
        logger.error(f"修复失败: {str(e)}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        raise
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()
        logger.info("数据库连接已关闭")

if __name__ == "__main__":
    asyncio.run(fix_test_steps_sequence())
