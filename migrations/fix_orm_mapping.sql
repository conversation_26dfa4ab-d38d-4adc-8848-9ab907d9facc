-- 检查aerich_models表中的TestCase模型定义
SELECT * FROM aerich_models WHERE app = 'models' AND name = 'TestCase';

-- 更新aerich_models表中的TestCase模型定义，修改steps字段的定义
UPDATE aerich_models
SET content = REPLACE(
    content,
    '"steps": {"model": "models.TestStep", "related_name": "test_cases", "through": null, "forward_key": null, "backward_key": null, "type": "ManyToManyField", "description": "测试步骤列表。"}',
    '"steps": {"model": "models.TestStep", "related_name": "test_cases", "through": "test_cases_test_steps", "forward_key": "test_cases_id", "backward_key": "test_step_id", "type": "ManyToManyField", "description": "测试步骤列表。"}'
)
WHERE app = 'models' AND name = 'TestCase';

-- 检查更新后的TestCase模型定义
SELECT * FROM aerich_models WHERE app = 'models' AND name = 'TestCase';
