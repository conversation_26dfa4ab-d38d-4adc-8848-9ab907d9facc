from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "knowledge_items" ADD "source_url" VARCHAR(500);
        ALTER TABLE "knowledge_items" ADD "source_reference_id" VARCHAR(100);
        CREATE INDEX IF NOT EXISTS "idx_knowledge_items_source_reference_id" ON "knowledge_items" ("source_reference_id");
    """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP INDEX IF EXISTS "idx_knowledge_items_source_reference_id";
        ALTER TABLE "knowledge_items" DROP COLUMN "source_reference_id";
        ALTER TABLE "knowledge_items" DROP COLUMN "source_url";
    """
