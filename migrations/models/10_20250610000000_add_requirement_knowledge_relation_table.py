from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "requirement_knowledge_relation" (
            "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            "requirement_id" INTEGER NOT NULL,
            "knowledge_id" INTEGER NOT NULL,
            "relevance_level" VARCHAR(20) NOT NULL DEFAULT '直接相关',
            "relevance_score" FLOAT NOT NULL DEFAULT 0.0,
            "source" VARCHAR(50) NOT NULL DEFAULT '用例生成'
        );
        CREATE INDEX IF NOT EXISTS "idx_req_knowledge_req_id" ON "requirement_knowledge_relation" ("requirement_id");
        CREATE INDEX IF NOT EXISTS "idx_req_knowledge_knowledge_id" ON "requirement_knowledge_relation" ("knowledge_id");
        CREATE INDEX IF NOT EXISTS "idx_req_knowledge_relevance" ON "requirement_knowledge_relation" ("relevance_level");
    """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "requirement_knowledge_relation";
    """
