from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "requirement_doc" (
            "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            "content" TEXT NOT NULL,
            "name" VARCHAR(255),
            "url" VARCHAR(500),
            "handler" VARCHAR(100),
            "developer" VARCHAR(100),
            "tester" VARCHAR(100),
            "project_id" INTEGER,
            "user_id" INTEGER,
            "source_type" VARCHAR(50),
            "status" VARCHAR(20)
        );
        CREATE INDEX IF NOT EXISTS "idx_requirementdoc_project_id" ON "requirement_doc" ("project_id");
        CREATE INDEX IF NOT EXISTS "idx_requirementdoc_user_id" ON "requirement_doc" ("user_id");
        CREATE INDEX IF NOT EXISTS "idx_requirementdoc_created_at" ON "requirement_doc" ("created_at");
        CREATE INDEX IF NOT EXISTS "idx_requirementdoc_name" ON "requirement_doc" ("name");
        CREATE INDEX IF NOT EXISTS "idx_requirementdoc_url" ON "requirement_doc" ("url");
    """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "requirement_doc";
    """
