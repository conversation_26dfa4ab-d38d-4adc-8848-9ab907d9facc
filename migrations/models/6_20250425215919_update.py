from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "requirement" ADD "tapd_url" VARCHAR(500);
        ALTER TABLE "test_cases" ADD "tapd_url" VARCHAR(500);
        ALTER TABLE "test_cases" ALTER COLUMN "tags" TYPE VARCHAR(5) USING "tags"::VARCHAR(5);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "requirement" DROP COLUMN "tapd_url";
        ALTER TABLE "test_cases" DROP COLUMN "tapd_url";
        ALTER TABLE "test_cases" ALTER COLUMN "tags" TYPE VARCHAR(4) USING "tags"::VARCHAR(4);"""
