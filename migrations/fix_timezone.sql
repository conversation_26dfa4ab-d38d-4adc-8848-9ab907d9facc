-- 为requirement表中的datetime字段添加时区信息
UPDATE requirement 
SET created_at = created_at AT TIME ZONE 'UTC' 
WHERE created_at IS NOT NULL AND extract(timezone from created_at) = 0;

UPDATE requirement 
SET updated_at = updated_at AT TIME ZONE 'UTC' 
WHERE updated_at IS NOT NULL AND extract(timezone from updated_at) = 0;

-- 为requirement_doc表中的datetime字段添加时区信息
UPDATE requirement_doc 
SET created_at = created_at AT TIME ZONE 'UTC' 
WHERE created_at IS NOT NULL AND extract(timezone from created_at) = 0;

UPDATE requirement_doc 
SET updated_at = updated_at AT TIME ZONE 'UTC' 
WHERE updated_at IS NOT NULL AND extract(timezone from updated_at) = 0;

-- 为其他可能包含datetime字段的表添加时区信息
-- 例如，如果有user表，可以添加类似的语句
-- UPDATE user 
-- SET created_at = created_at AT TIME ZONE 'UTC' 
-- WHERE created_at IS NOT NULL AND extract(timezone from created_at) = 0;

-- 打印修复结果
SELECT 'Timezone fix completed' AS result;
