-- 检查test_cases_test_steps表结构
\d test_cases_test_steps

-- 如果teststep_id列存在，则重命名为test_step_id
DO $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'test_cases_test_steps' AND column_name = 'teststep_id'
    ) THEN
        ALTER TABLE test_cases_test_steps RENAME COLUMN teststep_id TO test_step_id;
        RAISE NOTICE 'Column renamed from teststep_id to test_step_id';
    ELSE
        RAISE NOTICE 'Column teststep_id does not exist, no action taken';
    END IF;
END $$;
