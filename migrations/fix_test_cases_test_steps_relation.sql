-- 检查test_cases_test_steps表结构
\d test_cases_test_steps

-- 如果teststep_id列存在，则重命名为test_step_id
DO $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'test_cases_test_steps' AND column_name = 'teststep_id'
    ) THEN
        ALTER TABLE test_cases_test_steps RENAME COLUMN teststep_id TO test_step_id;
        RAISE NOTICE 'Column renamed from teststep_id to test_step_id';
    ELSE
        RAISE NOTICE 'Column teststep_id does not exist, no action taken';
    END IF;
END $$;

-- 检查是否有任何查询使用了teststep_id列
SELECT pg_get_viewdef(c.oid)
FROM pg_class c
JOIN pg_namespace n ON n.oid = c.relnamespace
WHERE c.relkind = 'v' AND n.nspname = 'public'
AND pg_get_viewdef(c.oid) LIKE '%teststep_id%';

-- 检查是否有任何函数使用了teststep_id列
SELECT p.proname, pg_get_functiondef(p.oid)
FROM pg_proc p
JOIN pg_namespace n ON n.oid = p.pronamespace
WHERE n.nspname = 'public'
AND pg_get_functiondef(p.oid) LIKE '%teststep_id%';
