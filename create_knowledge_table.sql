-- 创建知识库条目表
CREATE TABLE IF NOT EXISTS knowledge_items (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    item_type VARCHAR(50) NOT NULL DEFAULT '其他',
    tags VARCHAR(255),
    source VARCHAR(50) NOT NULL DEFAULT '手动添加',
    project_id INT NOT NULL,
    creator_id INT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_knowledge_items_project_id ON knowledge_items(project_id);
CREATE INDEX IF NOT EXISTS idx_knowledge_items_creator_id ON knowledge_items(creator_id);
CREATE INDEX IF NOT EXISTS idx_knowledge_items_item_type ON knowledge_items(item_type);
CREATE INDEX IF NOT EXISTS idx_knowledge_items_created_at ON knowledge_items(created_at);
CREATE INDEX IF NOT EXISTS idx_knowledge_items_updated_at ON knowledge_items(updated_at);

-- 添加API权限记录
DO $$
DECLARE
    api_exists INT;
BEGIN
    -- 检查API是否已存在
    SELECT COUNT(*) INTO api_exists FROM api WHERE path = '/knowledge/' AND method = 'POST';
    
    -- 如果API不存在，则创建它
    IF api_exists = 0 THEN
        INSERT INTO api (path, method, summary, tags, created_at, updated_at)
        VALUES 
            ('/knowledge/', 'GET', '获取知识条目列表', '知识库管理', NOW(), NOW()),
            ('/knowledge/{item_id}', 'GET', '获取单个知识条目详情', '知识库管理', NOW(), NOW()),
            ('/knowledge/', 'POST', '添加新的知识条目', '知识库管理', NOW(), NOW()),
            ('/knowledge/{item_id}', 'PUT', '更新指定的知识条目', '知识库管理', NOW(), NOW()),
            ('/knowledge/{item_id}', 'DELETE', '删除指定的知识条目', '知识库管理', NOW(), NOW());
        
        RAISE NOTICE '创建了知识库管理API权限记录';
    ELSE
        RAISE NOTICE '知识库管理API权限记录已存在';
    END IF;
END $$;
