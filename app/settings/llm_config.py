"""
大模型配置模块。
提供了大模型相关的配置和客户端创建功能。
"""

import os
import logging
from typing import Dict, Optional, Any

from autogen_core.models import ModelFamily
from autogen_ext.models.openai import OpenAIChatCompletionClient
from sentence_transformers import SentenceTransformer

# 配置日志
logger = logging.getLogger("llm_config")

# 从环境变量或配置中获取设置
DEFAULT_MODEL = os.environ.get("DEFAULT_LLM_MODEL", "deepseek-chat")
DEFAULT_API_BASE = os.environ.get("LLM_API_BASE", "https://api.deepseek.com/v1")
DEFAULT_API_KEY = os.environ.get("LLM_API_KEY", "sk-2cdc85223c2f4f9a976a57e3ae9ba4b9")
TIMEOUT_SECONDS = int(os.environ.get("LLM_TIMEOUT_SECONDS", "120"))
MAX_RETRIES = int(os.environ.get("LLM_MAX_RETRIES", "3"))

# 备用模型配置
DASHSCOPE_API_BASE = os.environ.get("DASHSCOPE_API_BASE", "https://dashscope.aliyuncs.com/compatible-mode/v1")
DASHSCOPE_API_KEY = os.environ.get("DASHSCOPE_API_KEY", "85477c3eb0424bb89d5421d2b28d2051")
DASHSCOPE_MODEL = os.environ.get("DASHSCOPE_MODEL", "deepseek-v3")

# 模型信息配置
DEEPSEEK_MODELS: Dict[str, Dict[str, Any]] = {
    "deepseek-chat": {
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": ModelFamily.UNKNOWN,
    },
    "deepseek-v3": {
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": ModelFamily.UNKNOWN,
    },
}

# 模型token限制
DEEPSEEK_TOKEN_LIMITS: Dict[str, int] = {
    "deepseek-chat": 128000,
    "deepseek-v3": 128000,
}

# 嵌入模型配置
EMBEDDING_MODEL = os.environ.get("EMBEDDING_MODEL", "BAAI/bge-large-zh")  # 使用large版本以获得更高的准确率
EMBEDDING_DEVICE = os.environ.get("EMBEDDING_DEVICE", "cpu")  # 可选: cpu, cuda
EMBEDDING_CACHE_DIR = os.environ.get("EMBEDDING_CACHE_DIR", "./embedding_models")  # 模型将下载到此目录
EMBEDDING_SIMILARITY_THRESHOLD = float(os.environ.get("EMBEDDING_SIMILARITY_THRESHOLD", "0.6"))  # 相似度阈值
EMBEDDING_LOCAL_ONLY = os.environ.get("EMBEDDING_LOCAL_ONLY", "false").lower() == "true"  # 是否只使用本地模型，不从网络下载


def create_model_client(
    model: Optional[str] = None,
    api_base: Optional[str] = None,
    api_key: Optional[str] = None,
    max_retries: Optional[int] = None,
) -> Optional[OpenAIChatCompletionClient]:
    """
    创建大模型客户端。

    参数:
        model: 模型名称，默认使用环境变量中的DEFAULT_LLM_MODEL
        api_base: API基础URL，默认使用环境变量中的LLM_API_BASE
        api_key: API密钥，默认使用环境变量中的LLM_API_KEY
        max_retries: 最大重试次数，默认使用环境变量中的LLM_MAX_RETRIES

    返回:
        OpenAIChatCompletionClient: 大模型客户端
    """
    model = model or DEFAULT_MODEL
    api_base = api_base or DEFAULT_API_BASE
    api_key = api_key or DEFAULT_API_KEY
    max_retries = max_retries or MAX_RETRIES

    try:
        # 获取模型信息
        model_info = DEEPSEEK_MODELS.get(model, {
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": ModelFamily.UNKNOWN,
        })

        # 创建客户端
        client = OpenAIChatCompletionClient(
            model=model,
            base_url=api_base,
            api_key=api_key,
            max_retries=max_retries,
            model_info=model_info,
        )
        logger.info(f"初始化模型客户端成功: {model}, API Base: {api_base}")
        return client
    except Exception as e:
        logger.error(f"初始化模型客户端失败: {str(e)}")
        return None


def create_dashscope_client() -> Optional[OpenAIChatCompletionClient]:
    """
    创建阿里云DashScope客户端。

    返回:
        OpenAIChatCompletionClient: 阿里云DashScope客户端
    """
    try:
        # 如果没有配置API密钥，则返回None
        if not DASHSCOPE_API_KEY:
            logger.warning("未配置DashScope API密钥，无法创建客户端")
            return None

        # 创建客户端
        client = OpenAIChatCompletionClient(
            model=DASHSCOPE_MODEL,
            base_url=DASHSCOPE_API_BASE,
            api_key=DASHSCOPE_API_KEY,
            max_retries=MAX_RETRIES,
            model_info=DEEPSEEK_MODELS.get(DASHSCOPE_MODEL, {
                "vision": False,
                "function_calling": True,
                "json_output": True,
                "family": ModelFamily.UNKNOWN,
            }),
        )
        logger.info(f"初始化DashScope客户端成功: {DASHSCOPE_MODEL}, API Base: {DASHSCOPE_API_BASE}")
        return client
    except Exception as e:
        logger.error(f"初始化DashScope客户端失败: {str(e)}")
        return None


# 嵌入模型单例
_embedding_model = None


def get_embedding_model() -> Optional[SentenceTransformer]:
    """
    获取嵌入模型实例（单例模式）

    返回:
        SentenceTransformer: 嵌入模型实例
    """
    global _embedding_model

    if _embedding_model is not None:
        return _embedding_model

    try:
        import os
        # 确保缓存目录存在
        if not os.path.exists(EMBEDDING_CACHE_DIR):
            os.makedirs(EMBEDDING_CACHE_DIR, exist_ok=True)
            logger.info(f"创建嵌入模型缓存目录: {EMBEDDING_CACHE_DIR}")

        logger.info(f"开始加载嵌入模型: {EMBEDDING_MODEL}, 这可能需要一些时间...")
        logger.info(f"模型将从缓存目录加载: {EMBEDDING_CACHE_DIR}")

        # 检查是否只使用本地模型
        if EMBEDDING_LOCAL_ONLY:
            logger.info("已启用本地模型模式，将只使用本地模型文件，不从网络下载")

            # 检查本地模型文件是否存在
            model_dir = os.path.join(EMBEDDING_CACHE_DIR, f"models--{EMBEDDING_MODEL.replace('/', '--')}")
            if not os.path.exists(model_dir):
                logger.error(f"本地模型目录不存在: {model_dir}")
                logger.error("请确保已手动下载模型文件到正确的目录")
                return None

            logger.info(f"找到本地模型目录: {model_dir}")

        # 创建嵌入模型实例
        from transformers.utils import logging as transformers_logging
        # 如果只使用本地模型，禁用transformers的下载警告
        if EMBEDDING_LOCAL_ONLY:
            transformers_logging.set_verbosity_error()

        _embedding_model = SentenceTransformer(
            EMBEDDING_MODEL,
            device=EMBEDDING_DEVICE,
            cache_folder=EMBEDDING_CACHE_DIR
        )

        # 获取模型信息
        model_size_mb = sum(p.numel() * 4 / (1024 * 1024) for p in _embedding_model.parameters())
        logger.info(f"嵌入模型加载成功: {EMBEDDING_MODEL}")
        logger.info(f"模型大小: {model_size_mb:.2f} MB, 设备: {EMBEDDING_DEVICE}")
        logger.info(f"模型维度: {_embedding_model.get_sentence_embedding_dimension()}")

        # 测试模型
        test_embedding = _embedding_model.encode("测试嵌入模型是否正常工作")
        logger.info(f"模型测试成功，嵌入维度: {len(test_embedding)}")

        return _embedding_model
    except ImportError as e:
        logger.error(f"导入sentence_transformers库失败，请确保已安装: pip install sentence-transformers")
        logger.error(f"错误详情: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"初始化嵌入模型失败: {str(e)}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return None


# 默认客户端
default_client = create_model_client()

# 备用客户端
dashscope_client = create_dashscope_client() if DASHSCOPE_API_KEY else None
