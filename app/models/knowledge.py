from tortoise import fields
from enum import Enum
from app.models.base import BaseModel, TimestampMixin


class KnowledgeItemType(str, Enum):
    """知识条目类型枚举"""
    BUSINESS_RULE = "业务规则"
    CORE_FEATURE = "核心功能"
    TERMINOLOGY = "术语解释"
    TEST_POINT = "测试要点"
    DEFECT_SUMMARY = "历史缺陷总结"
    NON_FUNCTIONAL = "非功能要求"
    ENV_CONFIG = "环境配置"
    OTHER = "其他"


class KnowledgeSource(str, Enum):
    """知识条目来源枚举"""
    MANUAL = "手动添加"
    REQUIREMENT = "需求分析导入"
    TESTCASE = "测试用例导入"
    DEFECT = "缺陷导入"
    OTHER = "其他来源"


class KnowledgeItem(BaseModel, TimestampMixin):
    """
    知识库条目模型，用于存储项目相关知识。
    """
    title = fields.CharField(max_length=255, null=False, description="知识条目标题")
    content = fields.TextField(null=False, description="知识条目的详细内容")
    item_type = fields.CharEnumField(KnowledgeItemType, default=KnowledgeItemType.OTHER, description="条目类型/分类", index=True)
    tags = fields.CharField(max_length=255, null=True, description="标签或关键词，用逗号分隔")
    source = fields.CharEnumField(KnowledgeSource, default=KnowledgeSource.MANUAL, description="来源", index=True)
    project_id = fields.IntField(null=False, index=True, description="关联项目ID")
    creator_id = fields.IntField(null=True, index=True, description="创建者用户ID")
    source_url = fields.CharField(max_length=500, null=True, description="源TAPD需求链接")
    source_reference_id = fields.CharField(max_length=100, null=True, index=True, description="源需求引用ID")

    class Meta:
        table = "knowledge_items"

    async def to_dict(self, m2m: bool = False, exclude_fields: list[str] | None = None):
        """
        重写to_dict方法，确保枚举类型被转换为字符串
        """
        # 调用父类的to_dict方法
        result = await super().to_dict(m2m, exclude_fields)

        # 确保枚举类型被转换为字符串
        if 'item_type' in result and hasattr(result['item_type'], 'value'):
            result['item_type'] = result['item_type'].value

        if 'source' in result and hasattr(result['source'], 'value'):
            result['source'] = result['source'].value

        return result
