import asyncio  # 用于异步操作
from datetime import datetime  # 用于处理日期和时间
import pytz  # 用于处理时区

from tortoise import fields, models  # Tortoise ORM 提供的字段和模型基类
from app.settings import settings  # 项目配置文件，包含 DATETIME_FORMAT 等设置

# 获取带时区的当前时间
def get_now_with_timezone():
    """获取带UTC时区的当前时间，统一整个应用的时区处理"""
    return datetime.now(pytz.UTC)

# 辅助函数，确保datetime对象有时区信息
def ensure_timezone(dt):
    """确保datetime对象有时区信息，如果没有则添加UTC时区"""
    if dt is None:
        return None
    if dt.tzinfo is None:
        return dt.replace(tzinfo=pytz.UTC)
    return dt

# 辅助函数，确保字典中所有datetime对象都有时区信息
def ensure_all_datetimes_have_timezone(obj_dict):
    """确保字典中所有datetime对象都有时区信息"""
    if not obj_dict or not isinstance(obj_dict, dict):
        return obj_dict

    for key, value in list(obj_dict.items()):
        if isinstance(value, datetime):
            if value.tzinfo is None:
                obj_dict[key] = value.replace(tzinfo=pytz.UTC)
        elif isinstance(value, str) and ('date' in key.lower() or 'time' in key.lower() or 'at' in key.lower()):
            try:
                dt = datetime.fromisoformat(value)
                if dt.tzinfo is None:
                    obj_dict[key] = dt.replace(tzinfo=pytz.UTC)
                else:
                    obj_dict[key] = dt
            except (ValueError, TypeError):
                pass
    return obj_dict


class BaseModel(models.Model):
    """
    基础模型类，提供通用字段和序列化方法。
    """
    id = fields.BigIntField(pk=True, index=True)  # 主键字段，使用大整数类型，并添加索引

    async def save(self, *args, **kwargs):
        """
        重写save方法，确保所有datetime字段都有时区信息
        """
        print(f"\n\n===== BaseModel.save 开始 ({self.__class__.__name__}) =====")

        # 初始化 update_fields 列表
        if 'update_fields' not in kwargs:
            kwargs['update_fields'] = []

        # 获取所有字段
        for field_name in dir(self):
            if not field_name.startswith('_') and not callable(getattr(self, field_name)):
                field_value = getattr(self, field_name)
                if isinstance(field_value, datetime):
                    print(f"字段名: {field_name}, 值: {field_value}, 时区: {field_value.tzinfo}")
                    # 如果没有时区信息，添加时区信息
                    if field_value.tzinfo is None:
                        new_value = ensure_timezone(field_value)
                        setattr(self, field_name, new_value)
                        print(f"  - 已添加时区信息: {new_value.tzinfo}")
                        # 添加到 update_fields
                        if field_name not in kwargs['update_fields']:
                            kwargs['update_fields'].append(field_name)

        # 特殊处理 _meta.fields 中的 datetime 字段
        for field_name, field_obj in self._meta.fields_map.items():
            if isinstance(field_obj, fields.DatetimeField):
                field_value = getattr(self, field_name, None)
                if field_value is not None and isinstance(field_value, datetime):
                    if field_value.tzinfo is None:
                        new_value = ensure_timezone(field_value)
                        setattr(self, field_name, new_value)
                        print(f"从_meta.fields处理字段: {field_name}, 添加时区信息: {new_value.tzinfo}")
                        # 添加到 update_fields
                        if field_name not in kwargs['update_fields']:
                            kwargs['update_fields'].append(field_name)

        # 如果 update_fields 为空，则不使用它
        if not kwargs['update_fields']:
            del kwargs['update_fields']

        # 调用父类的save方法
        print("调用父类的save方法...")
        try:
            result = await super().save(*args, **kwargs)
            print("保存成功!")
            print(f"===== BaseModel.save 结束 ({self.__class__.__name__}) =====\n\n")
            return result
        except Exception as e:
            print(f"保存失败: {str(e)}")
            print(f"错误类型: {type(e)}")
            import traceback
            print(f"错误堆栈: {traceback.format_exc()}")
            print(f"===== BaseModel.save 出错 ({self.__class__.__name__}) =====\n\n")
            raise

    async def to_dict(self, m2m: bool = False, exclude_fields: list[str] | None = None):
        """
        将模型对象转换为字典。

        参数:
            m2m (bool): 是否包含多对多字段，默认为 False。
            exclude_fields (list[str]): 需要排除的字段列表，默认为空。

        返回:
            dict: 模型对象的字典表示。
        """
        if exclude_fields is None:  # 如果未提供 exclude_fields，则初始化为空列表
            exclude_fields = []

        d = {}  # 初始化结果字典
        for field in self._meta.db_fields:  # 遍历数据库字段
            if field not in exclude_fields:  # 如果字段不在排除列表中
                value = getattr(self, field)  # 获取字段值
                if isinstance(value, datetime):  # 如果值是 datetime 类型
                    # 确保有时区信息
                    if value.tzinfo is None:
                        value = ensure_timezone(value)
                    value = value.strftime(settings.DATETIME_FORMAT)  # 格式化为指定的时间格式
                d[field] = value  # 将字段名和值添加到结果字典中

        if m2m:  # 如果需要包含多对多字段
            tasks = [  # 创建异步任务列表，用于获取所有多对多字段的值
                self.__fetch_m2m_field(field, exclude_fields)
                for field in self._meta.m2m_fields
                if field not in exclude_fields
            ]
            results = await asyncio.gather(*tasks)  # 并行执行所有任务
            for field, values in results:  # 遍历任务结果
                d[field] = values  # 将多对多字段及其值添加到结果字典中

        return d  # 返回结果字典

    async def __fetch_m2m_field(self, field, exclude_fields):
        """
        异步获取多对多字段的值，并格式化为字典。

        参数:
            field (str): 多对多字段名。
            exclude_fields (list[str]): 需要排除的字段列表。

        返回:
            tuple: 包含字段名和格式化后的值的元组。
        """
        values = await getattr(self, field).all().values()  # 获取多对多字段的所有值
        formatted_values = []  # 初始化格式化后的值列表
        for value in values:  # 遍历每个值
            formatted_value = {}  # 初始化单个值的字典
            for k, v in value.items():  # 遍历值中的键值对
                if k not in exclude_fields:  # 如果键不在排除列表中
                    if isinstance(v, datetime):  # 如果值是 datetime 类型
                        formatted_value[k] = v.strftime(settings.DATETIME_FORMAT)  # 格式化为指定的时间格式
                    else:
                        formatted_value[k] = v  # 否则直接添加到字典中
            formatted_values.append(formatted_value)  # 将格式化后的值添加到列表中

        return field, formatted_values  # 返回字段名和格式化后的值

    class Meta:
        abstract = True  # 声明这是一个抽象类，不能直接实例化


class UUIDModel:
    """
    UUID 模型类，提供一个唯一的 UUID 字段。
    """
    uuid = fields.UUIDField(unique=True, pk=False, index=True)  # UUID 字段，唯一且添加索引


class TimestampMixin:
    """
    时间戳混入类，提供创建时间和更新时间字段。
    """
    # 使用自定义字段，不使用auto_now_add和auto_now，以便我们可以手动控制时区
    # 注意：数据库表中这些字段被定义为NOT NULL，所以这里不能设置为null=True
    # 将字段设置为NOT NULL，确保数据库约束一致
    created_at = fields.DatetimeField(null=False, index=True)  # 创建时间字段，并添加索引
    updated_at = fields.DatetimeField(null=False, index=True)  # 更新时间字段，并添加索引

    async def save(self, *args, **kwargs):
        """重写save方法，确保datetime字段有时区信息"""
        print(f"\n\n===== TimestampMixin.save 开始 ({self.__class__.__name__}) =====")

        # 初始化 update_fields 列表
        if 'update_fields' not in kwargs:
            kwargs['update_fields'] = []

        # 手动设置当前时间（带时区）
        now = get_now_with_timezone()
        print(f"当前时间: {now}, 时区: {now.tzinfo}")

        # 确保updated_at有值且有时区信息
        if not hasattr(self, 'updated_at') or self.updated_at is None:
            self.updated_at = now
            print(f"手动设置 updated_at: {now}, 时区: {now.tzinfo}")
        elif self.updated_at.tzinfo is None:
            self.updated_at = self.updated_at.replace(tzinfo=pytz.UTC)
            print(f"为 updated_at 添加时区信息: {self.updated_at.tzinfo}")

        if 'updated_at' not in kwargs['update_fields']:
            kwargs['update_fields'].append('updated_at')

        # 确保created_at有值且有时区信息
        if not hasattr(self, 'created_at') or self.created_at is None:
            self.created_at = now
            print(f"手动设置 created_at: {now}, 时区: {now.tzinfo}")
        elif self.created_at.tzinfo is None:
            self.created_at = self.created_at.replace(tzinfo=pytz.UTC)
            print(f"为 created_at 添加时区信息: {self.created_at.tzinfo}")

        if 'created_at' not in kwargs['update_fields']:
            kwargs['update_fields'].append('created_at')

        # 如果 update_fields 为空，则不使用它
        if not kwargs['update_fields']:
            del kwargs['update_fields']

        # 调用父类的save方法
        print("调用父类的save方法...")
        try:
            result = await super().save(*args, **kwargs)
            print("保存成功!")
            print(f"===== TimestampMixin.save 结束 ({self.__class__.__name__}) =====\n\n")
            return result
        except Exception as e:
            print(f"保存失败: {str(e)}")
            print(f"错误类型: {type(e)}")
            import traceback
            print(f"错误堆栈: {traceback.format_exc()}")
            print(f"===== TimestampMixin.save 出错 ({self.__class__.__name__}) =====\n\n")
            raise