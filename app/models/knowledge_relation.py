from tortoise import fields
from enum import Enum
from app.models.base import BaseModel, TimestampMixin


class RelevanceLevel(str, Enum):
    """知识点相关性级别枚举"""
    DIRECT = "直接相关"
    INDIRECT = "间接相关"
    BACKGROUND = "背景相关"


class RelationSource(str, Enum):
    """关联关系来源枚举"""
    TESTCASE_GENERATION = "用例生成"
    REQUIREMENT_ANALYSIS = "需求分析"
    MANUAL = "手动添加"


class RequirementKnowledgeRelation(BaseModel, TimestampMixin):
    """
    需求与知识点关联关系模型，用于存储需求和知识点之间的关联。
    """
    requirement_id = fields.IntField(null=False, index=True, description="关联需求ID")
    knowledge_id = fields.IntField(null=False, index=True, description="关联知识点ID")
    relevance_level = fields.CharEnumField(RelevanceLevel, default=RelevanceLevel.DIRECT, description="相关性级别", index=True)
    relevance_score = fields.FloatField(default=0.0, description="相关性分数")
    source = fields.CharEnumField(RelationSource, default=RelationSource.TESTCASE_GENERATION, description="关联关系来源")

    class Meta:
        table = "requirement_knowledge_relation"
