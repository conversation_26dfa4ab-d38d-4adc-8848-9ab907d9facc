from typing import Optional, Dict, Any, <PERSON>, Tuple, List
from tortoise.expressions import Q
from tortoise.transactions import atomic

from app.core.crud import CRUDBase
from app.models.knowledge import KnowledgeItem
from app.schemas.knowledge import KnowledgeItemCreate, KnowledgeItemUpdate


class KnowledgeController(CRUDBase[KnowledgeItem, KnowledgeItemCreate, KnowledgeItemUpdate]):
    """
    知识库条目控制器类，用于管理知识库条目数据。
    """
    def __init__(self):
        super().__init__(model=KnowledgeItem)

    async def get_by_project(self, project_id: int, page: int = 1, page_size: int = 10) -> Tu<PERSON>[int, List[KnowledgeItem]]:
        """
        根据项目ID获取知识库条目列表。

        参数:
            project_id (int): 项目ID
            page (int): 页码，默认为1
            page_size (int): 每页数量，默认为10

        返回:
            Tuple[int, List[KnowledgeItem]]: 总数和知识库条目列表
        """
        total = await self.model.filter(project_id=project_id).count()
        items = await self.model.filter(project_id=project_id).offset((page - 1) * page_size).limit(page_size).order_by("-created_at")
        return total, items

    async def search(self,
                    project_id: Optional[int] = None,
                    search_term: Optional[str] = None,
                    item_type: Optional[str] = None,
                    page: int = 1,
                    page_size: int = 10) -> Tuple[int, List[KnowledgeItem]]:
        """
        搜索知识库条目。

        参数:
            project_id (Optional[int]): 项目ID，可选
            search_term (Optional[str]): 搜索关键词，可选
            item_type (Optional[str]): 条目类型，可选
            page (int): 页码，默认为1
            page_size (int): 每页数量，默认为10

        返回:
            Tuple[int, List[KnowledgeItem]]: 总数和知识库条目列表
        """
        query = Q()

        if project_id is not None:
            query &= Q(project_id=project_id)

        if search_term:
            query &= (
                Q(title__icontains=search_term) |
                Q(content__icontains=search_term) |
                Q(tags__icontains=search_term)
            )

        if item_type:
            query &= Q(item_type=item_type)

        total = await self.model.filter(query).count()
        items = await self.model.filter(query).offset((page - 1) * page_size).limit(page_size).order_by("-created_at")

        return total, items

    async def get_by_source_url(self, source_url: str) -> List[KnowledgeItem]:
        """
        根据源URL获取知识点列表。

        参数:
            source_url (str): 源TAPD需求链接

        返回:
            List[KnowledgeItem]: 知识点列表
        """
        return await self.model.filter(source_url=source_url).all()

    async def get_by_source_reference_id(self, source_reference_id: str) -> List[KnowledgeItem]:
        """
        根据源引用ID获取知识点列表。

        参数:
            source_reference_id (str): 源需求引用ID

        返回:
            List[KnowledgeItem]: 知识点列表
        """
        return await self.model.filter(source_reference_id=source_reference_id).all()

    @atomic()
    async def delete_by_source_url(self, source_url: str) -> int:
        """
        根据源URL删除知识点。

        参数:
            source_url (str): 源TAPD需求链接

        返回:
            int: 删除的知识点数量
        """
        knowledge_items = await self.get_by_source_url(source_url)
        count = len(knowledge_items)
        if count > 0:
            await self.model.filter(source_url=source_url).delete()
        return count

    @atomic()
    async def delete_by_source_reference_id(self, source_reference_id: str) -> int:
        """
        根据源引用ID删除知识点。

        参数:
            source_reference_id (str): 源需求引用ID

        返回:
            int: 删除的知识点数量
        """
        knowledge_items = await self.get_by_source_reference_id(source_reference_id)
        count = len(knowledge_items)
        if count > 0:
            await self.model.filter(source_reference_id=source_reference_id).delete()
        return count

    @atomic()
    async def create_with_creator(self, obj_in: KnowledgeItemCreate, creator_id: int) -> KnowledgeItem:
        """
        创建知识库条目，并设置创建者ID。

        参数:
            obj_in (KnowledgeItemCreate): 创建数据
            creator_id (int): 创建者ID

        返回:
            KnowledgeItem: 创建的知识库条目
        """
        import logging
        from datetime import datetime
        import pytz
        from app.models.base import get_now_with_timezone

        logger = logging.getLogger(__name__)
        logger.info("开始创建知识条目")

        try:
            # 获取当前时间（带时区）
            now = get_now_with_timezone()
            logger.info(f"当前时间: {now}, 时区: {now.tzinfo}")

            # 转换输入模型为字典
            obj_dict = obj_in.model_dump()
            obj_dict["creator_id"] = creator_id

            # 确保时间戳字段有值
            obj_dict["created_at"] = now
            obj_dict["updated_at"] = now

            logger.info(f"创建知识条目数据: {obj_dict}")

            # 创建模型实例
            db_obj = self.model(**obj_dict)

            # 确保时间戳字段有值（双重保险）
            db_obj.created_at = now
            db_obj.updated_at = now

            logger.info(f"保存前检查时间戳: created_at={db_obj.created_at}, updated_at={db_obj.updated_at}")

            # 保存到数据库
            await db_obj.save()
            logger.info(f"知识条目创建成功: ID={db_obj.id}")

            return db_obj
        except Exception as e:
            logger.error(f"创建知识条目失败: {str(e)}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            raise


# 实例化知识库条目控制器
knowledge_controller = KnowledgeController()
