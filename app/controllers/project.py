from tortoise.expressions import Q
from tortoise.transactions import atomic
from app.core.crud import CRUDBase
from app.models.admin import Project
from app.schemas.projects import *
from datetime import datetime
import pytz
from app.models.base import get_now_with_timezone
from tortoise import connections


class ProjectController(CRUDBase[Project, ProjectCreate, ProjectUpdate]):
    """
    项目控制器类，用于管理项目数据。
    """

    def __init__(self):
        super().__init__(model=Project)

    async def is_exist(self, name: str) -> bool:
        """
        检查项目名称是否已存在。
        参数:
            name (str): 项目名称
        返回:
            bool: 如果项目名称存在则返回 True，否则返回 False
        """
        return await self.model.filter(name=name).exists()

    @atomic()
    async def create_project(self, obj_in: ProjectCreate):
        """
        创建新项目。
        参数:
            obj_in (ProjectCreate): 项目创建模型
        """
        # 使用原始SQL语句创建项目，绕过ORM层的时区处理问题
        conn = connections.get("default")

        # 使用SQL语句插入数据，让数据库自动处理时间戳
        # 注意：desc是PostgreSQL的保留字，需要用双引号括起来

        # 获取下一个可用的ID
        max_id_query = "SELECT MAX(id) FROM project"
        max_id_result = await conn.execute_query(max_id_query)
        next_id = 1
        if max_id_result[1] and max_id_result[1][0][0] is not None:
            next_id = max_id_result[1][0][0] + 1

        print(f"下一个可用ID: {next_id}")

        # 在SQL语句中包含id列
        query = """
        INSERT INTO project (id, name, "desc", created_at, updated_at)
        VALUES ($1, $2, $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id, name, "desc"
        """

        result = await conn.execute_query(query, [next_id, obj_in.name, obj_in.desc])

        if result[1]:
            # 从结果中获取数据
            row = result[1][0]
            # 创建Project对象
            project = Project(
                id=row[0],
                name=row[1],
                desc=row[2]
            )
            return project
        else:
            raise Exception("Failed to create project")

    async def get_project_list(self, name_contains: str = ""):
        """
        获取项目列表。
        参数:
            name_contains (str): 项目名称（可选），用于模糊过滤项目
        返回:
            list: 项目列表
        """
        q = Q()
        if name_contains:
            q &= Q(name__contains=name_contains)
        return await self.model.filter(q).all()

    async def get_project_by_name(self, name: str):
        """
        根据项目名称获取项目详情。
        参数:
            name (str): 项目名称
        """
        return await self.get(name=name)

    @atomic()
    async def update_project(self, obj_in: ProjectUpdate):
        """
        更新项目信息。
        参数:
            name (str): 项目名称（主键）
            obj_in (ProjectUpdate): 项目更新模型
        """
        project_obj = await self.get(id=obj_in.id)
        project_obj.update_from_dict(obj_in.model_dump(exclude_unset=True))
        await project_obj.save()

    @atomic()
    async def delete_project(self, proj_id: int):
        """
        删除项目。
        参数:
            proj_id (int): 项目 ID（主键）
        """
        await self.remove(id=proj_id)


# 实例化项目控制器
project_controller = ProjectController()