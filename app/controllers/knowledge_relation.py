import logging
from typing import List, Dict, Any, Optional
from tortoise.expressions import Q
from tortoise.transactions import atomic

from app.core.crud import CRUDBase
from app.models.knowledge_relation import RequirementKnowledgeRelation, RelevanceLevel
from app.schemas.knowledge_relation import RequirementKnowledgeRelationCreate, RequirementKnowledgeRelationUpdate
from app.controllers.knowledge import knowledge_controller
from app.controllers.requirement import requirement_controller

logger = logging.getLogger(__name__)


class KnowledgeRelationController(CRUDBase[RequirementKnowledgeRelation, RequirementKnowledgeRelationCreate, RequirementKnowledgeRelationUpdate]):
    """
    需求与知识点关联关系控制器类，用于管理需求与知识点之间的关联关系。
    """

    def __init__(self):
        super().__init__(model=RequirementKnowledgeRelation)

    async def create_relation(self, obj_in: RequirementKnowledgeRelationCreate) -> RequirementKnowledgeRelation:
        """
        创建需求与知识点的关联关系。
        如果关联关系已存在，则更新相关性级别和分数。
        使用原始SQL查询而不是ORM，避免ORM连接问题。
        """
        try:
            logger.info(f"创建需求与知识点关联关系2: {obj_in}")

            # 使用原始SQL查询
            from tortoise import Tortoise
            try:
                # 尝试初始化Tortoise
                from app.settings.config import settings
                await Tortoise.init(config=settings.TORTOISE_ORM)

                # 获取数据库连接
                conn = Tortoise.get_connection("default")

                # 检查关联关系是否已存在
                query = """
                SELECT id, relevance_level, relevance_score, source
                FROM requirement_knowledge_relation
                WHERE requirement_id = $1 AND knowledge_id = $2
                """
                result = await conn.execute_query(query, [obj_in.requirement_id, obj_in.knowledge_id])

                if result and len(result) > 1 and result[1]:
                    # 关系已存在，更新相关性级别和分数
                    existing_id = result[1][0][0]
                    update_query = """
                    UPDATE requirement_knowledge_relation
                    SET relevance_level = $1, relevance_score = $2, source = $3, updated_at = CURRENT_TIMESTAMP
                    WHERE id = $4
                    RETURNING id, requirement_id, knowledge_id, relevance_level, relevance_score, source, created_at, updated_at
                    """
                    update_result = await conn.execute_query(
                        update_query,
                        [
                            obj_in.relevance_level.value if hasattr(obj_in.relevance_level, 'value') else obj_in.relevance_level,
                            obj_in.relevance_score,
                            obj_in.source.value if hasattr(obj_in.source, 'value') else obj_in.source,
                            existing_id
                        ]
                    )

                    if update_result and len(update_result) > 1 and update_result[1]:
                        # 构建返回对象
                        row = update_result[1][0]
                        relation = {
                            "id": row[0],
                            "requirement_id": row[1],
                            "knowledge_id": row[2],
                            "relevance_level": row[3],
                            "relevance_score": row[4],
                            "source": row[5],
                            "created_at": row[6],
                            "updated_at": row[7]
                        }
                        return relation
                else:
                    # 创建新的关联关系
                    insert_query = """
                    INSERT INTO requirement_knowledge_relation
                    (requirement_id, knowledge_id, relevance_level, relevance_score, source, created_at, updated_at)
                    VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    RETURNING id, requirement_id, knowledge_id, relevance_level, relevance_score, source, created_at, updated_at
                    """
                    insert_result = await conn.execute_query(
                        insert_query,
                        [
                            obj_in.requirement_id,
                            obj_in.knowledge_id,
                            obj_in.relevance_level.value if hasattr(obj_in.relevance_level, 'value') else obj_in.relevance_level,
                            obj_in.relevance_score,
                            obj_in.source.value if hasattr(obj_in.source, 'value') else obj_in.source
                        ]
                    )

                    if insert_result and len(insert_result) > 1 and insert_result[1]:
                        # 构建返回对象
                        row = insert_result[1][0]
                        relation = {
                            "id": row[0],
                            "requirement_id": row[1],
                            "knowledge_id": row[2],
                            "relevance_level": row[3],
                            "relevance_score": row[4],
                            "source": row[5],
                            "created_at": row[6],
                            "updated_at": row[7]
                        }
                        return relation

                # 如果执行到这里，说明没有成功创建或更新关系
                return None

            except Exception as db_err:
                logger.error(f"数据库操作失败: {str(db_err)}")
                import traceback
                logger.error(f"数据库操作错误堆栈: {traceback.format_exc()}")
                raise

        except Exception as e:
            logger.error(f"创建需求与知识点关联关系失败: {str(e)}")
            raise

    async def batch_create_relations(self, relations: List[RequirementKnowledgeRelationCreate]) -> List[RequirementKnowledgeRelation]:
        """
        批量创建需求与知识点的关联关系。
        使用原始SQL查询而不是ORM，避免ORM连接问题。
        """
        logger.info(f"批量创建需求与知识点关联关系: {relations}")
        try:
            created_relations = []
            for relation in relations:
                logger.info(f"创建需求与知识点关联关系1: {relation}")
                created_relation = await self.create_relation(relation)
                if created_relation:
                    created_relations.append(created_relation)
            return created_relations
        except Exception as e:
            logger.error(f"批量创建需求与知识点关联关系失败: {str(e)}")
            raise

    async def get_knowledge_by_requirement(self, requirement_id: int) -> Dict[str, List]:
        """
        获取与需求相关的所有知识点，按相关性级别分组。
        使用原始SQL查询而不是ORM，避免ORM连接问题。
        """
        try:
            # 初始化结果
            result = {
                "direct": [],
                "indirect": [],
                "background": [],
                "all_knowledge_points": []
            }

            # 使用原始SQL查询
            from tortoise import Tortoise
            try:
                # 尝试初始化Tortoise
                from app.settings.config import settings
                await Tortoise.init(config=settings.TORTOISE_ORM)

                # 获取数据库连接
                conn = Tortoise.get_connection("default")

                # 执行SQL查询，联接requirement_knowledge_relation和knowledge_items表
                query = """
                SELECT
                    r.id as relation_id,
                    r.requirement_id,
                    r.knowledge_id,
                    r.relevance_level,
                    r.relevance_score,
                    r.source,
                    k.id as knowledge_id,
                    k.title,
                    k.content,
                    k.item_type,
                    k.tags,
                    k.source as knowledge_source
                FROM
                    requirement_knowledge_relation r
                JOIN
                    knowledge_items k ON r.knowledge_id = k.id
                WHERE
                    r.requirement_id = $1
                ORDER BY
                    r.relevance_score DESC
                """

                # 执行查询
                query_result = await conn.execute_query(query, [requirement_id])

                # 处理查询结果
                if query_result and len(query_result) > 1 and query_result[1]:
                    rows = query_result[1]  # 查询结果的第二个元素是行数据

                    # 按相关性级别分组并添加索引
                    index = 1
                    for row in rows:
                        # 构建带有关联信息的知识点
                        knowledge_with_relation = {
                            "id": row[6],  # knowledge_id
                            "title": row[7],  # title
                            "content": row[8],  # content
                            "item_type": row[9],  # 修改 type 为 item_type
                            "tags": row[10] or "",  # tags
                            "source": row[11],  # knowledge_source
                            "relevance_level": row[3],  # 修改 level 为 relevance_level
                            "relevance_score": row[4],  # relevance_score
                            "index": index,
                            "project_id": 1,  # 添加默认的 project_id
                            "content_preview": row[8][:100] + "..." if len(row[8]) > 100 else row[8]  # content preview
                        }

                        # 添加到对应的分组
                        relevance_level = row[3]
                        logger.info(f"知识点 {row[6]} 的相关性级别: {relevance_level}, 类型: {type(relevance_level)}")

                        # 处理字符串和枚举值的情况
                        if relevance_level == RelevanceLevel.DIRECT.value or relevance_level == RelevanceLevel.DIRECT or relevance_level == '直接相关':
                            result["direct"].append(knowledge_with_relation)
                            logger.info(f"知识点 {row[6]} 添加到直接相关分组")
                        elif relevance_level == RelevanceLevel.INDIRECT.value or relevance_level == RelevanceLevel.INDIRECT or relevance_level == '间接相关':
                            result["indirect"].append(knowledge_with_relation)
                            logger.info(f"知识点 {row[6]} 添加到间接相关分组")
                        elif relevance_level == RelevanceLevel.BACKGROUND.value or relevance_level == RelevanceLevel.BACKGROUND or relevance_level == '背景相关':
                            result["background"].append(knowledge_with_relation)
                            logger.info(f"知识点 {row[6]} 添加到背景相关分组")
                        else:
                            # 如果无法匹配，默认添加到直接相关分组
                            logger.warning(f"知识点 {row[6]} 的相关性级别 {relevance_level} 无法匹配，默认添加到直接相关分组")
                            result["direct"].append(knowledge_with_relation)

                        # 添加到所有知识点列表
                        result["all_knowledge_points"].append(knowledge_with_relation)
                        index += 1

                # 关闭Tortoise连接
                await Tortoise.close_connections()

            except Exception as db_err:
                logger.error(f"数据库查询失败: {str(db_err)}")
                import traceback
                logger.error(f"数据库查询错误堆栈: {traceback.format_exc()}")

            return result

        except Exception as e:
            logger.error(f"获取需求相关知识点失败: {str(e)}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            # 返回空结果而不是抛出异常，避免前端崩溃
            return {
                "direct": [],
                "indirect": [],
                "background": [],
                "all_knowledge_points": []
            }

    async def delete_relations_by_requirement(self, requirement_id: int) -> int:
        """
        删除与需求相关的所有关联关系。
        使用原始SQL查询而不是ORM，避免ORM连接问题。
        """
        try:
            # 使用原始SQL查询
            from tortoise import Tortoise
            try:
                # 尝试初始化Tortoise
                from app.settings.config import settings
                await Tortoise.init(config=settings.TORTOISE_ORM)

                # 获取数据库连接
                conn = Tortoise.get_connection("default")

                # 执行SQL查询，删除与需求相关的所有关联关系
                query = """
                DELETE FROM requirement_knowledge_relation
                WHERE requirement_id = $1
                RETURNING id
                """

                # 执行查询
                query_result = await conn.execute_query(query, [requirement_id])

                # 计算删除的记录数
                count = 0
                if query_result and len(query_result) > 1:
                    count = len(query_result[1])

                # 关闭Tortoise连接
                await Tortoise.close_connections()

                return count

            except Exception as db_err:
                logger.error(f"数据库查询失败: {str(db_err)}")
                import traceback
                logger.error(f"数据库查询错误堆栈: {traceback.format_exc()}")
                return 0

        except Exception as e:
            logger.error(f"删除需求相关知识点关联关系失败: {str(e)}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            # 返回0而不是抛出异常，表示没有删除任何记录
            return 0


# 创建控制器实例
knowledge_relation_controller = KnowledgeRelationController()
