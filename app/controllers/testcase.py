from tortoise.transactions import atomic
from tortoise.functions import Max
from app.core.crud import CRUDBase
from app.models.admin import TestCase as TestCaseModel
from app.schemas.testcases import CaseCreate, CaseUpdate
import datetime
from tortoise import fields
import logging

logger = logging.getLogger(__name__)


class TestCaseController(CRUDBase[TestCaseModel, CaseCreate, CaseUpdate]):
    """
    测试用例控制器类，用于管理测试用例数据。
    """

    def __init__(self):
        super().__init__(model=TestCaseModel)

    @atomic()
    async def create_TestCase(self, obj_in: CaseCreate):
        """
        创建新测试用例。
        参数:
            obj_in (CaseCreate): 测试用例创建模型
        """
        try:
            # 保存测试步骤
            steps = None
            if hasattr(obj_in, 'steps') and obj_in.steps:
                steps = obj_in.steps

            # 获取最大的 test_case_id
            # 使用 Tortoise ORM 的查询方式获取最大的 test_case_id
            max_id_result = await self.model.annotate(max_id=Max('test_case_id')).first().values('max_id')
            next_id = 1
            if max_id_result and max_id_result.get('max_id'):
                next_id = max_id_result.get('max_id') + 1

            # 创建测试用例（不包含多对多关系）
            obj_in_dict = obj_in.model_dump(exclude={'steps'})
            obj_in_dict['test_case_id'] = next_id  # 设置 test_case_id 字段

            # 使用原始SQL插入数据，避免时区问题
            from tortoise import Tortoise
            conn = Tortoise.get_connection("default")

            # 构建SQL插入语句
            required_columns = [
                "test_case_id", "title", '"desc"', "priority", "status",
                "preconditions", "postconditions", "tags", "requirement_id",
                "project_id", "creator", "tapd_url"
]
            columns = []
            values = []
            placeholders = []

            # 添加test_case_id
            columns.append("test_case_id")
            values.append(next_id)
            # placeholders.append("$" + str(len(values)))
            placeholders.append("$1")

            # 添加其他字段
            # for key, value in obj_in_dict.items():
            #     if key != 'test_case_id' and value is not None:
            #         columns.append(key)
            #         values.append(value)
            #         placeholders.append("$" + str(len(values)))
            # if 'tapd_url' not in columns:
            #     columns.append("tapd_url")
            #     values.append(None)  # 如果没有提供tapd_url，则设置为None

            # # 添加created_at和updated_at字段
            # columns.append("created_at")
            # columns.append("updated_at")
            # 添加其他字段
            for key in required_columns[1:]:  # 跳过test_case_id
                actual_key = 'desc' if key == '"desc"' else key
                value = obj_in_dict.get(actual_key, None)
                columns.append(key)
                values.append(value)
                placeholders.append(f"${len(values)}")

            # 构建SQL语句
            # sql = f"""
            # INSERT INTO test_cases (test_case_id, title, "desc", priority, status, preconditions, postconditions, tags, requirement_id, project_id, creator, tapd_url, created_at, updated_at)
            # VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, NOW(), NOW())
            # RETURNING id
            # """
            # 构建SQL语句
            sql = f"""
            INSERT INTO test_cases ({', '.join(columns)}, created_at, updated_at)
            VALUES ({', '.join(placeholders)}, NOW(), NOW())
            RETURNING id
            """

            # 执行SQL语句
            result = await conn.execute_query(sql, values)
            logger.info(f"SQL execution result: {result}")  # 添加日志

            # 获取插入的ID
            # testcase_id = result[0][0][0]
            # testcase_id = result[0][0] if result and result[0] else None
            testcase_id = result[1][0]['id'] if result and len(result) > 1 and result[1] else None
            if testcase_id is None:
                raise Exception("Failed to get inserted test case ID")
            logger.info(f"Created test case with ID: {testcase_id}")

            # 获取创建的测试用例
            # testcase = await TestCaseModel.get(id=testcase_id)
            testcase = await self.model.get_or_none(id=testcase_id)
            if not testcase:
                raise Exception(f"Failed to retrieve created test case with ID: {testcase_id}")

            # 如果有测试步骤，创建测试步骤并关联到测试用例
            if steps:
                from app.models.admin import TestStep
                for i, step in enumerate(steps):
                    # 构建SQL插入语句
                    step_sql = """
                    INSERT INTO test_steps (step_id, test_case_id, description, expected_result, created_at, updated_at)
                    VALUES ($1, $2, $3, $4, NOW(), NOW())
                    RETURNING id
                    """

                    # 执行SQL语句
                    step_result = await conn.execute_query(
                        step_sql,
                        [i+1, testcase_id, step.description, step.expected_result]
                    )

                    # 获取插入的步骤ID
                    # step_id = step_result[0][0][0]
                    step_id = step_result[1][0]['id']
                    logger.info(f"Created step with ID: {step_id}")

                    # 构建关联SQL
                    relation_sql = """
                    INSERT INTO test_cases_test_steps (test_cases_id, test_step_id)
                    VALUES ($1, $2)
                    """

                    # 执行关联SQL
                    await conn.execute_query(relation_sql, [testcase_id, step_id])

                    logger.info(f"Created test step with ID: {step_id} for test case ID: {testcase_id}")

            return testcase
        except Exception as e:
            logger.error(f"Error creating test case: {str(e)}")
            logger.error(f"SQL: {sql}")
            logger.error(f"Values: {values}")
            raise

    async def get_TestCase_by_id(self, case_id: int):
        """
        根据测试用例ID获取测试用例详情。
        参数:
            ID (int): 测试用例ID
        """
        return await self.get(id=case_id)

    @atomic()
    async def update_TestCase(self, obj_in: CaseUpdate):
        """
        更新测试用例信息。
        参数:
            obj_in (CaseUpdate): 测试用例更新模型
        返回:
            TestCaseModel: 更新后的测试用例对象
        """
        try:
            # 获取测试用例
            testcase_id = obj_in.id
            testcase = await self.get(id=testcase_id)

            # 保存测试步骤
            steps = None
            if hasattr(obj_in, 'steps') and obj_in.steps:
                steps = obj_in.steps

            # 使用SQL脚本直接更新数据
            from tortoise import Tortoise
            conn = Tortoise.get_connection("default")

            # 构建SQL更新语句
            update_parts = []
            values = []

            # 添加要更新的字段
            obj_in_dict = obj_in.model_dump(exclude={'steps', 'id'})
            for key, value in obj_in_dict.items():
                if value is not None:
                    update_parts.append(f"{key} = ${len(values) + 1}")
                    values.append(value)

            # 添加更新时间
            update_parts.append("updated_at = NOW()")

            if update_parts:
                # 构建SQL语句
                sql = f"""
                UPDATE test_cases
                SET {', '.join(update_parts)}
                WHERE id = ${len(values) + 1}
                """

                # 添加ID到值列表
                values.append(testcase_id)

                # 执行SQL语句
                await conn.execute_query(sql, values)
                logger.info(f"Updated test case with ID: {testcase_id}")

            # 如果有测试步骤，先删除旧的步骤，再创建新的步骤
            if steps:
                # 删除旧的测试步骤
                from app.models.admin import TestStep
                await TestStep.filter(test_case_id=testcase_id).delete()

                # 删除旧的关联关系
                await conn.execute_query(
                    "DELETE FROM test_cases_test_steps WHERE test_cases_id = $1",
                    [testcase_id]
                )

                # 创建新的测试步骤
                for i, step in enumerate(steps):
                    # 构建SQL插入语句
                    step_sql = """
                    INSERT INTO test_steps (
                        step_id, test_case_id, description, expected_result, created_at, updated_at
                    ) VALUES (
                        $1, $2, $3, $4, NOW(), NOW()
                    )
                    RETURNING id
                    """

                    # 执行SQL语句
                    step_result = await conn.execute_query(
                        step_sql,
                        [i+1, testcase_id, step.description, step.expected_result]
                    )

                    # 获取插入的步骤ID
                    step_id = None
                    if step_result and len(step_result) > 0 and len(step_result[0]) > 0:
                        step_id = step_result[0][0][0]
                    if step_id is None:
                        raise Exception("Failed to get inserted test step ID")

                    # 构建关联SQL
                    relation_sql = """
                    INSERT INTO test_cases_test_steps (
                        test_cases_id, test_step_id
                    ) VALUES (
                        $1, $2
                    )
                    """

                    # 执行关联SQL
                    await conn.execute_query(relation_sql, [testcase_id, step_id])

                    logger.info(f"Created test step with ID: {step_id} for test case ID: {testcase_id}")

            # 获取更新后的测试用例
            return await self.get(id=testcase_id)
        except Exception as e:
            logger.error(f"Error updating test case: {str(e)}")
            raise

    @atomic()
    async def delete_TestCase(self, case_id: int):
        """
        删除测试用例。
        参数:
            case_id (int): 测试用例ID（主键）
        """
        try:
            # 获取测试用例
            testcase = await self.model.get(id=case_id)

            # 删除关联的测试步骤
            from app.models.admin import TestStep
            await TestStep.filter(test_case_id=case_id).delete()

            # 删除测试用例
            await testcase.delete()
            logger.info(f"Deleted test case with ID: {case_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting test case: {str(e)}")
            raise


    @atomic()
    async def update_adoption_status(self, case_id: int, is_adopted: bool):
        """
        更新测试用例的采纳状态。
        参数:
            case_id (int): 测试用例ID（主键）
            is_adopted (bool): 是否采纳
        返回:
            TestCaseModel: 更新后的测试用例对象
        """
        try:
            # 获取测试用例
            testcase = await self.get(id=case_id)

            # 使用SQL脚本直接更新数据
            from tortoise import Tortoise
            conn = Tortoise.get_connection("default")

            # 构建SQL更新语句
            sql = """
            UPDATE test_cases
            SET is_adopted = $1, updated_at = NOW()
            WHERE id = $2
            """

            # 执行SQL语句
            await conn.execute_query(sql, [is_adopted, case_id])
            logger.info(f"Updated test case adoption status with ID: {case_id}, is_adopted: {is_adopted}")

            # 获取更新后的测试用例
            return await self.get(id=case_id)
        except Exception as e:
            logger.error(f"Error updating test case adoption status: {str(e)}")
            raise

# 实例化项目控制器
testcase_controller = TestCaseController()
