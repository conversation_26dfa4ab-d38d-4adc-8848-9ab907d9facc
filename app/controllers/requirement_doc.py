from tortoise.expressions import Q
from tortoise.transactions import atomic
from app.core.crud import CRUDBase
from app.models.admin import RequirementDoc
from app.schemas.requirement_docs import (
    RequirementDocCreate,
    RequirementDocUpdate
)
import pytz
from datetime import datetime

class RequirementDocController(CRUDBase[RequirementDoc, RequirementDocCreate, RequirementDocUpdate]):
    """
    需求文档控制器，继承自CRUDBase并增强功能：
    1. 增加事务管理
    2. 补充复合查询能力
    3. 优化方法命名规范
    4. 确保时区处理正确
    """

    def __init__(self):
        super().__init__(model=RequirementDoc)

    @atomic()
    async def create(self, obj_in: RequirementDocCreate) -> RequirementDoc:
        """带事务的创建方法，确保时区处理正确"""
        # 确保创建时间有时区信息
        obj_dict = obj_in.model_dump(exclude_unset=True)

        # 手动添加带时区的创建和更新时间
        now = datetime.now(pytz.UTC)
        obj_dict["created_at"] = now
        obj_dict["updated_at"] = now

        # 确保所有datetime字段都有时区信息
        for key, value in obj_dict.items():
            if isinstance(value, datetime) and value.tzinfo is None:
                obj_dict[key] = value.replace(tzinfo=pytz.UTC)

        # 创建记录
        return await super().create(obj_in=obj_dict)

    @atomic()
    async def update(self, id: int, obj_in: RequirementDocUpdate) -> int:
        """带事务的更新方法"""
        # 确保更新时间有时区信息
        obj_dict = obj_in.model_dump(exclude_unset=True) if isinstance(obj_in, RequirementDocUpdate) else obj_in

        # 手动添加带时区的更新时间
        obj_dict["updated_at"] = datetime.now(pytz.UTC)

        # 确保所有datetime字段都有时区信息
        for key, value in obj_dict.items():
            if isinstance(value, datetime) and value.tzinfo is None:
                obj_dict[key] = value.replace(tzinfo=pytz.UTC)

        return await super().update(id, obj_in=obj_dict)

    @atomic()
    async def delete(self, id: int):
        """带事务的删除方法"""
        await super().remove(id)

    async def exists(self, *expressions) -> bool:
        """增强存在性检查，支持复合条件查询"""
        return await self.model.filter(*expressions).exists()

    async def get_by_project_id(self, project_id: int):
        """根据项目ID获取需求文档列表"""
        return await self.model.filter(project_id=project_id).all()

    async def get_by_user_id(self, user_id: int):
        """根据用户ID获取需求文档列表"""
        return await self.model.filter(user_id=user_id).all()

    async def get_by_url(self, url: str) -> RequirementDoc:
        """
        根据URL获取需求文档

        Args:
            url: TAPD或其他需求文档URL

        Returns:
            RequirementDoc: 需求文档对象，如果未找到返回None
        """
        if not url:
            return None

        # 处理URL格式，确保一致性比较
        normalized_url = url.strip().rstrip('/')

        try:
            # 尝试使用URL精确匹配查询
            # 注意：如果找到多个记录，get_or_none会抛出MultipleObjectsReturned异常
            doc = await self.model.get_or_none(url=normalized_url)
            if doc:
                return doc
        except Exception as e:
            # 捕获所有异常，包括MultipleObjectsReturned
            print(f"精确匹配URL时出现异常: {str(e)}")
            # 如果是多个对象返回的异常，继续执行下面的代码获取最新记录
            pass

        # 获取所有匹配的记录（精确匹配）
        docs = await self.model.filter(url=normalized_url).order_by("-created_at").all()
        if docs:
            # 返回最新创建的记录
            return docs[0]

        # 如果精确匹配未找到，尝试模糊匹配
        # 例如：URL可能包含额外的查询参数或锚点
        base_url = normalized_url.split('?')[0]
        docs = await self.model.filter(url__icontains=base_url).order_by("-created_at").all()

        # 如果找到结果，返回最新创建的那个
        if docs:
            return docs[0]

        return None

async def main():
    """
    主函数：测试需求文档获取功能
    """
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    url = 'https://www.tapd.cn/tapd_fe/22012671/story/detail/1122012671001004328'
    result = await requirement_doc_controller.verify_requirement_content(url)
    print(result)

if __name__ == "__main__":
    # 测试代码
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序执行失败: {str(e)}", file=sys.stderr)
        sys.exit(1)
# 实例化控制器（保持全局单例）
requirement_doc_controller = RequirementDocController()
