from tortoise.expressions import Q
from tortoise.transactions import atomic
from app.core.crud import CRUDBase
from app.models.admin import Requirement
from app.schemas.requirements import (
    RequirementCreate,
    RequirementUpdate,
    REQUIREMENT_CATEGORIES,
    RequirementDelete
)
import pytz
from datetime import datetime
from app.models.base import get_now_with_timezone, ensure_timezone, ensure_all_datetimes_have_timezone

class RequirementTimezoneController(CRUDBase[Requirement, RequirementCreate, RequirementUpdate]):
    """
    优化后的需求控制器，继承自CRUDBase并增强功能：
    1. 增加事务管理
    2. 补充复合查询能力
    3. 优化方法命名规范
    4. 确保时区处理正确
    """

    def __init__(self):
        super().__init__(model=Requirement)

    @atomic()
    async def create(self, obj_in: RequirementCreate) -> Requirement:
        """带事务的创建方法，确保时区处理正确"""
        try:
            print("\n\n===== RequirementTimezoneController.create 开始 =====")

            # 确保创建时间有时区信息
            if isinstance(obj_in, dict):
                obj_dict = obj_in
                print("输入是字典类型")
            else:
                obj_dict = obj_in.model_dump(exclude_unset=True)
                print("输入是模型类型")

            # 打印所有字段
            print("\n输入对象的所有字段:")
            for key, value in obj_dict.items():
                print(f"字段名: {key}, 值: {value}, 类型: {type(value)}")
                if isinstance(value, datetime):
                    print(f"  - 时区信息: {value.tzinfo}")

            # 手动添加带时区的创建和更新时间
            now = get_now_with_timezone()
            print(f"\n当前时间: {now}, 时区: {now.tzinfo}")
            obj_dict["created_at"] = now
            obj_dict["updated_at"] = now

            # 确保必填字段有值
            if "category" not in obj_dict or obj_dict["category"] is None or obj_dict["category"] == "":
                obj_dict["category"] = "功能"
                print(f"\n警告: category字段为空，已设置为默认值'功能'")

            if "name" not in obj_dict or obj_dict["name"] is None or obj_dict["name"] == "":
                obj_dict["name"] = f"需求-{now.strftime('%Y%m%d%H%M%S')}"
                print(f"\n警告: name字段为空，已设置为默认值'{obj_dict['name']}'")

            if "description" not in obj_dict or obj_dict["description"] is None or obj_dict["description"] == "":
                obj_dict["description"] = "需求描述"
                print(f"\n警告: description字段为空，已设置为默认值'需求描述'")

            if "level" not in obj_dict or obj_dict["level"] is None or obj_dict["level"] == "":
                obj_dict["level"] = "高"
                print(f"\n警告: level字段为空，已设置为默认值'高'")

            if "estimate" not in obj_dict or obj_dict["estimate"] is None:
                obj_dict["estimate"] = 8
                print(f"\n警告: estimate字段为空，已设置为默认值8")

            # 使用统一的时区处理函数确保所有datetime对象都有时区信息
            print("\n处理所有日期时间字段:")
            obj_dict = ensure_all_datetimes_have_timezone(obj_dict)
            print("已对所有datetime字段应用时区处理")

            # 强制设置category字段为"功能"，无论之前是什么值
            obj_dict["category"] = "功能"
            print("\n强制设置category字段为'功能'")

            # 创建记录
            print("\n调用父类的create方法...")
            result = await super().create(obj_in=obj_dict)
            print("创建成功!")
            print("===== RequirementTimezoneController.create 结束 =====\n\n")
            return result
        except Exception as e:
            print(f"\n创建过程出错: {str(e)}")
            print(f"错误类型: {type(e)}")
            import traceback
            print(f"错误堆栈: {traceback.format_exc()}")
            print("===== RequirementTimezoneController.create 出错 =====\n\n")
            raise

    @atomic()
    async def update(self, id: int, obj_in: RequirementUpdate) -> int:
        """带事务的更新方法"""
        # 确保更新时间有时区信息
        obj_dict = obj_in.model_dump(exclude_unset=True) if isinstance(obj_in, RequirementUpdate) else obj_in

        # 手动添加带时区的更新时间
        obj_dict["updated_at"] = get_now_with_timezone()

        # 使用统一的时区处理函数确保所有datetime对象都有时区信息
        obj_dict = ensure_all_datetimes_have_timezone(obj_dict)
        print("已对所有datetime字段应用时区处理")

        return await super().update(id, obj_in=obj_dict)

    @atomic()
    async def delete(self, id: int):
        """带事务的删除方法"""
        await super().remove(id)

    async def exists(self, *expressions) -> bool:
        """增强存在性检查，支持复合条件查询"""
        return await self.model.filter(*expressions).exists()

    async def list_with_project(self, search: Q, order_by: list = None):
        """带项目信息的联表查询"""
        query = self.model.filter(search).prefetch_related("project")
        if order_by:
            query = query.order_by(*order_by)
        return await query


# 实例化控制器（保持全局单例）
requirement_timezone_controller = RequirementTimezoneController()
