# 导入必要的模块
from typing import Optional, Dict, Any, Union  # 用于类型提示
from datetime import datetime  # 用于处理日期和时间
from tortoise.transactions import atomic  # 用于事务处理
from tortoise import fields  # 导入Tortoise ORM字段类型
from app.core.crud import CRUDBase  # CRUD 基类，提供通用的增删改查方法
from app.models.admin import Menu  # 定义菜单模型的 Tortoise ORM 类
from app.schemas.menus import MenuCreate, MenuUpdate  # 定义菜单创建和更新的 Pydantic 模型
from app.models.base import ensure_timezone, ensure_all_datetimes_have_timezone, get_now_with_timezone  # 导入时区处理函数


class MenuController(CRUDBase[Menu, MenuCreate, MenuUpdate]):
    """
    菜单控制器类，用于管理菜单数据。
    """
    def __init__(self):
        super().__init__(model=Menu)  # 初始化父类，指定模型为 Menu

    async def get_by_menu_path(self, path: str) -> Optional["Menu"]:
        """
        根据菜单路径获取菜单。
        参数:
            path (str): 菜单路径
        返回:
            Optional[Menu]: 如果找到菜单则返回菜单对象，否则返回 None
        """
        return await self.model.filter(path=path).first()  # 使用 Tortoise ORM 的 filter 方法查询菜单

    @atomic()
    async def update(self, id: int, obj_in: Union[MenuUpdate, Dict[str, Any]]) -> Menu:
        """
        带时区处理的菜单更新方法。
        参数:
            id (int): 菜单ID
            obj_in (Union[MenuUpdate, Dict[str, Any]]): 更新数据
        返回:
            Menu: 更新后的菜单对象
        """
        try:
            print(f"\n\n===== 开始更新菜单 ID: {id} =====")

            # 确保更新时间有时区信息
            obj_dict = obj_in.model_dump(exclude_unset=True, exclude={"id"}) if isinstance(obj_in, MenuUpdate) else obj_in
            print(f"输入数据: {obj_dict}")

            # 手动添加带时区的更新时间
            now = get_now_with_timezone()
            obj_dict["updated_at"] = now
            print(f"手动设置 updated_at: {now}, 时区: {now.tzinfo}")

            # 确保所有datetime字段都有时区信息
            obj_dict = ensure_all_datetimes_have_timezone(obj_dict)
            print("已对所有输入字段应用时区处理")

            # 获取菜单对象
            obj = await self.get(id=id)
            print(f"获取到原始对象: id={obj.id}, created_at={obj.created_at}, updated_at={obj.updated_at}")

            # 更新字段前先备份原始值
            original_created_at = obj.created_at

            # 更新字段
            obj.update_from_dict(obj_dict)
            print("已更新对象字段")

            # 使用原始created_at值，但确保有时区信息
            if original_created_at:
                if original_created_at.tzinfo is None:
                    obj.created_at = ensure_timezone(original_created_at)
                else:
                    obj.created_at = original_created_at
                print(f"恢复原始created_at: {obj.created_at}, 时区: {obj.created_at.tzinfo}")

            # 确保updated_at有时区信息
            obj.updated_at = now
            print(f"确保updated_at: {obj.updated_at}, 时区: {obj.updated_at.tzinfo}")

            # 使用SQL直接更新数据库，避免ORM时区问题
            conn = obj._meta.db
            table = obj._meta.db_table

            # 构建SQL更新语句 - 更新所有非时间字段
            fields_to_update = []
            values = []
            param_index = 1

            for field_name, value in obj_dict.items():
                if field_name not in ['created_at', 'updated_at', 'id']:
                    fields_to_update.append(f'"{field_name}" = ${param_index}')
                    values.append(value)
                    param_index += 1

            # 添加updated_at字段
            fields_to_update.append(f'"updated_at" = ${param_index}')
            values.append(now)
            param_index += 1

            # 添加WHERE条件
            values.append(id)

            # 构建完整SQL
            fields_sql = ", ".join(fields_to_update)
            query = f'UPDATE "{table}" SET {fields_sql} WHERE "id" = ${param_index}'

            # 执行SQL更新
            print(f"执行SQL更新: {query}")
            print(f"SQL参数: {values}")
            await conn.execute_query(query, values)
            print("SQL更新成功")

            # 重新获取对象以确保数据一致性
            updated_obj = await self.get(id=id)
            print(f"重新获取对象: id={updated_obj.id}, created_at={updated_obj.created_at}, updated_at={updated_obj.updated_at}")
            print(f"===== 菜单更新完成 ID: {id} =====\n\n")

            return updated_obj

        except Exception as e:
            print(f"菜单更新出错: {str(e)}")
            print(f"错误类型: {type(e)}")
            import traceback
            print(f"错误堆栈: {traceback.format_exc()}")
            raise


# 实例化菜单控制器
menu_controller = MenuController()