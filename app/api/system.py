"""
系统管理API

提供系统级别的API端点，如健康检查、模型预热等
"""

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from typing import Dict, Any

# 导入模型预加载工具
from app.utils.model_preloader import get_embedding_model, _preload_embedding_model

router = APIRouter(prefix="/system", tags=["系统管理"])

class SystemStatusResponse(BaseModel):
    """系统状态响应模型"""
    status: str
    version: str
    details: Dict[str, Any] = {}

@router.get("/health", response_model=SystemStatusResponse)
async def health_check():
    """
    健康检查端点
    
    返回系统当前状态和版本信息
    """
    return SystemStatusResponse(
        status="ok",
        version="1.0.0",
        details={
            "services": {
                "database": "connected",
                "api": "running"
            }
        }
    )

@router.post("/preload-models", response_model=SystemStatusResponse)
async def preload_models():
    """
    手动触发模型预加载
    
    可以在系统部署后调用此端点来预热模型
    """
    try:
        # 同步加载模型
        _preload_embedding_model()
        
        return SystemStatusResponse(
            status="ok",
            version="1.0.0",
            details={
                "message": "模型预加载成功",
                "models": ["BAAI/bge-large-zh"]
            }
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"模型预加载失败: {str(e)}"
        )
