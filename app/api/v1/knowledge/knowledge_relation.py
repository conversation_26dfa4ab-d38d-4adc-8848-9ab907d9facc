import logging
from fastapi import APIRouter, Query, Depends, HTTPException, status
from tortoise.exceptions import DoesNotExist

from app.controllers.knowledge_relation import knowledge_relation_controller
from app.controllers.requirement import requirement_controller
from app.schemas.knowledge_relation import RequirementKnowledgeResponse
from app.schemas.base import Success, SuccessExtra
from app.core.dependency import get_current_user_id

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/requirement/{requirement_id}", summary="获取需求相关的知识点")
async def get_requirement_knowledge(
    requirement_id: int,
    current_user_id: int = Depends(get_current_user_id)
):
    """
    获取与指定需求相关的所有知识点，按相关性级别分组
    """
    try:
        # 检查数据库连接
        from tortoise import Tortoise
        try:
            # 尝试获取连接，如果失败则说明连接未初始化
            Tortoise.get_connection("default")
        except Exception as conn_err:
            logger.warning(f"数据库连接未初始化或出错: {str(conn_err)}，尝试初始化连接...")
            from app.settings.config import settings
            try:
                await Tortoise.init(config=settings.TORTOISE_ORM)
                logger.info("数据库连接已初始化")
            except Exception as init_err:
                logger.error(f"初始化数据库连接失败: {str(init_err)}")

        # 使用原始SQL查询检查需求是否存在
        from tortoise import Tortoise
        conn = Tortoise.get_connection("default")
        query = "SELECT id, name FROM requirement WHERE id = $1"
        result = await conn.execute_query(query, [requirement_id])

        if not result or not result[1]:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"需求 ID {requirement_id} 不存在"
            )

        requirement_name = result[1][0][1]  # 获取需求名称

        # 获取需求相关的知识点
        logger.info(f"开始获取需求 {requirement_id} 的相关知识点")
        knowledge_points = await knowledge_relation_controller.get_knowledge_by_requirement(requirement_id)
        logger.info(f"获取到需求 {requirement_id} 的知识点: {knowledge_points}")

        # 构建响应数据
        direct_related = []
        indirect_related = []
        background_related = []

        # 处理直接相关的知识点
        direct_points = knowledge_points.get("direct", [])
        logger.info(f"直接相关知识点数量: {len(direct_points)}")
        for point in direct_points:
            direct_related.append(point)
            logger.info(f"添加直接相关知识点: {point.get('id')} - {point.get('title')}")

        # 处理间接相关的知识点
        indirect_points = knowledge_points.get("indirect", [])
        logger.info(f"间接相关知识点数量: {len(indirect_points)}")
        for point in indirect_points:
            indirect_related.append(point)
            logger.info(f"添加间接相关知识点: {point.get('id')} - {point.get('title')}")

        # 处理背景相关的知识点
        background_points = knowledge_points.get("background", [])
        logger.info(f"背景相关知识点数量: {len(background_points)}")
        for point in background_points:
            background_related.append(point)
            logger.info(f"添加背景相关知识点: {point.get('id')} - {point.get('title')}")

        # 计算总知识点数量
        total_points = len(direct_related) + len(indirect_related) + len(background_related)
        logger.info(f"总知识点数量: {total_points}")

        try:
            # 直接使用字典构建响应，绕过 Pydantic 验证
            response_dict = {
                "requirement_id": requirement_id,
                "requirement_name": requirement_name,
                "direct_related": direct_related,
                "indirect_related": indirect_related,
                "background_related": background_related,
                "total_points": total_points
            }

            logger.info(f"成功构建响应: {response_dict}")
            return response_dict
        except Exception as e:
            logger.error(f"构建响应失败: {str(e)}")
            # 打印详细的错误信息
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")

            # 打印每个知识点的详细信息，帮助排查问题
            logger.error("直接相关知识点详情:")
            for i, point in enumerate(direct_related):
                logger.error(f"直接相关知识点 {i}: {point}")

            logger.error("间接相关知识点详情:")
            for i, point in enumerate(indirect_related):
                logger.error(f"间接相关知识点 {i}: {point}")

            logger.error("背景相关知识点详情:")
            for i, point in enumerate(background_related):
                logger.error(f"背景相关知识点 {i}: {point}")

            # 返回一个空的响应，避免前端报错
            return {
                "requirement_id": requirement_id,
                "requirement_name": requirement_name,
                "direct_related": [],
                "indirect_related": [],
                "background_related": [],
                "total_points": 0
            }
    except DoesNotExist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"需求 ID {requirement_id} 不存在"
        )
    except Exception as e:
        logger.error(f"获取需求相关知识点失败: {str(e)}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")

        # 返回空响应而不是抛出异常
        return RequirementKnowledgeResponse(
            requirement_id=requirement_id,
            requirement_name="未知需求",
            direct_related=[],
            indirect_related=[],
            background_related=[],
            total_points=0
        )
