import logging
from fastapi import APIRouter, Query, Depends, HTTPException, status
from tortoise.exceptions import DoesNotExist

from app.controllers.knowledge import knowledge_controller
from app.schemas.knowledge import (
    KnowledgeItemCreate,
    KnowledgeItemUpdate,
    KnowledgeItemResponse,
    KnowledgeItemType
)
from app.schemas.base import Success, SuccessExtra
from app.core.dependency import get_current_user_id
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/", summary="添加新的知识条目", response_model=KnowledgeItemResponse)
async def create_knowledge_item(
    item_in: KnowledgeItemCreate,
    current_user_id: int = Depends(get_current_user_id)
):
    """
    添加新的知识条目

    - **title**: 知识条目标题
    - **content**: 知识条目的详细内容
    - **item_type**: 条目类型/分类
    - **tags**: 标签或关键词，用逗号分隔
    - **source**: 来源
    - **project_id**: 关联项目ID
    """
    try:
        # 记录请求数据
        logger.info(f"创建知识条目请求数据: {item_in.model_dump()}")

        # 调用控制器创建知识条目
        item = await knowledge_controller.create_with_creator(obj_in=item_in, creator_id=current_user_id)

        # 记录创建成功
        logger.info(f"知识条目创建成功: ID={item.id}")

        # 返回创建的知识条目
        result = await item.to_dict()

        # 确保枚举类型被转换为字符串
        if 'item_type' in result and hasattr(result['item_type'], 'value'):
            result['item_type'] = result['item_type'].value

        if 'source' in result and hasattr(result['source'], 'value'):
            result['source'] = result['source'].value

        logger.info(f"返回数据: {result}")
        return result
    except Exception as e:
        # 记录错误详情
        logger.error(f"创建知识条目失败: {str(e)}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")

        # 抛出HTTP异常
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建知识条目失败: {str(e)}"
        )


@router.get("/", summary="获取知识条目列表")
async def list_knowledge_items(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    project_id: int = Query(None, description="项目ID筛选"),
    search: str = Query(None, description="搜索关键词"),
    item_type: KnowledgeItemType = Query(None, description="按类型过滤"),
    source_url: str = Query(None, description="TAPD链接搜索")
):
    """
    获取知识条目列表

    - 支持分页
    - 支持按项目ID筛选
    - 支持按关键词搜索（标题、内容、标签）
    - 支持按类型筛选
    - 支持按TAPD链接搜索
    """
    try:
        # 如果提供了source_url，优先使用source_url搜索
        if source_url:
            items = await knowledge_controller.get_by_source_url(source_url)
            # 如果还有其他筛选条件，需要进一步过滤
            if project_id:
                items = [item for item in items if item.project_id == project_id]
            if item_type:
                items = [item for item in items if item.item_type == item_type]

            # 手动分页
            total = len(items)
            start_index = (page - 1) * page_size
            end_index = start_index + page_size
            items = items[start_index:end_index]
        else:
            total, items = await knowledge_controller.search(
                project_id=project_id,
                search_term=search,
                item_type=item_type.value if item_type else None,
                page=page,
                page_size=page_size
            )

        # 获取所有知识条目的字典表示
        items_dict = []
        for item in items:
            item_dict = await item.to_dict()

            # 确保枚举类型被转换为字符串
            if 'item_type' in item_dict and hasattr(item_dict['item_type'], 'value'):
                item_dict['item_type'] = item_dict['item_type'].value

            if 'source' in item_dict and hasattr(item_dict['source'], 'value'):
                item_dict['source'] = item_dict['source'].value

            items_dict.append(item_dict)

        return SuccessExtra(data=items_dict, total=total, page=page, page_size=page_size)
    except Exception as e:
        logger.error(f"获取知识条目列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取知识条目列表失败: {str(e)}"
        )


@router.get("/{item_id}", summary="获取单个知识条目详情")
async def get_knowledge_item(
    item_id: int
):
    """
    获取单个知识条目详情

    - **item_id**: 知识条目ID
    """
    try:
        item = await knowledge_controller.get(id=item_id)
        result = await item.to_dict()

        # 确保枚举类型被转换为字符串
        if 'item_type' in result and hasattr(result['item_type'], 'value'):
            result['item_type'] = result['item_type'].value

        if 'source' in result and hasattr(result['source'], 'value'):
            result['source'] = result['source'].value

        return Success(data=result)
    except DoesNotExist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"知识条目不存在: ID {item_id}"
        )
    except Exception as e:
        logger.error(f"获取知识条目详情失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取知识条目详情失败: {str(e)}"
        )


@router.put("/{item_id}", summary="更新指定的知识条目")
async def update_knowledge_item(
    item_id: int,
    item_in: KnowledgeItemUpdate,
    current_user_id: int = Depends(get_current_user_id)
):
    """
    更新指定的知识条目

    - **item_id**: 知识条目ID
    - **title**: 知识条目标题
    - **content**: 知识条目的详细内容
    - **item_type**: 条目类型/分类
    - **tags**: 标签或关键词，用逗号分隔
    - **source**: 来源
    - **project_id**: 关联项目ID
    """
    try:
        # 记录请求数据
        logger.info(f"更新知识条目请求数据: ID={item_id}, 数据={item_in.model_dump()}")

        # 检查知识条目是否存在
        item = await knowledge_controller.get(id=item_id)

        # 检查用户权限（只有创建者或管理员可以更新）
        # 这里简化处理，实际应用中可能需要更复杂的权限检查
        from app.models.admin import User
        user = await User.get(id=current_user_id)
        if not user.is_superuser and item.creator_id != current_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限更新此知识条目"
            )

        # 保存原始数据，用于后续比较
        original_item_dict = await item.to_dict()
        logger.info(f"原始知识条目数据: {original_item_dict}")

        # 更新知识条目
        updated_item = await knowledge_controller.update(id=item_id, obj_in=item_in)
        result = await updated_item.to_dict()

        # 确保枚举类型被转换为字符串
        if 'item_type' in result and hasattr(result['item_type'], 'value'):
            result['item_type'] = result['item_type'].value

        if 'source' in result and hasattr(result['source'], 'value'):
            result['source'] = result['source'].value

        # 检查是否需要更新关联关系
        content_changed = (
            (item_in.title is not None and item_in.title != original_item_dict.get('title')) or
            (item_in.content is not None and item_in.content != original_item_dict.get('content')) or
            (item_in.tags is not None and item_in.tags != original_item_dict.get('tags'))
        )

        # 如果内容、标题或标签发生变化，更新关联关系
        if content_changed:
            try:
                # 获取与该知识点相关的所有需求关联关系
                from tortoise import Tortoise
                conn = Tortoise.get_connection("default")

                # 查询与该知识点相关的所有需求ID
                query = """
                SELECT requirement_id, relevance_level, relevance_score, source
                FROM requirement_knowledge_relation
                WHERE knowledge_id = $1
                """
                result = await conn.execute_query(query, [item_id])

                if result and len(result) > 1 and result[1]:
                    # 导入相关模块
                    from app.api.v1.agent.knowledge_retrieval import calculate_relevance
                    from app.controllers.requirement import requirement_controller
                    from app.models.knowledge_relation import RelevanceLevel

                    # 遍历所有关联的需求
                    for row in result[1]:
                        requirement_id = row[0]
                        old_relevance_level = row[1]
                        old_relevance_score = row[2]
                        # relation_source = row[3]  # 暂时不使用，但保留注释以便未来扩展

                        try:
                            # 获取需求对象
                            requirement = await requirement_controller.get(id=requirement_id)

                            # 重新计算相关性分数
                            new_relevance_score = await calculate_relevance(requirement, updated_item)

                            # 确定新的相关性级别
                            new_relevance_level = old_relevance_level  # 默认保持原有级别
                            if new_relevance_score >= 6.0:
                                new_relevance_level = RelevanceLevel.DIRECT.value
                            elif new_relevance_score >= 4.0:
                                new_relevance_level = RelevanceLevel.INDIRECT.value
                            elif new_relevance_score >= 2.5:
                                new_relevance_level = RelevanceLevel.BACKGROUND.value

                            # 如果相关性分数或级别发生变化，更新关联关系
                            if new_relevance_score != old_relevance_score or new_relevance_level != old_relevance_level:
                                # 更新关联关系
                                update_query = """
                                UPDATE requirement_knowledge_relation
                                SET relevance_level = $1, relevance_score = $2, updated_at = CURRENT_TIMESTAMP
                                WHERE requirement_id = $3 AND knowledge_id = $4
                                """
                                await conn.execute_query(
                                    update_query,
                                    [new_relevance_level, new_relevance_score, requirement_id, item_id]
                                )

                                logger.info(f"已更新知识点 {item_id} 与需求 {requirement_id} 的关联关系: "
                                           f"相关性级别 {old_relevance_level} -> {new_relevance_level}, "
                                           f"相关性分数 {old_relevance_score} -> {new_relevance_score}")
                        except Exception as req_err:
                            logger.error(f"更新知识点 {item_id} 与需求 {requirement_id} 的关联关系失败: {str(req_err)}")
                            # 继续处理下一个关联关系，不中断整个流程

                logger.info(f"知识点 {item_id} 的关联关系更新完成")
            except Exception as rel_err:
                logger.error(f"处理知识点 {item_id} 的关联关系时出错: {str(rel_err)}")
                import traceback
                logger.error(f"关联关系处理错误堆栈: {traceback.format_exc()}")
                # 不抛出异常，继续返回更新成功的结果

        logger.info(f"知识条目更新成功: ID={item_id}")
        return Success(data=result)
    except DoesNotExist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"知识条目不存在: ID {item_id}"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新知识条目失败: {str(e)}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新知识条目失败: {str(e)}"
        )


class CleanupRequest(BaseModel):
    """清理知识点请求模型"""
    source_url: str = None
    source_reference_id: str = None


@router.post("/cleanup", summary="根据源链接清理知识点")
async def cleanup_knowledge_by_source(
    request: CleanupRequest,
    current_user_id: int = Depends(get_current_user_id)
):
    """
    根据源TAPD链接清理知识点

    - **source_url**: 源TAPD需求链接
    - **source_reference_id**: 源需求引用ID
    """
    try:
        # 检查用户权限（只有管理员可以执行清理操作）
        from app.models.admin import User
        user = await User.get(id=current_user_id)
        if not user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限执行清理操作，需要管理员权限"
            )

        deleted_count = 0

        # 根据源URL清理
        if request.source_url:
            logger.info(f"开始根据源URL清理知识点: {request.source_url}")
            deleted_count += await knowledge_controller.delete_by_source_url(request.source_url)

        # 根据源引用ID清理
        if request.source_reference_id:
            logger.info(f"开始根据源引用ID清理知识点: {request.source_reference_id}")
            deleted_count += await knowledge_controller.delete_by_source_reference_id(request.source_reference_id)

        if deleted_count == 0 and not request.source_url and not request.source_reference_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请提供source_url或source_reference_id参数"
            )

        logger.info(f"清理完成，共删除 {deleted_count} 个知识点")
        return Success(data={"deleted_count": deleted_count}, msg=f"清理完成，共删除 {deleted_count} 个知识点")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清理知识点失败: {str(e)}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清理知识点失败: {str(e)}"
        )


@router.get("/by-source", summary="根据源链接查询知识点")
async def get_knowledge_by_source(
    source_url: str = Query(None, description="源TAPD需求链接"),
    source_reference_id: str = Query(None, description="源需求引用ID")
):
    """
    根据源链接查询知识点

    - **source_url**: 源TAPD需求链接
    - **source_reference_id**: 源需求引用ID
    """
    try:
        items = []

        # 根据源URL查询
        if source_url:
            items.extend(await knowledge_controller.get_by_source_url(source_url))

        # 根据源引用ID查询
        if source_reference_id:
            items.extend(await knowledge_controller.get_by_source_reference_id(source_reference_id))

        if not source_url and not source_reference_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请提供source_url或source_reference_id参数"
            )

        # 去重（如果同时提供了两个参数可能会有重复）
        unique_items = {}
        for item in items:
            unique_items[item.id] = item
        items = list(unique_items.values())

        # 转换为字典格式
        items_dict = []
        for item in items:
            item_dict = await item.to_dict()

            # 确保枚举类型被转换为字符串
            if 'item_type' in item_dict and hasattr(item_dict['item_type'], 'value'):
                item_dict['item_type'] = item_dict['item_type'].value

            if 'source' in item_dict and hasattr(item_dict['source'], 'value'):
                item_dict['source'] = item_dict['source'].value

            items_dict.append(item_dict)

        return Success(data={"items": items_dict, "total": len(items_dict)})

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询知识点失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"查询知识点失败: {str(e)}"
        )


@router.delete("/{item_id}", summary="删除指定的知识条目")
async def delete_knowledge_item(
    item_id: int,
    current_user_id: int = Depends(get_current_user_id)
):
    """
    删除指定的知识条目

    - **item_id**: 知识条目ID
    """
    try:
        # 检查知识条目是否存在
        item = await knowledge_controller.get(id=item_id)

        # 检查用户权限（只有创建者或管理员可以删除）
        # 这里简化处理，实际应用中可能需要更复杂的权限检查
        from app.models.admin import User
        user = await User.get(id=current_user_id)
        if not user.is_superuser and item.creator_id != current_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限删除此知识条目"
            )

        # 删除知识条目
        await knowledge_controller.remove(id=item_id)
        return Success(msg="删除成功")
    except DoesNotExist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"知识条目不存在: ID {item_id}"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除知识条目失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除知识条目失败: {str(e)}"
        )
