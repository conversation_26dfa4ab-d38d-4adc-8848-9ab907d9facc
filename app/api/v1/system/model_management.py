"""
模型管理API

提供模型预热、状态查询等功能。
"""

import logging
from typing import Dict, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel

from app.core.model_preload import start_model_preloading, get_preload_status
from app.core.dependency import AuthControl

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由
router = APIRouter()


class PreloadResponse(BaseModel):
    """预热响应模型"""
    success: bool
    message: str
    status: Dict[str, Any]


@router.post("/preload/embedding", response_model=PreloadResponse, tags=["系统管理"])
async def preload_embedding_model(
    background: bool = Query(True, description="是否在后台运行预热过程"),
    current_user = Depends(AuthControl.is_authed)
):
    """
    预热嵌入模型

    - **background**: 是否在后台运行预热过程，默认为True
    """
    try:
        # 检查用户权限
        if not current_user.is_superuser:
            raise HTTPException(status_code=403, detail="只有超级管理员可以执行此操作")

        # 启动预热过程
        start_model_preloading(in_background=background)

        # 获取预热状态
        status = get_preload_status()

        return PreloadResponse(
            success=True,
            message="嵌入模型预热已启动" if background else "嵌入模型预热已完成",
            status=status
        )
    except Exception as e:
        logger.error(f"预热嵌入模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"预热嵌入模型失败: {str(e)}")


@router.get("/preload/status", response_model=PreloadResponse, tags=["系统管理"])
async def get_model_preload_status(
    current_user = Depends(AuthControl.is_authed)
):
    """
    获取模型预热状态
    """
    try:
        # 获取预热状态
        status = get_preload_status()

        return PreloadResponse(
            success=True,
            message="获取模型预热状态成功",
            status=status
        )
    except Exception as e:
        logger.error(f"获取模型预热状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取模型预热状态失败: {str(e)}")
