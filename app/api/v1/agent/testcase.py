import json
import logging
import os
import uuid
from typing import Any

import aiofiles
from autogen_agentchat.base import TaskResult
from autogen_agentchat.messages import TextMessage, UserInputRequestedEvent, ToolCallSummaryMessage
from autogen_core import CancellationToken, ClosureContext, MessageContext
from fastapi import APIRouter, Query, HTTPException
from starlette.websockets import WebSocket, WebSocketDisconnect

from app.schemas import Success

from fastapi import File, UploadFile
from pathlib import Path
from .testcase_agents import ResponseMessage, RequirementMessage, start_runtime

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/ws/status")
async def websocket_status():
    """检查WebSocket服务状态"""
    try:
        data = {
            "status": "ok",
            "message": "WebSocket服务正常运行",
            "endpoint": "/api/v1/agent/ws/generate",
            "protocol": "ws/wss"
        }
        return Success(data=data)
    except Exception as e:
        data = {
            "status": "error",
            "message": f"WebSocket服务异常: {str(e)}",
            "endpoint": "/api/v1/agent/ws/generate",
            "protocol": "ws/wss"
        }
        return Success(data=data)


@router.websocket("/ws/generate")
async def generate_testcase(websocket: WebSocket):
    await websocket.accept()

    # 前端发送任何智能体执行的结果
    # 将收到的消息发送给前端（浏览器）
    async def collect_result(_agent: ClosureContext, message: ResponseMessage, ctx: MessageContext) -> None:
        msg = message.model_dump()
        print("返回的消息：", msg)
        # 将收到的消息发送给前端（浏览器）
        await websocket.send_json(msg)

    async def _user_input(prompt: str, cancellation_token: CancellationToken | None) -> str:
        # 等待用户输入（代码阻塞执行）,下面的代码的效果类似 input
        data = await websocket.receive_json()
        message = TextMessage.model_validate(data)
        return message.content
    try:
        while True:
            data = await websocket.receive_json()
            print(f"\n\n[DEBUG] 收到的WebSocket数据: {data}\n\n")

            # 确保数据中包含必填字段
            if 'id' not in data:
                print(f"[DEBUG] 数据中缺少id字段，添加默认值")
                data['id'] = data.get('requirement_id', 0)  # 尝试从requirement_id获取，否则使用默认值0

            if 'name' not in data:
                print(f"[DEBUG] 数据中缺少name字段，添加默认值")
                data['name'] = data.get('task', '生成测试用例')  # 使用task字段或默认值

            if 'description' not in data:
                print(f"[DEBUG] 数据中缺少description字段，添加默认值")
                data['description'] = data.get('scenario', '')  # 使用scenario字段或空字符串

            try:
                requirement = RequirementMessage.model_validate(data)
            except Exception as validation_error:
                logger.error(f"数据验证错误: {str(validation_error)}")
                # 发送错误消息到前端
                await websocket.send_json({
                    "type": "error",
                    "content": f"数据验证错误: {str(validation_error)}",
                    "source": "system"
                })
                # 尝试手动创建RequirementMessage对象
                print(f"[DEBUG] 尝试手动创建RequirementMessage对象")

                # 检查是否有reviewer字段
                reviewer = data.get('reviewer')
                print(f"\n[DEBUG] 从前端数据中获取到reviewer值: '{reviewer}'\n")

                # 如果没有reviewer字段，尝试从数据库获取
                if reviewer is None and 'id' in data and data['id']:
                    try:
                        # 从数据库中获取需求
                        from app.controllers.requirement import requirement_controller
                        req = await requirement_controller.get(id=data['id'])
                        if req and req.reviewer and req.reviewer != "--":
                            reviewer = req.reviewer
                            print(f"\n[DEBUG] 从数据库中获取到reviewer值: '{reviewer}'\n")
                        else:
                            reviewer = "未指定"
                            print(f"\n[DEBUG] 数据库中没有有效的reviewer值，设置为默认值: '{reviewer}'\n")
                    except Exception as e:
                        print(f"\n[DEBUG] 从数据库获取reviewer值失败: {str(e)}\n")
                        reviewer = "未指定"
                        print(f"\n[DEBUG] 设置默认reviewer值: '{reviewer}'\n")

                requirement = RequirementMessage(
                    id=data.get('id', 0),
                    name=data.get('name', '生成测试用例'),
                    description=data.get('description', ''),
                    scenario=data.get('scenario', ''),
                    task=data.get('task', '生成测试用例'),
                    project_id=data.get('project_id'),
                    tapd_url=data.get('tapd_url'),
                    reviewer=reviewer
                )

            try:
                # 检查是否有多个需求
                if requirement.requirements and len(requirement.requirements) > 0:
                    print(f"\n[DEBUG] 检测到多个需求: {len(requirement.requirements)} 个\n")

                    # 处理多个需求
                    # 创建一个合并的需求描述，包含所有需求点
                    combined_description = "以下是多个需求点的集合，请为每个需求点生成差异化的测试用例，避免重复：\n\n"

                    for i, req in enumerate(requirement.requirements):
                        combined_description += f"需求点 {i+1}: {req.name}\n"
                        combined_description += f"描述: {req.description}\n\n"

                    # 添加差异化指导
                    combined_description += """
特别指导：
1. 为每个需求点设计独特的测试场景，考虑该需求点特有的业务规则和约束条件
2. 测试用例的标题应该明确反映其测试的具体功能点，避免使用通用的标题
3. 测试步骤应该具体且详细，与需求点的具体功能紧密相关
4. 如果多个需求点有相似的功能，应该从不同角度或不同场景设计测试用例，确保差异化
5. 每个测试用例必须在标题或描述中明确标注对应的需求点编号
"""

                    # 创建一个合并的需求消息
                    # 获取第一个需求的reviewer值，如果没有则使用默认值
                    reviewer_value = None
                    if hasattr(requirement.requirements[0], 'reviewer') and requirement.requirements[0].reviewer:
                        reviewer_value = requirement.requirements[0].reviewer
                        print(f"\n[DEBUG] 使用第一个需求的reviewer值: '{reviewer_value}'\n")
                    else:
                        # 尝试从数据库获取reviewer值
                        try:
                            from app.controllers.requirement import requirement_controller
                            req = await requirement_controller.get(id=requirement.requirements[0].id)
                            if req and req.reviewer and req.reviewer != "--":
                                reviewer_value = req.reviewer
                                print(f"\n[DEBUG] 从数据库中获取到reviewer值: '{reviewer_value}'\n")
                            else:
                                reviewer_value = "未指定"
                                print(f"\n[DEBUG] 数据库中没有有效的reviewer值，设置为默认值: '{reviewer_value}'\n")
                        except Exception as e:
                            print(f"\n[DEBUG] 从数据库获取reviewer值失败: {str(e)}\n")
                            reviewer_value = "未指定"
                            print(f"\n[DEBUG] 设置默认reviewer值: '{reviewer_value}'\n")

                    combined_req = RequirementMessage(
                        id=requirement.requirements[0].id,  # 使用第一个需求的ID
                        name=f"多需求点集合 ({len(requirement.requirements)}个)",
                        description=combined_description,
                        project_id=requirement.requirements[0].project_id,
                        tapd_url=requirement.requirements[0].tapd_url,
                        scenario=requirement.scenario,
                        reviewer=reviewer_value,  # 添加reviewer字段
                        task=requirement.task + "\n\n特别注意：请为每个需求点生成差异化的测试用例，避免重复。每个测试用例应该明确标注对应的需求点编号。"
                    )

                    # 发送合并的需求消息
                    await websocket.send_json({
                        "type": "info",
                        "content": f"正在处理 {len(requirement.requirements)} 个需求点，将生成差异化测试用例",
                        "source": "system"
                    })

                    # 为合并的需求生成测试用例
                    await start_runtime(requirement=combined_req,
                                      collect_result=collect_result,
                                      user_input_func=_user_input)
                else:
                    # 处理单个需求
                    print(f"\n[DEBUG] 处理单个需求: {requirement.name}\n")
                    await start_runtime(requirement=requirement,
                                    collect_result=collect_result,
                                    user_input_func=_user_input)

            except Exception as e:
                # Send error message to client
                error_message = {
                    "type": "error",
                    "content": f"Error: {str(e)}",
                    "source": "system"
                }
                await websocket.send_json(error_message)
                # Re-enable input after error
                await websocket.send_json({
                    "type": "UserInputRequestedEvent",
                    "content": "An error occurred. Please try again.",
                    "source": "system"
                })

    except WebSocketDisconnect:
        logger.info("Client disconnected")
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        try:
            await websocket.send_json({
                "type": "error",
                "content": f"Unexpected error: {str(e)}",
                "source": "system"
            })
        except:
            pass
