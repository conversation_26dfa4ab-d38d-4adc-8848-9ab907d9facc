# message_utils.py
# 消息处理工具类

import logging
from datetime import datetime
from typing import Optional, Any, Dict
from autogen_core import RoutedAgent, TopicId

from ..models import WebSocketMessage, TopicTypes

# 配置日志
logger = logging.getLogger("message_utils")

async def publish_log_message(agent: RoutedAgent, content: str, source: str) -> None:
    """发布日志消息到结果主题"""
    message = WebSocketMessage(
        type="log",
        content=content,
        source=source
    )

    await agent.publish_message(
        message,
        topic_id=TopicId(type=TopicTypes.TEST_RESULT, source=agent.id.key)
    )


async def publish_progress_message(
    agent: RoutedAgent, 
    stage: str, 
    percentage: int, 
    message: str, 
    source: str
) -> None:
    """发布进度消息到结果主题"""
    # 构建安全的content字典
    content = {
        "stage": stage,
        "progress": percentage,
        "message": str(message) if not isinstance(message, str) else message
    }

    # 使用WebSocketMessage对象
    msg = WebSocketMessage(
        type="progress",
        content=content,
        source=source
    )

    # 发布WebSocketMessage对象
    await agent.publish_message(
        msg,
        topic_id=TopicId(type=TopicTypes.TEST_RESULT, source=agent.id.key)
    )


async def publish_error_message(agent: RoutedAgent, error_message: str, source: str) -> None:
    """发布错误消息到结果主题"""
    # 确保错误消息是字符串
    if not isinstance(error_message, str):
        error_message = str(error_message)

    # 使用WebSocketMessage对象
    message = WebSocketMessage(
        type="error",
        content=error_message,
        source=source
    )

    # 发布WebSocketMessage对象
    await agent.publish_message(
        message,
        topic_id=TopicId(type=TopicTypes.TEST_RESULT, source=agent.id.key)
    )


def create_system_message(msg_type: str, content: str) -> Dict[str, Any]:
    """创建系统消息"""
    return {
        "type": msg_type,
        "content": content,
        "source": "system",
        "timestamp": datetime.now().isoformat()
    } 