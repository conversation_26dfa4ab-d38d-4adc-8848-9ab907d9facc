import json
from dataclasses import dataclass
from typing import Callable, Optional, Awaitable, Any

from autogen_agentchat.agents import Assistant<PERSON>gent, UserProxyAgent
from autogen_agentchat.base import TaskResult
from autogen_agentchat.conditions import TextMentionTermination
from autogen_agentchat.messages import ModelClientStreamingChunkEvent, TextMessage, UserInputRequestedEvent
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_core import RoutedAgent, type_subscription, message_handler, MessageContext, SingleThreadedAgentRuntime, \
    DefaultTopicId, TypeSubscription, ClosureAgent, CancellationToken, ClosureContext, TopicId
from autogen_core.memory import ListMemory, MemoryContent, MemoryMimeType
from pydantic import BaseModel, Field

from app.controllers.requirement import requirement_controller
from app.controllers.requirement_timezone import requirement_timezone_controller
from app.controllers.requirement_doc import requirement_doc_controller
from app.schemas.requirements import RequirementCreate
from app.schemas.requirement_docs import RequirementDoc<PERSON>reate
from llama_index.core import Simple<PERSON>ire<PERSON><PERSON><PERSON><PERSON><PERSON>, Document
from app.api.v1.agent.api.llms import model_client
from .utils import extract_text_from_llm
from autogen_ext.models.openai import OpenAIChatCompletionClient
import pytz
from datetime import datetime
import logging
from tortoise import connections
from app.api.v1.agent.common_messages import KnowledgeExtractionRequestMessage, knowledge_extraction_topic_type, ResponseMessage, task_result_topic_type
logger = logging.getLogger(__name__)

# Helper function to ensure datetime objects have timezone info
def ensure_timezone(dt):
    """Ensure a datetime object has timezone information (UTC if none)"""
    if dt is None:
        return None
    if dt.tzinfo is None:
        return dt.replace(tzinfo=pytz.UTC)
    return dt

def get_now_with_timezone():
    """Get current datetime with UTC timezone information"""
    return datetime.now(pytz.UTC)

# 辅助函数，确保字典中所有datetime对象都有时区信息
def ensure_all_datetimes_have_timezone(obj_dict):
    """确保字典中所有datetime对象都有时区信息"""
    if not obj_dict or not isinstance(obj_dict, dict):
        return obj_dict

    for key, value in list(obj_dict.items()):
        if isinstance(value, datetime):
            if value.tzinfo is None:
                obj_dict[key] = value.replace(tzinfo=pytz.UTC)
        elif isinstance(value, str) and ('date' in key.lower() or 'time' in key.lower() or 'at' in key.lower()):
            try:
                dt = datetime.fromisoformat(value)
                if dt.tzinfo is None:
                    obj_dict[key] = dt.replace(tzinfo=pytz.UTC)
                else:
                    obj_dict[key] = dt
            except (ValueError, TypeError):
                pass
    return obj_dict

# 定义主题类型
requirement_acquisition_topic_type = "requirement_acquisition"
requirement_analysis_topic_type = "requirement_analysis"
requirement_output_topic_type = "requirement_output"
requirement_database_topic_type = "requirement_database"
task_result_topic_type = "collect_result"


class RequirementList(BaseModel):
    requirements: list[RequirementCreate] = Field(..., description="业务需求列表")

    def __init__(self, **data):
        # 预处理requirements数据，确保每个需求都有必填字段
        if 'requirements' in data:
            for i, req_data in enumerate(data['requirements']):
                # 处理requirement_id -> name的转换
                if 'requirement_id' in req_data and 'name' not in req_data:
                    req_data['name'] = req_data['requirement_id']
                    print(f"\n预处理: 将requirement_id字段值({req_data['requirement_id']})复制到name字段\n")

                # 确保name字段存在
                if 'name' not in req_data or not req_data['name']:
                    req_data['name'] = f"需求-{get_now_with_timezone().strftime('%Y%m%d%H%M%S')}-{i}"
                    print(f"\n预处理: 添加默认name字段值: '{req_data['name']}'\n")

                # 确保description字段存在
                if 'description' not in req_data or not req_data['description']:
                    if 'content' in req_data:
                        req_data['description'] = req_data['content']
                        print(f"\n预处理: 将content字段值复制到description字段\n")
                    elif 'requirement_name' in req_data:
                        req_data['description'] = f"需求描述: {req_data['requirement_name']}"
                        print(f"\n预处理: 从requirement_name生成description字段\n")
                    else:
                        req_data['description'] = "需求描述"
                        print(f"\n预处理: 添加默认description字段值: '需求描述'\n")

        # 调用父类初始化
        super().__init__(**data)

        # 确保所有需求的必填字段都有值
        for req in self.requirements:
            # 确保category字段有值
            if not hasattr(req, 'category') or req.category is None or req.category == "":
                req.category = "功能"
                print(f"\n警告: 需求 '{req.name}' 的category字段为空，已设置为默认值'功能'\n")

            # 确保其他必填字段也有值
            if not hasattr(req, 'name') or req.name is None or req.name == "":
                req.name = f"需求-{get_now_with_timezone().strftime('%Y%m%d%H%M%S')}"
                print(f"\n警告: 需求的name字段为空，已设置为默认值'{req.name}'\n")

            if not hasattr(req, 'description') or req.description is None or req.description == "":
                req.description = "需求描述"
                print(f"\n警告: 需求 '{req.name}' 的description字段为空，已设置为默认值'需求描述'\n")

            if not hasattr(req, 'level') or req.level is None or req.level == "":
                req.level = "高"
                print(f"\n警告: 需求 '{req.name}' 的level字段为空，已设置为默认值'高'\n")

            if not hasattr(req, 'estimate') or req.estimate is None:
                req.estimate = 8
                print(f"\n警告: 需求 '{req.name}' 的estimate字段为空，已设置为默认值8\n")

            if not hasattr(req, 'reviewer') or req.reviewer is None or req.reviewer == "":
                req.reviewer = "默认测试人员"
                print(f"\n警告: 需求 '{req.name}' 的reviewer字段为空，已设置为默认值'默认测试人员'\n")


class UrlItem(BaseModel):
    id: str = Field(..., description="URL的唯一ID")
    url: str = Field(..., description="URL地址")
    title: str = Field(default="无标题", description="URL标题")
    content: str = Field(default="", description="URL内容")
    status: str = Field(default="success", description="URL状态")
    handler: str = Field(default="", description="处理人")
    developer: str = Field(default="", description="开发人员")
    tester: str = Field(default="", description="测试人员")

class RequirementFilesMessage(BaseModel):
    user_id: str = Field(..., description="用户ID")
    files: list[str] = Field(default=[], description="需求文件路径列表")
    urls: list[UrlItem] = Field(default=[], description="需求URL列表")
    content: str = Field(..., description="用户输入的内容")
    task: str = Field(default="分析需求文档", description="任务描述")
    project_id: int = Field(default=None, description="项目 ID")
    auto_link_requirements: bool = Field(default=False, description="是否自动关联需求")


class ResponseMessage(BaseModel):
    source: str
    content: str
    is_final: bool = False


@dataclass
class RequirementMessage:
    source: str
    content: Any
    project_id: int = None
    auto_link_requirements: bool = False
    tapd_url: str = None
    tapd_tester: str = None  # 添加TAPD测试人员字段

    def __post_init__(self):
        # 确保tapd_tester字段不为None或空字符串
        if self.tapd_tester is None or self.tapd_tester == '':
            print(f"\n[DEBUG] RequirementMessage初始化: tapd_tester为空，设置默认值\n")
            self.tapd_tester = "默认测试人员"
        print(f"\n[DEBUG] RequirementMessage初始化完成: tapd_tester='{self.tapd_tester}'\n")


@type_subscription(topic_type=requirement_acquisition_topic_type)
class RequirementAcquisitionAgent(RoutedAgent):
    def __init__(self, input_func=None):
        super().__init__("requirement acquisition agent")
        self.input_func = input_func

    @message_handler
    async def handle_message(self, message: RequirementFilesMessage, ctx: MessageContext) -> None:
        # 发送到前端提示
        await self.publish_message(ResponseMessage(source="user", content=f"收到用户指令，准备开始需求分析"),
                                   topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

        try:
            # 初始化文档内容
            doc_content = ""

            # 处理文件内容
            if message.files and len(message.files) > 0:
                file_content = await self.get_document_from_llm_files(message.files)
                doc_content += file_content

                # 发送处理状态到前端
                await self.publish_message(ResponseMessage(source="文档解析智能体", content="文件解析完成，开始对文档进行深入解析"),
                                          topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            # 处理URL内容
            if message.urls and len(message.urls) > 0:
                await self.publish_message(ResponseMessage(source="URL解析智能体", content=f"开始处理{len(message.urls)}个URL内容"),
                                          topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

                # 添加所有URL内容
                for i, url_item in enumerate(message.urls):
                    url_doc = f"## URL文档 {i+1}: {url_item.title}\n\n"
                    url_doc += f"**链接**: {url_item.url}\n\n"
                    url_doc += f"**内容**:\n{url_item.content}\n\n"
                    url_doc += "---\n\n"

                    doc_content += url_doc

                await self.publish_message(ResponseMessage(source="URL解析智能体", content="URL内容处理完成"),
                                          topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            # 添加用户输入的内容
            if message.content.strip():
                doc_content += f"\n\n## 用户输入\n\n{message.content.strip()}\n\n"

            # 检查是否有内容可分析
            if not doc_content.strip():
                raise Exception("没有可分析的内容，请提供文件或URL")

            # 保存需求文档到requirement_doc表
            try:
                # 提取TAPD URL和tester字段，如果有的话
                tapd_url = None
                tapd_handler = None
                tapd_developer = None
                tapd_tester = None
                tapd_name = None

                if message.urls and len(message.urls) > 0:
                    for url_item in message.urls:
                        if 'tapd.cn' in url_item.url:
                            tapd_url = url_item.url
                            tapd_name = url_item.title
                            tapd_handler = url_item.handler
                            tapd_developer = url_item.developer
                            tapd_tester = url_item.tester
                            break

                # 创建需求文档记录
                requirement_doc = RequirementDocCreate(
                    content=doc_content,
                    name=tapd_name,
                    url=tapd_url,
                    handler=tapd_handler,
                    developer=tapd_developer,
                    tester=tapd_tester,
                    project_id=message.project_id,
                    user_id=int(message.user_id) if message.user_id and message.user_id.isdigit() else None,
                    source_type="AI分析",
                    status="待处理"
                )

                # 保存到数据库 - 控制器会自动添加带时区的创建和更新时间
                await requirement_doc_controller.create(requirement_doc)

                # 发送状态消息到前端
                await self.publish_message(
                    ResponseMessage(source="数据库智能体", content="需求文档已保存到数据库"),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            except Exception as e:
                error_msg = f"保存需求文档到数据库失败: {str(e)}"
                print(error_msg)
                await self.publish_message(
                    ResponseMessage(source="数据库智能体", content=error_msg),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            # 创建需求获取智能体
            acquisition_agent = AssistantAgent(
                name="requirement_acquisition_agent",
                model_client=model_client,
                system_message="""
                你是一位专业的需求文档分析专家。请仔细阅读提供的需求文档内容，对其进行整理和摘要。
                重点提取以下信息:
                1. 主要功能需求
                2. 非功能性需求（性能、安全等）
                3. 业务背景和目标
                4. 用户角色和使用场景
                5. 核心术语和概念定义

                以结构化和易于理解的方式组织信息，markdown格式输出

                """,
                model_client_stream=True,
            )
            acquisition_content = ""

            # 运行需求获取流程
            if self.input_func:
                user_proxy = UserProxyAgent(
                    name="user_proxy",
                    input_func=self.input_func
                )

                # 设置对话终止条件
                termination_en = TextMentionTermination("APPROVE")
                termination_zh = TextMentionTermination("同意")
                team = RoundRobinGroupChat([acquisition_agent, user_proxy],
                                         termination_condition=termination_en | termination_zh)

                stream = team.run_stream(task=f"请分析以下需求文档内容:\n\n{doc_content}")
                update_count = 0

                # 存储需求获取记录
                acquisition_memory = ListMemory()

                async for msg in stream:
                    # 模拟流式输出
                    if isinstance(msg, ModelClientStreamingChunkEvent):
                        await self.publish_message(
                            ResponseMessage(source="文档解析智能体", content=msg.content),
                            topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                        continue

                    # 统计需求获取更新次数并保存结果
                    if isinstance(msg, TextMessage):
                        # 保存需求获取记录
                        await acquisition_memory.add(MemoryContent(content=msg.model_dump_json(), mime_type=MemoryMimeType.JSON))

                        if msg.source == "requirement_acquisition_agent":
                            # 用户参与反馈的次数
                            update_count += 1
                            acquisition_content = msg.content
                            continue

                    # 等待用户输入对需求获取的反馈
                    if isinstance(msg, UserInputRequestedEvent) and msg.source == "user_proxy":
                        await self.publish_message(
                            ResponseMessage(source=msg.source, content="请输入修改建议或者直接点击同意"),
                            topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                        continue

                # 如果用户反馈次数大于1，则整合修改后的内容
                if update_count > 1:
                    # 整合智能体
                    summarize_agent = AssistantAgent(
                        name="summarize_agent",
                        system_message="""你是一位需求整理优化专家，根据上下文对话信息，输出用户最终期望的优化后的需求分析。""",
                        model_client=model_client,
                        memory=[acquisition_memory],
                        model_client_stream=True,
                    )

                    stream = summarize_agent.run_stream(task="结合上下文对话信息，输出优化后的完整需求分析，markdown格式输出")
                    async for msg in stream:
                        # 流式输出到前端界面
                        if isinstance(msg, ModelClientStreamingChunkEvent):
                            # 流式输出结果到前端界面
                            await self.publish_message(
                                ResponseMessage(source="需求优化智能体", content=msg.content),
                                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                            continue
                        # 获取优化后的完整结果，将数据传递给下一个智能体
                        if isinstance(msg, TaskResult):
                            acquisition_content = msg.messages[-1].content
                            continue
            else:
                # 用户没有反馈
                task = f"请分析以下需求文档内容:\n\n{doc_content}"
                stream = acquisition_agent.run_stream(task=task)

                async for msg in stream:
                    if isinstance(msg, ModelClientStreamingChunkEvent):
                        await self.publish_message(
                            ResponseMessage(source="需求获取智能体", content=msg.content),
                            topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                        continue
                    if isinstance(msg, TaskResult):
                        acquisition_content = msg.messages[-1].content
                        continue

            # 提取TAPD URL和tester字段，如果有的话
            tapd_url = None
            tapd_tester = None

            # 打印所有URL对象的详细信息，便于调试
            print(f"\n\n[DEBUG] 收到的URL列表数量: {len(message.urls) if message.urls else 0}\n")
            if message.urls and len(message.urls) > 0:
                for i, url in enumerate(message.urls):
                    print(f"\n[DEBUG] URL #{i+1} 详细信息:\n")
                    print(f"  - id: {url.id}")
                    print(f"  - url: {url.url}")
                    print(f"  - title: {url.title}")
                    print(f"  - status: {url.status}")
                    print(f"  - handler: {url.handler}")
                    print(f"  - developer: {url.developer}")
                    print(f"  - tester: {url.tester}")
                    print(f"  - 字段类型: tester={type(url.tester)}, handler={type(url.handler)}")

                # 处理TAPD URL
                for url_item in message.urls:
                    if 'tapd.cn' in url_item.url:
                        tapd_url = url_item.url
                        print(f"\n[DEBUG] 找到TAPD URL: {tapd_url}\n")

                        # 直接使用tester字段，不需要使用hasattr检查
                        tapd_tester = url_item.tester
                        print(f"\n[DEBUG] 提取到TAPD需求的测试人员: '{tapd_tester}'\n")

                        # 如果找到了测试人员，记录详细信息
                        if tapd_tester and tapd_tester.strip():
                            print(f"\n[DEBUG] 成功从URL项中提取测试人员信息: URL={url_item.url}, tester='{tapd_tester}'\n")
                        else:
                            print(f"\n[DEBUG] 警告: URL项中没有测试人员信息或为空字符串: URL={url_item.url}, tester='{tapd_tester}'\n")
                            # 尝试从URL项的其他字段中获取更多信息
                            print(f"\n[DEBUG] URL项的完整信息: id={url_item.id}, title={url_item.title}, status={url_item.status}, handler='{url_item.handler}', developer='{url_item.developer}'\n")

                            # 如果tester为空但handler不为空，使用handler作为备选
                            if url_item.handler and url_item.handler.strip():
                                print(f"\n[DEBUG] 尝试使用handler字段作为测试人员: '{url_item.handler}'\n")
                                tapd_tester = url_item.handler
                                print(f"\n[DEBUG] 已将tapd_tester设置为handler值: '{tapd_tester}'\n")
                            # 如果handler也为空，但developer不为空，使用developer作为备选
                            elif url_item.developer and url_item.developer.strip():
                                print(f"\n[DEBUG] 尝试使用developer字段作为测试人员: '{url_item.developer}'\n")
                                tapd_tester = url_item.developer
                                print(f"\n[DEBUG] 已将tapd_tester设置为developer值: '{tapd_tester}'\n")
                            # 如果所有字段都为空，设置一个默认值
                            else:
                                print(f"\n[DEBUG] 所有字段都为空，设置默认值\n")
                                tapd_tester = "默认测试人员"
                                print(f"\n[DEBUG] 已将tapd_tester设置为默认值: '{tapd_tester}'\n")
                        break

            # 发送给下一个智能体，传递项目ID、自动关联标志、TAPD URL和tester字段
            await self.publish_message(
                RequirementMessage(
                    source=self.id.type,
                    content=acquisition_content,
                    project_id=message.project_id,
                    auto_link_requirements=message.auto_link_requirements,
                    tapd_url=tapd_url,
                    tapd_tester=tapd_tester
                ),
                topic_id=TopicId(requirement_analysis_topic_type, source=self.id.key))

        except Exception as e:
            error_msg = f"需求获取过程出错: {str(e)}"
            print(error_msg)
            await self.publish_message(
                ResponseMessage(source="需求获取智能体", content=error_msg),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

    async def get_document_from_files(self, files: list[str]) -> str:
        """获取文件内容"""
        try:
            data = SimpleDirectoryReader(input_files=files).load_data()
            doc = Document(text="\n\n".join([d.text for d in data[0:]]))
            return doc.text
        except Exception as e:
            raise Exception(f"文件读取失败: {str(e)}")
    async def get_document_from_llm_files(self, files: list[str]) -> str:
        """获取文件内容，支持图片、流程图、表格等数据"""
        extract_contents = ""
        for file in files:
            contents = extract_text_from_llm(file)
            extract_contents+=contents + "\n\n--------------\n\n"
        return extract_contents

@type_subscription(topic_type=requirement_analysis_topic_type)
class RequirementAnalysisAgent(RoutedAgent):
    def __init__(self):
        super().__init__("requirement analysis agent")
        self._prompt = """
        根据如下格式的需求文档，进行需求分析，输出需求分析报告：
            ## 1. Background
            - **角色定位**: 资深软件测试需求分析师，具备跨领域测试经验
            - **核心职责**: 将模糊需求转化为可执行的测试方案，识别需求盲区与风险点
            - **行业经验**: 25年以上金融/医疗/物联网领域测试体系构建经验

            ## 2. Profile
            - **姓名**: TesterBot
            - **职位**: 智能测试需求架构师
            - **特质**:
              - 严谨的逻辑推理能力
              - 敏锐的边界条件发现能力
              - 优秀的风险预判意识

            ## 3. Skills
            - 掌握ISTQB/敏捷测试方法论
            - 精通测试用例设计方法（等价类/边界值/场景法等）
            - 熟练使用JIRA/TestRail/XMind/TAPD
            - 擅长需求可测试性评估
            - 精通API/性能/安全测试策略制定

            ## 4. Goals
            1. 解析用户原始需求，明确测试范围
            2. 识别隐含需求与潜在风险点
            3. 生成结构化测试需求文档
            4. 输出可量化的验收标准
            5. 建立需求追溯矩阵

            ## 5. Constraints
            - 不涉及具体测试代码实现
            - 不替代人工需求评审
            - 保持技术中立立场
            - 遵守ISTQB伦理规范
            - 严格限制在需求文档提及的功能范围内，不要推断或添加文档中未明确提及的功能
            - 不要生成与需求文档无关的需求点

            ## 6. 特别要求
            - 在分析需求时，必须识别并明确标注该需求涉及的前端页面位置名称
            //- 前端页面位置名称可能包括：普通工作台、患者信息、公告、推荐商品、商品、照护商品、订单、物流、健康档案、SOAP列表、事件记录 等
            - 前端页面位置名称可能包括：
              - 共同照护web
                - 普通工作台: 带任务患者人数, 客服消息, 客服咨询, 用户信息, 辅助工具, 推荐商品, 帮助中心, 全部订单, 主动干预, 患者列表, 健康咨询, 饮食点评, 健康档案, 干预记录, 化验, 个人信息, 服务包, 照护提醒, 病历, 公告, 购买记录, 院外情况, 血糖监测, 血压监测, 体脂监测, SOAP列表, 事件记录
                - 预约工作台: 预约管理, 日期选择, 医院选择, 就诊列表, 已预约患者列表, 预约详情, 基本信息, 事件记录, 检查项目, 照护提醒, 预约&改期, AI通话记录, 待预约管理
                - 全科室工作台: 导诊签到, 全科室诊, 就诊日历, 患者管理, 医生看诊, 电子患教手册, 日期选额, 地区选择, 导诊列表,患者列表,预约详情,电子病历,营养处方,患者综述,评估表
                - 平台运营工作台: 糖友商城,FAQ,涨知识,涨知识文章,涨知识视频,活动管理,涨知识评论,商品管理,订单列表,售后记录,积分数据查询,商城顶部提醒,血糖仪发放管理,优惠券发放管理,积分或健康币发放,优惠券使用统计,置顶帖子管理,视频推荐商品管理,商品规格编码管理,服务包库存管理,服务通知,调查问券配置,话题管理,活动配置,商城促销管理,欢迎页配置,Banner配置,通栏配置,置顶文章配置,活动弹窗配置,同伴活动配置,积分优惠配置,甄选TAB页文案配置,福利专区,广播推荐配置
                - 管理员工作台: 诊断管理,分配照护师,部门管理,包产到护,组织管理,权限配置,医生配置,角色管理,归档管理,照护智能体
                - 数据工作台: 销售看板,院内数据,其他设置,收入数据,产品收入数据,服务收入数据,单品销售数据,机构收入,成本覆盖数据,试纸,CGM,福袋,首诊下单率,门诊试纸统计,待核查数据,新老用户,院内数据监控
              - 照护同道app
               - 门诊: 收案,导诊,入组,复盘,公卫,患者管理
               - 消息: 健康咨询,客服消息
               - 内容: 内容广场,我的内容,涨知识视频
               - 我的: 个人主页,我的钱包,健康币
              - 共同照护app
               - 首页: 测血糖,测血压,测体脂,用药,血糖历史,血压历史,体脂历史,问医生,健康咨询,首页Banner,血糖记录,血压记录,饮食记录,运动记录,体脂记录,福利专区,积分任务,血脂记录,闹钟
               - 知识:涨知识文章,视频,涨知识视频,减重训练营,搜索
               - 同伴: 广场,同伴,发帖,通知
               - 甄选: 搜素,商城促销活动,活动购物车,订单,商城列表
               - 我的: 用户信息,健康币,积分,积分乐园,优惠券,订单,我的订单,控糖计划,健康报告,亲友关注,使用指南,邀请有礼,常见问题,意见反馈,联系客服,设置,照护会员,VIP服务
              - 培训系统
              - 电视大屏
              - 共同照护ocr小程序
            - 在分析报告中，为每个需求点添加"[{前端页面位置名称}]"的标记
            - 如果一个需求涉及多个前端页面位置，请列出所有相关位置

            ## 7. Output Format
            ```markdown
            # 测试需求分析文档

            ## 测试目标
            - [清晰的功能目标描述]

            ## 需求拆解
            | 需求ID | 需求描述 | 测试类型 | 优先级 | 验收标准 | 功能模块 |
            |--------|----------|----------|--------|----------|----------|
            | REQ-001 | 用户可以查看商品列表 | 功能测试 | 高 | 能正确显示所有商品 | 商品、推荐商品 |

            ## 风险分析
            - **高优先级风险**:
              - [风险描述] → [缓解方案]

            ## 测试策略
            - [功能测试]:
              - 覆盖场景:
                - [场景1] [功能模块: 普通工作台-推荐商品]
                - [场景2] [功能模块: 甄选-商品列表]
            - [非功能测试]:
              - 性能指标: [RPS ≥ 1000]
              - 安全要求: [OWASP TOP10覆盖]

            ## 待澄清项
            - [问题1] (需业务方确认) [功能模块: 甄选-商城促销活动]
            - [问题2] (需架构师确认) [功能模块: 平台运营工作台-商品管理]
            ```

            ## 8. 项目关联性说明

            请注意以下三个相关项目的关系：
            - 共同照护web：医生及照护师使用的web端系统
            - 照护同道app：医生及照护师使用的移动app端
            - 共同照护app：患者使用的移动app端

            这三个项目属于同一个医疗生态系统的不同端口，面向不同的用户群体，但功能上有一定的关联性和互补性。在分析需求时，可以考虑这种关联性，但仍然要严格限制在需求文档提及的功能范围内。
        """

    @message_handler
    async def handle_message(self, message: RequirementMessage, ctx: MessageContext) -> None:
        # 导入知识点检索模块和项目控制器
        from app.api.v1.agent.knowledge_retrieval import get_relevant_knowledge_points, format_knowledge_points
        from app.controllers.project import project_controller

        await self.publish_message(ResponseMessage(source="user", content=f"被分析文档内容为：{message.content}"),
                                   topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
        # 发送状态消息到前端
        await self.publish_message(
            ResponseMessage(source="需求分析智能体", content="开始进行需求分析......"),
            topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

        # 获取当前项目信息
        project_name = "未知项目"
        project_desc = ""
        try:
            if message.project_id:
                project = await project_controller.get(id=message.project_id)
                project_name = project.name
                project_desc = project.desc
                await self.publish_message(
                    ResponseMessage(source="需求分析智能体", content=f"当前选择的项目: {project_name}"),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

                # 发送项目背景信息到前端
                if project_desc:
                    await self.publish_message(
                        ResponseMessage(source="需求分析智能体", content=f"项目背景信息: {project_desc}"),
                        topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
        except Exception as e:
            print(f"获取项目信息失败: {str(e)}")
            await self.publish_message(
                ResponseMessage(source="需求分析智能体", content=f"获取项目信息失败: {str(e)}"),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

        # 获取相关知识点
        try:
            # 发送状态消息
            await self.publish_message(
                ResponseMessage(source="需求分析智能体", content="正在获取项目相关知识点..."),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            # 获取与需求相关的知识点（允许在相关项目间检索，但限制数量）
            knowledge_points = await get_relevant_knowledge_points(
                project_id=message.project_id,
                requirement=message.content,
                max_direct=5,  # 限制直接相关知识点数量，避免引入过多噪音
                max_indirect=3,  # 限制间接相关知识点数量
                max_background=2,  # 限制背景知识点数量
                cross_project=True  # 启用跨项目知识点检索，考虑到三个项目间的关联性
            )

            # 格式化知识点为背景信息文本
            knowledge_context = format_knowledge_points(knowledge_points)

            # 统计获取的知识点数量
            total_points = (
                len(knowledge_points.get("direct", [])) +
                len(knowledge_points.get("indirect", [])) +
                len(knowledge_points.get("background", []))
            )

            # 记录使用了多少知识点
            if total_points > 0:
                # 基本信息
                await self.publish_message(
                    ResponseMessage(
                        source="需求分析智能体",
                        content=f"已获取{total_points}条相关知识点作为背景信息"
                    ),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
                )

                # 打印所有知识点详情
                all_knowledge_points = knowledge_points.get("all_knowledge_points", [])
                if all_knowledge_points:
                    # 构建知识点详情的Markdown格式文本
                    knowledge_details = "## 相似性检索获取到的所有知识点详情\n\n"

                    # 按相关性级别分组
                    for level in ["直接相关", "间接相关", "背景相关"]:
                        level_points = [p for p in all_knowledge_points if p["level"] == level]
                        if level_points:
                            knowledge_details += f"### {level} ({len(level_points)}条)\n\n"
                            for point in level_points:
                                knowledge_details += f"**{point['index']}. [{point['type']}] {point['title']}**\n"
                                knowledge_details += f"- 来源: {point['source']}\n"
                                knowledge_details += f"- 内容: {point['content_preview']}\n"
                                if point['tags']:
                                    knowledge_details += f"- 标签: {point['tags']}\n"
                                knowledge_details += "\n"

                    # 发送知识点详情
                    await self.publish_message(
                        ResponseMessage(
                            source="需求分析智能体",
                            content=knowledge_details
                        ),
                        topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
                    )
            else:
                await self.publish_message(
                    ResponseMessage(
                        source="需求分析智能体",
                        content="未找到相关知识点，将直接进行需求分析"
                    ),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
                )

        except Exception as e:
            print(f"获取知识点失败: {str(e)}")
            import traceback
            print(f"错误堆栈: {traceback.format_exc()}")
            knowledge_context = ""
            await self.publish_message(
                ResponseMessage(
                    source="需求分析智能体",
                    content=f"获取知识点过程中出错: {str(e)}"
                ),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
            )

        # 根据项目名称调整系统提示词中的功能位置名称列表
        system_message = self._prompt

        # 根据项目名称筛选功能位置名称
        if "共同照护web" in project_name or "web" in project_name.lower():
            # 只保留共同照护web相关的功能位置
            system_message = system_message.replace(
                "- 前端页面位置名称可能包括：",
                "- 前端页面位置名称包括："
            )
            # 确保只显示共同照护web的功能位置
            import re
            # 先找到共同照护web部分
            match = re.search(r'- 共同照护web([\s\S]*?)(?=- 照护同道app|$)', system_message)
            if match:
                web_content = match.group(1)
                # 替换整个功能位置列表部分
                system_message = re.sub(
                    r'- 前端页面位置名称包括：[\s\S]*?(?=## |$)',
                    f"- 前端页面位置名称包括：\n              - 共同照护web{web_content}",
                    system_message
                )
            # 移除其他项目的功能位置
            import re
            system_message = re.sub(
                r'- 照护同道app[\s\S]*?- 共同照护app[\s\S]*?- 培训系统[\s\S]*?- 电视大屏[\s\S]*?- 共同照护ocr小程序',
                '',
                system_message
            )
            # 添加项目说明
            system_message += f"\n\n## 当前项目\n当前选择的项目是 {project_name}，请只使用共同照护web相关的功能位置名称。"

        elif "照护同道" in project_name or "同道app" in project_name:
            # 只保留照护同道app相关的功能位置
            system_message = system_message.replace(
                "- 前端页面位置名称可能包括：",
                "- 前端页面位置名称包括："
            )
            # 确保只显示照护同道app的功能位置
            import re
            # 先找到照护同道app部分
            match = re.search(r'- 照护同道app([\s\S]*?)(?=- 共同照护app|$)', system_message)
            if match:
                app_content = match.group(1)
                # 替换整个功能位置列表部分
                system_message = re.sub(
                    r'- 前端页面位置名称包括：[\s\S]*?(?=## |$)',
                    f"- 前端页面位置名称包括：\n              - 照护同道app{app_content}",
                    system_message
                )
            # 移除其他项目的功能位置
            import re
            system_message = re.sub(
                r'- 共同照护web[\s\S]*?- 照护同道app',
                '- 照护同道app',
                system_message
            )
            system_message = re.sub(
                r'- 共同照护app[\s\S]*?- 培训系统[\s\S]*?- 电视大屏[\s\S]*?- 共同照护ocr小程序',
                '',
                system_message
            )
            # 添加项目说明
            system_message += f"\n\n## 当前项目\n当前选择的项目是 {project_name}，请只使用照护同道app相关的功能位置名称。"

        elif "共同照护app" in project_name or "照护app" in project_name:
            # 只保留共同照护app相关的功能位置
            system_message = system_message.replace(
                "- 前端页面位置名称可能包括：",
                "- 前端页面位置名称包括："
            )
            # 确保只显示共同照护app的功能位置
            import re
            # 先找到共同照护app部分
            match = re.search(r'- 共同照护app([\s\S]*?)(?=- 培训系统|$)', system_message)
            if match:
                app_content = match.group(1)
                # 替换整个功能位置列表部分
                system_message = re.sub(
                    r'- 前端页面位置名称包括：[\s\S]*?(?=## |$)',
                    f"- 前端页面位置名称包括：\n              - 共同照护app{app_content}",
                    system_message
                )
            # 移除其他项目的功能位置
            import re
            system_message = re.sub(
                r'- 共同照护web[\s\S]*?- 照护同道app[\s\S]*?- 共同照护app',
                '- 共同照护app',
                system_message
            )
            system_message = re.sub(
                r'- 培训系统[\s\S]*?- 电视大屏[\s\S]*?- 共同照护ocr小程序',
                '',
                system_message
            )
            # 添加项目说明
            system_message += f"\n\n## 当前项目\n当前选择的项目是 {project_name}，请只使用共同照护app相关的功能位置名称。"

        else:
            # 默认情况下保留所有功能位置，但添加项目说明
            system_message += f"\n\n## 当前项目\n当前选择的项目是 {project_name}，请根据需求内容选择最合适的功能位置名称。"

        # 添加项目背景信息
        if project_desc:
            system_message += f"\n\n## 项目背景:\n{project_desc}"

        # 添加知识点背景信息
        if knowledge_context:
            system_message += f"\n\n## 需求相关知识背景:\n{knowledge_context}"

        # 创建需求分析智能体
        analysis_agent = AssistantAgent(
            name="requirement_analysis_agent",
            model_client=model_client,
            system_message=system_message,
            model_client_stream=True,
        )

        # 构建任务描述
        task = f"""请根据以下需求内容进行分析，并输出规范的需求分析报告：

重要提示：
1. 严格限制在需求文档提及的功能范围内，不要推断或添加文档中未明确提及的功能
2. 不要生成与需求文档无关的需求点
3. 只标注文档中实际提到的前端页面位置，不要添加未提及的位置
4. 如果文档主要关于商品推荐，请只关注商品推荐相关功能，不要添加血糖测量、预约管理等无关功能
5. 当前项目是: {project_name}，请只使用该项目相关的功能位置名称

需求内容：
{message.content}"""
        analysis_report = ""

        # 流式执行需求分析
        try:
            stream = analysis_agent.run_stream(task=task)
            async for msg in stream:
                if isinstance(msg, ModelClientStreamingChunkEvent):
                    # 流式输出结果到前端界面
                    await self.publish_message(
                        ResponseMessage(source="需求分析智能体", content=msg.content),
                        topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                    continue
                if isinstance(msg, TaskResult):
                    analysis_report = msg.messages[-1].content
                    continue
        except Exception as e:
            error_msg = f"需求分析执行过程中出错: {str(e)}"
            print(error_msg)
            import traceback
            print(f"错误堆栈: {traceback.format_exc()}")
            await self.publish_message(
                ResponseMessage(source="需求分析智能体", content=error_msg),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
            # 设置一个默认的分析报告，以便流程能够继续
            analysis_report = f"需求分析执行失败，错误信息: {str(e)}"

        # 发送完成消息
        await self.publish_message(
            ResponseMessage(source="需求分析智能体", content="需求分析完成"),
            topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

        # 发送知识提取请求
        try:
            # 发送状态消息
            await self.publish_message(
                ResponseMessage(source="需求分析智能体", content="正在提取知识点..."),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
            )

            # 发送知识提取请求
            await self.publish_message(
                KnowledgeExtractionRequestMessage(
                    content=analysis_report,
                    project_id=message.project_id,
                    source_type="requirement",
                    title="需求分析报告",
                    source_url=getattr(message, 'tapd_url', None),
                    source_reference_id=getattr(message, 'tapd_url', None)  # 使用TAPD URL作为引用ID
                ),
                topic_id=TopicId(knowledge_extraction_topic_type, source=self.id.key)
            )
        except Exception as e:
            print(f"发送知识提取请求失败: {str(e)}")

        # 发送给下一个智能体，传递项目ID、自动关联标志、TAPD URL和tester字段
        await self.publish_message(
            RequirementMessage(
                source=self.id.type,
                content=analysis_report,
                project_id=message.project_id,
                auto_link_requirements=message.auto_link_requirements,
                tapd_url=message.tapd_url,  # 传递TAPD URL
                tapd_tester=message.tapd_tester  # 传递TAPD测试人员
            ),
            topic_id=TopicId(requirement_output_topic_type, source=self.id.key))


@type_subscription(topic_type=requirement_output_topic_type)
class RequirementOutputAgent(RoutedAgent):
    def __init__(self):
        super().__init__("requirement output agent")
        self._prompt = """
        请根据需求分析报告进行详细的需求整理，严格限制在报告中实际提及的功能范围内，每条需求信息都必须严格按照下面的格式和要求生成，生成合适条数的需求项。
        请注意，输出必须是一个有效的JSON格式，不要包含任何解释或前导文本。仅输出JSON对象本身，包含requirements数组。

        重要提示：
        1. 严格限制在需求分析报告提及的功能范围内，不要推断或添加报告中未明确提及的功能
        2. 不要生成与需求分析报告无关的需求点
        3. 只标注报告中实际提到的前端页面位置，不要添加未提及的位置
        4. 如果报告主要关于商品推荐，请只关注商品推荐相关功能，不要添加血糖测量、预约管理等无关功能
        5. 不要生成id字段，只使用name字段作为需求名称

        项目关联性说明：
        - 共同照护web：医生及照护师使用的web端系统
        - 照护同道app：医生及照护师使用的移动app端
        - 共同照护app：患者使用的移动app端

        这三个项目属于同一个医疗生态系统的不同端口，面向不同的用户群体，但功能上有一定的关联性和互补性。
        在处理需求时，可以考虑这种关联性，但仍然要严格限制在需求分析报告提及的功能范围内。

        生成的JSON格式必须符合这个结构:
        {
          "requirements": [
            {
              "name": "需求名称",
              "description": "作为一名<某类型的用户>，我希望<达成某些目的>，这样可以<开发的价值>。",
              "category": "功能/性能/安全/接口/体验/改进/UI/兼容/异常/其它",
              "parent": "该需求的上级需求",
              "module": "所属的业务模块",
              "level": "需求层级[BR]",
              "reviewer": "TAPD需求中的测试人员(tester)字段值",
              "estimate": 8,
              "criteria": "明确的验收标准",
              "remark": "备注信息 [功能模块: 具体的前端页面位置名称]",
              "keywords": "提取当前需求的关键词，逗号分隔",
              "project_id": 2,
              "tapd_url": "TAPD需求的URL地址，如果有的话"
            }
          ]
        }

        特别注意：
        1. 必须使用"name"字段而不是"id"字段来表示需求名称
        2. 必须使用"estimate"字段而不是"estimated"字段来表示预计工时
        3. 必须包含"project_id"字段，且值必须是数字类型
        4. 不要在JSON中添加任何额外的字段，如"id"等
        5. description字段必须严格按照用户故事格式编写："作为一名<某类型的用户>，我希望<达成某些目的>，这样可以<开发的价值>。"
           - 必须包含"作为一名..."、"我希望..."和"这样可以..."三个部分
           - 用户类型必须明确指出是医生、照护师、患者等具体角色
           - 目的必须具体明确，不能笼统
           - 开发价值必须说明此功能带来的实际好处
        6. criteria字段必须包含具体的验收标准，不能简单复述需求
        7. 每个需求必须有明确的keywords，用逗号分隔关键词

        请确保每个需求项都包含所有必填字段，并且值类型正确。尤其注意estimate必须是数字类型而不是字符串。
        如果有TAPD需求URL，请确保将其包含在tapd_url字段中。
        重要：reviewer字段必须使用TAPD需求中的测试人员(tester)字段值，不要使用“默认”或“默认值”。如果没有提供测试人员信息，请将reviewer字段留空。
        注意：reviewer字段是必填字段，必须从 TAPD需求的测试人员(tester)字段获取，这是一个强制要求。

        特别要求：
        1. 每个需求的备注字段(remark)必须包含"[功能模块: 具体的前端页面位置名称]"，其中"具体的前端页面位置名称"是该需求在前端的具体位置
        2. 如果需求已有备注内容，则在原有备注内容后追加"[功能模块: 具体的前端页面位置名称]"
        3. 前端页面位置名称应该简洁明了，能够准确描述该需求所属的前端页面位置
        4. 必须为每个需求指定功能模块位置，这是一个强制要求，不能省略
        # 5. 同时，module字段也应该包含相关的功能模块信息
        6. 前端页面位置名称可能包括：
          - 共同照护web
            - 普通工作台: 带任务患者人数, 客服消息, 客服咨询, 用户信息, 辅助工具, 推荐商品, 帮助中心, 全部订单, 主动干预, 患者列表, 健康咨询, 饮食点评, 健康档案, 干预记录, 化验, 个人信息, 服务包, 照护提醒, 病历, 公告, 购买记录, 院外情况, 血糖监测, 血压监测, 体脂监测, SOAP列表, 事件记录
            - 预约工作台: 预约管理, 日期选择, 医院选择, 就诊列表, 已预约患者列表, 预约详情, 基本信息, 事件记录, 检查项目, 照护提醒, 预约&改期, AI通话记录, 待预约管理
            - 全科室工作台: 导诊签到, 全科室诊, 就诊日历, 患者管理, 医生看诊, 电子患教手册, 日期选额, 地区选择, 导诊列表, 患者列表, 预约详情, 电子病历, 营养处方, 患者综述, 评估表
            - 平台运营工作台: 糖友商城, FAQ, 涨知识, 涨知识文章, 涨知识视频, 活动管理, 涨知识评论, 商品管理, 订单列表, 售后记录, 积分数据查询, 商城顶部提醒, 血糖仪发放管理, 优惠券发放管理, 积分或健康币发放, 优惠券使用统计, 置顶帖子管理, 视频推荐商品管理, 商品规格编码管理, 服务包库存管理, 服务通知, 调查问券配置, 话题管理, 活动配置, 商城促销管理, 欢迎页配置, Banner配置, 通栏配置, 置顶文章配置, 活动弹窗配置, 同伴活动配置, 积分优惠配置, 甄选TAB页文案配置, 福利专区, 广播推荐配置
            - 管理员工作台: 诊断管理, 分配照护师, 部门管理, 包产到护, 组织管理, 权限配置, 医生配置, 角色管理, 归档管理, 照护智能体
            - 数据工作台: 销售看板, 院内数据, 其他设置, 收入数据, 产品收入数据, 服务收入数据, 单品销售数据, 机构收入, 成本覆盖数据, 试纸, CGM, 福袋, 首诊下单率, 门诊试纸统计, 待核查数据, 新老用户, 院内数据监控
          - 照护同道app
            - 门诊: 收案, 导诊, 入组, 复盘, 公卫, 患者管理
            - 消息: 健康咨询, 客服消息
            - 内容: 内容广场, 我的内容, 涨知识视频
            - 我的: 个人主页, 我的钱包, 健康币
          - 共同照护app
            - 首页: 测血糖, 测血压, 测体脂, 用药, 血糖历史, 血压历史, 体脂历史, 问医生, 健康咨询, 首页Banner, 血糖记录, 血压记录, 饮食记录, 运动记录, 体脂记录, 福利专区, 积分任务, 血脂记录, 闹钟
            - 知识: 涨知识文章, 视频, 涨知识视频, 减重训练营, 搜索
            - 同伴: 广场, 同伴, 发帖, 通知
            - 甄选: 搜索, 商城促销活动, 活动购物车, 订单, 商城列表
            - 我的: 用户信息, 健康币, 积分, 积分乐园, 优惠券, 订单, 我的订单, 控糖计划, 健康报告, 亲友关注, 使用指南, 邀请有礼, 常见问题, 意见反馈, 联系客服, 设置, 照护会员, VIP服务
          - 培训系统
          - 电视大屏
          - 共同照护ocr小程序
        5. 仔细分析需求分析报告中的内容，识别每个需求涉及的前端页面位置
        6. 如果一个需求涉及多个前端页面位置，可以在备注中列出所有相关位置，如"[功能模块: 普通工作台,推荐商品,公告]"
        7. 如果需求分析报告中已经标注了功能模块，请直接使用这些标注
        """

    @message_handler
    async def handle_message(self, message: RequirementMessage, ctx: MessageContext) -> None:
        # 导入项目控制器
        from app.controllers.project import project_controller

        # 获取当前项目信息
        project_name = "未知项目"
        try:
            if message.project_id:
                project = await project_controller.get(id=message.project_id)
                project_name = project.name
                await self.publish_message(
                    ResponseMessage(source="需求结构化智能体", content=f"当前选择的项目: {project_name}"),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
        except Exception as e:
            print(f"获取项目信息失败: {str(e)}")
            await self.publish_message(
                ResponseMessage(source="需求结构化智能体", content=f"获取项目信息失败: {str(e)}"),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

        # 根据项目名称调整系统提示词中的功能位置名称列表
        system_message = self._prompt

        # 根据项目名称筛选功能位置名称
        if "共同照护web" in project_name or "web" in project_name.lower():
            # 只保留共同照护web相关的功能位置
            system_message = system_message.replace(
                "- 前端页面位置名称可能包括：",
                "- 前端页面位置名称包括："
            )
            # 确保只显示共同照护web的功能位置
            import re
            # 先找到共同照护web部分
            match = re.search(r'- 共同照护web([\s\S]*?)(?=- 照护同道app|$)', system_message)
            if match:
                web_content = match.group(1)
                # 替换整个功能位置列表部分
                system_message = re.sub(
                    r'- 前端页面位置名称包括：[\s\S]*?(?=## |$)',
                    f"- 前端页面位置名称包括：\n          - 共同照护web{web_content}",
                    system_message
                )
            # 移除其他项目的功能位置
            import re
            system_message = re.sub(
                r'- 照护同道app[\s\S]*?- 共同照护app[\s\S]*?- 培训系统[\s\S]*?- 电视大屏[\s\S]*?- 共同照护ocr小程序',
                '',
                system_message
            )
            # 添加项目说明
            system_message += f"\n\n## 当前项目\n当前选择的项目是 {project_name}，请只使用共同照护web相关的功能位置名称。"

        elif "照护同道" in project_name or "同道app" in project_name:
            # 只保留照护同道app相关的功能位置
            system_message = system_message.replace(
                "- 前端页面位置名称可能包括：",
                "- 前端页面位置名称包括："
            )
            # 确保只显示照护同道app的功能位置
            import re
            # 先找到照护同道app部分
            match = re.search(r'- 照护同道app([\s\S]*?)(?=- 共同照护app|$)', system_message)
            if match:
                app_content = match.group(1)
                # 替换整个功能位置列表部分
                system_message = re.sub(
                    r'- 前端页面位置名称包括：[\s\S]*?(?=## |$)',
                    f"- 前端页面位置名称包括：\n          - 照护同道app{app_content}",
                    system_message
                )
            # 移除其他项目的功能位置
            import re
            system_message = re.sub(
                r'- 共同照护web[\s\S]*?- 照护同道app',
                '- 照护同道app',
                system_message
            )
            system_message = re.sub(
                r'- 共同照护app[\s\S]*?- 培训系统[\s\S]*?- 电视大屏[\s\S]*?- 共同照护ocr小程序',
                '',
                system_message
            )
            # 添加项目说明
            system_message += f"\n\n## 当前项目\n当前选择的项目是 {project_name}，请只使用照护同道app相关的功能位置名称。"

        elif "共同照护app" in project_name or "照护app" in project_name:
            # 只保留共同照护app相关的功能位置
            system_message = system_message.replace(
                "- 前端页面位置名称可能包括：",
                "- 前端页面位置名称包括："
            )
            # 确保只显示共同照护app的功能位置
            import re
            # 先找到共同照护app部分
            match = re.search(r'- 共同照护app([\s\S]*?)(?=- 培训系统|$)', system_message)
            if match:
                app_content = match.group(1)
                # 替换整个功能位置列表部分
                system_message = re.sub(
                    r'- 前端页面位置名称包括：[\s\S]*?(?=## |$)',
                    f"- 前端页面位置名称包括：\n          - 共同照护app{app_content}",
                    system_message
                )
            # 移除其他项目的功能位置
            import re
            system_message = re.sub(
                r'- 共同照护web[\s\S]*?- 照护同道app[\s\S]*?- 共同照护app',
                '- 共同照护app',
                system_message
            )
            system_message = re.sub(
                r'- 培训系统[\s\S]*?- 电视大屏[\s\S]*?- 共同照护ocr小程序',
                '',
                system_message
            )
            # 添加项目说明
            system_message += f"\n\n## 当前项目\n当前选择的项目是 {project_name}，请只使用共同照护app相关的功能位置名称。"

        else:
            # 默认情况下保留所有功能位置，但添加项目说明
            system_message += f"\n\n## 当前项目\n当前选择的项目是 {project_name}，请根据需求内容选择最合适的功能位置名称。"

        # 使用预先创建的JSON模型客户端
        from app.api.v1.agent.api.llms import model_client_json

        output_agent = AssistantAgent(
            name="requirement_output_agent",
            model_client=model_client_json,
            system_message=system_message,
            model_client_stream=True,
        )

        # 发送状态消息到前端
        try:
            print("\n===== 开始执行需求结构化智能体 =====")
            print(f"消息类型: {type(message)}")
            print(f"消息内容长度: {len(message.content)} 字符")
            print(f"项目ID: {message.project_id}")
            print("===================================\n")

            print("\n===== 开始发送状态消息 =====")
            await self.publish_message(
                ResponseMessage(source="需求结构化智能体", content="正在进行需求结构化......\n\n"),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
            print("===== 状态消息发送成功 =====\n")

            # 构建任务描述
            task = f"""根据以下需求分析报告，生成结构化需求列表：

重要提示：
1. 每个需求的description字段必须严格按照用户故事格式："作为一名<某类型的用户>，我希望<达成某些目的>，这样可以<开发的价值>。"
2. 不要生成id字段，只使用name字段
3. 确保所有字段都符合要求，特别是description、criteria和keywords字段
4. 每个需求的remark字段必须包含"[功能模块: 具体的前端页面位置名称]"，这是一个强制要求
5. 每个需求的module字段必须包含需求的类型，如"功能"、"性能"、"安全"等
6. 每个需求的category字段必须包含需求的类型，如"功能"、"性能"、"安全"等
7. 必须为每个需求指定具体的前端页面位置，不能使用笼统的描述


需求分析报告内容：
{message.content}"""
            output_content = ""

            # 添加调试信息
            print("\n\n===== 开始需求结构化 =====")
            print(f"任务长度: {len(task)} 字符")
            print(f"使用模型: deepseek-chat")  # 直接使用初始化时指定的模型名称
            print(f"API基础URL: https://api.deepseek.com/v1")  # 直接使用硬编码的URL
            print("===========================\n\n")

            # 发送状态消息到前端
            await self.publish_message(
                ResponseMessage(source="需求结构化智能体", content="正在连接大模型API..."),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            # 设置超时
            import asyncio
            from contextlib import asynccontextmanager

            @asynccontextmanager
            async def timeout_context(seconds):
                try:
                    task = asyncio.create_task(asyncio.sleep(seconds))
                    yield
                    task.cancel()
                except asyncio.CancelledError:
                    print(f"操作被取消，可能是超时（{seconds}秒）")
                    raise

            # 流式执行需求结构化，添加超时控制
            try:
                async with timeout_context(120):  # 设置120秒超时
                    stream = output_agent.run_stream(task=task)
                    print("成功获取流式响应")

                    # 发送状态消息到前端
                    await self.publish_message(
                        ResponseMessage(source="需求结构化智能体", content="已连接大模型API，正在生成结果..."),
                        topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

                    chunk_count = 0
                    async for msg in stream:
                        chunk_count += 1
                        if chunk_count % 10 == 0:
                            print(f"已接收 {chunk_count} 个响应块")

                        if isinstance(msg, ModelClientStreamingChunkEvent):
                            # 流式输出结果到前端界面
                            await self.publish_message(
                                ResponseMessage(source="需求结构化智能体", content=msg.content),
                                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                            continue
                        if isinstance(msg, TaskResult):
                            output_content = msg.messages[-1].content
                            print(f"成功获取完整响应，长度: {len(output_content)} 字符")
                            continue
            except asyncio.TimeoutError:
                error_msg = "需求结构化超时（120秒），请尝试减少输入内容或稍后重试"
                print(error_msg)
                await self.publish_message(
                    ResponseMessage(source="需求结构化智能体", content=error_msg),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                raise Exception(error_msg)

            # 确保输出内容是有效的JSON
            parsed_output = json.loads(output_content)

            # 如果有TAPD URL，将其添加到每个需求中
            if hasattr(message, 'tapd_url') and message.tapd_url and 'requirements' in parsed_output:
                for req in parsed_output['requirements']:
                    # 添加TAPD URL
                    if 'tapd_url' not in req or not req['tapd_url']:
                        req['tapd_url'] = message.tapd_url

                    # 如果有TAPD测试人员，将其设置为reviewer
                    if hasattr(message, 'tapd_tester') and message.tapd_tester:
                        # 打印调试信息
                        print(f"\n处理需求 '{req.get('name', '')}' 的reviewer字段\n")
                        print(f"\n当前reviewer值: '{req.get('reviewer', '')}'\n")
                        print(f"\nTAPD测试人员值: '{message.tapd_tester}'\n")

                        # 无论如何，始终使用TAPD测试人员作为reviewer字段的值
                        req['reviewer'] = message.tapd_tester
                        print(f"\n需求 '{req.get('name', '')}' 的reviewer字段已设置为TAPD测试人员: {message.tapd_tester}\n")
                    else:
                        print(f"\n警告: 需求 '{req.get('name', '')}' 没有TAPD测试人员信息\n")
                        # 设置默认值以避免空字符串验证错误
                        if not req.get('reviewer') or req.get('reviewer') == "" or req.get('reviewer') == "TAPD需求中的测试人员(tester)字段值":
                            req['reviewer'] = "默认测试人员"
                            print(f"\n需求 '{req.get('name', '')}' 的reviewer字段设置为默认值: 默认测试人员\n")

                    # 再次检查reviewer字段，确保它不为空
                    if not req.get('reviewer'):
                        req['reviewer'] = "默认测试人员"
                        print(f"\n最终检查: 需求 '{req.get('name', '')}' 的reviewer字段设置为默认值\n")

                # 重新序列化为JSON
                output_content = json.dumps(parsed_output, ensure_ascii=False)

            # 发送完成消息
            await self.publish_message(
                ResponseMessage(source="需求结构化智能体", content=f"需求结构化完成，共生成{len(parsed_output.get('requirements', []))}条需求"),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            # 发送给下一个智能体，传递项目ID、自动关联标志、TAPD URL和tester字段
            await self.publish_message(
                RequirementMessage(
                    source=self.id.type,
                    content=output_content,
                    project_id=message.project_id,
                    auto_link_requirements=message.auto_link_requirements,
                    tapd_url=message.tapd_url,  # 传递TAPD URL
                    tapd_tester=message.tapd_tester  # 传递TAPD测试人员
                ),
                topic_id=TopicId(requirement_database_topic_type, source=self.id.key))

        except json.JSONDecodeError as e:
            error_msg = f"需求结构化生成的内容不是有效的JSON: {str(e)}"
            print(error_msg)

            # 打印输出内容的前200个字符，帮助调试
            if output_content:
                print(f"\n输出内容前200个字符: {output_content[:200]}\n")
            else:
                print("\n警告: 输出内容为空\n")

            # 尝试修复JSON格式错误
            from app.api.v1.agent.api.llms import model_client_json
            fixed_agent = AssistantAgent(
                name="json_fix_agent",
                model_client=model_client_json,
                system_message="""
                你是一位专业的JSON修复专家。你的任务是接收一个可能格式不正确的JSON字符串，
                并将其转换为有效的JSON格式。只返回修复后的JSON字符串，不要包含任何解释或额外文本。
                确保输出符合以下结构:
                {
                  "requirements": [
                    {
                      "name": "...",
                      "description": "...",
                      ...其他字段...
                    }
                  ]
                }
                """,
                model_client_stream=False,
            )

            # 通知前端
            await self.publish_message(
                ResponseMessage(source="需求结构化智能体", content="JSON格式有误，正在尝试修复..."),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            # 尝试修复JSON
            fix_result = await fixed_agent.run(task=f"修复以下内容为正确的JSON格式:\n\n{output_content}")
            fixed_content = fix_result.messages[-1].content

            try:
                # 再次验证JSON
                fixed_parsed = json.loads(fixed_content)

                # 如果有TAPD URL，将其添加到每个需求中
                if hasattr(message, 'tapd_url') and message.tapd_url and 'requirements' in fixed_parsed:
                    for req in fixed_parsed['requirements']:
                        # 添加TAPD URL
                        if 'tapd_url' not in req or not req['tapd_url']:
                            req['tapd_url'] = message.tapd_url

                        # 如果有TAPD测试人员，将其设置为reviewer
                        if hasattr(message, 'tapd_tester') and message.tapd_tester:
                            # 无论当前reviewer字段的值是什么，都替换为TAPD测试人员
                            req['reviewer'] = message.tapd_tester
                            print(f"\n需求 '{req.get('name', '')}' 的reviewer字段设置为TAPD测试人员: {message.tapd_tester}\n")
                        else:
                            print(f"\n警告: 需求 '{req.get('name', '')}' 没有TAPD测试人员信息\n")
                            # 设置默认值以避免空字符串验证错误
                            if not req.get('reviewer') or req.get('reviewer') == "" or req.get('reviewer') == "TAPD需求中的测试人员(tester)字段值":
                                req['reviewer'] = "默认测试人员"
                                print(f"\n需求 '{req.get('name', '')}' 的reviewer字段设置为默认值: 默认测试人员\n")

                        # 再次检查reviewer字段，确保它不为空
                        if not req.get('reviewer'):
                            req['reviewer'] = "默认测试人员"
                            print(f"\n最终检查: 需求 '{req.get('name', '')}' 的reviewer字段设置为默认值\n")

                    # 重新序列化为JSON
                    fixed_content = json.dumps(fixed_parsed, ensure_ascii=False)

                # 发送给下一个智能体，传递项目ID、自动关联标志、TAPD URL和tester字段
                await self.publish_message(
                    RequirementMessage(
                        source=self.id.type,
                        content=fixed_content,
                        project_id=message.project_id,
                        auto_link_requirements=message.auto_link_requirements,
                        tapd_url=message.tapd_url,  # 传递TAPD URL
                        tapd_tester=message.tapd_tester  # 传递TAPD测试人员
                    ),
                    topic_id=TopicId(requirement_database_topic_type, source=self.id.key))

                await self.publish_message(
                    ResponseMessage(source="需求结构化智能体", content="JSON修复成功，已完成需求结构化"),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            except json.JSONDecodeError:
                await self.publish_message(
                    ResponseMessage(source="需求结构化智能体", content="无法修复JSON格式，需求结构化失败"),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
        except Exception as e:
            import traceback
            error_msg = f"需求结构化过程出错: {str(e)}"
            print(error_msg)

            # 打印详细的堆栈跟踪
            print("\n===== 详细错误信息 =====")
            traceback.print_exc()
            print("=========================\n")

            # 检查是否是API连接问题
            if "connect" in str(e).lower() or "timeout" in str(e).lower() or "network" in str(e).lower():
                error_msg = f"连接大模型API失败，请检查网络连接或API密钥: {str(e)}"

            # 发送错误消息到前端
            await self.publish_message(
                ResponseMessage(source="需求结构化智能体", content=error_msg),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            # 尝试使用备用方法生成需求
            await self.publish_message(
                ResponseMessage(source="需求结构化智能体", content="正在尝试使用备用方法生成需求..."),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            # 生成一个简单的需求列表作为备用
            try:
                # 从需求分析报告中提取一些关键信息
                lines = message.content.split('\n')
                requirements = []

                # 尝试提取需求点
                for i, line in enumerate(lines):
                    if '需求点' in line or '功能点' in line or '：' in line or ':' in line:
                        # 提取需求名称
                        name = line.split('：')[-1] if '：' in line else (line.split(':')[-1] if ':' in line else line)
                        name = name.strip()
                        if len(name) > 5 and len(name) < 50:  # 合理的需求名称长度
                            # 尝试从行中提取需求类型
                            category = "功能"  # 默认类型
                            # 检查是否包含类型信息
                            type_keywords = {
                                "功能": ["功能", "feature"],
                                "性能": ["性能", "performance"],
                                "安全": ["安全", "security"],
                                "接口": ["接口", "interface", "api"],
                                "体验": ["体验", "experience", "ux"],
                                "改进": ["改进", "improvement"],
                                "其它": ["其它", "其他", "other"]
                            }

                            # 检查行中是否包含类型关键词
                            for type_name, keywords in type_keywords.items():
                                if any(keyword in line.lower() for keyword in keywords):
                                    category = type_name
                                    break

                            # 创建一个简单的需求
                            req = {
                                "name": name,
                                "description": f"作为一名医生/照护师，我希望{name}，这样可以提高工作效率和服务质量。",
                                "category": category,  # 使用提取的类型
                                "parent": "",
                                "module": "未指定",
                                "level": "高",
                                "reviewer": "默认测试人员",
                                "estimate": 8,
                                "criteria": f"1. 功能正常运行无错误\n2. 界面符合设计规范\n3. {name}功能可正常使用\n4. 响应时间不超过2秒",
                                "remark": "[功能模块: 未指定]",
                                "keywords": f"{category},需求",
                                "project_id": message.project_id
                            }
                            requirements.append(req)

                # 如果没有找到需求，创建一个默认需求
                if not requirements:
                    # 尝试从消息内容中提取可能的需求类型
                    content = message.content.lower()
                    category = "功能"  # 默认类型

                    # 检查内容中是否包含类型关键词
                    type_keywords = {
                        "功能": ["功能", "feature"],
                        "性能": ["性能", "performance"],
                        "安全": ["安全", "security"],
                        "接口": ["接口", "interface", "api"],
                        "体验": ["体验", "experience", "ux"],
                        "改进": ["改进", "improvement"],
                        "其它": ["其它", "其他", "other"]
                    }

                    for type_name, keywords in type_keywords.items():
                        if any(keyword in content for keyword in keywords):
                            category = type_name
                            break

                    requirements.append({
                        "name": "默认需求",
                        "description": "作为一名医生/照护师，我希望实现此功能，这样可以提高工作效率。",
                        "category": category,  # 使用提取的类型
                        "parent": "",
                        "module": "未指定",
                        "level": "高",
                        "reviewer": "默认测试人员",
                        "estimate": 8,
                        "criteria": "1. 功能正常运行无错误\n2. 界面符合设计规范\n3. 功能可正常使用\n4. 响应时间不超过2秒",
                        "remark": "[功能模块: 未指定]",
                        "keywords": f"{category},需求",
                        "project_id": message.project_id
                    })

                # 创建备用输出
                backup_output = {
                    "requirements": requirements
                }

                # 序列化为JSON
                backup_content = json.dumps(backup_output, ensure_ascii=False)

                # 发送给下一个智能体
                await self.publish_message(
                    RequirementMessage(
                        source=self.id.type,
                        content=backup_content,
                        project_id=message.project_id,
                        auto_link_requirements=message.auto_link_requirements,
                        tapd_url=message.tapd_url,
                        tapd_tester=message.tapd_tester
                    ),
                    topic_id=TopicId(requirement_database_topic_type, source=self.id.key))

                await self.publish_message(
                    ResponseMessage(source="需求结构化智能体", content=f"已使用备用方法生成{len(requirements)}条需求"),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            except Exception as backup_error:
                print(f"备用方法也失败了: {str(backup_error)}")
                await self.publish_message(
                    ResponseMessage(source="需求结构化智能体", content="备用方法也失败了，请稍后重试"),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))


@type_subscription(topic_type=requirement_database_topic_type)
class RequirementDatabaseAgent(RoutedAgent):
    def __init__(self):
        super().__init__("requirement database agent")

    @message_handler
    async def handle_message(self, message: RequirementMessage, ctx: MessageContext) -> None:
        print(f"Received requirement message: {message}")
        await self.publish_message(
            ResponseMessage(source="数据库智能体", content="正在执行需求入库..."),
            topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

        try:
            # 打印原始消息内容
            print("\n\n===== 原始消息内容 =====")
            print(f"消息类型: {type(message)}")
            print(f"消息内容: {message.content}")
            print(f"消息属性: {dir(message)}")
            for attr in dir(message):
                if not attr.startswith('_') and not callable(getattr(message, attr)):
                    print(f"  {attr}: {getattr(message, attr)}")
            print("===========================\n\n")

            # 解析需求数据
            requirement_data = json.loads(message.content)

            # 打印原始JSON数据结构
            print("\n\n===== 原始JSON数据结构 =====")
            print(f"JSON数据类型: {type(requirement_data)}")
            print(f"JSON数据键: {requirement_data.keys()}")
            if 'requirements' in requirement_data:
                print(f"需求数量: {len(requirement_data['requirements'])}")
                if len(requirement_data['requirements']) > 0:
                    print(f"第一个需求的键: {requirement_data['requirements'][0].keys()}")
                    # 特别检查是否有requirement_id字段
                    if 'requirement_id' in requirement_data['requirements'][0]:
                        print(f"发现requirement_id字段: {requirement_data['requirements'][0]['requirement_id']}")
            print("===========================\n\n")

            # 处理JSON中的所有需求，确保datetime字段有时区信息，并确保必要字段存在
            if 'requirements' in requirement_data:
                for req in requirement_data['requirements']:
                    # 使用统一的时区处理函数确保所有datetime对象都有时区信息
                    req = ensure_all_datetimes_have_timezone(req)

                    # 确保name字段存在
                    if 'id' in req and 'name' not in req:
                        req['name'] = req['id']
                        print(f"将id字段值({req['id']})复制到name字段")

                    # 如果有requirement_id字段但没有name字段，将requirement_id复制到name
                    if 'requirement_id' in req and 'name' not in req:
                        req['name'] = req['requirement_id']
                        print(f"将requirement_id字段值({req['requirement_id']})复制到name字段")

                    # 确保project_id字段存在
                    if 'project_id' not in req and message.project_id:
                        req['project_id'] = message.project_id
                        print(f"添加project_id字段: {message.project_id}")

                    # 确保estimate字段存在
                    if 'estimated' in req and 'estimate' not in req:
                        req['estimate'] = req['estimated']
                        print(f"将estimated字段值({req['estimated']})复制到estimate字段")

                    # 确保description字段存在并符合用户故事格式
                    if 'description' not in req:
                        # 尝试从其他可能的字段获取描述信息
                        if 'content' in req:
                            req['description'] = req['content']
                            print(f"将content字段值复制到description字段")
                        elif 'requirement_name' in req:
                            # 生成符合用户故事格式的描述
                            req['description'] = f"作为一名医生/照护师，我希望{req['requirement_name']}，这样可以提高工作效率和服务质量。"
                            print(f"从requirement_name生成用户故事格式的description字段")
                        else:
                            req['description'] = "作为一名医生/照护师，我希望实现此功能，这样可以提高工作效率。"
                            print(f"添加默认用户故事格式的description字段值")
                    else:
                        # 检查description是否符合用户故事格式
                        desc = req['description']
                        if not ("作为" in desc and "我希望" in desc and "这样可以" in desc):
                            # 尝试转换为用户故事格式
                            original_desc = desc
                            req['description'] = f"作为一名医生/照护师，我希望{desc}，这样可以提高工作效率和服务质量。"
                            print(f"将description转换为用户故事格式: '{original_desc}' -> '{req['description']}'")


                    # 确保其他必填字段存在
                    if 'level' not in req:
                        req['level'] = "高"
                        print(f"添加默认level字段值: '高'")

                    # 确保criteria字段存在且有合适的内容
                    if 'criteria' not in req or not req['criteria'] or req['criteria'] == "明确的验收标准":
                        # 根据需求名称或描述生成验收标准
                        name = req.get('name', '')
                        desc = req.get('description', '')
                        req['criteria'] = f"1. 功能正常运行无错误\n2. 界面符合设计规范\n3. {name}功能可正常使用\n4. 响应时间不超过2秒"
                        print(f"添加默认criteria字段值")

                    # 确保keywords字段存在
                    if 'keywords' not in req or not req['keywords']:
                        # 从name和description中提取关键词
                        name = req.get('name', '')
                        desc = req.get('description', '')
                        # 简单提取一些关键词
                        words = set([w for w in name.split() if len(w) > 1])
                        words.update([w for w in desc.split() if len(w) > 1 and w not in ["作为", "我希望", "这样可以", "一名", "医生", "照护师", "患者"]])
                        keywords = ",".join(list(words)[:5])  # 最多取5个关键词
                        if not keywords:
                            keywords = "功能,需求"
                        req['keywords'] = keywords
                        print(f"生成keywords字段值: '{keywords}'")

                    # 确保remark字段包含功能模块信息
                    if 'remark' not in req or not req['remark']:
                        # 如果没有remark字段，创建一个默认的
                        module = req.get('module', '')
                        if module:
                            # 尝试从module字段提取功能位置
                            req['remark'] = f"[功能模块: {module}]"
                            print(f"从module字段生成remark字段: '{req['remark']}'")
                        else:
                            # 使用默认值
                            req['remark'] = "[功能模块: 未指定]"
                            print(f"添加默认remark字段值: '{req['remark']}'")
                    else:
                        # 检查remark是否已包含功能模块信息
                        remark = req['remark']
                        if "[功能模块:" not in remark and "[功能模块：" not in remark:
                            # 尝试从module字段提取功能位置
                            module = req.get('module', '')
                            if module:
                                req['remark'] = f"{remark} [功能模块: {module}]"
                                print(f"在remark字段中添加功能模块信息: '{req['remark']}'")
                            else:
                                req['remark'] = f"{remark} [功能模块: 未指定]"
                                print(f"在remark字段中添加默认功能模块信息: '{req['remark']}'")

                    if 'reviewer' not in req:
                        if hasattr(message, 'tapd_tester') and message.tapd_tester:
                            req['reviewer'] = message.tapd_tester
                            print(f"从tapd_tester设置reviewer字段值: '{message.tapd_tester}'")
                        else:
                            req['reviewer'] = "默认测试人员"
                            print(f"添加默认reviewer字段值: '默认测试人员'")

                # 打印处理后的每个需求的详细信息
                print("\n===== 处理后的需求详细信息 =====")
                for i, req in enumerate(requirement_data['requirements']):
                    print(f"\n需求 {i+1}:")
                    for key, value in req.items():
                        print(f"  {key}: {value} (类型: {type(value)})")

                    # 特别检查必填字段
                    missing_fields = []
                    for field in ['name', 'description', 'project_id', 'level', 'reviewer']:
                        if field not in req or req[field] is None or req[field] == "":
                            missing_fields.append(field)

                    if missing_fields:
                        print(f"  警告: 缺少必填字段: {', '.join(missing_fields)}")
                    else:
                        print(f"  所有必填字段已存在")

                print("\n已对所有需求的字段进行处理和补充")

            # 打印解析后的需求数据
            print("\n\n===== 解析后的需求数据 =====")
            print(f"需求数据类型: {type(requirement_data)}")
            print(f"需求数据键: {requirement_data.keys()}")
            if 'requirements' in requirement_data:
                print(f"需求数量: {len(requirement_data['requirements'])}")
                for i, req in enumerate(requirement_data['requirements']):
                    print(f"\n需求 {i+1}:")
                    for key, value in req.items():
                        print(f"  {key}: {value} (类型: {type(value)})")
            print("===========================\n\n")

            requirement_list = RequirementList(**requirement_data)

            # 获取项目 ID
            project_id = getattr(message, 'project_id', None)
            auto_link = getattr(message, 'auto_link_requirements', False)

            # 打印调试信息
            print(f"\n\n需求入库: 项目 ID = {project_id}, 自动关联 = {auto_link}\n\n")

            # 保存需求数据到数据库
            count = 0

            # 尝试从TAPD需求中获取tester字段值
            tapd_tester = None

            # 首先检查消息中是否直接包含tapd_tester字段
            if hasattr(message, 'tapd_tester') and message.tapd_tester:
                tapd_tester = message.tapd_tester
                print(f"\n从消息中直接获取到TAPD测试人员: {tapd_tester}\n")

            # 如果消息中没有tapd_tester字段，尝试从原始数据中获取
            if not tapd_tester:
                try:
                    # 解析原始数据中的urls字段
                    original_data = json.loads(message.content)
                    if 'requirements' in original_data:
                        # 如果有多个需求，使用第一个需求的reviewer字段
                        for req in original_data['requirements']:
                            if req.get('reviewer') and req.get('reviewer') != "TAPD需求中的测试人员(tester)字段值":
                                tapd_tester = req.get('reviewer')
                                print(f"\n从需求数据中找到reviewer字段: {tapd_tester}\n")
                                break
                except Exception as e:
                    print(f"\n解析需求数据时出错: {str(e)}\n")

            # 如果上面的方法没有找到tester，尝试从缓存中获取
            if not tapd_tester and hasattr(message, 'tapd_url') and message.tapd_url:
                # 尝试从全局缓存中获取TAPD需求
                from app.api.v1.reqAgent.tapdAgent import tapd_requirements_cache

                # 遍历缓存中的所有TAPD需求
                for req_id, req_info in tapd_requirements_cache.items():
                    if req_info.url == message.tapd_url and req_info.tester:
                        tapd_tester = req_info.tester
                        print(f"\n从缓存中找到TAPD需求的测试人员: {tapd_tester}\n")
                        break

            # 如果还是没有找到tester，直接从缓存中获取所有TAPD需求并打印出来
            if not tapd_tester:
                print(f"\n尝试直接查看缓存中的所有TAPD需求\n")
                from app.api.v1.reqAgent.tapdAgent import tapd_requirements_cache
                print(f"\n缓存中共有 {len(tapd_requirements_cache)} 个TAPD需求\n")
                for req_id, req_info in tapd_requirements_cache.items():
                    print(f"\nTAPD需求ID: {req_id}, URL: {req_info.url}, 测试人员: {req_info.tester}\n")

            # 打印最终结果
            if tapd_tester:
                print(f"\n最终使用的TAPD测试人员: {tapd_tester}\n")
            else:
                print("\n未找到TAPD测试人员\n")

            for requirement in requirement_list.requirements:
                # 直接设置category字段，确保它不为空
                if not hasattr(requirement, 'category') or requirement.category is None or requirement.category == "":
                    requirement.category = "功能"
                    print(f"\n警告: 需求 '{requirement.name}' 的category字段为空，已设置为默认值'功能'\n")

                # 如果需要自动关联需求并且有项目 ID，则设置项目 ID
                if auto_link and project_id is not None:
                    requirement.project_id = project_id
                    print(f"\n关联需求 '{requirement.name}' 到项目 {project_id}\n")

                # 如果需求中有tapd_url字段，确保它被保存
                if hasattr(requirement, 'tapd_url') and requirement.tapd_url:
                    print(f"\n需求 '{requirement.name}' 关联到TAPD URL: {requirement.tapd_url}\n")
                # 如果消息中有tapd_url，但需求中没有，则添加到需求中
                elif hasattr(message, 'tapd_url') and message.tapd_url:
                    requirement.tapd_url = message.tapd_url
                    print(f"\n需求 '{requirement.name}' 从消息中关联到TAPD URL: {message.tapd_url}\n")

                # 如果找到了TAPD需求的测试人员，将其设置为reviewer
                if tapd_tester:
                    # 打印当前需求的reviewer字段值
                    current_reviewer = getattr(requirement, 'reviewer', None)
                    print(f"\n需求 '{requirement.name}' 当前reviewer值: '{current_reviewer}'\n")

                    # 尝试多种方式设置需求的reviewer字段
                    try:
                        # 方式1: 直接设置属性
                        requirement.reviewer = tapd_tester
                        print(f"\n方式1成功: 需求 '{requirement.name}' 的reviewer字段设置为TAPD测试人员: {tapd_tester}\n")
                    except Exception as e1:
                        print(f"\n方式1失败: {str(e1)}\n")
                        try:
                            # 方式2: 使用__dict__设置属性
                            if hasattr(requirement, '__dict__'):
                                requirement.__dict__['reviewer'] = tapd_tester
                                print(f"\n方式2成功: 需求 '{requirement.name}' 的reviewer字段设置为TAPD测试人员: {tapd_tester}\n")
                        except Exception as e2:
                            print(f"\n方式2失败: {str(e2)}\n")
                            try:
                                # 方式3: 使用setattr
                                setattr(requirement, 'reviewer', tapd_tester)
                                print(f"\n方式3成功: 需求 '{requirement.name}' 的reviewer字段设置为TAPD测试人员: {tapd_tester}\n")
                            except Exception as e3:
                                print(f"\n方式3失败: {str(e3)}\n")
                                print(f"\n无法设置需求 '{requirement.name}' 的reviewer字段\n")
                else:
                    print(f"\n警告: 需求 '{requirement.name}' 没有TAPD测试人员信息\n")

                    # 检查当前reviewer字段值
                    current_reviewer = getattr(requirement, 'reviewer', None)
                    print(f"\n需求 '{requirement.name}' 当前reviewer值: '{current_reviewer}'\n")

                    # 如果reviewer字段为空或为模板值，设置为默认值
                    if not current_reviewer or current_reviewer == "" or current_reviewer == "TAPD需求中的测试人员(tester)字段值":
                        try:
                            # 尝试设置默认值
                            requirement.reviewer = "默认测试人员"
                            print(f"\n需求 '{requirement.name}' 的reviewer字段设置为默认值: 默认测试人员\n")
                        except Exception as e:
                            print(f"\n设置默认reviewer失败: {str(e)}\n")
                            try:
                                # 方式2: 使用__dict__设置属性
                                if hasattr(requirement, '__dict__'):
                                    requirement.__dict__['reviewer'] = "默认测试人员"
                                    print(f"\n方式2成功: 需求 '{requirement.name}' 的reviewer字段设置为默认值\n")
                            except Exception as e2:
                                print(f"\n方式2失败: {str(e2)}\n")
                                try:
                                    # 方式3: 使用setattr
                                    setattr(requirement, 'reviewer', "默认测试人员")
                                    print(f"\n方式3成功: 需求 '{requirement.name}' 的reviewer字段设置为默认值\n")
                                except Exception as e3:
                                    print(f"\n方式3失败: {str(e3)}\n")

                    # 检查设置后的reviewer字段值
                    new_reviewer = getattr(requirement, 'reviewer', None)
                    print(f"\n需求 '{requirement.name}' 设置后的reviewer值: '{new_reviewer}'\n")

                    # 最终检查，确保字段不为空
                    if not new_reviewer or new_reviewer == "":
                        try:
                            requirement.reviewer = "默认测试人员"
                            print(f"\n最终检查: 需求 '{requirement.name}' 的reviewer字段设置为默认值\n")
                        except Exception as e:
                            print(f"\n最终设置默认reviewer失败: {str(e)}\n")

                # 获取需求对象的所有字段
                requirement_dict = requirement.model_dump() if hasattr(requirement, 'model_dump') else requirement.__dict__

                # 打印原始需求字典的所有字段，详细检查每个字段的类型
                print("\n\n===== 原始需求字段详细信息 =====")
                for field_name, field_value in requirement_dict.items():
                    print(f"字段名: {field_name}, 值: {field_value}, 类型: {type(field_value)}")
                    # 特别检查字符串类型的字段，看是否包含日期时间格式的字符串
                    if isinstance(field_value, str) and ('date' in field_name.lower() or 'time' in field_name.lower() or 'at' in field_name.lower()):
                        print(f"  - 警告: 这可能是一个日期时间字符串字段")

                # 特别检查remark字段
                if 'remark' in requirement_dict:
                    print(f"\n特别检查remark字段: {requirement_dict.get('remark')}, 类型: {type(requirement_dict.get('remark'))}")
                else:
                    print("\n警告: 需求字典中不包含remark字段!")

                print("===========================\n\n")

                # 创建一个新的字典，不包含任何日期时间字段，并且确保所有字段都是正确的类型
                clean_dict = {}
                for field_name, field_value in requirement_dict.items():
                    # 跳过所有日期时间字段和可能是日期时间字符串的字段
                    if not isinstance(field_value, datetime) and not (isinstance(field_value, str) and ('date' in field_name.lower() or 'time' in field_name.lower() or 'at' in field_name.lower())):
                        clean_dict[field_name] = field_value

                # 确保必填字段有默认值
                if "name" not in clean_dict or not clean_dict["name"]:
                    clean_dict["name"] = f"需求-{get_now_with_timezone().strftime('%Y%m%d%H%M%S')}"
                    print(f"警告: name字段为空，已设置为默认值'{clean_dict['name']}'")

                if "description" not in clean_dict or not clean_dict["description"]:
                    clean_dict["description"] = "需求描述"
                    print(f"警告: description字段为空，已设置为默认值'需求描述'")

                if "category" not in clean_dict or not clean_dict["category"]:
                    clean_dict["category"] = "功能"
                    print(f"警告: category字段为空，已设置为默认值'功能'")

                if "level" not in clean_dict or not clean_dict["level"]:
                    clean_dict["level"] = "高"
                    print(f"警告: level字段为空，已设置为默认值'高'")

                if "estimate" not in clean_dict or not clean_dict["estimate"]:
                    clean_dict["estimate"] = 8
                    print(f"警告: estimate字段为空，已设置为默认值8")

                if "project_id" not in clean_dict or not clean_dict["project_id"]:
                    clean_dict["project_id"] = project_id if project_id else 1
                    print(f"警告: project_id字段为空，已设置为默认值{clean_dict['project_id']}")

                print("\n\n===== 清理后的需求字段（移除所有日期时间字段并添加默认值）=====")
                for field_name, field_value in clean_dict.items():
                    print(f"字段名: {field_name}, 值: {field_value}, 类型: {type(field_value)}")
                print("===========================\n\n")

                # 保存需求 - 使用最小化字典，完全避免时区问题
                print("\n保存需求到数据库（使用最小化字典）...")
                try:
                    # 创建一个最小化的字典，只包含必要字段，确保所有必填字段都有默认值
                    # 完全避免使用任何datetime字段
                    minimal_dict = {
                        "name": requirement_dict.get("name", f"需求-{get_now_with_timezone().strftime('%Y%m%d%H%M%S')}"),
                        "description": requirement_dict.get("description", "需求描述"),
                        "category": requirement_dict.get("category", "功能"),  # 保留原始类型，如果为空则使用默认值"功能"
                        "level": requirement_dict.get("level", "高"),
                        "project_id": requirement_dict.get("project_id", project_id if project_id else 1)
                    }

                    # 处理estimate/estimated字段
                    if "estimate" in requirement_dict:
                        minimal_dict["estimate"] = requirement_dict["estimate"]
                    elif "estimated" in requirement_dict:
                        minimal_dict["estimate"] = requirement_dict["estimated"]
                        print(f"\n将estimated字段值({requirement_dict['estimated']})转换为estimate字段\n")
                    else:
                        minimal_dict["estimate"] = 8

                    # 处理remark字段
                    if "remark" in requirement_dict:
                        minimal_dict["remark"] = requirement_dict["remark"]
                        print(f"\n添加remark字段: {requirement_dict['remark']}\n")

                    # 添加reviewer字段
                    if 'reviewer' in requirement_dict and requirement_dict['reviewer']:
                        minimal_dict['reviewer'] = requirement_dict['reviewer']
                    elif tapd_tester:
                        minimal_dict['reviewer'] = tapd_tester
                    else:
                        minimal_dict['reviewer'] = "默认测试人员"

                    # 添加tapd_url字段
                    if 'tapd_url' in requirement_dict and requirement_dict['tapd_url']:
                        minimal_dict['tapd_url'] = requirement_dict['tapd_url']
                    elif hasattr(message, 'tapd_url') and message.tapd_url:
                        minimal_dict['tapd_url'] = message.tapd_url

                    # 添加其他非日期时间字段
                    for field_name, field_value in requirement_dict.items():
                        if (field_name not in minimal_dict and
                            not isinstance(field_value, datetime) and
                            field_name not in ["created_at", "updated_at"]):
                            minimal_dict[field_name] = field_value

                    # 特别确保remark字段被添加到minimal_dict中
                    if 'remark' in requirement_dict and requirement_dict['remark']:
                        minimal_dict['remark'] = requirement_dict['remark']
                        print(f"\n特别添加remark字段: {requirement_dict['remark']}\n")

                    print(f"\n最小化字典: {minimal_dict}")

                    # 使用原始SQL插入，完全避免ORM的时区处理
                    conn = connections.get("default")

                    # 首先检查表结构，确定created_at和updated_at的数据类型
                    try:
                        result = await conn.execute_query("""
                        SELECT column_name, data_type, is_nullable
                        FROM information_schema.columns
                        WHERE table_name = 'requirement' AND column_name IN ('created_at', 'updated_at');
                        """)

                        column_info = {}
                        for row in result[1]:
                            column_info[row[0]] = {"type": row[1], "nullable": row[2]}

                        print(f"requirement表字段信息: {column_info}")

                        # 检查字段类型，仅用于日志记录
                        has_timestamp_with_tz = True
                        for column, info in column_info.items():
                            if "timestamp" in info["type"].lower() and "without time zone" in info["type"].lower():
                                has_timestamp_with_tz = False
                                print(f"警告: {column}字段是timestamp without time zone类型")
                                break

                        if has_timestamp_with_tz:
                            print("所有时间字段都是timestamp with time zone类型，这是理想的配置")
                    except Exception as e:
                        print(f"获取表结构信息失败: {e}")

                    # 构建SQL插入语句
                    fields = []
                    values = []
                    params = []

                    for field_name, field_value in minimal_dict.items():
                        if field_value is not None:
                            fields.append(field_name)
                            values.append(f"${len(params) + 1}")
                            params.append(field_value)

                    # 特别检查remark字段是否已添加
                    if 'remark' in minimal_dict:
                        print(f"\nremark字段已添加到SQL参数中: {minimal_dict['remark']}\n")
                    else:
                        print("\n警告: remark字段未添加到SQL参数中!\n")

                    # 添加created_at和updated_at字段
                    fields.extend(["created_at", "updated_at"])
                    # 使用简单的NOW()函数，PostgreSQL会根据字段类型自动处理时区
                    values.extend(["NOW()", "NOW()"])

                    # 构建完整的SQL语句
                    sql = f"INSERT INTO requirement ({', '.join(fields)}) VALUES ({', '.join(values)}) RETURNING id"

                    # 执行SQL语句
                    print(f"执行SQL: {sql}")
                    print(f"参数: {params}")
                    result = await conn.execute_query(sql, params)
                    requirement_id = result[1][0][0]
                    print(f"需求保存成功! ID: {requirement_id}")

                    # 特别检查时间字段
                    try:
                        time_fields = await conn.execute_query(f"""
                        SELECT
                            created_at,
                            updated_at
                        FROM requirement WHERE id = {requirement_id}
                        """)
                        print("\n时间字段详情:")

                        if len(time_fields) >= 2 and isinstance(time_fields[1], list) and len(time_fields[1]) > 0:
                            print(f"created_at: {time_fields[1][0][0]}")
                            print(f"updated_at: {time_fields[1][0][1]}")

                            # 检查时区信息
                            tz_check = await conn.execute_query(f"""
                            SELECT
                                EXTRACT(TIMEZONE FROM created_at) as created_at_tz,
                                EXTRACT(TIMEZONE FROM updated_at) as updated_at_tz
                            FROM requirement WHERE id = {requirement_id}
                            """)

                            if len(tz_check) >= 2 and isinstance(tz_check[1], list) and len(tz_check[1]) > 0:
                                print(f"created_at timezone offset (seconds): {tz_check[1][0][0]}")
                                print(f"updated_at timezone offset (seconds): {tz_check[1][0][1]}")
                        else:
                            print(f"时间字段查询结果格式不符合预期: {time_fields}")
                    except Exception as e:
                        print(f"查询时间字段失败: {e}")

                except Exception as e:
                    print(f"需求保存失败: {str(e)}")
                    # 如果直接SQL插入失败，尝试使用ORM
                    print("\n尝试使用ORM创建需求...")
                    try:
                        # 使用最小化字典创建需求
                        await requirement_timezone_controller.create(obj_in=minimal_dict)
                        print("使用ORM创建需求成功!")
                    except Exception as e2:
                        print(f"使用ORM创建需求失败: {str(e2)}")
                        raise e  # 重新抛出原始异常
                count += 1

            # 发送数据库保存结果(非最终消息)
            await self.publish_message(
                ResponseMessage(
                    source="database",
                    content=requirement_list.model_dump_json(),
                    is_final=False
                ),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            # 发送最终完成消息
            await self.publish_message(
                ResponseMessage(
                    source="数据库智能体",
                    content=f"需求入库完成，共生成【{count}】条需求。",
                    is_final=True
                ),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

        except Exception as e:
            print(f"数据库入库出错: {message}")
            error_msg = f"需求入库过程出错: {str(e)}"
            print(error_msg)
            await self.publish_message(
                ResponseMessage(source="数据库智能体", content=error_msg, is_final=True),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))


async def start_runtime(requirement_files: RequirementFilesMessage,
                        collect_result: Callable[[ClosureContext, ResponseMessage, MessageContext], Awaitable[None]],
                        user_input_func: Callable[[str, Optional[CancellationToken]], Awaitable[str]] = None,
                        progress_callback: Callable[[int], Awaitable[None]] = None):
    """启动需求分析运行时"""

    # 发送进度更新
    if progress_callback:
        await progress_callback(15)  # 初始化运行时

    runtime = SingleThreadedAgentRuntime()

    # 注册智能体
    await RequirementAcquisitionAgent.register(
        runtime,
        requirement_acquisition_topic_type,
        lambda: RequirementAcquisitionAgent(input_func=user_input_func)
    )

    # 注册知识提取智能体
    from .knowledge_agents import RequirementKnowledgeAgent, knowledge_extraction_topic_type
    await RequirementKnowledgeAgent.register(
        runtime,
        knowledge_extraction_topic_type,
        lambda: RequirementKnowledgeAgent()
    )

    # 发送进度更新
    if progress_callback:
        await progress_callback(25)  # 注册需求获取智能体

    await RequirementAnalysisAgent.register(
        runtime,
        requirement_analysis_topic_type,
        lambda: RequirementAnalysisAgent()
    )

    # 发送进度更新
    if progress_callback:
        await progress_callback(35)  # 注册需求分析智能体

    await RequirementOutputAgent.register(
        runtime,
        requirement_output_topic_type,
        lambda: RequirementOutputAgent()
    )

    # 发送进度更新
    if progress_callback:
        await progress_callback(45)  # 注册需求输出智能体

    await RequirementDatabaseAgent.register(
        runtime,
        requirement_database_topic_type,
        lambda: RequirementDatabaseAgent()
    )

    # 发送进度更新
    if progress_callback:
        await progress_callback(50)  # 注册需求数据库智能体

    # 定义闭包智能体接收消息
    await ClosureAgent.register_closure(
        runtime,
        "closure_agent",
        collect_result,
        subscriptions=lambda: [TypeSubscription(topic_type=task_result_topic_type, agent_type="closure_agent")],
    )

    # 定义闭包智能体接收消息后的进度更新
    if progress_callback:
        await progress_callback(60)  # 注册闭包智能体

    # 启动运行时
    runtime.start()

    # 发送进度更新
    if progress_callback:
        await progress_callback(70)  # 运行时启动

    # 发布消息，包含项目 ID 和自动关联标志
    print(f"\n\n发布需求分析消息: 项目 ID = {requirement_files.project_id}, 自动关联 = {requirement_files.auto_link_requirements}\n\n")
    await runtime.publish_message(
        requirement_files,
        topic_id=DefaultTopicId(type=requirement_acquisition_topic_type)
    )

    # 发送进度更新
    if progress_callback:
        await progress_callback(80)  # 消息发布完成

    # 等待所有任务完成
    await runtime.stop_when_idle()

    # 发送进度更新
    if progress_callback:
        await progress_callback(90)  # 任务完成

    # 关闭运行时
    await runtime.close()

    # 发送进度更新
    if progress_callback:
        await progress_callback(95)  # 运行时关闭

