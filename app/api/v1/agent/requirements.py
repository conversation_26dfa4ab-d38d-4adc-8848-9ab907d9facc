import json
import logging
import os
import uuid
from typing import Any

import aiofiles
from autogen_agentchat.base import TaskResult
from autogen_agentchat.messages import TextMessage, UserInputRequestedEvent, ToolCallSummaryMessage
from autogen_core import CancellationToken, ClosureContext, MessageContext
from fastapi import APIRouter, Query, HTTPException
from starlette.websockets import WebSocket, WebSocketDisconnect

from app.schemas import Success
from .requirement_agents import RequirementFilesMessage, ResponseMessage, start_runtime, UrlItem

from fastapi import File, UploadFile
from pathlib import Path


logger = logging.getLogger(__name__)

router = APIRouter()

async def get_history(history_path: str) -> list[dict[str, Any]]:
    """Get chat history from file."""
    if not os.path.exists(history_path):
        return []
    async with aiofiles.open(history_path, "r") as file:
        return json.loads(await file.read())


@router.get("/requirements/history")
async def history(user_id: int = Query(..., description="用户ID"),) -> list[dict[str, Any]]:
    try:
        return await get_history(str(user_id)+"_history.json")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) from e

@router.websocket("/ws/analyze")
async def analyze_requirements(websocket: WebSocket):
    """
    WebSocket端点，用于处理需求分析请求
    完整路径: /api/v1/agent/ws/analyze
    """
    # 使用logger代替print，减少日志输出
    logger.info("WebSocket连接请求: /api/v1/agent/ws/analyze")
    await websocket.accept()
    logger.info("WebSocket连接已接受")

    # 存储当前处理进度
    current_progress = 0

    # 发送进度更新给前端
    async def send_progress(progress: int):
        nonlocal current_progress
        if progress > current_progress:  # 确保进度只能增加不能减少
            current_progress = progress
            await websocket.send_json({
                "type": "progress",
                "content": str(progress),
                "source": "system"
            })

    # 将收到的消息发送给前端（浏览器）
    async def collect_result(_agent: ClosureContext, message: ResponseMessage, ctx: MessageContext) -> None:
        msg = message.model_dump()
        # 使用logger代替print，减少日志输出
        logger.debug("返回的消息：%s", msg)

        # 根据消息类型更新进度
        if msg.get("source") == "system":
            # 分析消息内容，更新进度
            content = msg.get("content", "")
            if "开始分析" in content:
                await send_progress(25)
            elif "提取信息" in content or "关键点" in content:
                await send_progress(50)
            elif "生成报告" in content or "分析报告" in content:
                await send_progress(75)

        # 将收到的消息发送给前端（浏览器）
        await websocket.send_json(msg)

    async def _user_input(prompt: str, cancellation_token: CancellationToken | None) -> str:
        # 等待用户输入（代码阻塞执行）,下面的代码的效果类似 input
        data = await websocket.receive_json()
        message = TextMessage.model_validate(data)
        return message.content

    try:
        while True:
            data = await websocket.receive_json()

            # 创建需求文件消息
            # 处理URL数据
            urls_data = []

            # 使用logger记录URL数据，减少日志输出
            logger.debug("收到的URL数据: %s", data.get('urls', []))

            for url_data in data.get('urls', []):
                # 使用logger记录URL项信息，仅在debug级别输出
                if logger.isEnabledFor(logging.DEBUG):
                    logger.debug("处理URL项: %s", url_data)
                    logger.debug("URL项的tester字段: '%s'", url_data.get('tester', ''))
                    logger.debug("URL项的handler字段: '%s'", url_data.get('handler', ''))
                    logger.debug("URL项的developer字段: '%s'", url_data.get('developer', ''))

                # 创建UrlItem对象，包含所有字段
                url_item = UrlItem(
                    id=url_data.get('id'),
                    url=url_data.get('url'),
                    title=url_data.get('title', '无标题'),
                    content=url_data.get('content', ''),
                    status=url_data.get('status', 'success'),
                    handler=url_data.get('handler', ''),
                    developer=url_data.get('developer', ''),
                    tester=url_data.get('tester', '')
                )

                # 使用logger记录UrlItem对象，仅在debug级别输出
                if logger.isEnabledFor(logging.DEBUG):
                    logger.debug("创建的UrlItem对象: %s", url_item)
                    logger.debug("UrlItem的tester字段: '%s'", url_item.tester)

                urls_data.append(url_item)

            requirement_files = RequirementFilesMessage(
                user_id=str(data.get("userId", "")),
                files=[file['path'] for file in data.get('files', [])],
                urls=urls_data,
                content=data.get("content", ""),
                task=data.get("task", "分析需求文档"),
                project_id=data.get("projectId"),  # 添加项目 ID
                auto_link_requirements=data.get("autoLinkRequirements", False)  # 添加自动关联需求参数
            )

            try:
                # 启动需求分析运行时
                await send_progress(10)  # 初始进度

                # 创建进度回调函数
                async def progress_callback(progress: int):
                    await send_progress(progress)

                # 启动运行时，并传递进度回调
                await start_runtime(
                    requirement_files=requirement_files,
                    collect_result=collect_result,
                    user_input_func=_user_input,
                    progress_callback=progress_callback
                )

                # 分析完成，发送100%进度
                await send_progress(100)
            except Exception as e:
                # 重置进度
                current_progress = 0

                # 发送错误消息给客户端
                error_message = {
                    "type": "error",
                    "content": f"Error: {str(e)}",
                    "source": "system"
                }
                await websocket.send_json(error_message)

                # 错误后重新启用输入
                await websocket.send_json({
                    "type": "UserInputRequestedEvent",
                    "content": "发生错误，请重试。",
                    "source": "system"
                })

    except WebSocketDisconnect:
        logger.info("客户端断开连接")
    except Exception as e:
        logger.error(f"意外错误: {str(e)}")
        try:
            await websocket.send_json({
                "type": "error",
                "content": f"意外错误: {str(e)}",
                "source": "system"
            })
        except:
            pass


@router.post("/upload", summary="上传文件")
async def upload_file(
        user_id: int = Query(..., description="用户ID"),
        file: UploadFile = File(..., description="上传的文件")
):
    """处理文件上传并返回存储路径"""
    # 文件类型验证
    # ALLOWED_TYPES = [
    #     "application/pdf",
    #     "application/msword",
    #     "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    #     "text/plain"
    # ]
    # if file.content_type not in ALLOWED_TYPES:
    #     raise HTTPException(400, detail=f"不支持的文件类型: {file.content_type}")

    try:
        upload_dir = Path("uploads") / str(user_id)
        upload_dir.mkdir(parents=True, exist_ok=True)

        # 生成唯一文件名
        file_ext = Path(file.filename).suffix
        uuid_name = f"{uuid.uuid4().hex}{file_ext}"
        file_path = upload_dir / uuid_name

        # 流式写入文件并控制大小
        max_size = 10 * 1024 * 1024  # 10MB
        total_size = 0
        async with aiofiles.open(file_path, "wb") as buffer:
            while chunk := await file.read(8192):
                total_size += len(chunk)
                if total_size > max_size:
                    await buffer.close()
                    file_path.unlink(missing_ok=True)
                    raise HTTPException(413, detail="文件大小超过10MB限制")
                await buffer.write(chunk)

        return Success(data={
            "filePath": file_path.as_posix(),
            "fileId": uuid_name,
            "fileName": file.filename
        })
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(500, detail="文件上传失败") from e