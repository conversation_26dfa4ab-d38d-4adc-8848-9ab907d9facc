"""
知识提取智能体模块

该模块包含两个智能体：
1. RequirementKnowledgeAgent - 从需求分析结果中提取知识点
2. TestCaseKnowledgeAgent - 从测试用例中提取知识点

这两个智能体负责将提取的知识点保存到知识库中，并标记相应的来源。
"""

import json
import logging
import re
from typing import List, Optional
from difflib import SequenceMatcher

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.base import TaskResult
from autogen_agentchat.messages import ModelClientStreamingChunkEvent
from autogen_core import (
    RoutedAgent,
    type_subscription,
    message_handler,
    MessageContext,
    TopicId
)
from pydantic import BaseModel
# 导入所需模块

from app.controllers.knowledge import knowledge_controller
from app.schemas.knowledge import KnowledgeItemCreate, KnowledgeSource, KnowledgeItemType
from app.api.v1.agent.api.llms import model_client
from app.api.v1.agent.common_messages import ResponseMessage, task_result_topic_type, KnowledgeExtractionRequestMessage, knowledge_extraction_topic_type

# 日志配置
logger = logging.getLogger(__name__)

# 定义主题类型
knowledge_extraction_result_topic_type = "knowledge_extraction_result"

predefined_tags = [
"共同照护 web",
"普通工作台", "带任务患者人数", "客服消息", "客服咨询", "用户信息", "辅助工具", "推荐商品", "帮助中心", "全部订单", "主动干预", "患者列表", "健康咨询", "饮食点评", "健康档案", "干预记录", "化验", "个人信息", "服务包", "照护提醒", "病历", "公告", "购买记录", "院外情况", "血糖监测", "血压监测", "体脂监测", "SOAP 列表", "事件记录",
"预约工作台", "预约管理", "日期选择", "医院选择", "就诊列表", "已预约患者列表", "预约详情", "基本信息", "事件记录", "检查项目", "照护提醒", "预约 & 改期", "AI 通话记录", "待预约管理",
"全科室工作台", "导诊签到", "全科室诊", "就诊日历", "患者管理", "医生看诊", "电子患教手册", "日期选额", "地区选择", "导诊列表", "患者列表", "预约详情", "电子病历", "营养处方", "患者综述", "评估表",
"平台运营工作台", "糖友商城", "FAQ", "涨知识", "涨知识文章", "涨知识视频", "活动管理", "涨知识评论", "商品管理", "订单列表", "售后记录", "积分数据查询", "商城顶部提醒", "血糖仪发放管理", "优惠券发放管理", "积分或健康币发放", "优惠券使用统计", "置顶帖子管理", "视频推荐商品管理", "商品规格编码管理", "服务包库存管理", "服务通知", "调查问券配置", "话题管理", "活动配置", "商城促销管理", "欢迎页配置", "Banner 配置", "通栏配置", "置顶文章配置", "活动弹窗配置", "同伴活动配置", "积分优惠配置", "甄选 TAB 页文案配置", "福利专区", "广播推荐配置",
"管理员工作台", "诊断管理", "分配照护师", "部门管理", "包产到护", "组织管理", "权限配置", "医生配置", "角色管理", "归档管理", "照护智能体",
"数据工作台", "销售看板", "院内数据", "其他设置", "收入数据", "产品收入数据", "服务收入数据", "单品销售数据", "机构收入", "成本覆盖数据", "试纸", "CGM", "福袋", "首诊下单率", "门诊试纸统计", "待核查数据", "新老用户", "院内数据监控",
"照护同道 app",
"门诊", "收案", "导诊", "入组", "复盘", "公卫", "患者管理",
"消息", "健康咨询", "客服消息",
"内容", "内容广场", "我的内容", "涨知识视频",
"我的", "个人主页", "我的钱包", "健康币",
"共同照护 app",
"首页", "测血糖", "测血压", "测体脂", "用药", "血糖历史", "血压历史", "体脂历史", "问医生", "健康咨询", "首页 Banner", "血糖记录", "血压记录", "饮食记录", "运动记录", "体脂记录", "福利专区", "积分任务", "血脂记录", "闹钟",
"知识", "涨知识文章", "视频", "涨知识视频", "减重训练营", "搜索",
"同伴", "广场", "同伴", "发帖", "通知",
"甄选", "搜索", "商城促销活动", "活动购物车", "订单", "商城列表",
"我的", "用户信息", "健康币", "积分", "积分乐园", "优惠券", "订单", "我的订单", "控糖计划", "健康报告", "亲友关注", "使用指南", "邀请有礼", "常见问题", "意见反馈", "联系客服", "设置", "照护会员", "VIP 服务",
"培训系统",
"电视大屏",
"共同照护 ocr 小程序"
]
attention_topics = f"""注意事项：
    1. 每个知识点应该是独立的、有价值的信息
    2. 知识点标题应简洁明了，内容应详细具体
    3. 正确分类知识点类型
    4. 提取3-10个高质量知识点，而不是尽可能多
    5. 避免重复或过于宽泛的知识点
    6. 标签应该是关键词，用于后续检索
    7. 必须在每个知识点的tags中包含该知识点涉及的前端页面位置名称
    8. 前端页面位置名称可能包括：
       {predefined_tags}
    9. 仔细分析需求分析结果中的内容，识别每个知识点涉及的前端页面位置
    10. 如果一个知识点涉及多个前端页面位置，可以在tags中列出所有相关位置，如"普通工作台,推荐商品,公告"
    11. 如果需求分析结果中已经标注了功能模块，请直接使用这些标注
    12. 【重要】标签必须严格使用上述列出的前端页面位置名称，不要自行创造新的位置名称
    13. 【重要】每个知识点至少包含一个前端页面位置名称作为标签，且该名称必须来自上述列表
    14. 【重要】不要使用模糊或通用的标签，如"功能"、"页面"、"系统"等，应使用具体的前端"""

# 知识点模型

# 知识点模型
class KnowledgePoint(BaseModel):
    """提取的知识点"""
    title: str
    content: str
    item_type: str
    tags: Optional[str] = None
    source: str = "需求分析导入"  # 默认为需求分析导入
    project_id: int
    quality_score: float = 0.0  # 知识点质量评分
    is_duplicate: bool = False  # 是否是重复的知识点
    duplicate_id: Optional[int] = None  # 重复的知识点ID

# 知识点验证结果
class KnowledgeValidationResult(BaseModel):
    """知识点验证结果"""
    is_valid: bool
    reason: Optional[str] = None
    quality_score: float = 0.0

# 知识点重复检测结果
class KnowledgeDuplicateResult(BaseModel):
    """知识点重复检测结果"""
    is_duplicate: bool
    duplicate_id: Optional[int] = None
    similarity_score: float = 0.0
    reason: Optional[str] = None

# 知识提取结果消息
class KnowledgeExtractionResultMessage(BaseModel):
    """知识点提取结果消息"""
    knowledge_points: List[KnowledgePoint]
    saved_count: int
    project_id: int
    source_type: str

# 知识点规则配置
KNOWLEDGE_RULES = {
    "min_title_length": 5,  # 标题最小长度
    "max_title_length": 100,  # 标题最大长度
    "min_content_length": 10,  # 内容最小长度
    "max_content_length": 2000,  # 内容最大长度
    "min_tags_count": 1,  # 标签最小数量
    "max_tags_count": 10,  # 标签最大数量
    "similarity_threshold": 0.65,  # 相似度阈值，超过此值认为是重复
    "title_similarity_weight": 0.6,  # 标题相似度权重
    "content_similarity_weight": 0.4,  # 内容相似度权重
}

async def _normalize_tags(tags: str, module_name: Optional[str] = None) -> str:
    """
    规范化标签，确保使用预定义的功能点名称

    参数:
        tags: 原始标签字符串，逗号分隔
        module_name: 可选的模块名称

    返回:
        规范化后的标签字符串
    """
    if not tags:
        return module_name or ""

    # 分割标签
    tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]

    # 规范化标签
    normalized_tags = []

    # 添加模块名称（如果有且不在标签列表中）
    if module_name and module_name not in tag_list:
        normalized_tags.append(module_name)

    # 处理每个标签
    for tag in tag_list:
        # 检查是否是预定义标签
        if tag in predefined_tags:
            normalized_tags.append(tag)
        else:
            # 查找最相似的预定义标签
            best_match = None
            best_score = 0

            for predefined_tag in predefined_tags:
                # 简单的相似度计算：共同字符数量除以较长字符串的长度
                common_chars = set(tag) & set(predefined_tag)
                similarity = len(common_chars) / max(len(tag), len(predefined_tag))

                if similarity > 0.5 and similarity > best_score:  # 设置相似度阈值
                    best_score = similarity
                    best_match = predefined_tag

            if best_match:
                normalized_tags.append(best_match)
            elif len(tag) > 2:  # 保留长度大于2的非预定义标签作为关键词
                normalized_tags.append(tag)

    # 去重
    normalized_tags = list(set(normalized_tags))

    return ",".join(normalized_tags)

async def _optimize_knowledge_point(point: KnowledgePoint, module_name: Optional[str] = None) -> KnowledgePoint:
    """优化知识点"""
    # 标题优化：去除多余空格，确保首字母大写
    point.title = point.title.strip()

    # 内容优化：去除多余空格和换行符
    point.content = re.sub(r'\s+', ' ', point.content).strip()

    # 标签优化：规范化标签
    point.tags = await _normalize_tags(point.tags, module_name)

    return point

async def _validate_knowledge_point(point: KnowledgePoint) -> KnowledgeValidationResult:
    """验证知识点是否符合规则"""
    # 获取规则配置
    min_title_length = KNOWLEDGE_RULES["min_title_length"]
    max_title_length = KNOWLEDGE_RULES["max_title_length"]
    min_content_length = KNOWLEDGE_RULES["min_content_length"]
    max_content_length = KNOWLEDGE_RULES["max_content_length"]

    # 初始化质量评分
    quality_score = 5.0  # 基础分5分

    # 验证标题
    if not point.title or len(point.title) < min_title_length:
        return KnowledgeValidationResult(
            is_valid=False,
            reason=f"标题太短，最小长度为{min_title_length}个字符",
            quality_score=0.0
        )

    if len(point.title) > max_title_length:
        return KnowledgeValidationResult(
            is_valid=False,
            reason=f"标题太长，最大长度为{max_title_length}个字符",
            quality_score=0.0
        )

    # 验证内容
    if not point.content or len(point.content) < min_content_length:
        return KnowledgeValidationResult(
            is_valid=False,
            reason=f"内容太短，最小长度为{min_content_length}个字符",
            quality_score=0.0
        )

    if len(point.content) > max_content_length:
        return KnowledgeValidationResult(
            is_valid=False,
            reason=f"内容太长，最大长度为{max_content_length}个字符",
            quality_score=0.0
        )

    # 验证标签
    if point.tags:
        tags = point.tags.split(",")

        # 检查是否包含至少一个预定义的功能点名称

        has_predefined_tag = any(tag in predefined_tags for tag in tags)

        if not has_predefined_tag:
            # 不直接拒绝，但降低质量评分
            quality_score -= 2.0
            logger.warning(f"知识点 '{point.title}' 没有包含预定义的功能点名称标签")
    else:
        # 没有标签，降低质量评分
        quality_score -= 2.0

    # 计算标题质量（简单评估：长度适中加分）
    title_length = len(point.title)
    if 10 <= title_length <= 30:
        quality_score += 1.0

    # 计算内容质量（简单评估：长度适中加分）
    content_length = len(point.content)
    if 50 <= content_length <= 500:
        quality_score += 1.0

    # 确保评分在0-10之间
    quality_score = max(0.0, min(10.0, quality_score))

    return KnowledgeValidationResult(
        is_valid=True,
        reason="知识点验证通过",
        quality_score=quality_score
    )

@type_subscription(topic_type=knowledge_extraction_topic_type)
class RequirementKnowledgeAgent(RoutedAgent):
    """从需求分析结果中提取知识点的智能体"""

    def __init__(self):
        super().__init__("requirement knowledge agent")

        self._prompt = """
        你是一个专门从需求分析结果中提取知识点的智能体。你需要识别以下类型的知识点：

        1. 业务规则：包括业务逻辑、约束条件、计算公式等
        2. 核心功能：系统的关键功能点和特性
        3. 术语解释：业务领域的专业术语和概念
        4. 非功能要求：性能、安全、可用性等方面的要求

        请从以下需求分析结果中提取有价值的知识点，并按照以下JSON格式输出：

        ```json
        {
          "knowledge_points": [
            {
              "title": "知识点标题",
              "content": "知识点详细内容",
              "item_type": "业务规则|核心功能|术语解释|非功能要求|其他",
              "tags": "标签1,标签2,标签3,前端页面位置名称"
            },
            // 更多知识点...
          ]
        }
        ```
        """+attention_topics

    @message_handler
    async def handle_message(self, message: KnowledgeExtractionRequestMessage, ctx: MessageContext) -> None:
        """处理知识提取请求"""
        try:
            # 保存源链接信息到实例变量
            self._current_source_url = message.source_url
            self._current_source_reference_id = message.source_reference_id

            # 发送状态消息
            await self.publish_message(
                ResponseMessage(source="知识提取智能体", content="开始从需求分析结果中提取知识点..."),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
            )

            # 创建知识提取助手
            knowledge_extraction_agent = AssistantAgent(
                name="knowledge_extraction_agent",
                model_client=model_client,
                system_message=self._prompt,
                model_client_stream=True,
            )

            # 提取任务
            extraction_task = f"请从以下需求分析结果中提取知识点:\n\n{message.content}"

            # 流式输出
            stream = knowledge_extraction_agent.run_stream(task=extraction_task)
            extraction_content = ""

            async for msg in stream:
                if isinstance(msg, ModelClientStreamingChunkEvent):
                    # 流式输出到前端
                    await self.publish_message(
                        ResponseMessage(source="知识提取智能体", content=msg.content),
                        topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
                    )
                    continue

                if isinstance(msg, TaskResult):
                    extraction_content = msg.messages[-1].content

            # 尝试从需求分析结果中提取功能模块信息
            module_name = await self._extract_module_from_content(message.content)
            logger.info(f"从需求分析结果中提取的功能模块: {module_name}")

            # 解析提取的知识点
            knowledge_points = await self._parse_knowledge_points(extraction_content, message.project_id)

            # 将功能模块名称添加到知识点标签中
            if module_name:
                for point in knowledge_points:
                    # 将功能模块名称添加到标签中
                    if point.tags:
                        # 使用标签规范化函数处理
                        point.tags = await _normalize_tags(point.tags, module_name)
                    else:
                        point.tags = module_name
                    logger.info(f"知识点 '{point.title}' 添加功能模块标签: {point.tags}")

            # 保存知识点到知识库
            saved_count = await self._save_knowledge_points(knowledge_points, "需求分析导入")

            # 发送结果消息
            await self.publish_message(
                ResponseMessage(
                    source="知识提取智能体",
                    content=f"从需求分析中提取并保存了{saved_count}条知识点。",
                    is_final=True
                ),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
            )

        except Exception as e:
            error_msg = f"知识提取过程出错: {str(e)}"
            logger.error(error_msg)
            await self.publish_message(
                ResponseMessage(source="知识提取智能体", content=error_msg),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
            )

    async def _parse_knowledge_points(self, content: str, project_id: int) -> List[KnowledgePoint]:
        """解析提取的知识点"""
        try:
            # 提取JSON部分
            json_start = content.find('{')
            json_end = content.rfind('}') + 1

            if json_start == -1 or json_end == 0:
                logger.warning("未找到JSON格式的知识点")
                return []

            json_content = content[json_start:json_end]
            data = json.loads(json_content)

            # 转换为KnowledgePoint对象
            knowledge_points = []
            for point in data.get("knowledge_points", []):
                knowledge_points.append(
                    KnowledgePoint(
                        title=point["title"],
                        content=point["content"],
                        item_type=point["item_type"],
                        tags=point.get("tags", ""),
                        project_id=project_id,
                        source="需求分析导入",
                        quality_score=0.0,
                        is_duplicate=False,
                        duplicate_id=None
                    )
                )

            logger.info(f"从需求分析结果中解析出{len(knowledge_points)}个知识点")
            return knowledge_points

        except Exception as e:
            logger.error(f"解析知识点失败: {str(e)}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            return []



    async def _check_duplicate(self, point: KnowledgePoint) -> KnowledgeDuplicateResult:
        """检查知识点是否重复"""
        try:
            # 查询项目下的所有知识点
            _, items = await knowledge_controller.search(project_id=point.project_id, page=1, page_size=1000)

            if not items:
                return KnowledgeDuplicateResult(
                    is_duplicate=False,
                    reason="项目下没有知识点",
                    similarity_score=0.0
                )

            # 计算相似度
            max_similarity = 0.0
            duplicate_id = None
            duplicate_reason = None

            for item in items:
                # 计算标题相似度
                title_similarity = SequenceMatcher(None, point.title, item.title).ratio()

                # 计算内容相似度
                content_similarity = SequenceMatcher(None, point.content, item.content).ratio()

                # 加权计算总相似度
                similarity = (
                    title_similarity * KNOWLEDGE_RULES["title_similarity_weight"] +
                    content_similarity * KNOWLEDGE_RULES["content_similarity_weight"]
                )

                if similarity > max_similarity:
                    max_similarity = similarity
                    duplicate_id = item.id
                    duplicate_reason = f"与知识点ID={item.id}相似度为{similarity:.2f}"

            # 判断是否重复
            is_duplicate = max_similarity >= KNOWLEDGE_RULES["similarity_threshold"]

            return KnowledgeDuplicateResult(
                is_duplicate=is_duplicate,
                duplicate_id=duplicate_id if is_duplicate else None,
                similarity_score=max_similarity,
                reason=duplicate_reason if is_duplicate else "未发现重复知识点"
            )

        except Exception as e:
            logger.error(f"检查知识点重复失败: {str(e)}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")

            return KnowledgeDuplicateResult(
                is_duplicate=False,
                reason=f"检查重复失败: {str(e)}",
                similarity_score=0.0
            )

    # async def _optimize_knowledge_point(self, point: KnowledgePoint) -> KnowledgePoint:
        # """优化知识点"""
        # 标题优化：去除多余空格，确保首字母大写
        # point.title = point.title.strip()

        # 内容优化：去除多余空格和换行符
        # point.content = re.sub(r'\s+', ' ', point.content).strip()

        # 标签优化：去除重复标签，规范化格式
        # if point.tags:
            # tags = [tag.strip() for tag in point.tags.split(",") if tag.strip()]
            # unique_tags = list(set(tags))
            # point.tags = ",".join(unique_tags)

        # return point

    async def _save_knowledge_points(self, knowledge_points: List[KnowledgePoint], source: str) -> int:
        """保存知识点到知识库，增加规则验证和重复检测"""
        saved_count = 0
        validated_count = 0
        duplicate_count = 0

        for point in knowledge_points:
            try:
                # 1. 知识点规则验证
                validation_result = await _validate_knowledge_point(point)
                if not validation_result.is_valid:
                    logger.warning(f"知识点未通过规则验证: {point.title}, 原因: {validation_result.reason}")
                    continue

                validated_count += 1
                point.quality_score = validation_result.quality_score

                # 2. 知识点优化
                # optimized_point = await self._optimize_knowledge_point(point)
                optimized_point = await _optimize_knowledge_point(point)

                # 3. 重复检测
                duplicate_result = await self._check_duplicate(optimized_point)
                if duplicate_result.is_duplicate:
                    logger.warning(f"发现重复知识点: {optimized_point.title}, 原因: {duplicate_result.reason}")
                    duplicate_count += 1
                    continue

                # 4. 转换知识点类型
                item_type = self._map_knowledge_type(optimized_point.item_type)

                # 5. 创建知识条目
                knowledge_item = KnowledgeItemCreate(
                    title=optimized_point.title,
                    content=optimized_point.content,
                    item_type=item_type,
                    tags=optimized_point.tags,
                    source=KnowledgeSource.REQUIREMENT if source == "需求分析导入" else KnowledgeSource.TESTCASE,
                    project_id=optimized_point.project_id,
                    source_url=getattr(self, '_current_source_url', None),
                    source_reference_id=getattr(self, '_current_source_reference_id', None)
                )

                # 6. 保存到数据库 - 使用 create_with_creator 方法，它会自动处理时间戳
                # 使用系统用户ID 1 作为创建者
                await knowledge_controller.create_with_creator(obj_in=knowledge_item, creator_id=1)
                saved_count += 1

                logger.info(f"成功保存知识点: {optimized_point.title}, 质量评分: {optimized_point.quality_score:.2f}")

            except Exception as e:
                logger.error(f"保存知识点失败: {str(e)}")
                # 打印详细的错误堆栈
                import traceback
                logger.error(f"错误堆栈: {traceback.format_exc()}")

        logger.info(f"知识点处理统计: 总数={len(knowledge_points)}, 验证通过={validated_count}, 重复={duplicate_count}, 保存成功={saved_count}")
        return saved_count

    async def _extract_module_from_content(self, content: str) -> Optional[str]:
        """从需求分析结果中提取功能模块信息（前端页面位置名称）"""
        try:
            # 预定义的前端页面位置名称列表


            # 方式1: 查找"功能模块:"或"[功能模块:"的模式
            module_patterns = [
                r'\[功能模块:\s*([^\]]+)\]',
                r'\[功能模块：\s*([^\]]+)\]',
                r'功能模块:\s*([^\n,]+)',
                r'功能模块：\s*([^\n,]+)',
                r'所属模块[：:]\s*([^\n,]+)',
                r'模块[：:]\s*([^\n,]+)',
                r'module[：:]\s*([^\n,]+)',
                r'前端页面[：:]\s*([^\n,]+)',
                r'页面位置[：:]\s*([^\n,]+)',
                r'页面模块[：:]\s*([^\n,]+)'
            ]

            for pattern in module_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    # 取第一个匹配结果并清理
                    module_name = matches[0].strip()
                    logger.info(f"通过模式 '{pattern}' 提取到功能模块: {module_name}")
                    return module_name

            # 方式2: 查找特定段落中的模块信息
            sections = content.split('\n\n')
            for section in sections:
                if '模块' in section.lower() or 'module' in section.lower() or '页面' in section.lower():
                    # 提取该段落中的第一个短语作为模块名
                    words = re.sub(r'[^\w\s\u4e00-\u9fff]', ' ', section).split()
                    if len(words) >= 2:
                        for i, word in enumerate(words):
                            if '模块' in word or 'module' in word.lower() or '页面' in word:
                                if i + 1 < len(words):
                                    module_name = words[i + 1].strip()
                                    logger.info(f"从段落中提取到功能模块: {module_name}")
                                    return module_name

            # 方式3: 直接在内容中查找预定义的前端页面位置名称
            for module in predefined_tags:
                if module in content:
                    logger.info(f"从内容中直接匹配到前端页面位置: {module}")
                    return module

            # 方式4: 使用大模型分析提取
            # 这里可以添加调用大模型的逻辑，但为了简化，我们先使用上述方法

            # 如果上述方法都失败，返回默认值
            logger.warning("未能从需求分析结果中提取功能模块信息，使用默认值")
            return "通用功能"

        except Exception as e:
            logger.error(f"提取功能模块信息失败: {str(e)}")
            return "通用功能"

    def _map_knowledge_type(self, type_str: str) -> KnowledgeItemType:
        """映射知识点类型字符串到枚举值"""
        type_mapping = {
            "业务规则": KnowledgeItemType.BUSINESS_RULE,
            "核心功能": KnowledgeItemType.CORE_FEATURE,
            "术语解释": KnowledgeItemType.TERMINOLOGY,
            "测试要点": KnowledgeItemType.TEST_POINT,
            "历史缺陷总结": KnowledgeItemType.DEFECT_SUMMARY,
            "非功能要求": KnowledgeItemType.NON_FUNCTIONAL,
            "环境配置": KnowledgeItemType.ENV_CONFIG,
            "其他": KnowledgeItemType.OTHER
        }

        return type_mapping.get(type_str, KnowledgeItemType.OTHER)


@type_subscription(topic_type=knowledge_extraction_topic_type)
class TestCaseKnowledgeAgent(RoutedAgent):
    """从测试用例中提取知识点的智能体"""

    def __init__(self):
        super().__init__("testcase knowledge agent")

        self._prompt = """
        你是一个专门从测试用例中提取测试知识点的智能体。你需要识别以下类型的知识点：

        1. 测试要点：关键测试场景、边界条件、异常情况
        2. 业务规则：测试用例中隐含的业务规则和约束
        3. 核心功能：测试用例验证的核心功能点
        4. 历史缺陷总结：常见问题和解决方案

        请从以下测试用例中提取有价值的知识点，并按照以下JSON格式输出：

        ```json
        {
          "knowledge_points": [
            {
              "title": "知识点标题",
              "content": "知识点详细内容",
              "item_type": "测试要点|业务规则|核心功能|历史缺陷总结|其他",
              "tags": "标签1,标签2,标签3,前端页面位置名称"
            },
            // 更多知识点...
          ]
        }
        ```

        """+attention_topics

    @message_handler
    async def handle_message(self, message: KnowledgeExtractionRequestMessage, ctx: MessageContext) -> None:
        """处理知识提取请求"""
        try:
            # 保存源链接信息到实例变量
            self._current_source_url = message.source_url
            self._current_source_reference_id = message.source_reference_id

            # 发送状态消息
            await self.publish_message(
                ResponseMessage(source="知识提取智能体", content="开始从测试用例中提取知识点..."),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
            )

            # 创建知识提取助手
            knowledge_extraction_agent = AssistantAgent(
                name="knowledge_extraction_agent",
                model_client=model_client,
                system_message=self._prompt,
                model_client_stream=True,
            )

            # 提取任务
            extraction_task = f"请从以下测试用例中提取知识点:\n\n{message.content}"

            # 流式输出
            stream = knowledge_extraction_agent.run_stream(task=extraction_task)
            extraction_content = ""

            async for msg in stream:
                if isinstance(msg, ModelClientStreamingChunkEvent):
                    # 流式输出到前端
                    await self.publish_message(
                        ResponseMessage(source="知识提取智能体", content=msg.content),
                        topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
                    )
                    continue

                if isinstance(msg, TaskResult):
                    extraction_content = msg.messages[-1].content

            # 尝试从测试用例中提取功能模块信息
            module_name = await self._extract_module_from_content(message.content)
            logger.info(f"从测试用例中提取的功能模块: {module_name}")

            # 解析提取的知识点
            knowledge_points = await self._parse_knowledge_points(extraction_content, message.project_id)

            # 将功能模块名称添加到知识点标签中
            if module_name:
                for point in knowledge_points:
                    # 将功能模块名称添加到标签中
                    if point.tags:
                        # 使用标签规范化函数处理
                        point.tags = await _normalize_tags(point.tags, module_name)
                    else:
                        point.tags = module_name
                    logger.info(f"知识点 '{point.title}' 添加功能模块标签: {point.tags}")

            # 保存知识点到知识库
            saved_count = await self._save_knowledge_points(knowledge_points, "测试用例导入")

            # 发送结果消息
            await self.publish_message(
                ResponseMessage(
                    source="知识提取智能体",
                    content=f"从测试用例中提取并保存了{saved_count}条知识点。",
                    is_final=True
                ),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
            )

        except Exception as e:
            error_msg = f"知识提取过程出错: {str(e)}"
            logger.error(error_msg)
            await self.publish_message(
                ResponseMessage(source="知识提取智能体", content=error_msg),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
            )

    async def _parse_knowledge_points(self, content: str, project_id: int) -> List[KnowledgePoint]:
        """解析提取的知识点"""
        try:
            # 提取JSON部分
            json_start = content.find('{')
            json_end = content.rfind('}') + 1

            if json_start == -1 or json_end == 0:
                logger.warning("未找到JSON格式的知识点")
                return []

            json_content = content[json_start:json_end]
            data = json.loads(json_content)

            # 转换为KnowledgePoint对象
            knowledge_points = []
            for point in data.get("knowledge_points", []):
                knowledge_points.append(
                    KnowledgePoint(
                        title=point["title"],
                        content=point["content"],
                        item_type=point["item_type"],
                        tags=point.get("tags", ""),
                        project_id=project_id,
                        source="测试用例导入",
                        quality_score=0.0,
                        is_duplicate=False,
                        duplicate_id=None
                    )
                )

            logger.info(f"从测试用例中解析出{len(knowledge_points)}个知识点")
            return knowledge_points

        except Exception as e:
            logger.error(f"解析知识点失败: {str(e)}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            return []


    async def _check_duplicate(self, point: KnowledgePoint) -> KnowledgeDuplicateResult:
        """检查知识点是否重复"""
        try:
            # 查询项目下的所有知识点
            _, items = await knowledge_controller.search(project_id=point.project_id, page=1, page_size=1000)

            if not items:
                return KnowledgeDuplicateResult(
                    is_duplicate=False,
                    reason="项目下没有知识点",
                    similarity_score=0.0
                )

            # 计算相似度
            max_similarity = 0.0
            duplicate_id = None
            duplicate_reason = None

            for item in items:
                # 计算标题相似度
                title_similarity = SequenceMatcher(None, point.title, item.title).ratio()

                # 计算内容相似度
                content_similarity = SequenceMatcher(None, point.content, item.content).ratio()

                # 加权计算总相似度
                similarity = (
                    title_similarity * KNOWLEDGE_RULES["title_similarity_weight"] +
                    content_similarity * KNOWLEDGE_RULES["content_similarity_weight"]
                )

                if similarity > max_similarity:
                    max_similarity = similarity
                    duplicate_id = item.id
                    duplicate_reason = f"与知识点ID={item.id}相似度为{similarity:.2f}"

            # 判断是否重复
            is_duplicate = max_similarity >= KNOWLEDGE_RULES["similarity_threshold"]

            return KnowledgeDuplicateResult(
                is_duplicate=is_duplicate,
                duplicate_id=duplicate_id if is_duplicate else None,
                similarity_score=max_similarity,
                reason=duplicate_reason if is_duplicate else "未发现重复知识点"
            )

        except Exception as e:
            logger.error(f"检查知识点重复失败: {str(e)}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")

            return KnowledgeDuplicateResult(
                is_duplicate=False,
                reason=f"检查重复失败: {str(e)}",
                similarity_score=0.0
            )



    # async def _optimize_knowledge_point(self, point: KnowledgePoint,module_name: Optional[str] = None) -> KnowledgePoint:
        # """优化知识点"""
        # 标题优化：去除多余空格，确保首字母大写
        # point.title = point.title.strip()

        # 内容优化：去除多余空格和换行符
        # point.content = re.sub(r'\s+', ' ', point.content).strip()

        # 标签优化：去除重复标签，规范化格式
        # if point.tags:
        #     tags = [tag.strip() for tag in point.tags.split(",") if tag.strip()]
        #     unique_tags = list(set(tags))
        #     point.tags = ",".join(unique_tags)
         # 标签优化：规范化标签
        # point.tags = await self._normalize_tags(point.tags, module_name)

        # return point

    async def _save_knowledge_points(self, knowledge_points: List[KnowledgePoint], source: str) -> int:
        """保存知识点到知识库，增加规则验证和重复检测"""
        saved_count = 0
        validated_count = 0
        duplicate_count = 0

        for point in knowledge_points:
            try:
                # 1. 知识点规则验证
                validation_result = await _validate_knowledge_point(point)
                if not validation_result.is_valid:
                    logger.warning(f"知识点未通过规则验证: {point.title}, 原因: {validation_result.reason}")
                    continue

                validated_count += 1
                point.quality_score = validation_result.quality_score

                # 2. 知识点优化
                # optimized_point = await self._optimize_knowledge_point(point)
                optimized_point = await _optimize_knowledge_point(point)

                # 3. 重复检测
                duplicate_result = await self._check_duplicate(optimized_point)
                if duplicate_result.is_duplicate:
                    logger.warning(f"发现重复知识点: {optimized_point.title}, 原因: {duplicate_result.reason}")
                    duplicate_count += 1
                    continue

                # 4. 转换知识点类型
                item_type = self._map_knowledge_type(optimized_point.item_type)

                # 5. 创建知识条目
                knowledge_item = KnowledgeItemCreate(
                    title=optimized_point.title,
                    content=optimized_point.content,
                    item_type=item_type,
                    tags=optimized_point.tags,
                    source=KnowledgeSource.REQUIREMENT if source == "需求分析导入" else KnowledgeSource.TESTCASE,
                    project_id=optimized_point.project_id,
                    source_url=getattr(self, '_current_source_url', None),
                    source_reference_id=getattr(self, '_current_source_reference_id', None)
                )

                # 6. 保存到数据库 - 使用 create_with_creator 方法，它会自动处理时间戳
                # 使用系统用户ID 1 作为创建者
                await knowledge_controller.create_with_creator(obj_in=knowledge_item, creator_id=1)
                saved_count += 1

                logger.info(f"成功保存知识点: {optimized_point.title}, 质量评分: {optimized_point.quality_score:.2f}")

            except Exception as e:
                logger.error(f"保存知识点失败: {str(e)}")
                # 打印详细的错误堆栈
                import traceback
                logger.error(f"错误堆栈: {traceback.format_exc()}")

        logger.info(f"知识点处理统计: 总数={len(knowledge_points)}, 验证通过={validated_count}, 重复={duplicate_count}, 保存成功={saved_count}")
        return saved_count

    async def _extract_module_from_content(self, content: str) -> Optional[str]:
        """从测试用例中提取功能模块信息"""
        try:
            # 尝试多种方式提取功能模块信息

            # 方式1: 查找"功能模块:"或"[功能模块:"的模式
            module_patterns = [
                r'\[功能模块:\s*([^\]]+)\]',
                r'\[功能模块：\s*([^\]]+)\]',
                r'功能模块:\s*([^\n,]+)',
                r'功能模块：\s*([^\n,]+)',
                r'所属模块[：:]\s*([^\n,]+)',
                r'模块[：:]\s*([^\n,]+)',
                r'module[：:]\s*([^\n,]+)'
            ]

            for pattern in module_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    # 取第一个匹配结果并清理
                    module_name = matches[0].strip()
                    logger.info(f"通过模式 '{pattern}' 提取到功能模块: {module_name}")
                    return module_name

            # 方式2: 查找特定段落中的模块信息
            sections = content.split('\n\n')
            for section in sections:
                if '模块' in section.lower() or 'module' in section.lower():
                    # 提取该段落中的第一个短语作为模块名
                    words = re.sub(r'[^\w\s\u4e00-\u9fff]', ' ', section).split()
                    if len(words) >= 2:
                        for i, word in enumerate(words):
                            if '模块' in word or 'module' in word.lower():
                                if i + 1 < len(words):
                                    module_name = words[i + 1].strip()
                                    logger.info(f"从段落中提取到功能模块: {module_name}")
                                    return module_name

            # 方式3: 查找测试用例标题或描述中的模块信息
            title_patterns = [
                r'测试用例[：:]\s*([^\n]+)',
                r'用例标题[：:]\s*([^\n]+)',
                r'标题[：:]\s*([^\n]+)'
            ]

            for pattern in title_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    title = matches[0].strip()
                    # 从标题中提取可能的模块名
                    title_words = title.split()
                    if len(title_words) >= 2:
                        # 假设第一个词可能是模块名
                        module_name = title_words[0].strip()
                        if len(module_name) >= 2 and not module_name.isdigit():
                            logger.info(f"从标题中提取到可能的功能模块: {module_name}")
                            return module_name

            # 如果上述方法都失败，返回None
            logger.warning("未能从测试用例中提取功能模块信息")
            return None

        except Exception as e:
            logger.error(f"提取功能模块信息失败: {str(e)}")
            return None

    def _map_knowledge_type(self, type_str: str) -> KnowledgeItemType:
        """映射知识点类型字符串到枚举值"""
        type_mapping = {
            "业务规则": KnowledgeItemType.BUSINESS_RULE,
            "核心功能": KnowledgeItemType.CORE_FEATURE,
            "术语解释": KnowledgeItemType.TERMINOLOGY,
            "测试要点": KnowledgeItemType.TEST_POINT,
            "历史缺陷总结": KnowledgeItemType.DEFECT_SUMMARY,
            "非功能要求": KnowledgeItemType.NON_FUNCTIONAL,
            "环境配置": KnowledgeItemType.ENV_CONFIG,
            "其他": KnowledgeItemType.OTHER
        }

        return type_mapping.get(type_str, KnowledgeItemType.OTHER)
