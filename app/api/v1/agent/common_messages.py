"""
共享消息模块

该模块包含所有智能体共享的消息类型和主题类型，避免循环导入问题。
"""

from pydantic import BaseModel
from typing import Any, Dict, List, Optional

# 共享主题类型
task_result_topic_type = "collect_result"
knowledge_extraction_topic_type = "knowledge_extraction"

# 共享消息类型
class ResponseMessage(BaseModel):
    """通用响应消息"""
    source: str
    content: str
    is_final: bool = False

# 知识提取请求消息
class KnowledgeExtractionRequestMessage(BaseModel):
    """请求提取知识点的消息"""
    content: str
    project_id: int
    source_type: str  # "requirement" 或 "testcase"
    source_id: Optional[int] = None  # 需求ID或测试用例ID
    title: Optional[str] = None  # 需求或测试用例标题
    source_url: Optional[str] = None  # 源TAPD需求链接
    source_reference_id: Optional[str] = None  # 源需求引用ID
