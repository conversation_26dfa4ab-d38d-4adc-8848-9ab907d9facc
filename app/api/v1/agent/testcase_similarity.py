"""
测试用例相似度检测模块

该模块提供了计算测试用例相似度的功能，用于检测和标记相似或重复的测试用例。
"""

import logging
from difflib import SequenceMatcher
from typing import List, Dict, Tuple, Any, Optional, Set
import re

from app.schemas.testcases import CaseCreate, TestStepCreate

# 配置日志
logger = logging.getLogger(__name__)

# 相似度阈值配置
SIMILARITY_THRESHOLDS = {
    "title": 0.85,        # 标题相似度阈值
    "description": 0.80,  # 描述相似度阈值
    "steps": 0.75,        # 步骤相似度阈值
    "overall": 0.80       # 整体相似度阈值
}

# 相似度权重配置
SIMILARITY_WEIGHTS = {
    "title": 0.25,        # 标题相似度权重
    "description": 0.15,  # 描述相似度权重
    "steps": 0.60         # 步骤相似度权重
}


def calculate_text_similarity(text1: str, text2: str) -> float:
    """
    计算两个文本字符串的相似度

    参数:
        text1: 第一个文本
        text2: 第二个文本

    返回:
        相似度得分 (0-1)
    """
    if not text1 and not text2:
        return 1.0  # 两个空字符串视为完全相同
    if not text1 or not text2:
        return 0.0  # 一个空一个非空视为完全不同

    # 使用SequenceMatcher计算相似度
    return SequenceMatcher(None, text1, text2).ratio()


def calculate_steps_similarity(steps1: List[TestStepCreate], steps2: List[TestStepCreate]) -> float:
    """
    计算两组测试步骤的相似度

    参数:
        steps1: 第一组测试步骤
        steps2: 第二组测试步骤

    返回:
        相似度得分 (0-1)
    """
    if not steps1 and not steps2:
        return 1.0  # 两组都为空视为完全相同
    if not steps1 or not steps2:
        return 0.0  # 一组为空一组非空视为完全不同

    # 步骤数量差异过大，视为不相似
    if abs(len(steps1) - len(steps2)) > max(2, min(len(steps1), len(steps2)) // 2):
        return 0.3  # 返回一个较低的相似度

    # 计算每个步骤的描述和预期结果的相似度
    total_similarity = 0.0
    compared_pairs = 0

    # 对每个步骤进行比较，找到最相似的配对
    for step1 in steps1:
        best_match_similarity = 0.0
        for step2 in steps2:
            # 计算步骤描述的相似度
            desc_similarity = calculate_text_similarity(step1.description, step2.description)
            # 计算预期结果的相似度
            result_similarity = calculate_text_similarity(step1.expected_result, step2.expected_result)
            # 综合相似度 (描述权重0.6，预期结果权重0.4)
            step_similarity = desc_similarity * 0.6 + result_similarity * 0.4
            best_match_similarity = max(best_match_similarity, step_similarity)

        total_similarity += best_match_similarity
        compared_pairs += 1

    # 反向比较，确保所有步骤都被考虑
    for step2 in steps2:
        best_match_similarity = 0.0
        for step1 in steps1:
            desc_similarity = calculate_text_similarity(step2.description, step1.description)
            result_similarity = calculate_text_similarity(step2.expected_result, step1.expected_result)
            step_similarity = desc_similarity * 0.6 + result_similarity * 0.4
            best_match_similarity = max(best_match_similarity, step_similarity)

        total_similarity += best_match_similarity
        compared_pairs += 1

    # 计算平均相似度
    return total_similarity / compared_pairs if compared_pairs > 0 else 0.0


def calculate_testcase_similarity(testcase1: CaseCreate, testcase2: CaseCreate) -> Tuple[float, Dict[str, float]]:
    """
    计算两个测试用例的整体相似度

    参数:
        testcase1: 第一个测试用例
        testcase2: 第二个测试用例

    返回:
        (整体相似度得分, 各部分相似度详情)
    """
    # 计算标题相似度
    title_similarity = calculate_text_similarity(testcase1.title, testcase2.title)

    # 计算描述相似度
    desc1 = testcase1.desc or ""
    desc2 = testcase2.desc or ""
    description_similarity = calculate_text_similarity(desc1, desc2)

    # 计算步骤相似度
    steps1 = testcase1.steps or []
    steps2 = testcase2.steps or []
    steps_similarity = calculate_steps_similarity(steps1, steps2)

    # 计算整体相似度 (加权平均)
    overall_similarity = (
        title_similarity * SIMILARITY_WEIGHTS["title"] +
        description_similarity * SIMILARITY_WEIGHTS["description"] +
        steps_similarity * SIMILARITY_WEIGHTS["steps"]
    )

    # 返回整体相似度和详细相似度
    similarity_details = {
        "title": title_similarity,
        "description": description_similarity,
        "steps": steps_similarity,
        "overall": overall_similarity
    }

    return overall_similarity, similarity_details


def is_similar_testcase(testcase1: CaseCreate, testcase2: CaseCreate) -> Tuple[bool, float, Dict[str, float]]:
    """
    判断两个测试用例是否相似

    参数:
        testcase1: 第一个测试用例
        testcase2: 第二个测试用例

    返回:
        (是否相似, 整体相似度得分, 各部分相似度详情)
    """
    # 计算相似度
    overall_similarity, similarity_details = calculate_testcase_similarity(testcase1, testcase2)

    # 判断是否相似
    is_similar = (
        overall_similarity >= SIMILARITY_THRESHOLDS["overall"] or
        (similarity_details["title"] >= SIMILARITY_THRESHOLDS["title"] and
         similarity_details["steps"] >= SIMILARITY_THRESHOLDS["steps"])
    )

    return is_similar, overall_similarity, similarity_details


def find_similar_testcases(new_testcase: CaseCreate, existing_testcases: List[CaseCreate]) -> List[Tuple[CaseCreate, float, Dict[str, float]]]:
    """
    在现有测试用例中查找与新测试用例相似的用例

    参数:
        new_testcase: 新测试用例
        existing_testcases: 现有测试用例列表

    返回:
        相似测试用例列表，每项包含 (测试用例, 相似度得分, 相似度详情)
    """
    similar_testcases = []

    for existing_testcase in existing_testcases:
        is_similar, similarity, details = is_similar_testcase(new_testcase, existing_testcase)
        if is_similar:
            similar_testcases.append((existing_testcase, similarity, details))

    # 按相似度降序排序
    similar_testcases.sort(key=lambda x: x[1], reverse=True)

    return similar_testcases


def deduplicate_testcases(testcases: List[CaseCreate]) -> Tuple[List[CaseCreate], List[Tuple[CaseCreate, CaseCreate, float]]]:
    """
    对测试用例列表进行去重，移除高度相似的测试用例

    参数:
        testcases: 测试用例列表

    返回:
        (去重后的测试用例列表, 被移除的测试用例及其对应的相似用例和相似度)
    """
    if not testcases:
        return [], []

    # 去重后的测试用例列表
    unique_testcases = []
    # 被移除的测试用例及其对应的相似用例和相似度
    removed_testcases = []

    for testcase in testcases:
        # 查找与当前测试用例相似的用例
        similar_testcases = find_similar_testcases(testcase, unique_testcases)

        if similar_testcases:
            # 存在相似用例，记录并跳过当前用例
            most_similar = similar_testcases[0]  # 取相似度最高的用例
            removed_testcases.append((testcase, most_similar[0], most_similar[1]))
        else:
            # 不存在相似用例，添加到去重后的列表
            unique_testcases.append(testcase)

    return unique_testcases, removed_testcases


async def deduplicate_with_existing_testcases(new_testcases: List[CaseCreate], tapd_url: str) -> Tuple[List[CaseCreate], List[Tuple[CaseCreate, dict, float]]]:
    """
    将新生成的测试用例与数据库中已存在的相同TAPD URL的测试用例进行比较，去除重复或高度相似的测试用例

    参数:
        new_testcases: 新生成的测试用例列表
        tapd_url: TAPD URL，用于查询数据库中已存在的相关测试用例

    返回:
        (去重后的测试用例列表, 被移除的测试用例及其对应的相似用例和相似度)
    """
    if not new_testcases or not tapd_url:
        return new_testcases, []

    # 导入必要的模块
    from tortoise.expressions import Q
    from app.controllers.testcase import testcase_controller

    try:
        # 查询数据库中与当前TAPD URL相关的所有测试用例
        # 使用无限大的页码和页面大小，确保获取所有相关测试用例
        q = Q(tapd_url=tapd_url)
        total, existing_testcases = await testcase_controller.list(page=1, page_size=1000, search=q)

        if not existing_testcases:
            # 如果没有找到相关测试用例，直接返回原始列表
            return new_testcases, []

        # 去重后的测试用例列表
        unique_testcases = []
        # 被移除的测试用例及其对应的相似用例和相似度
        removed_testcases = []

        # 将数据库中的测试用例转换为CaseCreate对象进行比较
        existing_case_creates = []
        for existing_case in existing_testcases:
            # 提取测试步骤
            steps = []
            if hasattr(existing_case, 'steps') and existing_case.steps:
                for step in existing_case.steps:
                    steps.append(TestStepCreate(
                        description=step.description,
                        expected_result=step.expected_result
                    ))

            # 创建CaseCreate对象
            case_create = CaseCreate(
                title=existing_case.title,
                desc=existing_case.desc,
                priority=existing_case.priority,
                status=existing_case.status,
                preconditions=existing_case.preconditions,
                postconditions=existing_case.postconditions,
                tags=existing_case.tags,
                requirement_id=existing_case.requirement_id,
                project_id=existing_case.project_id,
                creator=existing_case.creator,
                tapd_url=existing_case.tapd_url,
                steps=steps
            )
            existing_case_creates.append(case_create)

        # 对每个新测试用例，检查是否与已存在的测试用例相似
        for new_case in new_testcases:
            # 查找与当前测试用例相似的已存在用例
            similar_existing_cases = find_similar_testcases(new_case, existing_case_creates)

            if similar_existing_cases:
                # 存在相似的已有用例，记录并跳过当前用例
                most_similar = similar_existing_cases[0]  # 取相似度最高的用例
                # 保存原始数据库对象以便显示
                original_db_case = existing_testcases[existing_case_creates.index(most_similar[0])]
                removed_testcases.append((new_case, original_db_case, most_similar[1]))
            else:
                # 不存在相似的已有用例，检查是否与其他新用例相似
                similar_new_cases = find_similar_testcases(new_case, unique_testcases)

                if similar_new_cases:
                    # 存在相似的新用例，记录并跳过当前用例
                    most_similar = similar_new_cases[0]  # 取相似度最高的用例
                    removed_testcases.append((new_case, most_similar[0], most_similar[1]))
                else:
                    # 不存在相似用例，添加到去重后的列表
                    unique_testcases.append(new_case)

        return unique_testcases, removed_testcases

    except Exception as e:
        # 如果查询过程中出现异常，记录错误并返回原始列表
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error comparing with existing testcases: {str(e)}")
        return new_testcases, []
