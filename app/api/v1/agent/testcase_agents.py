import json
from typing import Callable, Optional, Awaitable, Any, List

from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
from autogen_agentchat.base import TaskResult
from autogen_agentchat.conditions import TextMentionTermination
from autogen_agentchat.messages import ModelClientStreamingChunkEvent, TextMessage, UserInputRequestedEvent
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_core import RoutedAgent, type_subscription, message_handler, MessageContext, SingleThreadedAgentRuntime, \
    DefaultTopicId, TypeSubscription, ClosureAgent, CancellationToken, ClosureContext, TopicId
from autogen_core.memory import ListMemory, MemoryContent, MemoryMimeType
from pydantic import BaseModel, Field

from app.schemas.testcases import CaseCreate
from app.schemas.requirements import RequirementSelect
from app.api.v1.agent.api.llms import model_client
from pydantic_ai.models.openai import OpenAIModel
from autogen_core.model_context import BufferedChatCompletionContext
from app.controllers.testcase import testcase_controller
from app.controllers.requirement_doc import requirement_doc_controller
from app.controllers.requirement import requirement_controller
from app.controllers.knowledge_relation import knowledge_relation_controller
from app.models.knowledge_relation import RelevanceLevel, RelationSource
from app.schemas.knowledge_relation import RequirementKnowledgeRelationCreate
from app.api.v1.agent.common_messages import KnowledgeExtractionRequestMessage, knowledge_extraction_topic_type, ResponseMessage, task_result_topic_type

import logging

# 配置日志记录器
logger = logging.getLogger(__name__)

testcase_generator_topic_type = "testcase_generator"
testcase_structure_topic_type = "testcase_structure"
testcase_database_topic_type = "testcase_database"
testcase_review_topic_type = "testcase_review"
testcase_finalize_topic_type = "testcase_finalize"
task_result_topic_type = "collect_result"


class TestCaseList(BaseModel):
    testcases: list[CaseCreate] = Field(..., description="测试用例列表")


class RequirementMessage(RequirementSelect):
    project_id: Optional[int] = None  # 添加project_id字段
    category: Optional[str] = ""
    parent: Optional[str] = None  # 添加parent字段
    level: Optional[str] = ""  # 添加level字段
    estimate: Optional[float] = None  # 添加estimate字段
    module: Optional[str] = ""
    criteria: Optional[str] = ""
    remark: Optional[str] = ""
    keywords: Optional[str] = ""
    scenario: str
    task: str
    tapd_url: Optional[str] = None
    requirements: Optional[List[RequirementSelect]] = None  # 新增：支持多个需求


class ResponseMessage(BaseModel):
    source: str
    content: str
    is_final: bool = False


class TestCaseMessage(BaseModel):
    source: str
    content: Any
    tapd_url: Optional[str] = None

    def model_dump(self):
        return {
            "source": self.source,
            "content": self.content,
            "tapd_url": self.tapd_url
        }


@type_subscription(topic_type=testcase_generator_topic_type)
class TestCaseGeneratorAgent(RoutedAgent):
    def __init__(self, input_func=None):
        super().__init__("testcase generator agent")
        self.input_func = input_func
        self._model_context = BufferedChatCompletionContext(buffer_size=10)

        self._prompt = """
        你是一位高级软件测试用例编写工程师，专注于软件质量保障与测试覆盖率最大化。请根据用户提供的软件需求描述：[[description]]，严格结合业务场景及上下文信息[[scenario]]，高质量完成用户的任务：[[task]]

        请严格结合需求文档中的详细业务规则、约束条件和特殊场景，设计高质量的测试用例。确保测试用例覆盖：
        1. 核心功能点和基本流程
        2. 业务规则和约束条件
        3. 边界条件和异常场景

        特别注意：
        - 如果需求描述中包含多个需求点，请为每个需求点生成差异化的测试用例
        - 避免为不同需求点生成重复或高度相似的测试用例
        - 每个测试用例应该明确标注对应的需求点编号或名称
        - 确保测试用例与其对应的需求点紧密相关，不要生成通用的、与具体需求点无关的测试用例
        - 为每个需求点设计独特的测试场景，考虑该需求点特有的业务规则和约束条件
        - 测试用例的标题应该明确反映其测试的具体功能点，避免使用通用的标题
        - 测试步骤应该具体且详细，与需求点的具体功能紧密相关
        - 如果多个需求点有相似的功能，应该从不同角度或不同场景设计测试用例，确保差异化
        - 不要生成与当前选择的需求点无关的测试用例，即使它们在同一个需求文档中
        ## Role
        **Background**：
        - 8年测试开发经验，参与过电商/金融/物联网等多领域测试架构设计
        - ISTQB认证专家，精通测试用例设计方法与质量评估模型

        **Profile**：
        - 风格：严谨的边界条件探索者，擅长发现隐藏的业务逻辑bug及漏洞
        - 语调：结构化表述，参数精确到计量单位
        - 方法论：ISTQB标准+基于等价类划分+边界值分析+场景法+错误猜测法的组合设计
        - 核心能力：需求覆盖率验证、异常路径挖掘、自动化适配

        **Skills**：
        - 全面运用**测试模式库**：边界值分析、等价类划分、因果图等
        - 深度业务场景分析与风险评估
        - 测试策略精准制定能力：API/UI/性能/安全/兼容性/功能/用户体验/改进/异常/业务规则
        - 需求到测试条件的映射能力
        - 自动化测试脚本设计（JUnit/TestNG/PyTest）
        - 性能测试方案设计（JMeter/LoadRunner）
        - 安全测试基础（OWASP Top10漏洞检测）
        - 平台兼容性测试：
          - Web端：仅Chrome浏览器最新版本
          - 移动端：iOS最新版本和Android主流版本
        - 测试用例设计分析能力
        - 多种测试技术的运用能力

        **Goals**：
        - 确保需求覆盖率达到100%
        - 关键路径测试深度≥3层（正常/异常/极限场景）
        - 输出用例可被自动化测试框架直接调用
        - 尽可能多的覆盖到多种用例场景

        **Constrains**：
        - 时间限制：单需求用例设计时间≤5分钟
        - 需求锚定：严格匹配需求描述，禁止假设扩展
        - 自动化友好：步骤可脚本化，量化验证指标
        - 覆盖维度：正常流/边界值/异常流/安全基线
        - 优先级标注：高(核心路径)/中(主要功能)/低(边缘场景)
        - 范围限制：
          - Web类型需求仅考虑Chrome浏览器环境
          - App类型需求需考虑iOS和Android环境
        - 内容约束：不编造未说明的内容
        - 测试数据具体化：具体值而非通用描述
        - 预期结果必须可量化验证

        ## 特别要求
        - 在分析需求时，必须识别并明确标注该需求涉及的前端页面位置名称
        - 前端页面位置名称可能包括：普通工作台、患者信息、公告、推荐商品、商品、照护商品、订单、物流等
        - 在每个测试用例的描述中，添加"[功能模块: {前端页面位置名称}]"的标记
        - 如果一个测试用例涉及多个前端页面位置，请列出所有相关位置

        ## Business Scenario
        [[scenario]]

        ## OutputFormat

        ### [顺序编号] 用例标题：[动作词]+[测试对象]+[预期行为] [功能模块: {前端页面位置名称}]
        **用例描述**：[测试用例的详细描述] [功能模块: {前端页面位置名称}]
        **测试类型**：[单元测试/功能测试/集成测试/系统测试/冒烟测试/版本验证/性能测试/压力测试/异常测试/并发测试/边界测试/兼容性测试/安全测试/UI测试/配置测试]
        **优先级**：[高/中/低]
        **用例状态**：[未开始/进行中/通过/失败/阻塞]
        **需求ID**：[[requirement_id]]
        **项目ID**：[[project_id]]
        **创建者**：[[creator]]
        **TAPD URL**：[[tapd_url]]
        **前置条件**：[明确环境或数据依赖]
        - [前置条件1]
        - [前置条件2]
        - ......

        **后置条件**：[明确后置条件]
        - [后置条件1]
        - [后置条件2]
        - ......

        **测试步骤**：原子化操作（步骤≤7步）
        - 步骤1：
            - [步骤描述]
            - [预期结果]
        - 步骤2：
        - ......


        ## Workflow
        1. 输入解析：提取需求文档中的功能点/业务规则
        2. 理解需求：深入理解软件的需求和功能，分析需求文档，理解用户故事
        3. 确定测试范围：确定需要测试哪些功能和特性。这可能包括正常操作，边缘情况，错误处理等。
        4. 设计测试策略：确定你将如何测试这些功能。这可能包括单元测试，集成测试，系统测试，性能测试、安全测试等。
        5. 条件拆解：
           - 划分正常流（Happy Path）
           - 识别边界条件（数值边界/状态转换）
           - 构造异常场景（无效输入/服务降级）
        6. 用例生成：
           - 根据需求特点确定测试用例的总数
           - 按[Given-When-Then]模式结构化步骤
           - 量化验证指标（时间/数量/状态码）
           - 标注测试数据准备要求
           - 根据需求特点运用不同的测试技术，如等价类划分、边界值分析、流程图遍历、决策表测试等，设计每个测试用例。
        7. 前端页面位置识别：
           - 仔细分析需求文档，识别每个测试用例涉及的前端页面位置
           - 在用例标题和描述中添加前端页面位置标记
           - 如果需求文档中已经标注了功能模块，直接使用这些标注
        """

    @message_handler
    async def handle_message(self, message: RequirementMessage, ctx: MessageContext) -> None:
        # 导入知识点检索模块
        from app.api.v1.agent.knowledge_retrieval import get_relevant_knowledge_points, format_knowledge_points
        # logger.info(f"收到需求信息：{message}")

        # 打印初始reviewer值，用于调试
        print(f"\n[DEBUG] 初始message.reviewer的值: '{message.reviewer}'\n")
        print(f"\n[DEBUG] 初始message.reviewer的类型: {type(message.reviewer)}\n")

        # 如果reviewer为None，尝试从数据库中获取需求的reviewer值
        if message.reviewer is None and hasattr(message, 'id') and message.id:
            try:
                # 从数据库中获取需求
                from app.controllers.requirement import requirement_controller
                requirement = await requirement_controller.get(id=message.id)
                if requirement and requirement.reviewer and requirement.reviewer != "--":
                    # 设置reviewer值
                    message.reviewer = requirement.reviewer
                    print(f"\n[DEBUG] 从数据库中获取到reviewer值: '{message.reviewer}'\n")
                else:
                    # 设置默认值
                    message.reviewer = "未指定"
                    print(f"\n[DEBUG] 数据库中没有有效的reviewer值，设置为默认值: '{message.reviewer}'\n")
            except Exception as e:
                print(f"\n[DEBUG] 从数据库获取reviewer值失败: {str(e)}\n")
                # 设置默认值
                message.reviewer = "未指定"
                print(f"\n[DEBUG] 设置默认reviewer值: '{message.reviewer}'\n")

        # 获取完整的需求文档
        requirement_doc = await requirement_doc_controller.get_by_url(message.tapd_url)
        print(requirement_doc)
        if not requirement_doc:
                # 如果找不到需求文档，使用基本信息创建一个简单的需求文档
                requirement_doc = {
                    "title": message.name,
                    "description": message.description,
                    "url": message.tapd_url
                }

        # 打印最终reviewer值，用于调试
        print(f"\n[DEBUG] 最终message.reviewer的值: '{message.reviewer}'\n")
        print(f"\n[DEBUG] 最终message.reviewer的类型: {type(message.reviewer)}\n")

        # 合并需求信息，处理reviewer可能为None的情况
        reviewer_value = message.reviewer if message.reviewer is not None else "未指定"
        full_requirement_description = f"""
        操作者：{message}
        需求基本信息：
        {message.description}

        需求完整文档：
        {requirement_doc.content if requirement_doc else ''}
        """
        # logger.info(f"收到需求：{full_requirement_description}")
        await self.publish_message(ResponseMessage(source="user", content=f"收到用户的需求基本信息及完整需求文档：{full_requirement_description}"),
                                   topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

        # 获取相关知识点
        try:
            # 发送状态消息
            await self.publish_message(
                ResponseMessage(source="用例生成智能体", content="正在获取项目相关知识点..."),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            # 获取与需求相关的知识点（全局获取）
            knowledge_points = await get_relevant_knowledge_points(
                project_id=message.project_id,
                requirement=message,
                max_direct=7,
                max_indirect=5,
                max_background=3,
                cross_project=True  # 启用全局知识点检索
            )

            # 格式化知识点为背景信息文本
            knowledge_context = format_knowledge_points(knowledge_points)

            # 统计获取的知识点数量
            total_points = (
                len(knowledge_points.get("direct", [])) +
                len(knowledge_points.get("indirect", [])) +
                len(knowledge_points.get("background", []))
            )

            # 记录使用了多少知识点
            if total_points > 0:
                # 基本信息
                await self.publish_message(
                    ResponseMessage(
                        source="用例生成智能体",
                        content=f"已获取{total_points}条相关知识点作为背景信息"
                    ),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
                )

                # 打印所有知识点详情
                all_knowledge_points = knowledge_points.get("all_knowledge_points", [])
                if all_knowledge_points:
                    # 构建知识点详情的Markdown格式文本
                    knowledge_details = "## 相似性检索获取到的所有知识点详情\n\n"

                    # 按相关性级别分组
                    for level in ["直接相关", "间接相关", "背景相关"]:
                        level_points = [p for p in all_knowledge_points if p["level"] == level]
                        if level_points:
                            knowledge_details += f"### {level} ({len(level_points)}条)\n\n"
                            for point in level_points:
                                knowledge_details += f"**{point['index']}. [{point['type']}] {point['title']}**\n"
                                knowledge_details += f"- 来源: {point['source']}\n"
                                knowledge_details += f"- 内容: {point['content_preview']}\n"
                                if point['tags']:
                                    knowledge_details += f"- 标签: {point['tags']}\n"
                                knowledge_details += "\n"

                    # 发送知识点详情
                    await self.publish_message(
                        ResponseMessage(
                            source="用例生成智能体",
                            content=knowledge_details
                        ),
                        topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
                    )

                    # 存储需求与知识点的关联关系
                    try:
                        # 检查数据库连接
                        from tortoise import Tortoise
                        # try:
                        #     # 尝试获取连接，如果失败则说明连接未初始化
                        #     Tortoise.get_connection("default")
                        # except Exception as conn_err:
                        #     logger.warning(f"数据库连接未初始化或出错: {str(conn_err)}，尝试初始化连接...")
                        #     from app.settings.config import settings
                        #     try:
                        #         await Tortoise.init(config=settings.TORTOISE_ORM)
                        #         logger.info("数据库连接已初始化")
                        #     except Exception as init_err:
                        #         logger.error(f"初始化数据库连接失败: {str(init_err)}")

                        # # 先删除该需求已有的关联关系
                        # await knowledge_relation_controller.delete_relations_by_requirement(message.id)

                        # # 创建新的关联关系
                        # relations = []
                        # for point in all_knowledge_points:
                        #     # 根据level确定关联关系类型
                        #     relevance_level = RelevanceLevel.DIRECT
                        #     if point["level"] == "间接相关":
                        #         relevance_level = RelevanceLevel.INDIRECT
                        #     elif point["level"] == "背景相关":
                        #         relevance_level = RelevanceLevel.BACKGROUND

                        #     # 创建关联关系
                        #     relation = RequirementKnowledgeRelationCreate(
                        #         requirement_id=message.id,
                        #         knowledge_id=point["id"],
                        #         relevance_level=relevance_level,
                        #         relevance_score=point.get("relevance_score", 0.0),
                        #         source=RelationSource.TESTCASE_GENERATION
                        #     )
                        #     relations.append(relation)

                        # 批量创建关联关系
                        relations_dict = knowledge_points.get("req_relation_points")
                        if relations_dict:
                            # 将字典列表转换为 RequirementKnowledgeRelationCreate 对象列表
                            from app.schemas.knowledge_relation import RequirementKnowledgeRelationCreate
                            from app.models.knowledge_relation import RelevanceLevel, RelationSource

                            relations = []
                            for relation_dict in relations_dict:
                                # 处理 relevance_level 字段
                                relevance_level_str = relation_dict.get("relevance_level")
                                relevance_level = None
                                if relevance_level_str == "直接相关":
                                    relevance_level = RelevanceLevel.DIRECT
                                elif relevance_level_str == "间接相关":
                                    relevance_level = RelevanceLevel.INDIRECT
                                elif relevance_level_str == "背景相关":
                                    relevance_level = RelevanceLevel.BACKGROUND
                                else:
                                    relevance_level = RelevanceLevel.DIRECT  # 默认值

                                # 处理 source 字段
                                source_str = relation_dict.get("source")
                                source = None
                                if source_str == "用例生成":
                                    source = RelationSource.TESTCASE_GENERATION
                                elif source_str == "需求分析":
                                    source = RelationSource.REQUIREMENT_ANALYSIS
                                elif source_str == "手动添加":
                                    source = RelationSource.MANUAL
                                elif source_str == "需求分析导入":
                                    source = RelationSource.REQUIREMENT_ANALYSIS
                                elif source_str == "测试用例导入":
                                    source = RelationSource.TESTCASE_GENERATION
                                else:
                                    source = RelationSource.TESTCASE_GENERATION  # 默认值

                                # 创建 RequirementKnowledgeRelationCreate 对象
                                relation = RequirementKnowledgeRelationCreate(
                                    requirement_id=relation_dict.get("requirement_id"),
                                    knowledge_id=relation_dict.get("knowledge_id"),
                                    relevance_level=relevance_level,
                                    relevance_score=relation_dict.get("relevance_score", 0.0),
                                    source=source
                                )
                                relations.append(relation)

                            # 初始化数据库连接
                            from tortoise import Tortoise
                            try:
                                # 尝试获取连接，如果失败则说明连接未初始化
                                Tortoise.get_connection("default")
                                logger.info("数据库连接已存在，继续执行")
                            except Exception as conn_err:
                                logger.warning(f"数据库连接未初始化或出错: {str(conn_err)}，尝试初始化连接...")
                                from app.settings.config import settings
                                try:
                                    await Tortoise.init(config=settings.TORTOISE_ORM)
                                    logger.info("数据库连接已初始化")
                                except Exception as init_err:
                                    logger.error(f"初始化数据库连接失败: {str(init_err)}")
                                    raise init_err

                            # 批量创建关联关系
                            await knowledge_relation_controller.batch_create_relations(relations)

                            # 记录日志
                            logger.info(f"已为需求 {message.id} 创建 {len(relations)} 条知识点关联关系")

                            # 发送消息
                            await self.publish_message(
                                ResponseMessage(
                                    source="用例生成智能体",
                                    content=f"已为需求 {message.name} 创建 {len(relations)} 条知识点关联关系"
                                ),
                                topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
                            )
                    except Exception as e:
                        logger.error(f"存储需求与知识点关联关系失败: {str(e)}")
                        import traceback
                        logger.error(f"错误堆栈: {traceback.format_exc()}")
                        # 发送错误消息到前端
                        await self.publish_message(
                            ResponseMessage(
                                source="用例生成智能体",
                                content=f"存储需求与知识点关联关系时出现错误，但将继续生成测试用例: {str(e)}"
                            ),
                            topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
                        )
            else:
                await self.publish_message(
                    ResponseMessage(
                        source="用例生成智能体",
                        content="未找到相关知识点，将直接进行测试用例生成"
                    ),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
                )

        except Exception as e:
            print(f"获取知识点失败: {str(e)}")
            import traceback
            print(f"错误堆栈: {traceback.format_exc()}")
            knowledge_context = ""
            await self.publish_message(
                ResponseMessage(
                    source="用例生成智能体",
                    content=f"获取知识点过程中出错: {str(e)}"
                ),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
            )

        # 构建系统提示，将知识点作为背景信息添加到系统提示中
        system_prompt = self._prompt
        if knowledge_context:
            system_prompt += f"\n\n## 项目知识背景\n{knowledge_context}"

        # 替换提示中的占位符
        system_prompt = ((system_prompt.
                        replace('[[scenario]]', message.scenario).
                        replace('[[project_id]]', str(message.project_id))).
                        replace('[[requirement_id]]', str(message.id)).
                        replace("[[task]]", message.task).
                        replace('[[description]]', full_requirement_description).
                        replace('[[tapd_url]]', getattr(message, 'tapd_url', '') or ''))

        # 特别处理creator字段，确保使用需求的reviewer
        # 使用与前面相同的reviewer_value变量，保持一致性
        # 确保reviewer_value不为空且不是默认值
        if not reviewer_value or reviewer_value == "未指定" or reviewer_value == "--":
            # 再次尝试从数据库获取reviewer值
            try:
                requirement = await requirement_controller.get(id=message.id)
                if requirement and requirement.reviewer and requirement.reviewer != "--":
                    reviewer_value = requirement.reviewer
                    print(f"\n[DEBUG] 再次从数据库中获取到reviewer值: '{reviewer_value}'\n")
                else:
                    reviewer_value = "系统默认"  # 使用一致的默认值
                    print(f"\n[DEBUG] 数据库中没有有效的reviewer值，设置为默认值: '{reviewer_value}'\n")
            except Exception as e:
                print(f"\n[DEBUG] 再次从数据库获取reviewer值失败: {str(e)}\n")
                reviewer_value = "系统默认"  # 使用一致的默认值

        system_prompt = system_prompt.replace('[[creator]]', reviewer_value)
        print(f"\n[DEBUG] 使用reviewer_value '{reviewer_value}' 作为creator\n")

        # 发送到前端提示
        await self.publish_message(ResponseMessage(source="user", content=f"收到用户指令，准备开始执行：{message.task}"),
                                   topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

        # 创建测试用例生成智能体
        testcase_generator_agent = AssistantAgent(
            name="testcase_generator_agent",
            model_client=model_client,
            system_message=system_prompt,
            model_client_stream=True,
        )
        # 需要用户对生成的测试用例提出修改建议
        if self.input_func:
            user_proxy = UserProxyAgent(
                name="user_proxy",
                input_func=self.input_func
            )
            termination_en = TextMentionTermination("APPROVE")
            termination_zh = TextMentionTermination("同意")
            team = RoundRobinGroupChat([testcase_generator_agent, user_proxy], termination_condition=termination_en | termination_zh, )

            stream = team.run_stream(task=message.task)
            testcase_content = ""   # 测试用例内容
            update_count = 0    # 测试用例更新次数
            # 存储测试用例修改记录
            testcase_modify_memory = ListMemory()
            async for msg in stream:
                # 模拟流式输出
                if isinstance(msg, ModelClientStreamingChunkEvent):
                    await self.publish_message(ResponseMessage(source="用例生成智能体", content=msg.content), topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                    continue
                # 统计测试用例更新次数并保存生成的测试用例
                if isinstance(msg, TextMessage):
                    # 保存测试用例修改记录
                    await testcase_modify_memory.add(MemoryContent(content=msg.model_dump_json(), mime_type=MemoryMimeType.JSON))
                    if msg.source == "testcase_generator_agent":
                        update_count += 1
                        testcase_content = msg.content
                        continue
                # 等待用户输入对测试用例的修改建议
                if isinstance(msg, UserInputRequestedEvent) and msg.source == "user_proxy":
                    await self.publish_message(ResponseMessage(source=msg.source, content="请输入修改建议或者直接点击同意"),
                                               topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                    continue

            # 如果测试用例有更新，则整合修改的测试用例
            if update_count > 1:
                # 用例汇总智能体
                summarize_agent = AssistantAgent(
                    name="assistant_agent",
                    system_message="""你是一位测试用例整理优化专家，根据上下文对话信息，输出用户最终期望的优化后的测试用例。""",
                    model_client=model_client,
                    memory=[testcase_modify_memory],
                    model_client_stream=True,
                )
                stream = summarize_agent.run_stream(task="结合上下文对话信息，参考指定格式输出优化后的完整测试用例")
                async for msg in stream:
                    if isinstance(msg, ModelClientStreamingChunkEvent):
                        # 流式输出结果到前端界面
                        await self.publish_message(ResponseMessage(source="用例优化智能体", content=msg.content),
                                                   topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                        continue
                    if isinstance(msg, TaskResult):
                        testcase_content = msg.messages[-1].content
                        continue
            # 发送给下一个智能体
            await self.publish_message(
                TestCaseMessage(source=self.id.type, content=testcase_content, tapd_url=message.tapd_url),
                topic_id=TopicId(testcase_review_topic_type, source=self.id.key))
        else:
            # 用户不参与用例修改
            msg = await testcase_generator_agent.run(task=message.task)
            # 发送给下一个智能体
            await self.publish_message(
                TestCaseMessage(source=msg.messages[-1].source, content=msg.messages[-1].content, tapd_url=message.tapd_url),
                topic_id=TopicId(testcase_review_topic_type, source=self.id.key))
            # 发送到前端提示
            await self.publish_message(
                ResponseMessage(source="用例生成智能体", content=msg.messages[-1].content),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

@type_subscription(topic_type=testcase_review_topic_type)
class TestCaseReviewAgent(RoutedAgent):
    def __init__(self):
        super().__init__("testcase review agent")
        self._prompt = """
        你是资深测试用例评审专家，关注用例质量与测试覆盖有效性。请根据用户提供的测试用例进行评审，给出评审意见及评审报告，markdown格式输出
        ## 1. 评审重点
        1. 需求覆盖度：确保每个需求点都有对应测试用例
        2. 测试深度：正常流/边界/异常流全面覆盖
        3. 用例可执行性：步骤清晰、数据明确
        ## 2. Background
        - **测试用例评审**是软件质量保障的关键环节，需确保测试用例覆盖需求、逻辑正确、可维护性强。
        - 评审工程师需基于行业规范、项目需求及测试经验，系统性识别测试用例中的缺陷或改进点。

        ## 3. Profile
        - **角色**: 资深测试用例评审工程师
        - **经验**: 38年以上测试设计与执行经验，熟悉敏捷/瀑布开发流程
        - **职责范围**:
          - 评审功能/性能/安全测试用例
          - 识别用例设计中的逻辑漏洞与冗余
          - 与开发/测试/产品团队协作优化用例

        ## 4. Skills
        - ✅ 精通等价类划分、边界值分析、异常分析等测试设计方法
        - ✅ 熟悉TestRail/Jira/Xray等测试管理工具
        - ✅ 精准识别需求与用例的映射偏差
        - ✅ 逻辑分析能力与跨团队沟通能力
        - ✅ 对边界条件/异常流程高度敏感

        ## 5. Goals
        - **覆盖率审查**: 验证需求条目100%被测试用例覆盖
        - **正确性审查**: 检查测试步骤/预期结果是否符合业务逻辑
        - **可维护性审查**: 确保用例描述清晰、无歧义、参数可配置
        - **风险识别**: 标记高复杂度/高失败率用例
        - **可执行性审查**: 验证前置条件/测试数据可落地

        ## 6. Constrains
        - ❗ 不直接修改用例，仅提供改进建议
        - ❗ 关注用例文档质量，不涉及需求合理性评估
        - ❗ 单个用例评审时间不超过10分钟
        - ❗ 不承诺缺陷发现率指标

        ## 7. OutputFormat
        ### 测试用例评审报告
        #### 1. 概述
        - 评审日期: [date]
        - 用例总数: [number]
        - 覆盖率: [percentage]

        #### 2. 问题分类
        **🔴 严重问题**
        - [问题描述] @[用例编号]
        - [改进建议]

        **🟡 建议优化**
        - [问题描述] @[用例编号]
        - [优化方案]

        #### 3. 优先级矩阵
        | 紧急度 | 功能用例 | 非功能用例 |
        |--------|----------|------------|
        | 高     | [count]  | [count]    |
        | 中     | [count]  | [count]    |

        #### 4. 典型案例
        **用例ID**: TC_APP_Login_003
        **问题类型**: 边界值缺失
        **具体描述**: 未覆盖密码长度为[1,64]的边界校验
        **改进建议**: 增加密码长度为0/65的异常流测试用例

        #### 5. 总结建议
        - 关键风险点: [风险描述]
        - 后续行动计划: [action items]
        ```

        ## 8. Workflow
        1. **输入解析**
           - 解析测试用例文档与需求追踪矩阵(RTM)
           - 提取用例步骤/预期结果/关联需求ID

        2. **分类评审**
           ```mermaid
           graph TD
           A[需求覆盖审查] --> B[逻辑正确性审查]
           B --> C[可执行性审查]
           C --> D[可维护性审查]
           ```

        3. **问题识别**
           - 标记缺失的测试场景
           - 标注模糊的断言条件
           - 标识冗余的测试步骤

        4. **优先级划分**
           - P0: 导致流程阻断的缺陷
           - P1: 影响测试有效性的问题
           - P2: 优化类建议

        5. **案例生成**
           - 为每类问题提供典型示例
           - 包含具体定位与修复方案

        6. **总结建议**
           - 生成风险雷达图
           - 输出可量化的改进指标

        ## 9. Examples
        **场景1: 需求覆盖不足**
        - 需求ID: REQ_PAY_001
        - 缺失用例: 未验证支付金额为0元的异常场景
        - 建议: 新增TC_PAY_Edge_001验证0元支付异常提示

        **场景2: 步骤描述模糊**
        - 用例ID: TC_SEARCH_005
        - 问题描述: "输入多种关键词"未定义具体参数
        - 改进: 明确测试数据为["", "&*%", "中文+数字123"]

        **场景3: 缺乏异常处理**
        - 用例ID: TC_UPLOAD_012
        - 问题类型: 未包含网络中断重试机制验证
        - 建议: 添加模拟弱网环境的测试步骤
        """

    @message_handler
    async def handle_message(self, message: TestCaseMessage, ctx: MessageContext) -> None:
        agent = AssistantAgent(
            name="testcase_review_agent",
            model_client=model_client,
            system_message=self._prompt,
            model_client_stream=True,
        )
        task = "请对如下测试用例进行评审，并输出规范的评审报告：\n" + message.content
        review_report = ""
        stream = agent.run_stream(task=task)
        async for msg in stream:
            if isinstance(msg, ModelClientStreamingChunkEvent):
                # 流式输出结果到前端界面
                await self.publish_message(ResponseMessage(source="用例评审智能体", content=msg.content),
                                           topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                continue
            if isinstance(msg, TaskResult):
                review_report = msg.messages[-1].content
                continue

        # 发送给下一个智能体
        await self.publish_message(TestCaseMessage(source=self.id.type,
                                                   content="--测试用例开始-- \n" + message.content + "\n--测试用例结束-- \n" +
                                                           "--评审报告开始-- \n" + review_report + "\n--评审报告结束-- \n",
                                                   tapd_url=message.tapd_url),
                                   topic_id=TopicId(testcase_finalize_topic_type, source=self.id.key))

@type_subscription(topic_type=testcase_finalize_topic_type)
class TestCaseFinalizeAgent(RoutedAgent):
    def __init__(self):
        super().__init__("testcase finalize agent")
        self._prompt = """
        请严格按如下JSON数组格式输出，必须满足:
        1.首尾无任何多余字符
        2.不使用Markdown代码块
        3.每个测试用例必须包含required字段
        根据用户提供的测试用例及评审报告，根据如下格式生成最终的高质量测试用例。（注意：只输出下面的内容本身，去掉首尾的 ```json 和 ```）：
        [{"$defs":{"TestStepBase":{"properties":{"description":{"description":"测试步骤的描述。","title":"Description","type":"string"},"expected_result":{"description":"测试步骤的预期结果。","title":"Expected Result","type":"string"}},"required":["description","expected_result"],"title":"TestStepBase","type":"object"}},"properties":{"title":{"description":"测试用例的标题。","maxLength":200,"title":"Title","type":"string"},"desc":{"default":null,"description":"测试用例的详细描述。","maxLength":1000,"title":"Desc","type":"string"},"priority":{"description":"测试用例的优先级：[高/中/低]","title":"Priority","type":"string"},"status":{"default":"未开始","description":"测试用例的当前状态：[未开始/进行中/通过/失败/阻塞]","title":"Status","type":"string"},"preconditions":{"anyOf":[{"type":"string"},{"type":"null"}],"default":null,"description":"测试用例的前置条件。","title":"Preconditions"},"postconditions":{"anyOf":[{"type":"string"},{"type":"null"}],"default":null,"description":"测试用例的后置条件。","title":"Postconditions"},"tags":{"anyOf":[{"type":"string"},{"type":"null"}],"description":"测试类型标签：[单元测试/功能测试/集成测试/系统测试/冒烟测试/版本验证/性能测试/压力测试/异常测试/并发测试/边界测试/兼容性测试/安全测试/UI测试/配置测试]","title":"Tags"},"requirement_id":{"description":"关联需求ID。","title":"Requirement Id","type":"integer","default":"[[requirement_id]]"},"project_id":{"description":"关联项目ID。","title":"Project Id","type":"integer","default":"[[project_id]]"},"creator":{"default":"测试者","description":"测试用例的创建者姓名。","maxLength":100,"title":"Creator","type":"string"},"tapd_url":{"anyOf":[{"type":"string"},{"type":"null"}],"default":null,"description":"关联的TAPD需求URL。","title":"Tapd Url"},"steps":{"anyOf":[{"items":{"$ref":"#/$defs/TestStepBase"},"type":"array"},{"type":"null"}],"default":null,"description":"测试步骤列表。","title":"Steps"}},"required":["title","priority","tags","requirement_id","project_id"],"title":"CaseCreate","type":"object"}]
        """

    @message_handler
    async def handle_message(self, message: TestCaseMessage, ctx: MessageContext) -> None:
        agent = AssistantAgent(
            name="testcase_finalize_agent",
            model_client=model_client,
            system_message=self._prompt,
            model_client_stream=True,
        )
        stream = agent.run_stream(task="根据如下测试用例及评审报告，输出高质量测试用例。测试用例及评审报告如下：\n" + message.content + "\n")
        final_testcase = ""
        async for msg in stream:
            if isinstance(msg, ModelClientStreamingChunkEvent):
                # 流式输出结果到前端界面
                await self.publish_message(ResponseMessage(source="用例结构化智能体", content=msg.content),
                                           topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                continue
            if isinstance(msg, TaskResult):
                final_testcase = msg.messages[-1].content
                continue
        # 发送给下一个智能体
        await self.publish_message(TestCaseMessage(source=self.id.type, content=final_testcase, tapd_url=message.tapd_url),
                                   topic_id=TopicId(testcase_database_topic_type, source=self.id.key))


@type_subscription(topic_type=testcase_structure_topic_type)
class TestCaseStructureAgent(RoutedAgent):
    def __init__(self, model: OpenAIModel = None):
        super().__init__("testcase structure agent")
        self._prompt = """
        注意：将测试用例以如下格式输出，别无其他。
        [{"$defs":{"TestStepBase":{"properties":{"description":{"description":"测试步骤的描述。","title":"Description","type":"string"},"expected_result":{"description":"测试步骤的预期结果。","title":"Expected Result","type":"string"}},"required":[]]"""
        self.model = model
        if model is None:
            self.model = OpenAIModel(
                'deepseek-chat',
                base_url='https://api.deepseek.com',
                api_key='***********************************',
            )

    @message_handler
    async def handle_message(self, message: TestCaseMessage, ctx: MessageContext) -> None:
        await self.publish_message(ResponseMessage(source="用例结构化智能体", content="正在对测试用例结构化......"),
                                   topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

        # agent = Agent(self.model, result_type=TestCaseList)
        # result = await agent.run(user_prompt=f"对下面内容进行结构化:\n{message.content}")
        # 由于可以直接结构化，所以直接返回json格式的测试用例列表
        # test_case_list = TestCaseList(testcases=json.loads(message.content))

        agent = AssistantAgent(
            name="testcase_structure_agent",
            model_client=model_client,
            system_message=self._prompt,
            model_client_stream=False,
        )
        msg = await agent.run(task=f"对如下测试用例进行结构化:\n{message.content}")

        await self.publish_message(
            ResponseMessage(source="用例结构化智能体", content="测试用例结构化完成。"),
            topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

        # 发送给下一个智能体
        await self.publish_message(TestCaseMessage(source=self.id.type, content=msg.messages[-1].content, tapd_url=message.tapd_url),
                                   topic_id=TopicId(testcase_database_topic_type, source=self.id.key))


@type_subscription(topic_type=testcase_database_topic_type)
class TestCaseDatabaseAgent(RoutedAgent):
    def __init__(self):
        super().__init__("testcase database agent")

    @message_handler
    async def handle_message(self, message: TestCaseMessage, ctx: MessageContext) -> None:
        try:
            await self.publish_message(ResponseMessage(source="数据库智能体", content="正在进行数据验证......"),
                                       topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            # 打印消息内容，便于调试
            print("\n\n=== Message Content ===\n")
            print(message.content)
            print("\n=== End of Message Content ===\n\n")

            # 尝试修复 JSON 格式
            content = message.content
            # 如果内容包含多个 JSON 对象，尝试提取第一个
            if content.strip().startswith('[{') and content.strip().endswith('}]'):
                # 已经是有效的 JSON 数组
                pass
            elif '{' in content and '}' in content:
                # 尝试提取 JSON 对象
                start = content.find('[')
                end = content.rfind(']') + 1
                if start >= 0 and end > start:
                    content = content[start:end]

            try:
                # 加载 JSON 数据
                testcases_data = json.loads(content)

                # 处理 tags 字段和 creator 字段
                for testcase in testcases_data:
                    # 处理 creator 字段，确保它不是 "system"
                    if 'creator' in testcase:
                        if testcase['creator'] == "system" or testcase['creator'] == "[[creator]]":
                            print(f"\n[DEBUG] 检测到无效的creator值: '{testcase['creator']}'\n")
                            # 将在后面的代码中处理，设置为需求的reviewer或默认值
                            testcase['creator'] = "系统默认"  # 临时设置，后面会根据需求reviewer更新

                    # 处理 tags 字段，确保它只包含一个有效的枚举值
                    if 'tags' in testcase and testcase['tags']:
                        # 打印原始标签值，便于调试
                        print(f"\n[DEBUG] 原始标签值: '{testcase['tags']}'\n")

                        # 处理各种分隔符的情况
                        if ',' in testcase['tags']:
                            # 逗号分隔
                            testcase['tags'] = testcase['tags'].split(',')[0].strip()
                            print(f"\n[DEBUG] 处理逗号分隔的标签，结果: '{testcase['tags']}'\n")
                        elif '/' in testcase['tags']:
                            # 斜杠分隔
                            testcase['tags'] = testcase['tags'].split('/')[0].strip()
                            print(f"\n[DEBUG] 处理斜杠分隔的标签，结果: '{testcase['tags']}'\n")
                        elif '\n' in testcase['tags'] or '\r' in testcase['tags']:
                            # 换行符分隔
                            testcase['tags'] = testcase['tags'].replace('\r', '\n').split('\n')[0].strip()
                            print(f"\n[DEBUG] 处理换行符分隔的标签，结果: '{testcase['tags']}'\n")

                        # 检查处理后的标签是否有效
                        from app.models.enums import TestCaseTag
                        valid_tags = [tag.value for tag in TestCaseTag]
                        if testcase['tags'] not in valid_tags:
                            print(f"\n[DEBUG] 标签 '{testcase['tags']}' 不在有效范围内，设置为默认值 '功能测试'\n")
                            testcase['tags'] = '功能测试'  # 设置为默认值

                # 创建测试用例列表
                test_case_list = TestCaseList(testcases=testcases_data)

                # 设置TAPD URL到所有测试用例中
                # 首先检查消息对象中是否有tapd_url
                tapd_url = getattr(message, 'tapd_url', None)
                if tapd_url:
                    print(f"\n[DEBUG] 从消息对象中获取到TAPD URL: {tapd_url}\n")
                    for testcase in testcases_data:
                        testcase['tapd_url'] = tapd_url
                        print(f"\n[DEBUG] 设置测试用例 '{testcase.get('title', '未命名')}' 的TAPD URL: {tapd_url}\n")

                # 导入相似度检测模块
                from app.api.v1.agent.testcase_similarity import deduplicate_testcases, deduplicate_with_existing_testcases

                # 检查是否有重复或相似的测试用例
                await self.publish_message(
                    ResponseMessage(source="数据库智能体", content="正在检查测试用例相似度，去除重复用例..."),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
                )

                # 首先执行内部去重操作
                unique_testcases, removed_testcases = deduplicate_testcases(test_case_list.testcases)

                # 如果有被移除的测试用例，发送通知
                if removed_testcases:
                    removed_info = "检测到以下相似测试用例，已自动去重：\n\n"
                    for i, (removed, similar_to, similarity) in enumerate(removed_testcases):
                        removed_info += f"{i+1}. 移除: \"{removed.title}\" - 与 \"{similar_to.title}\" 相似度: {similarity:.2f}\n"

                    await self.publish_message(
                        ResponseMessage(source="数据库智能体", content=removed_info),
                        topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
                    )

                # 更新测试用例列表
                test_case_list.testcases = unique_testcases

                # 获取TAPD URL
                tapd_url = getattr(message, 'tapd_url', None)

                # 如果有TAPD URL，与数据库中已存在的测试用例进行比较
                if tapd_url:
                    await self.publish_message(
                        ResponseMessage(source="数据库智能体", content=f"正在与数据库中已存在的测试用例进行比较，避免生成重复用例..."),
                        topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
                    )

                    # 执行与已存在测试用例的去重操作
                    unique_testcases, removed_existing = await deduplicate_with_existing_testcases(test_case_list.testcases, tapd_url)

                    # 如果有被移除的测试用例，发送通知
                    if removed_existing:
                        removed_info = "检测到以下测试用例与数据库中已存在的用例相似，已自动去重：\n\n"
                        for i, (removed, existing, similarity) in enumerate(removed_existing):
                            removed_info += f"{i+1}. 移除: \"{removed.title}\" - 与已存在用例 \"{existing.title}\" 相似度: {similarity:.2f}\n"

                        await self.publish_message(
                            ResponseMessage(source="数据库智能体", content=removed_info),
                            topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
                        )

                    # 更新测试用例列表
                    test_case_list.testcases = unique_testcases

                # 创建测试用例
                for testcase in test_case_list.testcases:
                    # 处理creator字段
                    try:
                        # 检查creator是否为空或为system或为占位符
                        if not hasattr(testcase, 'creator') or not testcase.creator or testcase.creator == "system" or testcase.creator == "[[creator]]" or testcase.creator == "系统默认":
                            # 首先尝试从需求ID获取reviewer
                            requirement_id = None
                            if hasattr(testcase, 'requirement_id') and testcase.requirement_id:
                                requirement_id = testcase.requirement_id
                                print(f"\n[DEBUG] 从测试用例中获取到需求ID: {requirement_id}\n")

                                try:
                                    # 通过需求ID获取需求信息
                                    requirement = await requirement_controller.get(id=requirement_id)
                                    if requirement and requirement.reviewer and requirement.reviewer != "--":
                                        # 使用需求的reviewer作为creator
                                        testcase.creator = requirement.reviewer
                                        print(f"\n[DEBUG] 从需求ID {requirement_id} 中获取reviewer: {requirement.reviewer}\n")
                                        continue  # 已找到有效的reviewer，跳过后续处理
                                    else:
                                        print(f"\n[DEBUG] 需求ID {requirement_id} 中没有有效的reviewer\n")
                                except Exception as e:
                                    print(f"\n[DEBUG] 通过需求ID {requirement_id} 获取reviewer失败: {str(e)}\n")

                            # 如果通过需求ID未能获取有效的reviewer，尝试通过TAPD URL获取
                            tapd_url = None
                            if hasattr(testcase, 'tapd_url') and testcase.tapd_url:
                                tapd_url = testcase.tapd_url
                            elif hasattr(message, 'tapd_url') and message.tapd_url:
                                tapd_url = message.tapd_url

                            if tapd_url:
                                try:
                                    # 获取需求信息
                                    requirement = await requirement_controller.get_by_tapd_url(tapd_url)
                                    if requirement and requirement.reviewer and requirement.reviewer != "--":
                                        # 使用需求的reviewer作为creator
                                        testcase.creator = requirement.reviewer
                                        print(f"\n[DEBUG] 从TAPD URL获取到需求的reviewer: {requirement.reviewer}\n")
                                    else:
                                        testcase.creator = "系统默认"
                                        print("\n[DEBUG] TAPD URL对应的需求中没有有效的reviewer，使用默认值\n")
                                except Exception as e:
                                    print(f"\n[DEBUG] 通过TAPD URL获取需求reviewer失败: {str(e)}\n")
                                    testcase.creator = "系统默认"
                            else:
                                testcase.creator = "系统默认"
                                print("\n[DEBUG] 没有TAPD URL，使用默认creator\n")
                    except Exception as e:
                        print(f"\n[DEBUG] 处理creator字段时出错: {str(e)}\n")
                        if not hasattr(testcase, 'creator') or not testcase.creator:
                            testcase.creator = "系统默认"

                    print(f"\n[DEBUG] 最终creator值: {testcase.creator}\n")
                    print(testcase)
                    await testcase_controller.create_TestCase(testcase)

                # 发送知识提取请求
                try:
                    # 发送状态消息
                    await self.publish_message(
                        ResponseMessage(source="数据库智能体", content="正在提取测试用例知识点..."),
                        topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
                    )

                    # 发送知识提取请求
                    await self.publish_message(
                        KnowledgeExtractionRequestMessage(
                            content=test_case_list.model_dump_json(),
                            project_id=test_case_list.testcases[0].project_id if test_case_list.testcases else 1,
                            source_type="testcase",
                            title="测试用例",
                            source_url=getattr(message, 'tapd_url', None),
                            source_reference_id=getattr(message, 'tapd_url', None)  # 使用TAPD URL作为引用ID
                        ),
                        topic_id=TopicId(knowledge_extraction_topic_type, source=self.id.key)
                    )
                except Exception as e:
                    print(f"发送知识提取请求失败: {str(e)}")

            except json.JSONDecodeError as json_error:
                # JSON 解析错误
                print(f"JSON 解析错误: {str(json_error)}")
                raise Exception(f"JSON 解析错误: {str(json_error)}")

            await self.publish_message(ResponseMessage(source="database",content=test_case_list.model_dump_json(),
                                                       is_final=False),
                                       topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            # 更新消息内容，包含去重信息
            success_message = f"测试用例入库完成，共生成【{len(test_case_list.testcases)}】条测试用例。"

            # 统计去重信息
            removed_count = 0
            if removed_testcases:
                removed_count += len(removed_testcases)
                success_message += f"\n已自动去除【{len(removed_testcases)}】条相似或重复的测试用例。"

            # 添加与数据库中已存在用例的去重信息
            if 'removed_existing' in locals() and removed_existing:
                removed_count += len(removed_existing)
                success_message += f"\n已自动去除【{len(removed_existing)}】条与数据库中已存在用例相似的测试用例。"

            if removed_count > 0:
                success_message += f"\n总计去除【{removed_count}】条重复或相似的测试用例。"

            await self.publish_message(ResponseMessage(source="数据库智能体",
                                                       content=success_message,
                                                       is_final=True),
                                       topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
        except Exception as e:
            await self.publish_message(ResponseMessage(source="数据库智能体",
                                                       content=f"测试用例入库失败：{str(e)}",
                                                       is_final=True),
                                       topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

async def start_runtime(requirement: RequirementMessage,
                        collect_result: Callable[[ClosureContext, ResponseMessage, MessageContext], Awaitable[None]],
                        user_input_func: Callable[[str, Optional[CancellationToken]],Awaitable[str]]):

    runtime = SingleThreadedAgentRuntime()
    await TestCaseGeneratorAgent.register(runtime, testcase_generator_topic_type, lambda: TestCaseGeneratorAgent(input_func=user_input_func))
    await TestCaseReviewAgent.register(runtime, testcase_review_topic_type,
                                       lambda: TestCaseReviewAgent())
    await TestCaseFinalizeAgent.register(runtime, testcase_finalize_topic_type, lambda: TestCaseFinalizeAgent())
    await TestCaseStructureAgent.register(runtime, testcase_structure_topic_type, lambda: TestCaseStructureAgent())
    await TestCaseDatabaseAgent.register(runtime, testcase_database_topic_type, lambda: TestCaseDatabaseAgent())

    # 注册知识提取智能体
    from .knowledge_agents import TestCaseKnowledgeAgent, knowledge_extraction_topic_type
    await TestCaseKnowledgeAgent.register(
        runtime,
        knowledge_extraction_topic_type,
        lambda: TestCaseKnowledgeAgent()
    )

    # 定义一个专门用来接收其它agent的消息的智能体，只需要定义一个接收消息的函数即可
    await ClosureAgent.register_closure(
        runtime,
        "closure_agent",
        collect_result,
        subscriptions=lambda: [TypeSubscription(topic_type=task_result_topic_type, agent_type="closure_agent")],
    )
    runtime.start()
    await runtime.publish_message(requirement,
                                  topic_id=DefaultTopicId(type=testcase_generator_topic_type))
    await runtime.stop_when_idle()
    await runtime.close()
