
"""
知识点相关性判断和检索模块

该模块提供了一系列函数，用于判断知识点与需求的相关性，
并根据相关性检索和分层知识点，为需求分析和测试用例生成提供背景知识。
"""

import re
import logging
import numpy as np
import time
import asyncio
import concurrent.futures
from typing import List, Dict, Any, Tuple, Optional, Set, Union
from tortoise.expressions import Q
from pydantic import BaseModel
from functools import lru_cache

from app.controllers.knowledge import knowledge_controller
from app.models.knowledge import KnowledgeItem, KnowledgeItemType, KnowledgeSource
from app.settings.llm_config import get_embedding_model, EMBEDDING_SIMILARITY_THRESHOLD

# 配置日志
logger = logging.getLogger(__name__)

# 相关性阈值 - 调整阈值以提高筛选质量
DIRECT_RELEVANCE_THRESHOLD = 6.0  # 直接相关阈值（原为7.0）
INDIRECT_RELEVANCE_THRESHOLD = 4.0  # 间接相关阈值（原为5.0）
BACKGROUND_RELEVANCE_THRESHOLD = 2.5  # 背景相关阈值（原为3.0）

# 知识点类型权重
TYPE_WEIGHTS = {
    "业务规则": 1.2,
    "核心功能": 1.1,
    "术语解释": 1.0,
    "非功能要求": 0.9,
    "测试要点": 0.8,
    "历史缺陷总结": 1.0,
    "环境配置": 0.7,
    "其他": 0.6
}

# 知识点来源权重
SOURCE_WEIGHTS = {
    "需求分析导入": 1.2,
    "手动添加": 1.1,
    "测试用例导入": 0.9,
    "缺陷导入": 1.0,
    "其他来源": 0.8
}


def extract_keywords(text: str) -> List[str]:
    """
    从文本中提取关键词

    参数:
        text: 需要提取关键词的文本

    返回:
        关键词列表
    """
    if not text:
        return []

    # 简单的关键词提取实现
    # 1. 移除标点符号
    text = re.sub(r'[^\w\s]', ' ', text)

    # 2. 分词
    words = text.split()

    # 3. 移除停用词（简化版）
    stopwords = {'的', '了', '和', '是', '在', '有', '与', '为', '以', '及', '或', '由', '上', '中', '下', '等'}
    words = [word for word in words if word.lower() not in stopwords and len(word) > 1]

    # 4. 提取2-3个词的短语
    phrases = []
    for i in range(len(words) - 1):
        phrases.append(words[i] + words[i+1])
        if i < len(words) - 2:
            phrases.append(words[i] + words[i+1] + words[i+2])

    # 5. 合并单词和短语，去重
    keywords = list(set(words + phrases))

    # 6. 限制关键词数量
    return keywords[:20]  # 最多返回20个关键词


# 嵌入向量缓存
_embedding_cache = {}
_cache_hits = 0
_cache_misses = 0
_cache_max_size = 1000  # 最大缓存条目数

def get_text_embedding(text: str) -> Optional[List[float]]:
    """
    获取文本的嵌入向量，使用LRU缓存提高性能

    参数:
        text: 需要嵌入的文本

    返回:
        嵌入向量，如果失败则返回None
    """
    global _embedding_cache, _cache_hits, _cache_misses

    if not text:
        return None

    # 对长文本进行截断，避免过长的文本
    if len(text) > 10000:
        text = text[:10000]

    # 计算文本的哈希值作为缓存键
    cache_key = hash(text)

    # 检查缓存
    if cache_key in _embedding_cache:
        _cache_hits += 1
        if _cache_hits % 10 == 0:  # 每10次命中记录一次日志
            logger.debug(f"嵌入缓存命中率: {_cache_hits/(_cache_hits+_cache_misses):.2%}, 命中: {_cache_hits}, 未命中: {_cache_misses}")
        return _embedding_cache[cache_key]

    _cache_misses += 1

    try:
        start_time = time.time()

        # 获取嵌入模型
        embed_model = get_embedding_model()
        if embed_model is None:
            logger.warning("嵌入模型初始化失败，无法获取文本嵌入")
            return None

        # 获取文本嵌入
        embedding = embed_model.encode(text)
        result = embedding.tolist()

        # 记录处理时间
        process_time = time.time() - start_time
        logger.debug(f"文本嵌入处理耗时: {process_time:.3f}秒, 文本长度: {len(text)}")

        # 更新缓存
        _embedding_cache[cache_key] = result

        # 如果缓存过大，删除最早的条目
        if len(_embedding_cache) > _cache_max_size:
            # 简单的LRU实现：删除第一个键
            oldest_key = next(iter(_embedding_cache))
            del _embedding_cache[oldest_key]
            logger.debug(f"嵌入缓存达到上限，删除最早的条目，当前缓存大小: {len(_embedding_cache)}")

        return result
    except Exception as e:
        logger.error(f"获取文本嵌入失败: {str(e)}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return None


def get_batch_embeddings(texts: List[str], batch_size: int = 32) -> Dict[str, List[float]]:
    """
    批量获取多个文本的嵌入向量，利用模型的批处理能力提高性能

    参数:
        texts: 需要嵌入的文本列表
        batch_size: 批处理大小，默认为32

    返回:
        文本到嵌入向量的映射字典，如果某个文本处理失败则不包含在结果中
    """
    global _embedding_cache, _cache_hits, _cache_misses

    if not texts:
        return {}

    # 预处理文本并检查缓存
    processed_texts = []
    cache_keys = []
    result_dict = {}
    texts_to_embed = []
    texts_to_embed_indices = []

    # 第一步：检查缓存并准备需要嵌入的文本
    for i, text in enumerate(texts):
        if not text:
            continue

        # 对长文本进行截断
        if len(text) > 10000:
            text = text[:10000]

        # 计算缓存键
        cache_key = hash(text)
        cache_keys.append(cache_key)
        processed_texts.append(text)

        # 检查缓存
        if cache_key in _embedding_cache:
            _cache_hits += 1
            result_dict[text] = _embedding_cache[cache_key]
        else:
            _cache_misses += 1
            texts_to_embed.append(text)
            texts_to_embed_indices.append(i)

    # 如果所有文本都在缓存中，直接返回
    if not texts_to_embed:
        if _cache_hits % 10 == 0 and _cache_hits > 0:  # 每10次命中记录一次日志
            logger.debug(f"嵌入缓存命中率: {_cache_hits/(_cache_hits+_cache_misses):.2%}, 命中: {_cache_hits}, 未命中: {_cache_misses}")
        return result_dict

    # 第二步：批量嵌入未缓存的文本
    try:
        start_time = time.time()

        # 获取嵌入模型
        embed_model = get_embedding_model()
        if embed_model is None:
            logger.warning("嵌入模型初始化失败，无法获取文本嵌入")
            return result_dict

        # 批量获取文本嵌入
        embeddings = embed_model.encode(texts_to_embed, batch_size=batch_size, show_progress_bar=False)

        # 记录处理时间
        process_time = time.time() - start_time
        logger.debug(f"批量嵌入处理耗时: {process_time:.3f}秒, 处理 {len(texts_to_embed)} 个文本")

        # 更新缓存和结果
        for i, text in enumerate(texts_to_embed):
            embedding = embeddings[i].tolist() if isinstance(embeddings, np.ndarray) else embeddings[i]
            cache_key = hash(text)
            _embedding_cache[cache_key] = embedding
            result_dict[text] = embedding

        # 如果缓存过大，删除最早的条目
        while len(_embedding_cache) > _cache_max_size:
            oldest_key = next(iter(_embedding_cache))
            del _embedding_cache[oldest_key]

    except Exception as e:
        logger.error(f"批量获取文本嵌入失败: {str(e)}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")

    return result_dict


def calculate_embedding_similarity(embedding1: List[float], embedding2: List[float]) -> float:
    """
    计算两个嵌入向量的余弦相似度

    参数:
        embedding1: 第一个嵌入向量
        embedding2: 第二个嵌入向量

    返回:
        余弦相似度 (0-1)
    """
    try:
        # 转换为numpy数组
        vec1 = np.array(embedding1)
        vec2 = np.array(embedding2)

        # 计算余弦相似度
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)

        # 避免除零错误
        if norm1 == 0 or norm2 == 0:
            return 0.0

        similarity = dot_product / (norm1 * norm2)

        # 确保结果在0-1范围内
        return max(0.0, min(1.0, similarity))
    except Exception as e:
        logger.error(f"计算嵌入相似度失败: {str(e)}")
        return 0.0


def calculate_keyword_relevance(requirement_text: str, knowledge_point: KnowledgeItem) -> float:
    """
    基于关键词计算知识点与需求的相关性

    参数:
        requirement_text: 需求文本
        knowledge_point: 知识点

    返回:
        相关性分数 (0-10)
    """
    # 提取需求关键词
    requirement_keywords = extract_keywords(requirement_text)

    # 初始化分数
    score = 0.0

    # 检查标题匹配
    for keyword in requirement_keywords:
        if keyword.lower() in knowledge_point.title.lower():
            score += 3.0  # 标题匹配权重高

    # 检查内容匹配
    content_score = 0.0
    for keyword in requirement_keywords:
        if keyword.lower() in knowledge_point.content.lower():
            content_score += 1.0  # 内容匹配权重中等
    # 内容匹配分数上限为5分
    score += min(content_score, 5.0)

    # 检查标签匹配 - 增加标签匹配权重
    if knowledge_point.tags:
        tag_score = 0.0
        # 分割标签
        tags = knowledge_point.tags.lower().split(',')
        
        for keyword in requirement_keywords:
            # 精确标签匹配给予更高权重
            if any(keyword.lower() == tag.strip() for tag in tags):
                tag_score += 3.0  # 精确匹配权重高
            # 部分匹配给予中等权重
            elif any(keyword.lower() in tag.strip() for tag in tags):
                tag_score += 1.5  # 部分匹配权重中等
        
        # 标签匹配分数上限为5分（提高上限）
        score += min(tag_score, 5.0)

    # 总分上限为10分
    return min(score, 10.0)


def calculate_business_relevance(requirement: Any, knowledge_point: KnowledgeItem) -> float:
    """
    基于业务规则计算知识点与需求的相关性

    参数:
        requirement: 需求对象
        knowledge_point: 知识点

    返回:
        相关性分数 (0-10)
    """
    score = 0.0

    # 模块匹配
    if hasattr(requirement, 'module') and requirement.module:
        if requirement.module.lower() in knowledge_point.title.lower() or \
           requirement.module.lower() in knowledge_point.content.lower():
            score += 5.0

    # 功能类型匹配
    if hasattr(requirement, 'category') and requirement.category:
        # 安全类需求与安全相关知识点
        if requirement.category == "安全" and knowledge_point.item_type == "非功能要求" and \
           ("安全" in knowledge_point.title.lower() or "安全" in knowledge_point.content.lower()):
            score += 4.0

        # 性能类需求与性能相关知识点
        elif requirement.category == "性能" and knowledge_point.item_type == "非功能要求" and \
             ("性能" in knowledge_point.title.lower() or "性能" in knowledge_point.content.lower()):
            score += 4.0

        # 功能类需求与功能相关知识点
        elif requirement.category == "功能" and knowledge_point.item_type == "核心功能":
            score += 3.0

    # 总分上限为10分
    return min(score, 10.0)


async def calculate_relevance(requirement: Any, knowledge_point: KnowledgeItem) -> float:
    """
    计算知识点与需求的综合相关性

    参数:
        requirement: 需求对象
        knowledge_point: 知识点

    返回:
        相关性分数 (0-10)
    """
    # 获取更全面的需求文本
    requirement_text = ""
    if hasattr(requirement, 'description'):
        requirement_text += requirement.description + " "
    if hasattr(requirement, 'content'):
        requirement_text += requirement.content + " "
    if hasattr(requirement, 'remark'):
        requirement_text += requirement.remark + " "

    # 如果所有字段都为空，尝试使用单个字段
    if not requirement_text.strip():
        if hasattr(requirement, 'remark'):
            requirement_text = requirement.remark
        elif hasattr(requirement, 'description'):
            requirement_text = requirement.description
        elif hasattr(requirement, 'content'):
            requirement_text = requirement.content

    # 1. 关键词相关性 (0-10分)
    keyword_score = calculate_keyword_relevance(requirement_text, knowledge_point)

    # 2. 业务规则相关性 (0-10分)
    business_score = calculate_business_relevance(requirement, knowledge_point)

    # 3. 向量相似度相关性 (0-10分)
    embedding_score = 0.0
    content_similarity = 0.0
    tag_similarity = 0.0

    try:
        # 获取需求文本的嵌入向量
        req_embedding = get_text_embedding(requirement_text)

        # 获取知识点内容的嵌入向量
        knowledge_content_embedding = get_text_embedding(knowledge_point.content)

        # 计算内容相似度（主要相似度）
        if req_embedding and knowledge_content_embedding:
            content_similarity = calculate_embedding_similarity(req_embedding, knowledge_content_embedding)
            logger.debug(f"知识点 '{knowledge_point.title}' 内容相似度: {content_similarity:.4f}")

        # 获取知识点标签的嵌入向量（如果有）
        if knowledge_point.tags:
            knowledge_tag_embedding = get_text_embedding(knowledge_point.tags)
            if req_embedding and knowledge_tag_embedding:
                tag_similarity = calculate_embedding_similarity(req_embedding, knowledge_tag_embedding)
                logger.debug(f"知识点 '{knowledge_point.title}' 标签相似度: {tag_similarity:.4f}")

        # 综合考虑内容和标签的相似度，内容权重更高
        if content_similarity > 0 or tag_similarity > 0:
            # 内容相似度权重0.7，标签相似度权重0.3
            similarity = content_similarity * 0.7 + tag_similarity * 0.3
            # 将相似度转换为0-10分
            embedding_score = similarity * 10.0
            logger.debug(f"知识点 '{knowledge_point.title}' 综合相似度: {similarity:.4f}, 分数: {embedding_score:.2f}")
    except Exception as e:
        logger.error(f"计算向量相似度失败: {str(e)}")

    # 4. 知识点元数据加权

    # 根据知识点类型加权
    type_value = knowledge_point.item_type
    if hasattr(type_value, 'value'):
        type_value = type_value.value
    type_multiplier = TYPE_WEIGHTS.get(type_value, 0.7)

    # 根据知识点来源加权
    source_value = knowledge_point.source
    if hasattr(source_value, 'value'):
        source_value = source_value.value
    source_multiplier = SOURCE_WEIGHTS.get(source_value, 0.8)

    # 根据知识点质量评分加权（如果有）
    quality_multiplier = 1.0
    if hasattr(knowledge_point, 'quality_score') and knowledge_point.quality_score is not None:
        quality_multiplier = 0.5 + (knowledge_point.quality_score / 10) * 0.5  # 0.5-1.0

    # 计算最终相关性分数 - 加入向量相似度
    # 使用加权平均而不是简单乘法，避免多个权重相乘导致分数过低
    if embedding_score > 0:
        # 如果有向量相似度，则三者加权平均，提高向量相似度的权重
        raw_score = (keyword_score * 0.3 + business_score * 0.2 + embedding_score * 0.5)
    else:
        # 如果没有向量相似度，则使用原来的权重
        raw_score = (keyword_score * 0.7 + business_score * 0.3)

    # 应用元数据权重
    final_score = raw_score * (type_multiplier * 0.4 + source_multiplier * 0.3 + quality_multiplier * 0.3 + 0.3)

    # 总分上限为10分
    return min(final_score, 10.0)


async def calculate_vector_similarities_concurrent(req_embedding: List[float], knowledge_points: List[KnowledgeItem],
                                         threshold: float = EMBEDDING_SIMILARITY_THRESHOLD,
                                         max_workers: int = 10) -> List[Tuple[KnowledgeItem, float]]:
    """
    并发计算向量相似度

    参数:
        req_embedding: 需求文本的嵌入向量
        knowledge_points: 知识点列表
        threshold: 相似度阈值
        max_workers: 最大工作线程数

    返回:
        知识点和相似度的元组列表，按相似度降序排序
    """
    if not req_embedding or not knowledge_points:
        return []

    # 准备知识点内容列表
    knowledge_contents = [point.content for point in knowledge_points]

    # 批量获取嵌入向量
    start_time = time.time()
    embeddings_dict = get_batch_embeddings(knowledge_contents, batch_size=32)

    # 使用线程池并发计算相似度
    results = []

    def calculate_similarity(point_idx):
        point = knowledge_points[point_idx]
        content = point.content
        if content in embeddings_dict:
            point_embedding = embeddings_dict[content]
            similarity = calculate_embedding_similarity(req_embedding, point_embedding)
            if similarity >= threshold:
                return (point, similarity)
        return None

    # 使用线程池并发处理
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = [executor.submit(calculate_similarity, i) for i in range(len(knowledge_points))]
        for future in concurrent.futures.as_completed(futures):
            result = future.result()
            if result:
                results.append(result)

    # 按相似度排序
    results.sort(key=lambda x: x[1], reverse=True)

    process_time = time.time() - start_time
    logger.debug(f"并发向量相似度计算耗时: {process_time:.3f}秒, 处理 {len(knowledge_points)} 个知识点, 找到 {len(results)} 个相关知识点")

    return results


async def get_relevant_knowledge_points(project_id: int = None, requirement: Any = None,
                                        max_direct: int = 10,  # 从8增加到10
                                        max_indirect: int = 8,  # 从5增加到8
                                        max_background: int = 5,  # 从3增加到5
                                        cross_project: bool = False) -> Dict[str, List[KnowledgeItem]]:
    """
    获取与需求相关的分层知识点

    参数:
        project_id: 项目ID
        requirement: 需求对象
        max_direct: 最大直接相关知识点数量
        max_indirect: 最大间接相关知识点数量
        max_background: 最大背景相关知识点数量
        cross_project: 是否跨项目检索

    返回:
        分层知识点字典 {"direct": [...], "indirect": [...], "background": [...]}
    """
    try:
        # 获取更全面的需求文本
        logger.info(f"获取更全面的需求文本:  {requirement}")
        requirement_text = ""
        if hasattr(requirement, 'description'):
            requirement_text += requirement.description + " "
        if hasattr(requirement, 'content'):
            requirement_text += requirement.content + " "
        if hasattr(requirement, 'remark'):
            requirement_text += requirement.remark + " "

        # 如果所有字段都为空，尝试使用单个字段
        if not requirement_text.strip():
            if hasattr(requirement, 'remark'):
                requirement_text = requirement.remark
            elif hasattr(requirement, 'description'):
                requirement_text = requirement.description
            elif hasattr(requirement, 'content'):
                requirement_text = requirement.content

        if not requirement_text:
            logger.warning("需求文本为空，无法获取相关知识点")
            return {"direct": [], "indirect": [], "background": []}
        logger.info(f"开始获取需求 {requirement.id} 的相关知识点")
        logger.info(f"需求文本: {requirement_text}")
        # 1. 提取关键词
        keywords = extract_keywords(requirement_text)
        logger.info(f"从需求中提取的关键词: {keywords}")

        if not keywords:
            logger.warning("未能从需求中提取到关键词")
            return {"direct": [], "indirect": [], "background": []}

        # 2. 初步筛选 - 基于关键词和向量相似度
        if project_id is not None and not cross_project:
            query = Q(project_id=project_id)
        else:
            query = Q()  # 不筛选项目ID，检索所有项目的知识点

        # 2.1 关键词筛选
        keyword_conditions = Q()
        for keyword in keywords:
            keyword_conditions |= (
                Q(title__icontains=keyword) |
                Q(content__icontains=keyword) |
                Q(tags__icontains=keyword)
            )
        query &= keyword_conditions

        # 获取候选知识点（增加数量以提高召回率）
        keyword_candidates = await knowledge_controller.model.filter(query).limit(60)  # 从30增加到60
        logger.info(f"关键词初步筛选出 {len(keyword_candidates)} 条候选知识点")

        # 2.2 向量相似度筛选 - 使用并发处理
        vector_candidates = []
        try:
            # 获取需求文本的嵌入向量
            req_embedding = get_text_embedding(requirement_text)
            if req_embedding:
                # 获取所有知识点
                if project_id is not None and not cross_project:
                    all_knowledge = await knowledge_controller.model.filter(project_id=project_id).limit(100)
                else:
                    all_knowledge = await knowledge_controller.model.all().limit(100)

                # 使用并发计算向量相似度
                vector_scores = await calculate_vector_similarities_concurrent(
                    req_embedding,
                    all_knowledge,
                    threshold=EMBEDDING_SIMILARITY_THRESHOLD,
                    max_workers=10
                )

                # 取前30个相似度最高的知识点
                vector_candidates = [point for point, _ in vector_scores[:30]]
                logger.info(f"`向量相似度筛选出` {len(vector_candidates)} 条候选知识点")
        except Exception as e:
            logger.error(f"向量相似度筛选失败: {str(e)}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")

        # 2.3 合并两种方式筛选出的候选知识点
        candidate_ids = set(point.id for point in keyword_candidates)
        for point in vector_candidates:
            if point.id not in candidate_ids:
                keyword_candidates.append(point)
                candidate_ids.add(point.id)

        candidate_points = keyword_candidates
        logger.info(f"合并后共有 {len(candidate_points)} 条候选知识点")

        # 如果候选知识点太少，放宽条件
        if len(candidate_points) < 15:  # 从10增加到15
            logger.info("候选知识点数量不足，放宽筛选条件")
            additional_points = await knowledge_controller.model.filter(
                Q(project_id=project_id)
            ).limit(60)  # 从30增加到60

            # 合并额外的知识点
            for point in additional_points:
                if point.id not in candidate_ids:
                    candidate_points.append(point)
                    candidate_ids.add(point.id)

            logger.info(f"放宽条件后共有 {len(candidate_points)} 条候选知识点")

        # 3. 计算相关性分数 - 使用并发处理
        scored_points = []

        # 创建任务列表
        tasks = []
        for point in candidate_points:
            tasks.append(calculate_relevance(requirement, point))

        # 并发执行所有任务
        relevance_scores = await asyncio.gather(*tasks)

        # 使用字典而不是列表，以便通过point.id快速查找
        point_id_core = {}
        # 组合结果
        for i, point in enumerate(candidate_points):
            relevance_score = relevance_scores[i]
            scored_points.append((point, relevance_score))
            logger.debug(f"知识点 '{point.title}' 相关性分数: {relevance_score}")
            # 将point.id作为键，relevance_score作为值存储在字典中
            point_id_core[point.id] = relevance_score

        # 4. 排序
        scored_points.sort(key=lambda x: x[1], reverse=True)

        # 5. 分层
        direct_relevant = []  # 直接相关
        indirect_relevant = []  # 间接相关
        background_relevant = []  # 背景相关

        # 记录分数分布情况
        score_distribution = {
            "high": 0,  # 分数 >= 7.0
            "medium": 0,  # 分数 5.0-7.0
            "low": 0,  # 分数 3.0-5.0
            "very_low": 0  # 分数 < 3.0
        }

        for point, score in scored_points:
            # 更新分数分布统计
            if score >= 7.0:
                score_distribution["high"] += 1
            elif score >= 5.0:
                score_distribution["medium"] += 1
            elif score >= 3.0:
                score_distribution["low"] += 1
            else:
                score_distribution["very_low"] += 1

            # 根据新阈值分层
            if score >= DIRECT_RELEVANCE_THRESHOLD:
                direct_relevant.append(point)
            elif score >= INDIRECT_RELEVANCE_THRESHOLD:
                indirect_relevant.append(point)
            elif score >= BACKGROUND_RELEVANCE_THRESHOLD:
                background_relevant.append(point)

        logger.info(f"分层结果: 直接相关 {len(direct_relevant)} 条, 间接相关 {len(indirect_relevant)} 条, 背景相关 {len(background_relevant)} 条")
        logger.info(f"分数分布: 高分(>=7.0): {score_distribution['high']}条, 中分(5.0-7.0): {score_distribution['medium']}条, " +
                   f"低分(3.0-5.0): {score_distribution['low']}条, 很低分(<3.0): {score_distribution['very_low']}条")

        # 6. 控制每层数量
        direct_relevant = direct_relevant[:max_direct]
        indirect_relevant = indirect_relevant[:max_indirect]
        background_relevant = background_relevant[:max_background]

        # 7. 记录详细的知识点信息
        all_knowledge_points = []
        req_relation_points = []
        # 记录直接相关知识点
        for i, point in enumerate(direct_relevant, 1):
            all_knowledge_points.append({
                "level": "直接相关",
                "index": i,
                "title": point.title,
                "type": point.item_type.value if hasattr(point.item_type, 'value') else point.item_type,
                "source": point.source.value if hasattr(point.source, 'value') else point.source,
                "content_preview": point.content[:100] + "..." if len(point.content) > 100 else point.content,
                "tags": point.tags
            })
            requirement_score_point = point_id_core.get(point.id, 5.0)  # 默认值为5.0
            req_relation_points.append({
                "relevance_level": "直接相关",
                "knowledge_id":point.id,
                "requirement_id":requirement.id,
                "relevance_score":requirement_score_point,
                "source":point.source.value if hasattr(point.source, 'value') else point.source,
            })

        # 记录间接相关知识点
        for i, point in enumerate(indirect_relevant, 1):
            all_knowledge_points.append({
                "level": "间接相关",
                "index": i,
                "title": point.title,
                "type": point.item_type.value if hasattr(point.item_type, 'value') else point.item_type,
                "source": point.source.value if hasattr(point.source, 'value') else point.source,
                "content_preview": point.content[:100] + "..." if len(point.content) > 100 else point.content,
                "tags": point.tags
            })
            requirement_score_point = point_id_core.get(point.id, 4.0)  # 默认值为4.0
            req_relation_points.append({
                "relevance_level": "间接相关",
                "knowledge_id":point.id,
                "requirement_id":requirement.id,
                "relevance_score":requirement_score_point,
                "source":point.source.value if hasattr(point.source, 'value') else point.source,
            })

        # 记录背景相关知识点
        for i, point in enumerate(background_relevant, 1):
            all_knowledge_points.append({
                "level": "背景相关",
                "index": i,
                "title": point.title,
                "type": point.item_type.value if hasattr(point.item_type, 'value') else point.item_type,
                "source": point.source.value if hasattr(point.source, 'value') else point.source,
                "content_preview": point.content[:100] + "..." if len(point.content) > 100 else point.content,
                "tags": point.tags
            })
            requirement_score_point = point_id_core.get(point.id, 3.0)  # 默认值为3.0
            req_relation_points.append({
                "relevance_level": "背景相关",
                "knowledge_id":point.id,
                "requirement_id":requirement.id,
                "relevance_score":requirement_score_point,
                "source":point.source.value if hasattr(point.source, 'value') else point.source,
            })

        # 将所有知识点信息记录到日志中
        if all_knowledge_points:
            logger.info(f"相似性检索获取到的所有知识点详情 ({len(all_knowledge_points)}条):")
            for point_info in all_knowledge_points:
                logger.info(f"[{point_info['level']}] {point_info['index']}. [{point_info['type']}] {point_info['title']} - 来源: {point_info['source']}")
                logger.info(f"   内容: {point_info['content_preview']}")
                if point_info['tags']:
                    logger.info(f"   标签: {point_info['tags']}")
                logger.info("---")
        else:
            logger.info("相似性检索未获取到任何知识点")

        # 8. 返回分层结果
        result = {
            "direct": direct_relevant,
            "indirect": indirect_relevant,
            "background": background_relevant,
            "all_knowledge_points": all_knowledge_points,  # 添加所有知识点的详细信息
            "req_relation_points": req_relation_points,  # 添加需求与知识点的关联信息
        }
        return result

    except Exception as e:
        logger.error(f"获取相关知识点时出错: {str(e)}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return {"direct": [], "indirect": [], "background": []}


def format_knowledge_points(knowledge_points: Dict[str, List[KnowledgeItem]]) -> str:
    """
    将知识点格式化为背景信息文本

    参数:
        knowledge_points: 分层知识点字典

    返回:
        格式化后的背景信息文本
    """
    if not knowledge_points or (not knowledge_points["direct"] and
                               not knowledge_points["indirect"] and
                               not knowledge_points["background"]):
        return ""

    result = "以下是与本需求相关的项目知识点，请在分析时参考：\n\n"

    # 直接相关知识点
    if knowledge_points["direct"]:
        result += "## 核心相关知识点\n\n"
        for i, point in enumerate(knowledge_points["direct"], 1):
            type_value = point.item_type
            if hasattr(type_value, 'value'):
                type_value = type_value.value

            result += f"{i}. [{type_value}] {point.title}\n"
            # 截取内容前200个字符
            content_preview = point.content[:200]
            if len(point.content) > 200:
                content_preview += "..."
            result += f"   {content_preview}\n\n"

    # 间接相关知识点
    if knowledge_points["indirect"]:
        result += "## 相关背景知识\n\n"
        for i, point in enumerate(knowledge_points["indirect"], 1):
            type_value = point.item_type
            if hasattr(type_value, 'value'):
                type_value = type_value.value

            result += f"{i}. [{type_value}] {point.title}\n"
            # 截取内容前150个字符
            content_preview = point.content[:150]
            if len(point.content) > 150:
                content_preview += "..."
            result += f"   {content_preview}\n\n"

    # 背景相关知识点
    if knowledge_points["background"]:
        result += "## 通用项目知识\n\n"
        for i, point in enumerate(knowledge_points["background"], 1):
            type_value = point.item_type
            if hasattr(type_value, 'value'):
                type_value = type_value.value

            result += f"{i}. [{type_value}] {point.title}\n"
            # 只显示标题，不显示内容
            if point.tags:
                tags_value = point.tags
                result += f"   标签: {tags_value}\n\n"
            else:
                result += "\n"

    return result
