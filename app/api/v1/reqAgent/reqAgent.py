from fastapi import FastAPI, APIRouter, WebSocket, Depends, HTTPException
import asyncio
import os
import traceback
import json

# 导入AutoGen团队分析模块
from .autogen_team import stream_analyze_with_autogen

from fastapi.middleware.cors import CORSMiddleware

app = FastAPI()
router = APIRouter()



# 使用AutoGen团队进行需求分析
async def analyze_requirements(requirements: str):
    """使用AutoGen团队进行需求分析过程"""

    # 使用AutoGen团队进行流式分析
    async for result in stream_analyze_with_autogen(requirements):
        yield result

# WebSocket 路由
@router.websocket("/ws/analyze")
async def websocket_analyze(websocket: WebSocket):
    await websocket.accept()
    try:
        data = await websocket.receive_json()
        print("Received data:", data)

        # 发送智能体执行输出 - 初始化
        await websocket.send_json({
            "source": "system",
            "content": "智能体初始化中...",
            "type": "system"
        })

        # 从请求中提取需求内容
        content = data.get("content", "")

        # 检查是否有TAPD需求
        tapd_requirements = []
        if "urls" in data and isinstance(data["urls"], list):
            tapd_requirements = [url for url in data["urls"] if url.get("url", "").startswith("https://www.tapd.cn")]
            print(f"Found {len(tapd_requirements)} TAPD requirements")

            # 发送智能体执行输出 - TAPD需求检测
            await websocket.send_json({
                "source": "agent",
                "content": f"检测到 {len(tapd_requirements)} 个TAPD需求",
                "type": "normal"
            })

        # 准备完整的需求文本，包含用户输入和TAPD需求
        full_requirements = content

        # 如果有TAPD需求，将其内容添加到分析中
        if tapd_requirements:
            # 添加TAPD需求标题
            full_requirements += "\n\n## TAPD需求列表\n"

            # 将TAPD需求内容添加到输入中
            for i, req in enumerate(tapd_requirements):
                if req.get("content"):
                    # 添加需求标题和内容
                    full_requirements += f"\n### TAPD需求 {i+1}: {req.get('title', '无标题')}\n"

                    # 添加需求元数据
                    if req.get("handler"):
                        full_requirements += f"- 处理人: {req.get('handler')}\n"
                    if req.get("developer"):
                        full_requirements += f"- 开发人员: {req.get('developer')}\n"
                    if req.get("tester"):
                        full_requirements += f"- 测试人员: {req.get('tester')}\n"

                    # 添加需求内容
                    full_requirements += f"\n需求内容:\n{req.get('content')}\n"

        # 检查是否有需求内容
        if not full_requirements.strip() and not tapd_requirements:
            await websocket.send_json({
                "source": "error",
                "content": "未提供需求内容，请输入需求或上传需求文件",
                "type": "error",
                "is_final": True
            })
            return

        # 如果需求描述为空但有TAPD需求，添加默认描述
        if not full_requirements.strip() and tapd_requirements:
            full_requirements = "请分析以下TAPD需求，生成需求分析报告。"

        # 开始分析
        await websocket.send_json({
            "source": "system",
            "content": "正在启动AutoGen需求分析团队...",
            "type": "system"
        })

        # 如果有TAPD需求，发送一个额外的消息
        if tapd_requirements:
            await websocket.send_json({
                "source": "agent",
                "content": f"检测到 {len(tapd_requirements)} 个TAPD需求，正在整合分析...",
                "type": "normal"
            })

        # 使用AutoGen团队分析需求
        async for output in analyze_requirements(full_requirements):
            if output and isinstance(output, str):
                if output.endswith("END"):
                    output = output[:-3]  # 去掉结束标记
                    # 发送最终结果
                    await websocket.send_json({
                        "source": "agent",
                        "content": output,
                        "is_final": True
                    })

                    # 发送智能体执行输出 - 完成
                    await websocket.send_json({
                        "source": "system",
                        "content": "需求分析完成",
                        "type": "system",
                        "is_final": True
                    })
                else:
                    # 发送中间结果
                    await websocket.send_json({
                        "source": "agent",
                        "content": output
                    })
    except Exception as e:
        # 记录详细错误信息
        error_traceback = traceback.format_exc()
        print("Error in websocket_analyze:", str(e))
        print("Traceback:", error_traceback)

        # 检查是否是序列化错误
        if "could not be serialized" in str(e) or "JSON" in str(e):
            # 发送序列化错误消息
            await websocket.send_json({
                "source": "error",
                "content": "分析结果包含无法序列化的内容。正在尝试修复...",
                "type": "error"
            })

            try:
                # 尝试发送一个安全的错误消息
                await websocket.send_json({
                    "source": "agent",
                    "content": "需求分析完成，但结果包含无法显示的内容。请查看服务器日志获取详细信息。",
                    "is_final": True
                })
            except Exception as inner_e:
                print("Failed to send safe error message:", str(inner_e))
        else:
            # 发送一般错误消息
            await websocket.send_json({
                "source": "error",
                "content": f"分析过程中出现错误: {str(e)}",
                "type": "error"
            })
    finally:
        await websocket.close()

# REST API 路由
@router.post("/analyze")
async def analyze_post(requirements: str, tapd_requirements: list = []):
    """通过 POST 请求分析需求"""
    # 如果需求描述为空但有TAPD需求，添加默认描述
    if not requirements and not tapd_requirements:
        raise HTTPException(status_code=400, detail={"error": "No requirements provided."})

    # 如果需求描述为空但有TAPD需求，添加默认描述
    if not requirements and tapd_requirements:
        requirements = "请分析以下TAPD需求，生成需求分析报告。"

    # 使用AutoGen团队分析需求
    result = ""
    try:
        # 收集所有输出，只返回最后一个结果
        async for output in analyze_requirements(requirements):
            if output and isinstance(output, str):
                result = output

        return {"result": result}
    except Exception as e:
        # 记录详细错误信息
        error_traceback = traceback.format_exc()
        print("Error in analyze_post:", str(e))
        print("Traceback:", error_traceback)

        # 检查是否是序列化错误
        if "could not be serialized" in str(e) or "JSON" in str(e):
            # 序列化错误，返回安全的错误消息
            return {
                "result": "需求分析完成，但结果包含无法显示的内容。请查看服务器日志获取详细信息。",
                "error": "序列化错误"
            }
        else:
            # 一般错误
            raise HTTPException(
                status_code=500,
                detail={"error": f"分析需求时出错: {str(e)}"}
            )