def process_tapd_content(html_content: str) -> str:
    """专门处理TAPD内容的特殊格式，并保存Base64编码的图片到本地"""
    soup = BeautifulSoup(html_content, 'html.parser')

    # 处理TAPD特有的表格格式
    for table in soup.find_all('table', class_='tapd-table'):
        # 确保表格有正确的Markdown格式
        for row in table.find_all('tr'):
            cells = row.find_all(['td', 'th'])
            for cell in cells:
                # 移除单元格中的多余换行
                cell.string = ' '.join(cell.stripped_strings)

    # 处理TAPD特有的代码块
    for pre in soup.find_all('pre'):
        if 'class' in pre.attrs:
            # 添加语言标识
            language = pre['class'][0] if pre['class'] else ''
            pre['class'] = f'language-{language}'
    
    # 处理并保存Base64编码的图片
    output_dir = "requirement_images"  # 保存图片的目录
    os.makedirs(output_dir, exist_ok=True)  # 确保目录存在
    
    # 查找所有需求文档图片
    req_images = soup.find_all('img', alt="需求文档图片")
    logger.info(f"在TAPD内容中找到 {len(req_images)} 张需求文档图片")
    
    # 处理每张图片
    for i, img in enumerate(req_images):
        src = img.get('src', '')
        
        # 只处理Base64编码的图片
        if src.startswith('data:image'):
            # 生成文件名前缀
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            prefix = f"req_image_{i+1}_{timestamp}"
            
            # 保存图片
            filepath = save_base64_image(src, output_dir, prefix)
            if filepath:
                # 将src属性替换为本地文件路径
                img['src'] = filepath
                # 添加原始Base64数据的引用，以便后续处理
                img['data-original-src'] = src
                logger.info(f"保存并替换需求文档图片 {i+1}/{len(req_images)}: {filepath}")
    
    # logger.info(f"{str(soup)}--------------处理后的HTML内容")
    return str(soup)
