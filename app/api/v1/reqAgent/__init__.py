from fastapi import APIRouter

from .reqAgent import router
from .tapdAgent import router as tapd_router
from .cors_handler import router as cors_router

reqAgent_router = APIRouter()
# 首先包含CORS处理路由，确保它首先处理请求
reqAgent_router.include_router(cors_router)
reqAgent_router.include_router(router, tags=["需求分析模块"])
reqAgent_router.include_router(tapd_router, prefix="/tapd", tags=["需求TAPD解析"])

__all__ = ["reqAgent_router"]
