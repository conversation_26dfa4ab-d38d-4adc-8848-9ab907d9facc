# 图片解析功能

本模块实现了将HTML转换为Markdown时，自动使用大模型解析图片内容的功能。

## 功能特点

1. 自动检测HTML中的图片标签
2. 将图片发送给大模型进行分析
3. 将分析结果整合到Markdown中
4. 支持URL图片和Base64编码图片
5. 可通过环境变量控制是否启用图片分析

## 使用方法

### 基本用法

```python
from app.api.v1.reqAgent.image_markdown_converter import html_to_markdown_with_image_analysis

# HTML内容
html_content = """
<div>
    <h1>测试文档</h1>
    <p>这是一个包含图片的测试文档</p>
    <img src="https://example.com/image.jpg" alt="示例图片">
    <p>这是图片下方的文字</p>
</div>
"""

# 转换为Markdown并分析图片
markdown_result = html_to_markdown_with_image_analysis(html_content)
print(markdown_result)
```

### 配置选项

可以通过环境变量或函数参数配置图片解析功能：

```python
import os
from app.api.v1.reqAgent.image_markdown_converter import html_to_markdown_with_image_analysis

# 通过环境变量配置
os.environ["ENABLE_IMAGE_ANALYSIS"] = "true"  # 启用图片分析
os.environ["LLM_API_KEY"] = "your-api-key"    # 设置API密钥
os.environ["LLM_API_BASE"] = "https://api.openai.com/v1"  # 设置API基础URL
os.environ["DEFAULT_LLM_MODEL"] = "gpt-4-vision-preview"  # 设置模型名称

# 或者通过函数参数配置
markdown_result = html_to_markdown_with_image_analysis(
    html_content,
    image_analysis_enabled=True,
    api_key="your-api-key",
    api_base="https://api.openai.com/v1",
    model="gpt-4-vision-preview"
)
```

### 在tapdAgent中的使用

`tapdAgent.py`中的`html_to_markdown`函数已经集成了图片解析功能，可以通过环境变量`ENABLE_IMAGE_ANALYSIS`控制是否启用：

```python
# 启用图片解析
os.environ["ENABLE_IMAGE_ANALYSIS"] = "true"

# 禁用图片解析
os.environ["ENABLE_IMAGE_ANALYSIS"] = "false"
```

## 输出示例

输入HTML:
```html
<img src="example.jpg" alt="一张猫的图片">
```

输出Markdown:
```markdown
![一张猫的图片](example.jpg)

<!-- 图片分析 -->
这是一张橙色虎斑猫的照片。猫咪正面对着镜头，有着明亮的绿色眼睛和特征性的橙色和棕色条纹毛发。猫咪看起来很警觉，耳朵竖起，表情显得很专注。背景是模糊的，使得猫咪成为照片的焦点。
```

## 注意事项

1. 需要有效的大模型API密钥
2. 图片解析会增加处理时间和API调用成本
3. 大型图片可能需要更长的处理时间
4. 建议在生产环境中设置适当的超时时间
