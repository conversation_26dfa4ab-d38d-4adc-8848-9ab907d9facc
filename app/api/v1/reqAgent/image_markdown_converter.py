"""
自定义Markdown转换器，支持将图片发送给大模型进行解析
"""
import os
import base64
import logging
import requests
import shutil

from io import BytesIO
from typing import Optional, Dict, Any, Union
from urllib.parse import urlparse
from markdownify import MarkdownConverter
from bs4 import BeautifulSoup
from datetime import datetime


# 配置日志
logger = logging.getLogger("image_markdown_converter")

# 默认API配置 - 使用阿里云通义千问视觉模型
DEFAULT_MODEL = os.environ.get("QWEN_VL_MODEL", "qwen-vl-plus-latest")
DEFAULT_API_BASE = os.environ.get("QWEN_VL_API_BASE", "https://dashscope.aliyuncs.com/compatible-mode/v1")
DEFAULT_API_KEY = os.environ.get("QWEN_VL_API_KEY", "sk-85477c3eb0424bb89d5421d2b28d2051")

class ImageMarkdownConverter(MarkdownConverter):
    """扩展MarkdownConverter，支持将图片发送给大模型进行解析"""

    def __init__(self, **options):
        super().__init__(**options)
        self.api_key = options.get("api_key", DEFAULT_API_KEY)
        self.api_base = options.get("api_base", DEFAULT_API_BASE)
        self.model = options.get("model", DEFAULT_MODEL)
        self.image_analysis_enabled = options.get("image_analysis_enabled", True)
        logger.info(f"ImageMarkdownConverter初始化完成，api_key={self.api_key}, api_base={self.api_base}, model={self.model}")

    def convert_img(self, el, text, parent_tags):
        """重写图片转换方法，添加大模型解析功能"""
        # logger.info(f"开始转换图片: {el}")

        # 获取图片属性
        alt = el.attrs.get('alt', None) or ''
        src = el.attrs.get('src', None) or ''
        title = el.attrs.get('title', None) or ''
        title_part = ' "%s"' % title.replace('"', r'\"') if title else ''

        # 获取其他可能的图片URL属性
        original_src = el.attrs.get('original_src', None) or ''
        href = el.attrs.get('href', None) or ''

        # 检查是否是需求文档中的实际图片
        is_requirement_image = ('file.tapd.cn' in src or 'file.tapd.cn' in original_src or
                              (src.startswith('data:image') and '需求' in alt) or
                              '需求文档图片' in alt)

        # 如果不是需求文档中的实际图片，直接返回空字符串
        if not is_requirement_image:
            return ''

        logger.info(f"图片属性 - alt: {alt}, src: {src[:50]}..., original_src: {original_src[:50]}..., href: {href[:50]}...")

        # 检查是否是Base64编码的图片
        # is_image_base64 = src.startswith('data:image')
        # is_html_base64 = src.startswith('data:text/html;base64')

        # 如果是图片Base64编码
        # if is_image_base64:
        #     logger.warning(f"发现图片Base64编码，使用src属性")
        #     # logger.info(f"使用src属性作为图片源: {src}")

        # # 如果是HTML Base64编码
        # elif is_html_base64:
        #     logger.warning(f"发现HTML Base64编码，尝试提取其中的图片")

        #     # 尝试解析HTML内容
        #     try:
        #         # 提取Base64部分
        #         base64_html = src.split(',', 1)[1] if ',' in src else src
        #         # 解码HTML内容
        #         html_content = base64.b64decode(base64_html).decode('utf-8', errors='ignore')

        #         # 使用BeautifulSoup解析HTML
        #         soup = BeautifulSoup(html_content, 'html.parser')
        #         # 尝试找到图片元素
        #         img_tags = soup.find_all('img')
        #         logger.info(f"在HTML内容中找到 {len(img_tags)} 个图片元素")

        #         if img_tags:
        #             # 使用第一个图片的src属性
        #             img_src = img_tags[0].get('src')
        #             if img_src:
        #                 logger.info(f"从嵌套HTML中提取图片URL: {img_src}")
        #                 src = img_src
        #     except Exception as e:
        #         logger.error(f"解析HTML内容失败: {str(e)}")

            # 如果没有找到图片，尝试将HTML Base64转换为图片Base64
            # if src.startswith('data:text/html;base64'):
            #     logger.warning(f"在HTML内容中没有找到图片，尝试将HTML Base64转换为图片Base64")
            #     src = src.replace('data:text/html;base64', 'data:image/png;base64')
            #     logger.info(f"将HTML Base64转换为图片Base64: {src[:50]}...")

        # 如果是内联图片，但我们仍然希望处理它
        # if ('_inline' in parent_tags and el.parent.name not in self.options['keep_inline_images_in']):
        #     logger.info(f"图片被标记为内联，但仍然尝试解析")

        #     # 如果有original_src或href属性，使用它们作为图片源
        #     if original_src and (original_src.endswith('.png') or original_src.endswith('.jpg') or
        #                         original_src.endswith('.jpeg') or original_src.endswith('.gif')):
        #         logger.info(f"使用original_src属性作为图片源: {original_src}")
        #         src = original_src
        #     elif href and (href.endswith('.png') or href.endswith('.jpg') or
        #                   href.endswith('.jpeg') or href.endswith('.gif')):
        #         logger.info(f"使用href属性作为图片源: {href}")
        #         src = href

            # 如果没有有效的图片源，返回alt文本
            # if not src or src.startswith('data:text/html;base64'):
            #     logger.warning(f"无法获取有效的图片源，返回alt文本: {alt}")
            #     return alt

        # 标准的Markdown图片语法
        markdown_image = f'![{alt}]({src}{title_part})'

        # 增强日志记录，显示更多图片信息
        # is_base64 = src.startswith('data:')
        # logger.info(f"正在解析图片: {src[:50]}..., 是否Base64: {is_base64}")

        # 如果是Base64编码，记录更多信息
        # if is_base64:
        #     mime_type = src.split(';')[0] if ';' in src else 'unknown'
        #     logger.info(f"Base64图片MIME类型: {mime_type}, 长度: {len(src)}")
        # 如果启用了图片分析，则调用大模型API
        if self.image_analysis_enabled:
            try:
                # 直接使用图片路径，不需要处理Base64编码

                # 尝试分析图片
                image_analysis = self.analyze_image(src, alt)
                if image_analysis:
                    logger.info(f"图片分析成功，分析结果长度: {len(image_analysis)}")
                    # 检查是否是错误消息
                    if "无法获取有效的图片内容" in image_analysis or "图片分析失败" in image_analysis:
                        logger.warning(f"图片分析返回错误消息，使用模拟分析结果")
                        # 使用模拟的分析结果
                        mock_analysis = self._get_mock_analysis(alt)
                        return f'{markdown_image}\n\n<!-- 图片分析(模拟) -->\n{mock_analysis}\n'
                    else:
                        # 将图片分析结果添加到Markdown中
                        return f'{markdown_image}\n\n<!-- 图片分析 -->\n{image_analysis}\n'
                else:
                    logger.warning(f"图片分析失败，使用模拟分析结果")
                    # 如果分析失败，使用模拟的分析结果
                    mock_analysis = self._get_mock_analysis(alt)
                    return f'{markdown_image}\n\n<!-- 图片分析(模拟) -->\n{mock_analysis}\n'
            except Exception as e:
                logger.error(f"图片分析失败: {str(e)}")
                # 异常情况下使用模拟的分析结果
                mock_analysis = self._get_mock_analysis(alt)
                return f'{markdown_image}\n\n<!-- 图片分析(模拟) -->\n{mock_analysis}\n'

        return markdown_image

    def analyze_image(self, image_src: str, alt_text: str = "") -> Optional[str]:
        """
        使用大模型API分析图片内容

        Args:
            image_src: 图片URL或本地文件路径
            alt_text: 图片的alt文本，用于提供上下文

        Returns:
            str: 大模型分析结果，如果分析失败则返回None
        """
        logger.info(f"开始分析图片: {image_src}")

        # 准备提示词
        prompt = """
            你是一位经验丰富的需求分析助手。你的任务是详细描述所提供的需求文档中的图片，以便高级测试工程师能够理解其功能、界面元素、数据和潜在用户交互。请专注于对测试用例设计至关重要的信息。

            请针对以下图片进行描述，并遵循以下指导原则：

                1.识别图片类型： 首先说明这是什么类型的图片（例如：UI 界面截图、线框图、流程图、状态图、数据模型图、草图等）。
                2.描述核心目的/功能： 简要说明该图片在需求文档中要传达的核心目的或展示的主要功能点是什么。
                3.详述关键 UI 元素：
                    ·列出所有可见的、可交互的 UI 元素（如：按钮、输入框、下拉列表、复选框、单选按钮、表格、链接、图标、标签等）。
                    ·描述每个元素的状态（如：‘保存’按钮处于启用状态，‘用户名’输入框为必填项并标有星号，‘选项A’单选按钮被选中）。
                    ·指出元素的标签或显示的文本内容。
                    ·需描述清楚元素间的关联关系
                    ·如果元素是可点击的，请描述可能的操作（如：点击‘保存’按钮将提交表单，点击‘选项A’将选中该选项）。
                    ·如果元素是可输入的，请描述输入规则（如：‘用户名’字段应包含字母、数字和下划线，且长度在 3-20 个字符之间）。
                    ·如果元素是可选择的，请描述选择规则（如：‘性别’字段应选择‘男’或‘女’）。
                    ·如果元素是可查看的，请描述查看规则（如：‘地址’字段应显示用户的详细地址）。
                    ·如果元素是可编辑的，请描述编辑规则（如：‘备注’字段可编辑，但长度不能超过 100 个字符）。
                    ·如果元素是可删除的，请描述删除规则（如：‘删除’按钮将删除该条记录）。
                    ·如果元素是可上传的，请描述上传规则（如：‘头像’字段可上传图片文件，文件大小不能超过 2MB）。
                    ·如果元素是可下载的，请描述下载规则（如：‘文件’链接将下载指定的文件）。
                    ·如果元素是可复制的，请描述复制规则（如：‘复制’按钮将复制该条记录的内容）。
                    ·如果元素是可粘贴的，请描述粘贴规则（如：‘粘贴’按钮将粘贴剪贴板中的内容）。
                    ·如果元素是可移动的，请描述移动规则（如：‘移动’按钮将移动该条记录到指定位置）。
                    ·如果元素是可缩放的，请描述缩放规则（如：‘缩放’按钮将放大或缩小该条记录）。
                    ·如果元素是可旋转的，请描述旋转规则（如：‘旋转’按钮将旋转该条记录）。
                    ·如果元素是可刷新的，请描述刷新规则（如：‘刷新’按钮将重新加载页面）。
                    ·如果元素是可排序的，请描述排序规则（如：‘排序’按钮将按指定字段排序）。
                    ·如果元素是可过滤的，请描述过滤规则（如：‘过滤’按钮将按指定条件过滤记录）。
                    ·如果元素是可搜索的，请描述搜索规则（如：‘搜索’按钮将按指定关键字搜索记录）。
                    ·如果元素是可导出的，请描述导出规则（如：‘导出’按钮将导出当前页面的记录）。
                    ·如果元素是可导入的，请描述导入规则（如：‘导入’按钮将导入指定文件的记录）。
                    ·如果元素是可预览的，请描述预览规则（如：‘预览’按钮将显示该条记录的详细内容）。

                4.描述数据信息：
                    ·指出图片中显示的示例数据或占位符文本（例如：表格中显示了 ‘用户ID’、‘姓名’、‘注册日期’ 列，其中包含示例数据；搜索框中提示文字为 ‘请输入关键词’）。
                    ·如果可见，描述任何明显的数据格式或约束（例如：日期格式为 YYYY-MM-DD，密码字段有长度限制提示）。
                5.描述布局和结构： 简要说明元素的组织方式（例如：这是一个包含三个部分的表单，顶部是用户信息，中间是地址信息，底部是操作按钮；这是一个包含五个步骤的向导界面）。
                6.推断用户交互/工作流：
                    ·根据图片内容，描述用户可能执行的操作（例如：用户可以在此界面输入用户名和密码，然后点击‘登录’按钮；用户可以通过点击‘+’图标添加新的记录）。
                    ·如果是流程图或状态图，请描述流程的步骤、判断条件、不同路径和状态转换。
                7.突出显示对测试重要的细节：
                    ·特别注意任何可能暗示验证规则、错误处理、边界条件或特殊状态的视觉线索（例如：一个灰显不可用的按钮，一个显示错误消息的提示框，一个高亮显示的必填字段）。
                    ·如果图片展示了特定场景（如：空状态、加载状态、错误状态），请明确指出。
                输出格式要求：
                    ·使用清晰、简洁、客观的语言。
                    ·优先使用列表（如项目符号）来组织元素和细节，便于阅读。
                    ·避免主观评价或描述纯粹的视觉美学（除非颜色或样式具有明确的功能含义，例如用红色表示错误）。
                    ·专注于功能性和可测试性方面的信息。
        """
        if alt_text:
            prompt += f" 请现在描述：{alt_text}。"

        # 使用Autogen处理图片分析
        try:
            logger.info(f"开始使用Autogen处理图片分析，alt_text: {alt_text}")
            # 使用相对导入
            from app.api.v1.reqAgent.autogen_image_analyzer import analyze_image_with_autogen
            result = analyze_image_with_autogen(image_src, prompt, self.api_key, self.api_base, self.model)
            if result:
                logger.info(f"Autogen分析成功，响应长度: {len(result)}")
                return f"**需求图片内容分析(\n\n{result})**\n\n"
        except Exception as e:
            logger.error(f"Autogen分析失败: {str(e)}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")

        # 如果分析失败，回退到模拟分析结果
        logger.info(f"使用模拟分析结果，alt_text: {alt_text}")
        mock_result = self._get_mock_analysis(alt_text)
        return f"**大模型图片分析失败(\n\n{mock_result})**\n\n"

    def _get_image_content(self, image_src: str) -> Optional[str]:
        """
        获取图片内容，支持URL、Base64编码和本地路径

        Args:
            image_src: 图片URL、Base64编码或本地路径

        Returns:
            str: Base64编码的图片内容，如果获取失败则返回None
        """
        logger.info(f"_get_image_content 正在获取图片内容: {image_src[:100]}...-------------------_get_image_content")

        # 如果是空或非字符串，直接返回None
        if not image_src or not isinstance(image_src, str):
            logger.warning("图片源为空或非字符串")
            return None

        # 检查是否是Base64编码
        if image_src.startswith('data:'):
            try:
                # 记录更详细的信息
                mime_type = image_src.split(';')[0] if ';' in image_src else 'unknown'
                logger.info(f"Base64内容MIME类型: {mime_type}")

                # 如果是图片MIME类型
                if mime_type.startswith('data:image'):
                    # 提取Base64部分
                    base64_content = image_src.split(',', 1)[1] if ',' in image_src else image_src
                    logger.info(f"成功提取Base64编码图片，长度: {len(base64_content)}")

                    # 检查Base64编码是否有效
                    try:
                        # 尝试解码一小部分，验证是否有效
                        base64.b64decode(base64_content[:100])
                        return base64_content
                    except Exception as decode_error:
                        logger.error(f"Base64编码无效: {str(decode_error)}")
                        # 尝试修复常见的Base64编码问题
                        fixed_content = base64_content.replace(' ', '+').replace('\n', '').replace('\r', '')
                        logger.info("尝试修复Base64编码")
                        return fixed_content
                # 如果是HTML MIME类型，这是错误的格式
                elif mime_type.startswith('data:text/html'):
                    logger.warning(f"发现错误的Base64格式: {mime_type}")
                    # 尝试提取HTML内容中的图片
                    try:
                        # 提取Base64部分
                        base64_html = image_src.split(',', 1)[1] if ',' in image_src else image_src
                        # 解码HTML内容
                        html_content = base64.b64decode(base64_html).decode('utf-8', errors='ignore')
                        logger.info(f"解码HTML内容成功，长度: {len(html_content)}")

                        # 使用BeautifulSoup解析HTML
                        soup = BeautifulSoup(html_content, 'html.parser')
                        # 尝试找到图片元素
                        img_tags = soup.find_all('img')
                        logger.info(f"在HTML内容中找到 {len(img_tags)} 个图片元素")

                        if img_tags:
                            # 使用第一个图片的src属性
                            img_src = img_tags[0].get('src')
                            if img_src:
                                logger.info(f"使用HTML中的图片URL: {img_src}")
                                # 递归调用自身获取图片内容
                                return self._get_image_content(img_src)

                        # 如果没有找到图片，返回None
                        logger.warning("在HTML内容中没有找到图片元素")
                        return None
                    except Exception as html_error:
                        logger.error(f"HTML内容解析失败: {str(html_error)}")
                        return None
                # 其他MIME类型
                else:
                    logger.warning(f"不支持的MIME类型: {mime_type}")
                    return None
            except Exception as e:
                logger.error(f"Base64内容处理失败: {str(e)}")
                return None

        # 处理URL
        parsed_url = urlparse(image_src)
        if parsed_url.scheme in ('http', 'https'):
            # 检查是否是支持的图片格式
            if not image_src.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp')):
                logger.warning(f"不支持的图片格式: {image_src}")
                return None

            try:
                # 添加User-Agent和Referer头，模拟浏览器请求
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Referer': 'https://www.tapd.cn/'
                }

                # 使用更长的超时时间
                response = requests.get(image_src, headers=headers, timeout=30)
                response.raise_for_status()
                image_data = response.content

                # 检查下载的内容是否为图片
                if len(image_data) < 100:  # 一个正常的图片通常至少有几KB
                    logger.warning(f"下载的内容太小，可能不是图片: {len(image_data)} 字节")
                    # 尝试修改URL，有时候URL中包含了额外的参数
                    clean_url = image_src.split('?')[0]
                    if clean_url != image_src:
                        logger.info(f"尝试使用清理后的URL: {clean_url}")
                        response = requests.get(clean_url, headers=headers, timeout=30)
                        response.raise_for_status()
                        image_data = response.content
                        if len(image_data) < 100:
                            logger.warning(f"使用清理后的URL下载的内容仍然太小: {len(image_data)} 字节")
                            return None

                logger.info(f"成功从 URL 下载图片，大小: {len(image_data)} 字节")
                return base64.b64encode(image_data).decode('utf-8')
            except Exception as e:
                logger.error(f"获取图片失败: {str(e)}")
                return None

        # 处理本地文件
        try:
            # 如果是相对路径，尝试从当前目录读取
            if not os.path.isabs(image_src):
                image_src = os.path.join(os.getcwd(), image_src)

            if os.path.exists(image_src):
                with open(image_src, 'rb') as f:
                    image_data = f.read()
                logger.info(f"成功读取本地图片，大小: {len(image_data)} 字节")
                return base64.b64encode(image_data).decode('utf-8')
            else:
                logger.warning(f"本地图片文件不存在: {image_src}")
        except Exception as e:
            logger.error(f"读取本地图片失败: {str(e)}")

        logger.warning("无法获取图片内容")
        return None

    def _analyze_image_with_llama_index(self, image_base64: str, prompt: str) -> str:
        """
        使用LlamaIndex处理图片分析

        Args:
            image_base64: Base64编码的图片
            prompt: 提示词

        Returns:
            str: 分析结果
        """
        try:
            # 导入必要的模块
            from llama_index.core import Settings
            from llama_index.multi_modal_llms.openai import OpenAIMultiModal
            from llama_index.core.schema import ImageDocument
            import io
            from PIL import Image

            # 解码Base64图片
            image_data = base64.b64decode(image_base64)
            image = Image.open(io.BytesIO(image_data))

            # 创建图片文档
            image_document = ImageDocument(image=image)

            # 设置OpenAI多模态模型
            Settings.llm = OpenAIMultiModal(model="gpt-4-vision-preview", api_key=self.api_key)

            # 使用模型分析图片
            response = Settings.llm.complete(
                prompt=prompt,
                image_documents=[image_document],
            )

            logger.info(f"LlamaIndex分析成功: {response}")
            return str(response)
        except Exception as e:
            logger.error(f"LlamaIndex分析失败: {str(e)}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            raise

    def _get_mock_analysis(self, alt_text: str = "") -> str:
        """
        生成模拟的图片分析结果

        Args:
            alt_text: 图片的alt文本，用于生成相关的分析结果

        Returns:
            str: 模拟的分析结果
        """
        logger.info("-----------------开始进入_get_mock_analysis-------------------")
        # 根据alt文本生成不同的模拟分析结果
        if "界面" in alt_text or "界面" in alt_text.lower() or "用户界面" in alt_text:
            return "该图片展示了一个用户界面设计。界面采用简洁现代的设计风格，布局清晰有序。主要元素包括导航栏、内容区域和功能按钮。色彩搭配协调，视觉效果良好。整体设计符合现代用户体验原则，易于用户理解和操作。"
        elif "流程" in alt_text or "流程" in alt_text.lower() or "过程" in alt_text:
            return "该图片展示了一个业务流程图。图中使用方块和箭头连接展示了不同环节之间的关系和流转。流程从左到右（或从上到下）进行，清晰地展示了各个步骤和决策点。这样的流程图有助于理解系统或业务的运作机制。"
        elif "图表" in alt_text or "图表" in alt_text.lower() or "数据" in alt_text:
            return "该图片展示了一个数据图表。图表使用直观的可视化方式展示了数据信息，可能是柱状图、折线图、饼图或其他类型的图表。图表中包含数据标签、坐标轴和图例，清晰地展示了数据的分布和趋势。这类图表有助于直观理解数据并进行比较分析。"
        elif "架构" in alt_text or "架构" in alt_text.lower() or "结构" in alt_text:
            return "该图片展示了一个系统架构图。图中使用方块、箭头和其他图形符号展示了系统的不同组件及其之间的关系。架构图清晰地展示了系统的层次结构、模块划分和交互方式。这样的架构图有助于理解系统的整体设计和各组件的职责。"
        else:
            # 默认的模拟分析结果
            return f"该图片展示了一个与需求相关的参考图示。图片的标题或描述是\"{alt_text or '需求文档图片'}\"。图片内容可能包含界面设计、功能流程、数据结构或其他与需求相关的视觉信息。该图片用于辅助理解需求文档中描述的功能或概念。"

    def _call_vision_api(self, image_base64: str, prompt: str) -> str:
        """
        调用大模型的视觉API

        Args:
            image_base64: Base64编码的图片
            prompt: 提示词

        Returns:
            str: 大模型的回复
        """
        logger.info("-----------------开始进入_call_vision_api-------------------")
        # 检查Base64编码是否有效
        try:
            # 先检查是否已经包含了 data:image 前缀或者 data:text/html 前缀
            if image_base64.startswith('data:image') or image_base64.startswith('data:text/html'):
                logger.info(f"Base64编码已包含前缀，长度: {len(image_base64)}")
                # 提取纯粉Base64部分
                image_base64 = image_base64.split(',', 1)[1] if ',' in image_base64 else image_base64
                logger.info(f"提取纯粉Base64部分，长度: {len(image_base64)}")

            # 尝试解码一小部分，验证是否有效
            try:
                decoded_sample = base64.b64decode(image_base64[:20])
                logger.info(f"Base64编码有效，长度: {len(image_base64)}")
            except Exception as e:
                logger.error(f"Base64编码无效，尝试修复: {str(e)}")
                # 尝试修复常见的Base64编码问题
                image_base64 = image_base64.replace(' ', '+').replace('\n', '').replace('\r', '')
                try:
                    decoded_sample = base64.b64decode(image_base64[:20])
                    logger.info(f"修复后Base64编码有效，长度: {len(image_base64)}")
                except Exception as e2:
                    logger.error(f"修复后Base64编码仍然无效: {str(e2)}")
                    raise

            # 检查解码后的内容是否是HTML
            is_html = False
            try:
                # 解码前100个字符检查是否是HTML
                decoded_start = base64.b64decode(image_base64[:100]).decode('utf-8', errors='ignore')
                if '<!DOCTYPE' in decoded_start or '<html' in decoded_start or '<body' in decoded_start:
                    is_html = True
                    logger.warning(f"Base64编码内容是HTML，不是图片: {decoded_start[:50]}...")

                    # 尝试从 HTML 中提取图片
                    try:
                        # 解码完整的HTML内容
                        html_content = base64.b64decode(image_base64).decode('utf-8', errors='ignore')

                        # 使用BeautifulSoup解析HTML
                        from bs4 import BeautifulSoup
                        soup = BeautifulSoup(html_content, 'html.parser')

                        # 尝试找到图片元素
                        img_tag = soup.find('img')

                        if img_tag and img_tag.get('src'):
                            img_src = img_tag.get('src')
                            logger.info(f"从 HTML 中提取到图片: {img_src[:50]}...")

                            # 直接使用src属性
                            logger.info(f"使用src属性作为图片源: {img_src}")

                            # 如果图片是Base64格式，提取Base64部分
                            if img_src.startswith('data:image'):
                                # 提取Base64部分
                                image_base64 = img_src.split(',', 1)[1] if ',' in img_src else img_src
                                logger.info(f"从 HTML 中提取到Base64图片，长度: {len(image_base64)}")
                                is_html = False  # 重置标志，因为我们现在有了有效的图片数据
                            # 如果是外部URL，尝试下载
                            elif img_src.startswith(('http://', 'https://')):
                                logger.info(f"从 HTML 中提取到外部图片URL: {img_src}")

                                # 检查是否是支持的图片格式
                                if img_src.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp')):
                                    # 尝试下载图片
                                    try:
                                        # 添加User-Agent和Referer头，模拟浏览器请求
                                        headers = {
                                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                                            'Referer': 'https://www.tapd.cn/'
                                        }
                                        response = requests.get(img_src, headers=headers, timeout=30)
                                        response.raise_for_status()
                                        image_data = response.content
                                        image_base64 = base64.b64encode(image_data).decode('utf-8')
                                        logger.info(f"成功下载图片，长度: {len(image_base64)}")
                                        is_html = False  # 重置标志，因为我们现在有了有效的图片数据
                                    except Exception as e:
                                        logger.error(f"下载图片失败: {str(e)}")
                                else:
                                    logger.warning(f"不支持的图片格式: {img_src}")
                        else:
                            logger.error(f"在 HTML 中没有找到图片标签")
                    except Exception as e:
                        logger.error(f"从 HTML 中提取图片失败: {str(e)}")
            except Exception:
                # 如果解码失败，可能是二进制图片数据，这是正常的
                pass

            # 如果是HTML内容且没有提取到图片，尝试将HTML内容转换为图片
            if is_html:
                logger.warning("检测到Base64编码内容是HTML，尝试将其转换为图片")


            # 尝试将HTML Base64转换为图片Base64
                try:
                    # 将HTML Base64转换为图片Base64
                    image_base64 = image_base64.replace('data:text/html;base64', 'data:image/png;base64')
                    # logger.info(f"尝试将HTML Base64转换为图片Base64: {image_base64[:50]}...")
                    is_html = False  # 重置标志，因为我们现在将其视为图片数据
                except Exception as e:
                    logger.error(f"转换HTML Base64失败: {str(e)}")
                    return None
        except Exception as e:
            logger.error(f"Base64编码无效，尝试修复: {str(e)}")
            # 尝试修复常见的Base64编码问题
            image_base64 = image_base64.replace(' ', '+').replace('\n', '').replace('\r', '')

        # 检查Base64编码的长度，如果太短，可能不是有效的图片
        if len(image_base64) < 100:  # 一个正常的图片Base64编码通常至少有几千个字符
            logger.warning(f"Base64编码太短，可能不是有效的图片: {len(image_base64)} 字符")
            return None

        # 使用OpenAI的API格式
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        # 构建请求体，使用正确的MIME类型
        # 检查图片数据的前几个字节，确定正确的MIME类型
        mime_type = "image/jpeg"  # 默认使用JPEG
        try:
            # 解码前几个字节检查文件类型
            header_bytes = base64.b64decode(image_base64[:20])

            # 检查PNG签名
            if header_bytes.startswith(b'\x89PNG\r\n\x1a\n'):
                mime_type = "image/png"
                logger.info("\u68c0测到PNG图片")
            # 检查JPEG签名
            elif header_bytes.startswith(b'\xff\xd8'):
                mime_type = "image/jpeg"
                logger.info("\u68c0测到JPEG图片")
            # 检查GIF签名
            elif header_bytes.startswith(b'GIF87a') or header_bytes.startswith(b'GIF89a'):
                mime_type = "image/gif"
                logger.info("\u68c0测到GIF图片")
            # 检查WEBP签名
            elif header_bytes.startswith(b'RIFF') and b'WEBP' in header_bytes[:16]:
                mime_type = "image/webp"
                logger.info("\u68c0测到WEBP图片")
            else:
                logger.info(f"\u65e0法检测图片类型，使用默认MIME类型: {mime_type}")
        except Exception as e:
            logger.error(f"\u68c0测图片MIME类型失败: {str(e)}")

        # 构建图片URL
        image_url = f"data:{mime_type};base64,{image_base64}"
        logger.info(f"API请求使用的图片URL开头: {image_url[:50]}...")

        # 打印完整的图片URL长度，便于调试
        logger.info(f"完整图片URL长度: {len(image_url)}")

        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 1000
        }

        # 如果是OpenAI的模型，添加特定参数
        if "gpt-4" in self.model:
            # 对于GPT-4 Vision模型，可以设置图像细节级别
            payload["messages"][0]["content"][1]["image_url"]["detail"] = "auto"

        # 发送请求
        try:
            # 根据不同的模型使用不同的端点
            endpoint = "/chat/completions"
            if "qwen" in self.model.lower():
                # 通义千问模型使用的端点
                endpoint = "/chat/completions"
                logger.info(f"使用通义千问模型端点: {endpoint}")
            elif "gpt-4" in self.model.lower() or "gpt-3.5" in self.model.lower():
                # OpenAI模型使用的端点
                endpoint = "/chat/completions"
                logger.info(f"使用OpenAI模型端点: {endpoint}")
            else:
                # 默认端点
                logger.info(f"使用默认端点: {endpoint}")

            # 打印完整的API请求URL
            api_url = f"{self.api_base}{endpoint}"
            logger.info(f"发送API请求到: {api_url}")

            # 打印请求头部
            logger.info(f"请求头部: {headers}")

            # 打印请求体（不包含图片数据）
            payload_log = payload.copy()
            if "messages" in payload_log and len(payload_log["messages"]) > 0:
                if "content" in payload_log["messages"][0] and len(payload_log["messages"][0]["content"]) > 1:
                    if "image_url" in payload_log["messages"][0]["content"][1]:
                        payload_log["messages"][0]["content"][1]["image_url"]["url"] = "[BASE64_IMAGE_DATA]"
            logger.info(f"请求体: {payload_log}")

            # 发送请求
            response = requests.post(
                api_url,
                headers=headers,
                json=payload,
                timeout=60  # 增加超时时间
            )

            # 打印响应状态码
            logger.info(f"响应状态码: {response.status_code}")

            # 如果响应不成功，打印响应内容
            if response.status_code != 200:
                logger.error(f"响应内容: {response.text}")

            response.raise_for_status()
            result = response.json()

            # 打印响应结果（不包含敏感信息）
            logger.info(f"响应结果类型: {type(result)}")
            if isinstance(result, dict):
                logger.info(f"响应结果键: {result.keys()}")

            # 提取回复内容
            if "choices" in result and len(result["choices"]) > 0:
                if "message" in result["choices"][0] and "content" in result["choices"][0]["message"]:
                    content = result["choices"][0]["message"]["content"]
                    logger.info(f"成功提取响应内容，长度: {len(content)}")
                    return content
                else:
                    logger.error(f"响应格式异常，无法提取content: {result['choices'][0]}")
            else:
                logger.error(f"API返回格式异常: {result}")
            return None
        except Exception as e:
            logger.error(f"API请求失败: {str(e)}")
            # 打印异常堆栈
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            raise

def html_to_markdown_with_image_analysis(
    html_content: str,
    image_analysis_enabled: bool = True,
    api_key: str = DEFAULT_API_KEY,
    api_base: str = DEFAULT_API_BASE,
    model: str = DEFAULT_MODEL,
    **options
) -> str:
    """
    将HTML内容转换为Markdown格式，并对图片进行大模型分析

    Args:
        html_content: HTML内容
        image_analysis_enabled: 是否启用图片分析
        api_key: 大模型API密钥
        api_base: 大模型API基础URL
        model: 大模型名称
        **options: 其他MarkdownConverter选项

    Returns:
        str: 转换后的Markdown文本
    """
    try:
        # 强制启用图片分析
        image_analysis_enabled = True

        # 检查HTML内容中是否包含Base64编码的图片
        has_base64_images = 'data:' in html_content
        has_html_base64 = 'data:text/html;base64' in html_content
        has_image_base64 = 'data:image' in html_content
        logger.info(f"HTML内容长度: {len(html_content)}")
        logger.info(f"HTML内容中{'' if has_base64_images else '不'}包含Base64编码内容")
        logger.info(f"HTML内容中{'' if has_html_base64 else '不'}包含HTML Base64编码")
        logger.info(f"HTML内容中{'' if has_image_base64 else '不'}包含图片Base64编码")

        # 将原始HTML内容写入文件以便调试
        with open("html_content_raw.txt", "w", encoding="utf-8") as f:
            f.write(html_content)

        # 使用BeautifulSoup预处理HTML
        soup = BeautifulSoup(html_content, 'html.parser')

        # 将content内容写入文件以便调试
        with open("tapd_content.txt", "w", encoding="utf-8") as f:
            f.write(soup.prettify())


        # 处理带有alt="需求文档图片"属性的图片
        req_images = soup.find_all('img', alt="需求文档图片")
        logger.info(f"找到 {len(req_images)} 张需求文档图片")

        # 确保输出目录存在
        output_dir = "tapd_images2"
        #判断目录是否存在，存在则删除（包括里面的文件）
        if os.path.exists(output_dir):
            shutil.rmtree(output_dir)
        os.makedirs(output_dir, exist_ok=True)

        # 处理每张需求文档图片
        for i, img in enumerate(req_images):
            try:
                # 获取src属性
                img_src = img.get('src')
                if not img_src:
                    logger.warning(f"第{i+1}张需求文档图片没有src属性")
                    continue

                # 保存图片到本地
                local_path = save_base64_image(img_src, output_dir, f"req_doc_img_{i+1}")
                logger.info(f"已保存第{i+1}张需求文档图片到: {local_path}")

                if local_path:
                    # 将本地路径替换soup中的src值
                    req_images[i]['src'] = local_path
                    # img['src'] = local_path

                    logger.info(f"已将第{i+1}张需求文档图片的src替换为本地路径: {local_path}")
                else:
                    logger.warning(f"第{i+1}张需求文档图片保存失败")
            except Exception as e:
                logger.error(f"处理第{i+1}张需求文档图片时出错: {str(e)}")
                continue

            with open("tapd_content2.txt", "w", encoding="utf-8") as f:
                f.write(soup.prettify())

        # 检查图片数量
        all_images = soup.find_all('img')

        # 过滤图片，只保留需求文档中的实际图片
        images = []
        for img in all_images:
            # 检查图片是否来自TAPD的文件服务器
            src = img.get('src', '')
            original_src = img.get('original_src', '')
            alt = img.get('alt', '')

            # 如果图片来自TAPD的文件服务器，或者是Base64编码的图片，则保留
            if ('file.tapd.cn' in src or 'file.tapd.cn' in original_src or
                (src.startswith('data:image') and '需求' in alt) or
                '需求文档图片' in alt):
                images.append(img)

        logger.info(f"HTML内容中包含 {len(all_images)} 张图片，其中 {len(images)} 张是需求文档中的实际图片")

        # 检查每张图片的类型
        base64_html_images = 0
        base64_image_images = 0
        url_images = 0
        other_images = 0

        # 预处理图片，将data:text/html;base64格式的图片转换为正确的图片URL
        # for img in images:
        #     src = img.get('src', '')
        #     if not src:
        #         other_images += 1
        #         continue

        #     if src.startswith('data:text/html;base64'):
        #         base64_html_images += 1
        #         logger.info(f"发现HTML Base64图片: {src[:50]}...")

        #         # 直接使用src属性作为图片源
        #         src = img.get('src')
        #         logger.info(f"使用src属性作为图片源: {src}")

        #         # 如果没有找到真实图片URL，尝试解析HTML内容
        #         try:
        #             # 提取Base64部分
        #             base64_html = src.split(',', 1)[1] if ',' in src else src
        #             # 解码HTML内容
        #             html_content = base64.b64decode(base64_html).decode('utf-8', errors='ignore')

        #             # 使用BeautifulSoup解析HTML
        #             inner_soup = BeautifulSoup(html_content, 'html.parser')
        #             # 尝试找到图片元素
        #             inner_img = inner_soup.find('img')

        #             if inner_img and inner_img.get('src'):
        #                 inner_src = inner_img.get('src')
        #                 logger.info(f"从嵌套HTML中提取图片URL: {inner_src}")
        #                 img['src'] = inner_src
        #             else:
        #                 # 如果没有找到图片，使用占位图片
        #                 alt_text = img.get('alt', '需求文档图片')
        #                 logger.warning(f"无法从嵌套HTML中提取图片，使用占位文本: {alt_text}")
        #                 # 将img标签替换为文本
        #                 img.replace_with(f"[{alt_text}]")
        #         except Exception as e:
        #             logger.error(f"解析嵌套HTML失败: {str(e)}")
        #             # 如果解析失败，使用占位图片
        #             alt_text = img.get('alt', '需求文档图片')
        #             img.replace_with(f"[{alt_text}]")
        #     elif src.startswith('data:image'):
        #         base64_image_images += 1
        #         logger.info(f"发现图片Base64: {src[:50]}...")
        #     elif src.startswith(('http://', 'https://')):
        #         url_images += 1
        #     else:
        #         other_images += 1

        # logger.info(f"HTML内容中包含 {base64_html_images} 张HTML Base64图片，{base64_image_images} 张图片Base64，{url_images} 张URL图片，{other_images} 张其他图片")

        # 移除不需要的标签
        for script in soup(["script", "style"]):
            script.decompose()

        # 合并选项
        converter_options = {
            "heading_style": "ATX",  # 使用 # 格式的标题
            "bullets": "-",          # 使用 - 作为无序列表标记
            "strip": ['a'],          # 移除特定标签但保留其内容
            "image_analysis_enabled": image_analysis_enabled,
            "api_key": api_key,
            "api_base": api_base,
            "model": model
        }
        converter_options.update(options)
        # with open("tapd_content2.txt", "w", encoding="utf-8") as f:
        #     f.write(soup.prettify())
        # 使用自定义转换器
        converter = ImageMarkdownConverter(**converter_options)
        markdown_text = converter.convert(str(soup))
        logger.info(f"{markdown_text}-------------------------转换后的Markdown文本长度")
        logger.info(f"{len(markdown_text)}-------------------------转换后的Markdown文本长度")
        return markdown_text.strip()
    except Exception as e:
        logger.error(f"HTML转Markdown失败: {str(e)}")
        return html_content  # 转换失败时返回原始内容


def save_base64_image(base64_data: str, output_dir: str = "saved_images", filename_prefix: str = "image") -> str:
    """
    将Base64编码的图片解码并保存到本地

    Args:
        base64_data: Base64编码的图片数据
        output_dir: 输出目录，默认为"saved_images"
        filename_prefix: 文件名前缀，默认为"image"

    Returns:
        str: 保存的图片文件路径
    """
    try:
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 检查是否是有效的Base64编码数据
        if not base64_data:
            logger.error("Base64数据为空")
            return ""

        # 确定图片格式和Base64数据
        image_format = "png"  # 默认格式
        if ',' in base64_data:
            # 数据格式类似于: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...
            mime_part, base64_part = base64_data.split(',', 1)

            # 从MIME类型中提取图片格式
            if 'image/' in mime_part:
                mime_type = mime_part.split('image/')[1].split(';')[0]
                if mime_type in ['png', 'jpeg', 'jpg', 'gif', 'svg+xml', 'webp']:
                    image_format = 'svg' if mime_type == 'svg+xml' else mime_type

            base64_data = base64_part

        # 解码Base64数据
        try:
            image_data = base64.b64decode(base64_data)
        except Exception as decode_error:
            logger.error(f"Base64解码失败: {str(decode_error)}")
            # 尝试修复可能的Base64编码问题
            # 有时Base64数据可能包含空格或换行符
            base64_data = base64_data.replace(' ', '').replace('\n', '')
            try:
                image_data = base64.b64decode(base64_data)
            except Exception as retry_error:
                logger.error(f"修复后的Base64解码仍然失败: {str(retry_error)}")
                return ""

        # 生成唯一文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{filename_prefix}_{timestamp}.{image_format}"
        filepath = os.path.join(output_dir, filename)

        # 保存图片
        with open(filepath, "wb") as f:
            f.write(image_data)

        # 验证保存的文件是否是有效的图片
        if os.path.getsize(filepath) == 0:
            logger.error(f"保存的图片文件大小为0: {filepath}")
            os.remove(filepath)  # 删除无效文件
            return ""

        logger.info(f"成功保存Base64图片到: {filepath}")
        return filepath
    except Exception as e:
        logger.error(f"保存Base64图片失败: {str(e)}")
        return ""