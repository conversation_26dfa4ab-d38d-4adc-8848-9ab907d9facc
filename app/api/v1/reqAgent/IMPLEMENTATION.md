# 图片解析功能实现说明

## 实现原理

本功能通过扩展`markdownify`库的`MarkdownConverter`类，重写了`convert_img`方法，使其能够在转换HTML图片标签为Markdown格式时，同时调用大模型API对图片内容进行分析，并将分析结果整合到Markdown输出中。

## 核心组件

1. **ImageMarkdownConverter类**：继承自`markdownify.MarkdownConverter`，重写了图片处理逻辑
2. **html_to_markdown_with_image_analysis函数**：提供了便捷的API，用于将HTML转换为带有图片分析的Markdown
3. **图片获取与处理**：支持从URL、Base64编码和本地文件获取图片
4. **大模型API调用**：使用OpenAI的GPT-4 Vision API进行图片内容分析

## 关键方法

### convert_img

重写了`MarkdownConverter`的`convert_img`方法，在生成标准Markdown图片语法后，调用大模型API分析图片内容，并将分析结果添加到Markdown中。

```python
def convert_img(self, el, text, parent_tags):
    # 生成标准Markdown图片语法
    markdown_image = f'![{alt}]({src}{title_part})'
    
    # 调用大模型API分析图片
    if self.image_analysis_enabled:
        image_analysis = self.analyze_image(src, alt)
        if image_analysis:
            # 将分析结果添加到Markdown中
            return f'{markdown_image}\n\n<!-- 图片分析 -->\n{image_analysis}\n'
    
    return markdown_image
```

### analyze_image

负责调用大模型API分析图片内容：

```python
def analyze_image(self, image_src: str, alt_text: str = "") -> Optional[str]:
    # 获取图片内容
    image_content = self._get_image_content(image_src)
    if not image_content:
        return None
    
    # 准备提示词
    prompt = f"请详细描述这张图片的内容。"
    if alt_text:
        prompt += f" 图片的标题或描述是：{alt_text}。"
    
    # 调用大模型API
    response = self._call_vision_api(image_content, prompt)
    return response
```

### _get_image_content

处理不同来源的图片（URL、Base64、本地文件），并将其转换为Base64编码：

```python
def _get_image_content(self, image_src: str) -> Optional[str]:
    # 处理Base64编码的图片
    if image_src.startswith('data:image'):
        return image_src.split(',', 1)[1]
    
    # 处理URL图片
    if parsed_url.scheme in ('http', 'https'):
        response = requests.get(image_src, timeout=10)
        return base64.b64encode(response.content).decode('utf-8')
    
    # 处理本地文件
    if os.path.exists(image_src):
        with open(image_src, 'rb') as f:
            return base64.b64encode(f.read()).decode('utf-8')
```

### _call_vision_api

调用大模型API进行图片分析：

```python
def _call_vision_api(self, image_base64: str, prompt: str) -> str:
    # 构建API请求
    payload = {
        "model": self.model,
        "messages": [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{image_base64}"
                        }
                    }
                ]
            }
        ]
    }
    
    # 发送请求并处理响应
    response = requests.post(
        f"{self.api_base}/chat/completions",
        headers=headers,
        json=payload
    )
    result = response.json()
    return result["choices"][0]["message"]["content"]
```

## 集成到现有系统

通过修改`tapdAgent.py`中的`html_to_markdown`函数，我们将图片解析功能集成到了现有系统中：

```python
def html_to_markdown(html_content: str) -> str:
    # 获取环境变量，控制是否启用图片分析
    image_analysis_enabled = os.environ.get("ENABLE_IMAGE_ANALYSIS", "true").lower() == "true"
    
    # 使用支持图片分析的Markdown转换器
    if image_analysis_enabled:
        return html_to_markdown_with_image_analysis(html_content)
    else:
        # 使用原始的markdownify转换
        # ...
```

## 配置与扩展

系统支持通过环境变量或函数参数进行配置：

1. **ENABLE_IMAGE_ANALYSIS**：控制是否启用图片分析
2. **LLM_API_KEY**：大模型API密钥
3. **LLM_API_BASE**：API基础URL
4. **DEFAULT_LLM_MODEL**：使用的模型名称

未来可以扩展的方向：

1. 支持更多的大模型提供商
2. 添加图片缓存机制，避免重复分析
3. 实现更智能的图片分析提示词生成
4. 添加图片分析结果的缓存机制
