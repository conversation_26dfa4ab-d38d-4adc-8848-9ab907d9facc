import autogen
import os

# 使用环境变量中的API密钥
# 配置 LLM
config_list = [
    {
        "model": os.environ.get("DEFAULT_LLM_MODEL", "deepseek-chat"),
        "api_key": os.environ.get("LLM_API_KEY", "sk-2cdc85223c2f4f9a976a57e3ae9ba4b9"),
        "base_url": os.environ.get("LLM_API_BASE", "https://api.deepseek.com/v1"),
    }
]

# 创建助手代理
assistant = autogen.AssistantAgent(
    name="assistant",
    llm_config={"config_list": config_list}
)

# 创建用户代理
user_proxy = autogen.UserProxyAgent(
    name="user_proxy",
    human_input_mode="NEVER",
    max_consecutive_auto_reply=0,
    is_termination_msg=lambda x: x.get("content", "").rstrip().endswith("TERMINATE"),
    code_execution_config={"work_dir": "coding", "use_docker": False},
)

# 启动对话
user_proxy.initiate_chat(
    assistant,
    message="Hello, can you help me analyze this requirement: 'The system should allow users to login and register'?"
)

print("AutoGen test completed successfully!")
