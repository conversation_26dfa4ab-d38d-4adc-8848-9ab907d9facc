import logging
import requests
import os
import base64
import io
from PIL import Image

# 配置日志
logger = logging.getLogger('image_markdown_converter')

def compress_image(image_data: bytes, max_size_kb: int = 1024, quality: int = 85) -> bytes:
    """
    压缩图片，使其大小不超过指定的KB数

    Args:
        image_data: 图片二进制数据
        max_size_kb: 最大大小，单位KB
        quality: 初始压缩质量，范围1-100

    Returns:
        bytes: 压缩后的图片二进制数据
    """
    try:
        # 如果图片已经小于最大大小，直接返回
        if len(image_data) <= max_size_kb * 1024:
            logger.info(f"图片大小已经小于 {max_size_kb}KB，无需压缩")
            return image_data

        # 使用PIL打开图片
        img = Image.open(io.BytesIO(image_data))

        # 如果图片是RGBA模式（带透明通道），转换为RGB模式
        if img.mode == 'RGBA':
            logger.info("转换RGBA图片为RGB模式")
            background = Image.new('RGB', img.size, (255, 255, 255))
            background.paste(img, mask=img.split()[3])  # 使用alpha通道作为mask
            img = background

        # 如果图片尺寸太大，先调整尺寸
        max_dimension = 1024  # 最大宽度或高度
        if max(img.size) > max_dimension:
            logger.info(f"图片尺寸过大，调整为最大维度 {max_dimension}px")
            # 计算调整后的尺寸，保持宽高比
            if img.width > img.height:
                new_width = max_dimension
                new_height = int(img.height * (max_dimension / img.width))
            else:
                new_height = max_dimension
                new_width = int(img.width * (max_dimension / img.height))

            img = img.resize((new_width, new_height), Image.LANCZOS)

        # 尝试不同的质量级别，直到图片大小小于最大大小
        current_quality = quality
        output = io.BytesIO()

        while current_quality > 10:  # 最低质量为10
            output.seek(0)
            output.truncate(0)  # 清空输出缓冲区

            # 保存为JPEG格式，使用当前质量级别
            img.save(output, format='JPEG', quality=current_quality, optimize=True)

            # 检查大小
            if output.tell() <= max_size_kb * 1024:
                logger.info(f"成功压缩图片，质量: {current_quality}，大小: {output.tell() / 1024:.2f}KB")
                break

            # 如果仍然太大，降低质量
            current_quality -= 10
            logger.info(f"图片仍然太大，降低质量到 {current_quality}")

        # 如果所有质量级别都尝试过，但仍然太大，返回最低质量的结果
        compressed_data = output.getvalue()
        logger.info(f"压缩后图片大小: {len(compressed_data) / 1024:.2f}KB，原始大小: {len(image_data) / 1024:.2f}KB，压缩率: {len(compressed_data) / len(image_data) * 100:.2f}%")

        return compressed_data
    except Exception as e:
        logger.error(f"压缩图片失败: {str(e)}")
        # 如果压缩失败，返回原始图片数据
        return image_data

def analyze_image_with_autogen(image_path: str, prompt: str, api_key: str, api_base: str, model: str = None) -> str:
    # 如果未提供模型名称，使用环境变量中的配置
    if model is None:
        model = os.environ.get("QWEN_VL_MODEL", "qwen-vl-plus-latest")
    """
    使用API直接处理图片分析，不依赖autogen库

    Args:
        image_path: 图片路径（本地文件路径或Base64编码）
        prompt: 提示词
        api_key: API密钥
        api_base: API基础URL
        model: 模型名称

    Returns:
        str: 分析结果
    """
    try:
        logger.info(f"开始分析图片，使用模型: {model}")

        # 检查是否是本地文件路径
        is_local_file = os.path.exists(image_path) and not image_path.startswith('data:')
        is_url = image_path.startswith(('http://', 'https://'))
        is_base64 = image_path.startswith('data:')

        if is_local_file:
            logger.info(f"检测到本地图片文件路径: {image_path}")
            # 读取本地图片文件
            with open(image_path, 'rb') as f:
                image_data = f.read()

            # 获取文件扩展名，确定MIME类型
            _, ext = os.path.splitext(image_path)
            mime_type = "image/jpeg"  # 默认MIME类型

            if ext.lower() in ['.png']:
                mime_type = "image/png"
            elif ext.lower() in ['.jpg', '.jpeg']:
                mime_type = "image/jpeg"
            elif ext.lower() in ['.gif']:
                mime_type = "image/gif"
            elif ext.lower() in ['.webp']:
                mime_type = "image/webp"

            # 压缩图片，最大大小为800KB
            logger.info(f"原始图片大小: {len(image_data) / 1024:.2f}KB")
            if len(image_data) > 800 * 1024 and mime_type in ["image/jpeg", "image/png"]:
                logger.info("图片大小超过800KB，进行压缩")
                image_data = compress_image(image_data, max_size_kb=800)
                # 压缩后强制使用JPEG格式
                mime_type = "image/jpeg"

            # 构建图片URL
            image_url = f"data:{mime_type};base64,{base64.b64encode(image_data).decode('utf-8')}"
            logger.info(f"成功读取本地图片并转换为URL，长度: {len(image_url)}")
        elif is_url:
            logger.info(f"检测到图片URL: {image_path}")
            try:
                # 添加User-Agent和Referer头，模拟浏览器请求
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Referer': 'https://www.tapd.cn/'
                }

                # 下载图片
                response = requests.get(image_path, headers=headers, timeout=30)
                response.raise_for_status()
                image_data = response.content

                # 检查下载的内容是否为图片
                if len(image_data) < 100:  # 一个正常的图片通常至少有几KB
                    logger.warning(f"下载的内容太小，可能不是图片: {len(image_data)} 字节")
                    return "图片下载失败，内容太小，可能不是有效的图片"

                # 记录原始图片大小
                logger.info(f"下载的图片大小: {len(image_data) / 1024:.2f}KB")

                # 检查内容类型
                content_type = response.headers.get('Content-Type', '')
                logger.info(f"下载的内容类型: {content_type}")

                # 检查内容的前几个字节，确定是否是图片
                if image_data[:4] == b'%PDF':
                    logger.warning("下载的内容是PDF文件，不是图片")
                    return "下载的内容是PDF文件，不是图片"
                elif content_type.startswith('image/svg'):
                    logger.warning("下载的内容是SVG图片，API不支持此格式")
                    return "下载的内容是SVG图片，API不支持此格式。这可能是一个图标或Logo。"
                elif image_data[:5] == b'<!DOC' or image_data[:5] == b'<html' or content_type.startswith('text/html'):
                    logger.warning("下载的内容是HTML文件，不是图片")
                    # 尝试从HTML中提取图片
                    try:
                        from bs4 import BeautifulSoup
                        soup = BeautifulSoup(image_data, 'html.parser')
                        img_tags = soup.find_all('img')
                        if img_tags:
                            img_src = img_tags[0].get('src')
                            if img_src:
                                # 如果是相对路径，转换为绝对路径
                                if not img_src.startswith(('http://', 'https://')):
                                    # 解析原始URL
                                    from urllib.parse import urlparse, urljoin
                                    parsed_url = urlparse(image_path)
                                    base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
                                    img_src = urljoin(base_url, img_src)

                                logger.info(f"从HTML中提取到图片URL: {img_src}")
                                # 递归调用自身分析图片
                                return analyze_image_with_autogen(img_src, prompt, api_key, api_base, model)
                    except Exception as e:
                        logger.error(f"从HTML中提取图片失败: {str(e)}")

                    # 如果无法从HTML中提取图片，使用模拟图片
                    logger.warning("无法从HTML中提取有效图片，使用模拟图片")
                    return "无法获取有效的图片内容。这可能是因为图片URL需要登录才能访问，或者图片URL已经过期。"

                # 根据URL确定MIME类型
                mime_type = "image/jpeg"  # 默认MIME类型
                if image_path.lower().endswith('.png'):
                    mime_type = "image/png"
                elif image_path.lower().endswith(('.jpg', '.jpeg')):
                    mime_type = "image/jpeg"
                elif image_path.lower().endswith('.gif'):
                    mime_type = "image/gif"
                elif image_path.lower().endswith('.webp'):
                    mime_type = "image/webp"

                # 压缩图片，最大大小为800KB
                if len(image_data) > 800 * 1024 and mime_type in ["image/jpeg", "image/png"]:
                    logger.info("图片大小超过800KB，进行压缩")
                    image_data = compress_image(image_data, max_size_kb=800)
                    # 压缩后强制使用JPEG格式
                    mime_type = "image/jpeg"

                # 保存下载的内容到文件，用于调试
                try:
                    with open("downloaded_image.bin", "wb") as f:
                        f.write(image_data)
                    logger.info(f"已保存下载的内容到 downloaded_image.bin，大小: {len(image_data)} 字节")
                except Exception as e:
                    logger.error(f"保存下载内容失败: {str(e)}")

                # 构建图片URL
                image_url = f"data:{mime_type};base64,{base64.b64encode(image_data).decode('utf-8')}"
                logger.info(f"成功下载图片并转换为Base64，长度: {len(image_url)}")

                # 保存Base64编码到文件，用于调试
                try:
                    with open("image_base64.txt", "w") as f:
                        f.write(image_url)
                    logger.info(f"已保存Base64编码到 image_base64.txt，长度: {len(image_url)}")
                except Exception as e:
                    logger.error(f"保存Base64编码失败: {str(e)}")
            except Exception as e:
                logger.error(f"下载图片失败: {str(e)}")
                return f"图片下载失败: {str(e)}"
        elif is_base64:
            # 如果已经是Base64编码，直接使用
            image_url = image_path
            logger.info(f"使用提供的Base64编码图片，长度: {len(image_url)}")
        else:
            # 未知格式
            logger.error(f"未知的图片格式: {image_path}")
            return "未知的图片格式，无法处理"

        # 使用API直接调用
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }

        payload = {
            "model": model,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": image_url
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 1000
        }

        # 发送请求
        endpoint = "/chat/completions"
        api_url = f"{api_base}{endpoint}"

        logger.info(f"发送API请求到: {api_url}")

        # 设置重试次数和超时时间
        max_retries = 3
        timeout_seconds = 120  # 增加到120秒

        # 实现重试机制
        for retry in range(max_retries):
            try:
                logger.info(f"尝试发送请求 (尝试 {retry + 1}/{max_retries})，超时时间: {timeout_seconds}秒")
                response = requests.post(
                    api_url,
                    headers=headers,
                    json=payload,
                    timeout=timeout_seconds
                )
                # 如果请求成功，跳出循环
                break
            except requests.exceptions.Timeout:
                logger.warning(f"请求超时 (尝试 {retry + 1}/{max_retries})")
                if retry < max_retries - 1:
                    # 如果不是最后一次尝试，等待一段时间后重试
                    import time
                    wait_time = 2 ** retry  # 指数退避策略：1, 2, 4秒...
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    # 如果是最后一次尝试，重新抛出异常
                    logger.error("所有重试都失败了")
                    raise
            except Exception as e:
                logger.error(f"请求失败: {str(e)}")
                raise

        # 检查响应状态
        if response.status_code != 200:
            logger.error(f"API请求失败，状态码: {response.status_code}")
            logger.error(f"响应内容: {response.text}")
            return f"图片分析失败，API返回错误: {response.status_code}"

        # 解析响应
        result = response.json()

        # 提取回复内容
        if "choices" in result and len(result["choices"]) > 0:
            if "message" in result["choices"][0] and "content" in result["choices"][0]["message"]:
                content = result["choices"][0]["message"]["content"]
                logger.info(f"API分析成功，响应长度: {len(content)}")
                return content

        logger.error(f"API响应格式异常: {result}")
        return "无法解析API响应，图片分析失败。"
    except Exception as e:
        logger.error(f"图片分析失败: {str(e)}")
        import traceback
        logger.error(f"异常堆栈: {traceback.format_exc()}")
        return f"图片分析过程中发生错误: {str(e)}"
