"""
专门处理CORS预检请求的路由
"""
from fastapi import APIRouter, Request, Response
import logging

logger = logging.getLogger(__name__)
router = APIRouter(tags=["CORS处理"])

@router.options("/{path:path}")
async def handle_options_request(request: Request, path: str):
    """
    处理所有OPTIONS预检请求
    """
    logger.info(f"处理OPTIONS预检请求: /{path}")
    logger.info(f"请求头: {dict(request.headers)}")
    
    # 创建空响应
    response = Response(content="")
    
    # 添加CORS头
    response.headers["Access-Control-Allow-Origin"] = "*"
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization, token, X-Requested-With"
    response.headers["Access-Control-Max-Age"] = "86400"  # 24小时
    
    return response
