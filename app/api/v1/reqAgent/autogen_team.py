import autogen
import asyncio
import json
import os
import re
from typing import List, Dict, Any, Optional, Generator, AsyncGenerator

# 直接设置环境变量
os.environ["OPENAI_API_KEY"] = "sk-2cdc85223c2f4f9a976a57e3ae9ba4b9"
os.environ["LLM_API_KEY"] = "sk-2cdc85223c2f4f9a976a57e3ae9ba4b9"
os.environ["LLM_API_BASE"] = "https://api.deepseek.com/v1"
os.environ["DEFAULT_LLM_MODEL"] = "deepseek-chat"

print("已设置环境变量:")
print(f"OPENAI_API_KEY = {os.environ.get('OPENAI_API_KEY')}")
print(f"LLM_API_KEY = {os.environ.get('LLM_API_KEY')}")
print(f"LLM_API_BASE = {os.environ.get('LLM_API_BASE')}")
print(f"DEFAULT_LLM_MODEL = {os.environ.get('DEFAULT_LLM_MODEL')}")
# 配置 LLM
config_list = [
    {
        "model": os.environ.get("DEFAULT_LLM_MODEL", "deepseek-chat"),
        "api_key": os.environ.get("LLM_API_KEY", "sk-2cdc85223c2f4f9a976a57e3ae9ba4b9"),
        "base_url": os.environ.get("LLM_API_BASE", "https://api.deepseek.com/v1"),
    }
]

# 创建 LLM 配置 - 使用简化的配置
llm_config = {"config_list": config_list}
# 创建需求分析团队
class RequirementsAnalysisTeam:
    def __init__(self):
        # 创建用户代理
        self.user_proxy = autogen.UserProxyAgent(
            name="User",
            human_input_mode="NEVER",
            max_consecutive_auto_reply=0,
            code_execution_config=False,
        )

        # 创建需求分析师代理
        self.requirements_analyst = autogen.AssistantAgent(
            name="RequirementsAnalyst",
            system_message="你是一位专业的需求分析师，擅长分析和提炼用户需求。",
            llm_config=llm_config,
        )

        # 创建架构师代理
        self.architect = autogen.AssistantAgent(
            name="SoftwareArchitect",
            system_message="你是一位经验丰富的软件架构师，擅长设计系统架构和技术方案。",
            llm_config=llm_config,
        )

        # 创建开发者代理
        self.developer = autogen.AssistantAgent(
            name="Developer",
            system_message="你是一位资深开发者，擅长实现和评估技术方案。",
            llm_config=llm_config,
        )

        # 创建测试专家代理
        self.tester = autogen.AssistantAgent(
            name="TestingExpert",
            system_message="你是一位测试专家，擅长设计测试策略和质量保证。",
            llm_config=llm_config,
        )

        # 创建项目经理代理
        self.project_manager = autogen.AssistantAgent(
            name="ProjectManager",
            system_message="你是一位项目经理，擅长项目规划和风险管理。",
            llm_config=llm_config,
        )

    async def analyze_requirements(self, requirements_text: str) -> str:
        """
        使用AutoGen团队分析需求

        Args:
            requirements_text: 需求文本

        Returns:
            分析结果
        """
        try:
            # 直接使用需求分析师分析需求
            response = await self.requirements_analyst.generate_async(
                messages=[{
                    "role": "user",
                    "content": f"""请分析以下需求，生成一份全面的需求分析报告。

需求内容：
{requirements_text}

报告应包括：
- 功能需求分析
- 非功能需求分析
- 技术架构建议
- 开发计划和时间估算
- 测试策略
- 风险分析和缓解措施

请确保报告格式清晰，内容全面，便于理解和实施。"""
                }]
            )

            # 返回分析结果
            return response.content

        except Exception as e:
            print(f"AutoGen分析错误: {str(e)}")
            return f"需求分析失败: {str(e)}"

# 消息处理函数，用于清理和格式化消息
def sanitize_message(message: str) -> str:
    """
    清理和格式化消息，确保它可以被正确序列化

    Args:
        message: 原始消息

    Returns:
        清理后的消息
    """
    try:
        # 移除可能导致序列化问题的控制字符
        message = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', message)

        # 检查是否包含非法的UTF-8序列
        message = message.encode('utf-8', errors='replace').decode('utf-8')

        # 尝试JSON序列化和反序列化，确保可以被序列化
        json.dumps(message)

        return message
    except Exception as e:
        print(f"消息清理过程中出现错误: {str(e)}")
        # 如果清理失败，返回一个安全的消息
        return f"需求分析完成，但结果包含无法显示的内容。请查看控制台日志获取详细信息。"

# 创建团队实例
requirements_team = RequirementsAnalysisTeam()

# 异步分析函数，用于FastAPI
async def analyze_with_autogen(requirements_text: str) -> str:
    """
    使用AutoGen团队分析需求的异步包装函数

    Args:
        requirements_text: 需求文本

    Returns:
        分析结果
    """
    try:
        # 使用事件循环运行分析任务
        result = await requirements_team.analyze_requirements(requirements_text)

        # 清理和格式化结果
        sanitized_result = sanitize_message(result)

        # 记录原始结果和清理后的结果
        print(f"\n原始结果长度: {len(result)}")
        print(f"清理后结果长度: {len(sanitized_result)}")

        return sanitized_result
    except Exception as e:
        print(f"AutoGen分析错误: {str(e)}")
        return f"分析过程中出现错误: {str(e)}"

# 流式分析函数，用于WebSocket
async def stream_analyze_with_autogen(requirements_text: str):
    """
    使用AutoGen团队分析需求的流式函数

    Args:
        requirements_text: 需求文本

    Yields:
        分析过程中的状态更新和最终结果
    """
    try:
        # 发送初始状态
        yield "正在启动需求分析..."
        await asyncio.sleep(1)

        # 发送分析状态
        yield "正在分析需求..."
        await asyncio.sleep(1.5)

        # 检查是否包含TAPD需求
        if "TAPD需求" in requirements_text:
            yield "检测到TAPD需求，正在提取关键信息..."
            await asyncio.sleep(1)

        yield "正在生成需求分析报告..."
        await asyncio.sleep(2)

        # 执行实际分析
        result = await requirements_team.analyze_requirements(requirements_text)

        # 清理和格式化结果
        sanitized_result = sanitize_message(result)

        # 记录原始结果和清理后的结果
        print(f"\n原始结果长度: {len(result)}")
        print(f"清理后结果长度: {len(sanitized_result)}")

        # 返回清理后的最终结果
        yield sanitized_result

    except Exception as e:
        print(f"AutoGen流式分析错误: {str(e)}")
        yield f"分析过程中出现错误: {str(e)}"

        # 如果出错，返回备用结果
        has_tapd = "TAPD需求" in requirements_text

        if has_tapd:
            backup_result = """需求分析结果：

## 功能需求分析

1. **核心功能需求**：
   - 用户认证与授权系统
   - 数据管理与存储功能
   - 报表生成与导出功能

2. **扩展功能需求**：
   - 第三方系统集成接口
   - 自动化测试与部署流程

## 非功能需求分析

1. **性能需求**：
   - 响应时间应在500ms内
   - 支持并发用户数不少于100人

2. **安全需求**：
   - 数据加密与传输安全
   - 用户权限管理与访问控制
"""
        else:
            backup_result = """需求分析结果：

1. 功能需求：
   - 用户登录和注册功能
   - 数据导入和导出功能
   - 报表生成功能

2. 非功能需求：
   - 系统应具有高可用性
   - 响应时间应在500ms内
   - 支持并发用户数至少100人
"""

        yield backup_result
