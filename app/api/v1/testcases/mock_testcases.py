import io
import pandas as pd
from fastapi import APIRouter, Query, HTTPException
from fastapi.responses import StreamingResponse
from app.schemas.base import Success, SuccessExtra
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

# 模拟数据
MOCK_TESTCASES = [
    {
        "id": 1,
        "title": "登录功能测试",
        "desc": "测试用户登录功能",
        "preconditions": "用户已注册",
        "steps": [
            {"description": "输入用户名和密码", "expected_result": "输入框正确显示"},
            {"description": "点击登录按钮", "expected_result": "登录成功，跳转到首页"}
        ],
        "postconditions": "用户成功登录系统",
        "priority": "高",
        "status": "通过",
        "tags": ["登录", "用户"],
        "project_id": 1,
        "project": {"id": 1, "name": "用户中心项目", "desc": "用户中心相关功能"},
        "requirement_id": 1,
        "requirement": {
            "id": 1,
            "name": "用户登录功能",
            "description": "实现用户登录功能，支持账号密码登录和第三方登录",
            "tapd_url": "https://www.tapd.cn/tapd_fe/43702532/story/detail/1143702532001004918"
        },
        "creator": "张三",
        "created_at": "2025-04-25T10:00:00",
        "updated_at": "2025-04-25T10:00:00"
    },
    {
        "id": 2,
        "title": "注册功能测试",
        "desc": "测试用户注册功能",
        "preconditions": "用户未注册",
        "steps": [
            {"description": "输入用户名、密码和确认密码", "expected_result": "输入框正确显示"},
            {"description": "点击注册按钮", "expected_result": "注册成功，跳转到登录页"}
        ],
        "postconditions": "用户成功注册",
        "priority": "高",
        "status": "通过",
        "tags": ["注册", "用户"],
        "project_id": 1,
        "project": {"id": 1, "name": "用户中心项目", "desc": "用户中心相关功能"},
        "requirement_id": 2,
        "requirement": {
            "id": 2,
            "name": "用户注册功能",
            "description": "实现用户注册功能，支持邮箱注册和手机号注册",
            "tapd_url": "https://www.tapd.cn/tapd_fe/43702532/story/detail/1143702532001004919"
        },
        "creator": "张三",
        "created_at": "2025-04-25T10:30:00",
        "updated_at": "2025-04-25T10:30:00"
    },
    {
        "id": 3,
        "title": "修改密码测试",
        "desc": "测试用户修改密码功能",
        "preconditions": "用户已登录",
        "steps": [
            {"description": "进入个人中心", "expected_result": "显示个人信息"},
            {"description": "点击修改密码", "expected_result": "显示修改密码页面"},
            {"description": "输入旧密码和新密码", "expected_result": "输入框正确显示"},
            {"description": "点击确认按钮", "expected_result": "密码修改成功"}
        ],
        "postconditions": "用户密码已修改",
        "priority": "中",
        "status": "通过",
        "tags": ["密码", "用户"],
        "project_id": 1,
        "project": {"id": 1, "name": "用户中心项目", "desc": "用户中心相关功能"},
        "requirement_id": 3,
        "requirement": {
            "id": 3,
            "name": "用户信息修改",
            "description": "实现用户信息修改功能，支持修改用户名、头像、密码等",
            "tapd_url": "https://www.tapd.cn/tapd_fe/43702532/story/detail/1143702532001004920"
        },
        "creator": "李四",
        "created_at": "2025-04-25T11:00:00",
        "updated_at": "2025-04-25T11:00:00"
    }
]


@router.post("/create", summary="创建测试用例")
async def create_testcase(
    case_in: dict,
):
    """
    创建新需求。
    """
    return Success(msg="Created Successfully")


@router.get("/list", summary="查看测试用例列表")
async def list_testcase(
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    project_id: int = Query(None, description="项目名称，用于查询"),
    requirement_id: int = Query(None, description="需求ID，用于查询"),
    requirement_keyword: str = Query(None, description="需求名称或TAPD链接关键词"),
    creator: str = Query(None, description="创建者名称"),
):
    """
    获取测试用例列表。
    支持按项目、需求ID、需求名称、TAPD链接或创建者进行查询。
    """
    # 过滤模拟数据
    filtered_data = MOCK_TESTCASES
    if project_id is not None:
        filtered_data = [item for item in filtered_data if item["project_id"] == project_id]
    if requirement_id is not None:
        filtered_data = [item for item in filtered_data if item["requirement_id"] == requirement_id]

    # 如果提供了需求关键词，则查询相关需求的测试用例
    if requirement_keyword:
        filtered_data = [item for item in filtered_data if 
                         (item["requirement"] and 
                          (requirement_keyword.lower() in item["requirement"]["name"].lower() or 
                           (item["requirement"]["tapd_url"] and requirement_keyword.lower() in item["requirement"]["tapd_url"].lower())))]

    # 如果提供了创建者名称，则查询该创建者的测试用例
    if creator:
        filtered_data = [item for item in filtered_data if 
                         item["creator"] and creator.lower() in item["creator"].lower()]

    # 分页
    total = len(filtered_data)
    start_idx = (page - 1) * page_size
    end_idx = min(start_idx + page_size, total)
    paged_data = filtered_data[start_idx:end_idx]

    return SuccessExtra(data=paged_data, total=total, page=page, page_size=page_size)


@router.get("/get", summary="查看测试用例详情")
async def get_testcase(
    id: str = Query(..., description="用例ID"),
):
    """
    获取单个用例详情。
    """
    # 查找对应ID的测试用例
    case_obj = next((item for item in MOCK_TESTCASES if str(item["id"]) == id), None)
    if not case_obj:
        raise HTTPException(status_code=404, detail="测试用例不存在")
    
    return Success(data=case_obj)


@router.put("/update", summary="更新用例")
async def update_testcase(
    case_in: dict
):
    """
    更新需求信息。
    """
    return Success(msg="Update Successfully")


@router.delete("/delete", summary="删除用例")
async def delete_testcase(
    case_id: int = Query(..., description="用例ID")
):
    """
    删除需求。
    """
    return Success(msg="Deleted Successfully")


@router.get("/fix-tags", summary="修复测试用例标签")
async def fix_testcase_tags():
    """
    修复所有标签值不在枚举范围内的测试用例。
    """
    return SuccessExtra(data={"fixed_count": 0}, msg="Fix Tags Successfully")


@router.get("/invalid-tags", summary="获取无效标签的测试用例")
async def get_invalid_testcase_tags():
    """
    获取所有标签值不在枚举范围内的测试用例。
    """
    return SuccessExtra(data=[], msg="Get Invalid Tags Successfully")


@router.get("/export/excel", summary="导出测试用例到Excel", response_class=StreamingResponse)
async def export_testcases_to_excel(
    page: int = Query(1, description="页码"),
    page_size: int = Query(1000, description="每页数量，导出时默认较大"),
    project_id: int = Query(None, description="项目名称，用于查询"),
    requirement_id: int = Query(None, description="需求ID，用于查询"),
    requirement_keyword: str = Query(None, description="需求名称或TAPD链接关键词"),
    creator: str = Query(None, description="创建者名称"),
):
    """
    导出测试用例到Excel文件。
    支持按项目、需求ID、需求名称、TAPD链接或创建者进行筛选。
    """
    try:
        # 过滤模拟数据
        filtered_data = MOCK_TESTCASES
        if project_id is not None:
            filtered_data = [item for item in filtered_data if item["project_id"] == project_id]
        if requirement_id is not None:
            filtered_data = [item for item in filtered_data if item["requirement_id"] == requirement_id]

        # 如果提供了需求关键词，则查询相关需求的测试用例
        if requirement_keyword:
            filtered_data = [item for item in filtered_data if 
                            (item["requirement"] and 
                            (requirement_keyword.lower() in item["requirement"]["name"].lower() or 
                            (item["requirement"]["tapd_url"] and requirement_keyword.lower() in item["requirement"]["tapd_url"].lower())))]

        # 如果提供了创建者名称，则查询该创建者的测试用例
        if creator:
            filtered_data = [item for item in filtered_data if 
                            item["creator"] and creator.lower() in item["creator"].lower()]

        # 处理数据
        data = []
        for item in filtered_data:
            processed_item = {
                "id": item["id"],
                "title": item["title"],
                "desc": item["desc"],
                "preconditions": item["preconditions"],
                "postconditions": item["postconditions"],
                "priority": item["priority"],
                "status": item["status"],
                "tags": ",".join(item["tags"]) if isinstance(item["tags"], list) else item["tags"],
                "project_name": item["project"]["name"] if item["project"] else "",
                "requirement_name": item["requirement"]["name"] if item["requirement"] else "",
                "requirement_tapd_url": item["requirement"]["tapd_url"] if item["requirement"] else "",
                "creator": item["creator"],
                "created_at": item["created_at"]
            }
            
            # 处理测试步骤
            steps = item.get("steps", [])
            if steps:
                # 将步骤合并为一个字符串
                steps_str = ""
                for i, step in enumerate(steps):
                    steps_str += f"{i+1}. {step.get('description', '')}\n"
                processed_item["steps_text"] = steps_str

                # 将预期结果合并为一个字符串
                expected_results_str = ""
                for i, step in enumerate(steps):
                    expected_results_str += f"{i+1}. {step.get('expected_result', '')}\n"
                processed_item["expected_results_text"] = expected_results_str
            else:
                processed_item["steps_text"] = ""
                processed_item["expected_results_text"] = ""
            
            data.append(processed_item)

        # 创建DataFrame
        df = pd.DataFrame(data)

        # 选择要导出的列并重命名
        columns_mapping = {
            "id": "用例ID",
            "title": "用例标题",
            "desc": "用例描述",
            "preconditions": "前置条件",
            "steps_text": "测试步骤",
            "expected_results_text": "预期结果",
            "postconditions": "后置条件",
            "priority": "优先级",
            "status": "状态",
            "tags": "标签",
            "project_name": "项目名称",
            "requirement_name": "需求名称",
            "requirement_tapd_url": "TAPD链接",
            "creator": "创建者",
            "created_at": "创建时间"
        }

        # 选择并重命名列
        export_columns = [col for col in columns_mapping.keys() if col in df.columns]
        df = df[export_columns]
        df = df.rename(columns=columns_mapping)

        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='测试用例', index=False)

            # 获取xlsxwriter工作簿和工作表对象
            workbook = writer.book
            worksheet = writer.sheets['测试用例']

            # 设置列宽
            for i, col in enumerate(df.columns):
                # 计算列宽度（根据内容长度）
                max_len = max(
                    df[col].astype(str).map(len).max(),  # 最长数据
                    len(col)  # 标题长度
                ) + 2  # 额外空间

                # 限制最大宽度
                col_width = min(max_len, 50)
                worksheet.set_column(i, i, col_width)

            # 添加自动筛选
            worksheet.autofilter(0, 0, len(df), len(df.columns) - 1)

            # 设置标题行格式
            header_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'top',
                'fg_color': '#D7E4BC',
                'border': 1
            })

            # 写入标题行
            for col_num, value in enumerate(df.columns.values):
                worksheet.write(0, col_num, value, header_format)

        # 设置文件指针到开始
        output.seek(0)

        # 生成文件名
        filename = "测试用例导出.xlsx"

        # 返回Excel文件
        return StreamingResponse(
            output,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except Exception as e:
        logger.error(f"导出测试用例失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"导出测试用例失败: {str(e)}")
