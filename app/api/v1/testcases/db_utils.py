"""
数据库工具函数，用于手动处理测试用例标签。
"""
import logging
from tortoise.expressions import Q
from app.models.admin import TestCase
from app.models.enums import TestCaseTag

logger = logging.getLogger(__name__)

async def validate_and_convert_tag(tag_value: str) -> str:
    """
    验证并转换测试用例标签。
    如果标签值不在枚举范围内，则转换为默认值。
    """
    valid_tags = TestCaseTag.get_member_values()
    if tag_value in valid_tags:
        return tag_value
    
    # 标签值不在枚举范围内，转换为默认值
    logger.warning(f"标签值 '{tag_value}' 不在枚举范围内，转换为默认值 '功能测试'")
    return TestCaseTag.FUNCTIONAL_TEST.value

async def update_testcase_tag(testcase_id: int, tag_value: str) -> bool:
    """
    更新测试用例标签。
    """
    try:
        # 验证并转换标签值
        valid_tag = await validate_and_convert_tag(tag_value)
        
        # 更新测试用例标签
        testcase = await TestCase.get(id=testcase_id)
        testcase.tags = valid_tag
        await testcase.save()
        
        logger.info(f"测试用例 {testcase_id} 的标签已更新为 '{valid_tag}'")
        return True
    except Exception as e:
        logger.error(f"更新测试用例 {testcase_id} 的标签时出错：{str(e)}")
        return False

async def get_all_testcases_with_invalid_tags() -> list:
    """
    获取所有标签值不在枚举范围内的测试用例。
    """
    try:
        valid_tags = TestCaseTag.get_member_values()
        
        # 获取所有测试用例
        all_testcases = await TestCase.all()
        
        # 筛选出标签值不在枚举范围内的测试用例
        invalid_testcases = []
        for testcase in all_testcases:
            if testcase.tags not in valid_tags:
                invalid_testcases.append({
                    "id": testcase.id,
                    "title": testcase.title,
                    "tags": testcase.tags
                })
        
        return invalid_testcases
    except Exception as e:
        logger.error(f"获取标签值不在枚举范围内的测试用例时出错：{str(e)}")
        return []

async def fix_all_invalid_tags() -> dict:
    """
    修复所有标签值不在枚举范围内的测试用例。
    """
    try:
        invalid_testcases = await get_all_testcases_with_invalid_tags()
        
        success_count = 0
        failed_count = 0
        
        for testcase in invalid_testcases:
            success = await update_testcase_tag(testcase["id"], TestCaseTag.FUNCTIONAL_TEST.value)
            if success:
                success_count += 1
            else:
                failed_count += 1
        
        return {
            "total": len(invalid_testcases),
            "success": success_count,
            "failed": failed_count
        }
    except Exception as e:
        logger.error(f"修复标签值不在枚举范围内的测试用例时出错：{str(e)}")
        return {
            "total": 0,
            "success": 0,
            "failed": 0,
            "error": str(e)
        }
