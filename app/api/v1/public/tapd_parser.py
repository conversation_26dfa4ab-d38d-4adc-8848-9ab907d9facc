from fastapi import APIRouter, HTTPException, status, Request
from pydantic import BaseModel
from typing import Optional, Dict, Any
import logging
import re
from bs4 import BeautifulSoup

logger = logging.getLogger(__name__)
router = APIRouter(tags=["公开TAPD解析API"])

class TapdContentResponse(BaseModel):
    status: str
    message: str
    data: Optional[Dict[str, Any]] = None

@router.post("/tapd-parse", response_model=TapdContentResponse)
async def parse_tapd_content(request: Request):
    """
    解析TAPD内容并提取关键信息（公开API，无需认证）
    """
    try:
        # 获取原始请求数据
        body = await request.body()
        body_str = body.decode('utf-8')
        logger.info(f"接收到TAPD内容原始数据: {body_str[:100]}...")
        
        # 尝试解析JSON
        try:
            data = await request.json()
            logger.info(f"解析后JSON: {data}")
        except Exception as e:
            logger.error(f"JSON解析错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"JSON解析错误: {str(e)}"
            )
        
        # 提取必要字段
        url = data.get('url', '')
        title = data.get('title', '')
        content = data.get('content', '')
        
        # 记录请求信息
        logger.info(f"接收到TAPD内容: {url}, 标题: {title[:30]}...")
        
        # 解析TAPD内容
        requirement_info = parse_requirement(url, title, content)
        
        # 返回成功响应
        return TapdContentResponse(
            status="success",
            message="TAPD内容已成功解析",
            data=requirement_info
        )
    
    except Exception as e:
        logger.error(f"解析TAPD内容失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"解析TAPD内容失败: {str(e)}"
        )

def parse_requirement(url: str, title: str, content: str) -> Dict[str, Any]:
    """
    解析TAPD内容，提取需求信息
    """
    # 提取TAPD ID
    tapd_id = ""
    id_match = re.search(r'/(\d+)$', url)
    if id_match:
        tapd_id = id_match.group(1)
    
    # 使用BeautifulSoup解析HTML内容
    soup = BeautifulSoup(content, 'html.parser')
    
    # 初始化结果
    result = {
        "tapd_id": tapd_id,
        "name": title,
        "content": "",
        "handler": "",
        "developer": "",
        "tester": "",
        "url": url,
        "status": "",
        "priority": ""
    }
    
    try:
        # 尝试提取需求内容
        content_div = soup.find('div', class_='story-content')
        if content_div:
            result["content"] = content_div.get_text(strip=True)
        else:
            # 尝试其他可能的内容选择器
            desc_div = soup.find('div', class_='description-content')
            if desc_div:
                result["content"] = desc_div.get_text(strip=True)
            else:
                # 如果找不到特定的内容区域，使用整个HTML内容
                result["content"] = soup.get_text(strip=True)
        
        # 尝试提取处理人、开发人员、测试人员等信息
        # 这些信息通常在表格或特定的div中
        
        # 查找所有表格
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all('td')
                if len(cells) >= 2:
                    field_name = cells[0].get_text(strip=True)
                    field_value = cells[1].get_text(strip=True)
                    
                    if '处理人' in field_name:
                        result["handler"] = field_value
                    elif '开发人员' in field_name:
                        result["developer"] = field_value
                    elif '测试人员' in field_name:
                        result["tester"] = field_value
                    elif '状态' in field_name:
                        result["status"] = field_value
                    elif '优先级' in field_name:
                        result["priority"] = field_value
        
        # 如果表格中没有找到，尝试查找特定的div或span
        field_divs = soup.find_all('div', class_='field')
        for div in field_divs:
            label = div.find('div', class_='field-label')
            value = div.find('div', class_='field-value')
            if label and value:
                field_name = label.get_text(strip=True)
                field_value = value.get_text(strip=True)
                
                if '处理人' in field_name:
                    result["handler"] = field_value
                elif '开发人员' in field_name:
                    result["developer"] = field_value
                elif '测试人员' in field_name:
                    result["tester"] = field_value
                elif '状态' in field_name:
                    result["status"] = field_value
                elif '优先级' in field_name:
                    result["priority"] = field_value
    
    except Exception as e:
        logger.error(f"解析需求内容时出错: {str(e)}")
        # 即使解析出错，也返回基本信息
    
    return result
