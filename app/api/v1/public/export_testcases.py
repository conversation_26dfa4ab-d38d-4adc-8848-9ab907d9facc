import io
import pandas as pd
from fastapi import APIRouter, Query, HTTPException
from fastapi.responses import StreamingResponse
from app.controllers.testcase import testcase_controller
from app.controllers.requirement import requirement_controller
from app.controllers.project import project_controller
from tortoise.expressions import Q
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/testcases/excel", summary="导出测试用例到Excel", response_class=StreamingResponse)
async def export_testcases_to_excel(
    page: int = Query(1, description="页码"),
    page_size: int = Query(1000, description="每页数量，导出时默认较大"),
    project_id: int = Query(None, description="项目名称，用于查询"),
    requirement_id: int = Query(None, description="需求ID，用于查询"),
    requirement_keyword: str = Query(None, description="需求名称或TAPD链接关键词"),
    creator: str = Query(None, description="创建者名称"),
):
    """
    导出测试用例到Excel文件。
    支持按项目、需求ID、需求名称、TAPD链接或创建者进行筛选。
    """
    try:
        # 构建查询条件，与list_testcase相同
        q = Q()
        if project_id is not None:
            q &= Q(project_id=project_id)
        if requirement_id is not None:
            q &= Q(requirement_id=requirement_id)

        # 如果提供了需求关键词，则查询相关需求的测试用例
        if requirement_keyword:
            # 首先查询匹配关键词的需求
            req_query = Q(name__icontains=requirement_keyword) | Q(tapd_url__icontains=requirement_keyword)
            matching_reqs = await requirement_controller.model.filter(req_query).values('id')
            matching_req_ids = [req['id'] for req in matching_reqs]

            if matching_req_ids:
                # 如果找到匹配的需求，则查询这些需求的测试用例
                q &= Q(requirement_id__in=matching_req_ids)
            else:
                # 如果没有找到匹配的需求，则直接查询测试用例的TAPD URL
                q &= Q(tapd_url__icontains=requirement_keyword)

        # 如果提供了创建者名称，则查询该创建者的测试用例
        if creator:
            q &= Q(creator__icontains=creator)

        # 调用控制器的 list 方法
        result = await testcase_controller.list(page=page, page_size=page_size, search=q)
        total_count = result[0]
        testcase_objs = result[1]
        
        # 处理数据，与list_testcase相同
        data = []
        for obj in testcase_objs:
            item = await obj.to_dict()
            
            # 替换 project_id 为项目名称
            project_id = item.pop("project_id", None)
            try:
                if project_id:
                    project = await project_controller.get(id=project_id)
                    item["project_name"] = project.name
                else:
                    item["project_name"] = ""
            except Exception as e:
                logger.error(f"Error fetching project with id {project_id}: {str(e)}")
                item["project_name"] = ""

            # 替换 requirement_id 为需求详情
            requirement_id = item.pop("requirement_id", None)
            try:
                if requirement_id:
                    requirement = await requirement_controller.get(id=requirement_id)
                    requirement_dict = await requirement.to_dict()
                    item["requirement_name"] = requirement_dict.get("name", "")
                    item["requirement_tapd_url"] = requirement_dict.get("tapd_url", "")
                else:
                    item["requirement_name"] = ""
                    item["requirement_tapd_url"] = ""
            except Exception as e:
                logger.error(f"Error fetching requirement with id {requirement_id}: {str(e)}")
                item["requirement_name"] = ""
                item["requirement_tapd_url"] = ""
                
            # 处理测试步骤
            steps = item.get("steps", [])
            if steps:
                # 将步骤合并为一个字符串
                steps_str = ""
                for i, step in enumerate(steps):
                    steps_str += f"{i+1}. {step.get('description', '')}\n"
                item["steps_text"] = steps_str
                
                # 将预期结果合并为一个字符串
                expected_results_str = ""
                for i, step in enumerate(steps):
                    expected_results_str += f"{i+1}. {step.get('expected_result', '')}\n"
                item["expected_results_text"] = expected_results_str
            else:
                item["steps_text"] = ""
                item["expected_results_text"] = ""
                
            data.append(item)

        # 创建DataFrame
        df = pd.DataFrame(data)
        
        # 选择要导出的列并重命名
        columns_mapping = {
            "id": "用例ID",
            "title": "用例标题",
            "desc": "用例描述",
            "preconditions": "前置条件",
            "steps_text": "测试步骤",
            "expected_results_text": "预期结果",
            "postconditions": "后置条件",
            "priority": "优先级",
            "status": "状态",
            "tags": "标签",
            "project_name": "项目名称",
            "requirement_name": "需求名称",
            "requirement_tapd_url": "TAPD链接",
            "creator": "创建者",
            "created_at": "创建时间"
        }
        
        # 选择并重命名列
        export_columns = [col for col in columns_mapping.keys() if col in df.columns]
        df = df[export_columns]
        df = df.rename(columns=columns_mapping)
        
        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='测试用例', index=False)
            
            # 获取xlsxwriter工作簿和工作表对象
            workbook = writer.book
            worksheet = writer.sheets['测试用例']
            
            # 设置列宽
            for i, col in enumerate(df.columns):
                # 计算列宽度（根据内容长度）
                max_len = max(
                    df[col].astype(str).map(len).max(),  # 最长数据
                    len(col)  # 标题长度
                ) + 2  # 额外空间
                
                # 限制最大宽度
                col_width = min(max_len, 50)
                worksheet.set_column(i, i, col_width)
            
            # 添加自动筛选
            worksheet.autofilter(0, 0, len(df), len(df.columns) - 1)
            
            # 设置标题行格式
            header_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'top',
                'fg_color': '#D7E4BC',
                'border': 1
            })
            
            # 写入标题行
            for col_num, value in enumerate(df.columns.values):
                worksheet.write(0, col_num, value, header_format)
        
        # 设置文件指针到开始
        output.seek(0)
        
        # 生成文件名
        filename = "测试用例导出.xlsx"
        
        # 返回Excel文件
        return StreamingResponse(
            output,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except Exception as e:
        logger.error(f"导出测试用例失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"导出测试用例失败: {str(e)}")
