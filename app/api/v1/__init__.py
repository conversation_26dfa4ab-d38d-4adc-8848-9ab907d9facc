from fastapi import APIRouter, Request
from fastapi.responses import RedirectResponse

from app.core.dependency import Depend<PERSON>er<PERSON>son

from .apis import apis_router
from .auditlog import auditlog_router
from .auth import auth_router
from .base import base_router
from .depts import depts_router
from .menus import menus_router
from .roles import roles_router
from .users import users_router
from .projects import projects_router
from .requirements import requirements_router
from .testcases import testcases_router
from .agent import agent_router
from .requirements.public_api import router as public_api_router
from .reqAgent import reqAgent_router
from .public import public_router
from .knowledge import router as knowledge_router
from .system import system_router
from .dashboard import dashboard_router

v1_router = APIRouter()

# 添加一个路由来处理重复的路径
@v1_router.get("/api/v1/{path:path}")
async def handle_duplicate_path(request: Request, path: str):
    """
    处理重复的路径，将 /api/v1/api/v1/xxx 重定向到 /api/v1/xxx
    """
    # 构建正确的URL
    correct_url = f"/api/v1/{path}"
    print(f"重定向请求: {request.url.path} -> {correct_url}")
    return RedirectResponse(url=correct_url)

v1_router.include_router(auth_router, prefix="/auth")
v1_router.include_router(base_router, prefix="/base")
v1_router.include_router(users_router, prefix="/user", dependencies=[DependPermisson])
v1_router.include_router(roles_router, prefix="/role", dependencies=[DependPermisson])
v1_router.include_router(menus_router, prefix="/menu", dependencies=[DependPermisson])
v1_router.include_router(apis_router, prefix="/api", dependencies=[DependPermisson])
v1_router.include_router(depts_router, prefix="/dept", dependencies=[DependPermisson])
v1_router.include_router(projects_router, prefix="/project", dependencies=[DependPermisson])
v1_router.include_router(requirements_router, prefix="/requirement", dependencies=[DependPermisson])
v1_router.include_router(testcases_router, prefix="/testcase", dependencies=[DependPermisson])
v1_router.include_router(auditlog_router, prefix="/auditlog", dependencies=[DependPermisson])
v1_router.include_router(agent_router, prefix="/agent")    #, dependencies=[DependPermisson])
v1_router.include_router(knowledge_router, prefix="/knowledge", dependencies=[DependPermisson])
v1_router.include_router(public_api_router, prefix="/public")
v1_router.include_router(reqAgent_router, prefix="/reqAgent")
v1_router.include_router(public_router, prefix="/public")
v1_router.include_router(system_router, prefix="/system", dependencies=[DependPermisson])
v1_router.include_router(dashboard_router, prefix="/dashboard", dependencies=[DependPermisson])
