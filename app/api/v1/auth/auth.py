from fastapi import APIRouter
from fastapi.responses import JSONResponse

from app.controllers.user import user_controller
from app.models.admin import User
from app.schemas.login import CredentialsSchema, JWTOut, JWTPayload
from app.utils.jwt_util import create_access_token

router = APIRouter()


@router.post("/login", summary="用户登录")
async def login(credentials: CredentialsSchema):
    """
    处理用户登录请求，与 /api/v1/base/access_token 功能相同
    但直接返回包含access_token的对象，而不是包装在Success响应中
    """
    user: User = await user_controller.authenticate(credentials)

    # 生成 JWT token
    access_token = create_access_token(
        data=JWTPayload(
            user_id=user.id,
            username=user.username,
        )
    )
    data = JWTOut(
        access_token=access_token,
        username=user.username,
    )
    # 直接返回包含access_token的对象，而不是包装在Success响应中
    return JSONResponse(content=data.model_dump())
