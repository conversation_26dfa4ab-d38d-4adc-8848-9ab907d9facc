from fastapi import APIRouter, Request
import logging

logger = logging.getLogger(__name__)
router = APIRouter(tags=["调试路由"])

@router.get("/debug")
async def debug_route(request: Request):
    """
    调试路由，用于检查请求路径
    """
    logger.info(f"收到调试请求: {request.url}")
    logger.info(f"请求方法: {request.method}")
    logger.info(f"请求头: {request.headers}")

    return {
        "status": "success",
        "message": "调试路由正常工作",
        "path": str(request.url),
        "method": request.method
    }

@router.post("/echo")
async def echo_post(request: Request):
    """
    回显POST请求内容
    """
    try:
        body = await request.body()
        body_str = body.decode('utf-8')
        logger.info(f"收到POST请求: {request.url}")
        logger.info(f"请求体原始数据: {body_str}")

        # 尝试解析JSON
        try:
            json_body = await request.json()
            logger.info(f"解析后JSON: {json_body}")
        except Exception as e:
            logger.error(f"JSON解析错误: {str(e)}")
            json_body = {"error": "Invalid JSON"}

        # 获取请求头
        headers = dict(request.headers)
        logger.info(f"请求头: {headers}")

        return {
            "status": "success",
            "message": "POST请求已接收",
            "path": str(request.url),
            "method": request.method,
            "headers": headers,
            "body_raw": body_str,
            "body_json": json_body
        }
    except Exception as e:
        logger.error(f"处理请求时出错: {str(e)}")
        return {
            "status": "error",
            "message": f"处理请求时出错: {str(e)}"
        }
