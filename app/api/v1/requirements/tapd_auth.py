from fastapi import APIRouter, HTTPException, status, Request
from pydantic import BaseModel
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)
router = APIRouter(tags=["TAPD认证处理"])

class TapdAuthRequest(BaseModel):
    url: str
    cookies: Optional[Dict[str, str]] = None
    localStorage: Optional[Dict[str, Any]] = None
    sessionStorage: Optional[Dict[str, Any]] = None

class TapdAuthResponse(BaseModel):
    status: str
    message: str
    data: Optional[dict] = None

@router.post("/auth", response_model=TapdAuthResponse)
async def receive_tapd_auth(request: Request):
    """
    接收从Chrome插件发送的TAPD认证信息
    """
    try:
        # 获取原始请求数据
        body = await request.body()
        body_str = body.decode('utf-8')
        logger.info(f"接收到TAPD认证信息原始数据: {body_str[:100]}...")
        
        # 尝试解析JSON
        try:
            data = await request.json()
            logger.info(f"解析后JSON: {data}")
        except Exception as e:
            logger.error(f"JSON解析错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"JSON解析错误: {str(e)}"
            )
        
        # 提取必要字段
        url = data.get('url', '')
        cookies = data.get('cookies', {})
        localStorage = data.get('localStorage', {})
        sessionStorage = data.get('sessionStorage', {})
        
        # 记录请求信息
        logger.info(f"接收到TAPD认证信息: {url}")
        logger.info(f"Cookie数量: {len(cookies)}")
        logger.info(f"Local Storage项数: {len(localStorage)}")
        logger.info(f"Session Storage项数: {len(sessionStorage)}")
        
        # 这里可以添加更多的处理逻辑，比如：
        # 1. 保存认证信息到数据库
        # 2. 使用认证信息访问TAPD API
        # 3. 提取更多的TAPD数据等
        
        # 返回成功响应
        return TapdAuthResponse(
            status="success",
            message="TAPD认证信息已成功接收",
            data={
                "url": url,
                "cookiesCount": len(cookies),
                "localStorageCount": len(localStorage),
                "sessionStorageCount": len(sessionStorage)
            }
        )
    
    except Exception as e:
        logger.error(f"处理TAPD认证信息失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理TAPD认证信息失败: {str(e)}"
        )
