from fastapi import APIRouter, HTTPException, status, Request
from pydantic import BaseModel
from typing import Optional
import logging

logger = logging.getLogger(__name__)
router = APIRouter(tags=["TAPD内容处理"])

class TapdContentRequest(BaseModel):
    url: str
    title: str
    content: str
    cookies: Optional[str] = None
    user_id: int
    project_id: int

class TapdContentResponse(BaseModel):
    status: str
    message: str
    data: Optional[dict] = None

@router.post("/content", response_model=TapdContentResponse)
async def receive_tapd_content(request: Request):
    """
    接收从Chrome插件发送的TAPD内容
    """
    try:
        # 获取原始请求数据
        body = await request.body()
        body_str = body.decode('utf-8')
        logger.info(f"接收到TAPD内容原始数据: {body_str[:100]}...")

        # 尝试解析JSON
        try:
            data = await request.json()
            logger.info(f"解析后JSON: {data}")
        except Exception as e:
            logger.error(f"JSON解析错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"JSON解析错误: {str(e)}"
            )

        # 提取必要字段
        url = data.get('url', '')
        title = data.get('title', '')
        content = data.get('content', '')
        cookies = data.get('cookies', '')
        user_id = data.get('user_id', 1)
        project_id = data.get('project_id', 2)

        # 记录请求信息
        logger.info(f"接收到TAPD内容: {url}, 标题: {title[:30]}...")

        # 这里可以添加更多的处理逻辑，比如：
        # 1. 保存到数据库
        # 2. 进行内容分析
        # 3. 提取关键信息等

        # 返回成功响应
        return TapdContentResponse(
            status="success",
            message="TAPD内容已成功接收",
            data={
                "url": url,
                "title": title,
                "content_length": len(content)
            }
        )

    except Exception as e:
        logger.error(f"处理TAPD内容失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理TAPD内容失败: {str(e)}"
        )
