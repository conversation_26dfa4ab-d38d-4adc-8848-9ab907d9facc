from fastapi import APIRouter, HTTPException, status
import httpx
from bs4 import BeautifulSoup
import logging
from typing import Optional
from pydantic import BaseModel, HttpUrl
import asyncio
from app.settings.config import settings  # 修正导入路径

logger = logging.getLogger(__name__)
router = APIRouter(tags=["URL需求处理"])

class URLContentRequest(BaseModel):
    url: str  # 改为str类型，不使用HttpUrl验证，以支持更多类型的URL
    user_id: int
    project_id: int

class URLContentResponse(BaseModel):
    title: str
    content: str
    status: str

async def fetch_url_content(url: str, headers: Optional[dict] = None) -> tuple[str, str]:
    """
    获取URL内容
    :param url: 目标URL
    :param headers: 请求头
    :return: (标题, 内容)
    """
    # 验证URL
    if not url or not isinstance(url, str):
        logger.error(f"URL无效: {url}, 类型: {type(url)}")
        raise ValueError(f"URL无效: {url}")

    # 确保URL有正确的协议前缀
    if not url.startswith('http://') and not url.startswith('https://'):
        url = 'https://' + url

    # 检查是否是TAPD链接
    is_tapd_url = 'tapd.cn' in url
    if is_tapd_url:
        logger.info(f"检测到TAPD链接: {url}")

    logger.info(f"开始获取URL内容: {url}")

    try:
        default_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        if headers:
            default_headers.update(headers)

        async with httpx.AsyncClient(timeout=30.0, follow_redirects=True) as client:
            logger.debug(f"发送HTTP请求到: {url}")
            response = await client.get(url, headers=default_headers)
            response.raise_for_status()

            logger.debug(f"收到响应: {response.status_code}, 内容长度: {len(response.text)}")

            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # 获取标题
            title = soup.title.string if soup.title else "无标题"
            logger.debug(f"提取到标题: {title}")

            # 检查是否是登录页面
            if is_tapd_url and ('登录' in title or 'login' in title.lower()):
                logger.warning(f"TAPD链接需要登录: {url}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="TAPD链接需要登录，无法自动获取内容。请手动复制需求内容。"
                )

            # 获取正文内容
            # 移除script和style标签
            for script in soup(["script", "style"]):
                script.decompose()

            # 获取文本内容
            text = soup.get_text()

            # 清理文本
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)

            # 截取内容长度，防止过大
            if len(text) > 50000:
                text = text[:50000] + "... [内容过长已截断]"
                logger.warning(f"内容过长，已截断到 50000 字符")

            logger.info(f"成功获取URL内容: {url}, 内容长度: {len(text)}")
            return title, text

    except Exception as e:
        logger.error(f"获取URL内容失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取URL内容失败: {str(e)}"
        )

@router.post("/fetch", response_model=URLContentResponse)
async def fetch_url(request: URLContentRequest):
    """
    获取URL内容并返回处理后的数据
    """
    try:
        # 记录请求信息
        logger.info(f"接收到URL获取请求: {request.url}, 类型: {type(request.url)}, 用户ID: {request.user_id}, 项目ID: {request.project_id}")

        # 确保URL是字符串类型
        url_str = str(request.url)
        logger.info(f"处理后的URL: {url_str}")

        # 检查是否是TAPD链接
        if 'tapd.cn' in url_str:
            logger.info(f"检测到TAPD链接，直接返回401状态码")
            # 对于TAPD链接，直接返回401状态码，避免尝试获取内容
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="TAPD链接需要登录，无法自动获取内容。请手动复制需求内容。"
            )

        # 获取URL内容
        title, content = await fetch_url_content(url_str)

        # 这里可以添加更多的处理逻辑，比如：
        # 1. 保存到数据库
        # 2. 进行内容分析
        # 3. 提取关键信息等

        response = URLContentResponse(
            title=title or "无标题",  # 确保标题不为空
            content=content or "",  # 确保内容不为空
            status="success"
        )

        logger.info(f"成功处理URL请求: {url_str}, 标题: {title[:30] if title else '无标题'}...")
        return response

    except httpx.HTTPStatusError as e:
        # HTTP状态错误（如404、4xx、5xx等）
        status_code = e.response.status_code
        error_msg = f"HTTP错误 ({status_code}): 无法访问该URL"
        logger.error(f"{error_msg}: {request.url}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except httpx.RequestError as e:
        # 请求错误（如DNS解析失败、连接超时等）
        error_msg = f"请求错误: 无法连接到目标服务器"
        logger.error(f"{error_msg}: {request.url} - {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except Exception as e:
        # 其他未预期的错误
        error_msg = f"处理URL请求失败"
        logger.error(f"{error_msg}: {request.url} - {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"{error_msg}: {str(e)}"
        )
