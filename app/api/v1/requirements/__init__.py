from fastapi import APIRouter

# 使用真实实现替代模拟实现
from .requirements import router
from .requirements_url import router as url_router
from .tapd_content import router as tapd_router
from .debug_route import router as debug_router
from .tapd_auth import router as auth_router

requirements_router = APIRouter()
requirements_router.include_router(router, tags=["需求模块"])
requirements_router.include_router(url_router, prefix="/url", tags=["需求URL处理"])
requirements_router.include_router(tapd_router, prefix="/tapd", tags=["需求TAPD处理"])
requirements_router.include_router(debug_router, prefix="/debug", tags=["调试路由"])
requirements_router.include_router(auth_router, prefix="/tapd", tags=["需求TAPD认证"])

__all__ = ["requirements_router"]
