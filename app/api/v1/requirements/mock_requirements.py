from fastapi import APIRouter, Query, status
from app.schemas.base import Success, SuccessExtra

router = APIRouter(tags=["需求管理"])

# 模拟数据
MOCK_DATA = [
    {
        "id": 1,
        "name": "用户登录功能",
        "description": "实现用户登录功能，支持账号密码登录和第三方登录",
        "category": "功能",
        "parent": None,
        "module": "用户模块",
        "level": "高",
        "reviewer": "张三",
        "keywords": "登录,用户,认证",
        "estimate": 8,
        "criteria": "1. 用户可以使用账号密码登录\n2. 用户可以使用第三方账号登录\n3. 登录成功后跳转到首页",
        "remark": "需要支持微信、QQ、微博登录",
        "project_id": 1,
        "project": {"id": 1, "name": "用户中心项目", "desc": "用户中心相关功能"},
        "tapd_url": None,
        "created_at": "2025-04-25T10:00:00",
        "updated_at": "2025-04-25T10:00:00"
    },
    {
        "id": 2,
        "name": "用户注册功能",
        "description": "实现用户注册功能，支持邮箱注册和手机号注册",
        "category": "功能",
        "parent": None,
        "module": "用户模块",
        "level": "高",
        "reviewer": "张三",
        "keywords": "注册,用户,邮箱,手机",
        "estimate": 8,
        "criteria": "1. 用户可以使用邮箱注册\n2. 用户可以使用手机号注册\n3. 注册成功后跳转到登录页",
        "remark": "需要支持邮箱验证和手机验证码",
        "project_id": 1,
        "project": {"id": 1, "name": "用户中心项目", "desc": "用户中心相关功能"},
        "tapd_url": None,
        "created_at": "2025-04-25T10:30:00",
        "updated_at": "2025-04-25T10:30:00"
    },
    {
        "id": 3,
        "name": "用户信息修改",
        "description": "实现用户信息修改功能，支持修改用户名、头像、密码等",
        "category": "功能",
        "parent": None,
        "module": "用户模块",
        "level": "中",
        "reviewer": "李四",
        "keywords": "用户,信息,修改",
        "estimate": 5,
        "criteria": "1. 用户可以修改用户名\n2. 用户可以修改头像\n3. 用户可以修改密码",
        "remark": "需要支持头像上传和裁剪",
        "project_id": 1,
        "project": {"id": 1, "name": "用户中心项目", "desc": "用户中心相关功能"},
        "tapd_url": None,
        "created_at": "2025-04-25T11:00:00",
        "updated_at": "2025-04-25T11:00:00"
    }
]


@router.post("/create", summary="创建需求", status_code=status.HTTP_201_CREATED)
async def create_requirement(requirement_in: dict):
    """
    创建新需求（需校验项目存在性）
    - **category**: 必须为预定义枚举值
    - **keywords**: 最多支持10个逗号分隔关键词
    """
    # 模拟创建操作，直接返回成功
    return Success(msg="创建成功", code=status.HTTP_201_CREATED)


@router.get("/list", summary="分页筛选需求列表")
async def list_requirements(
        page: int = Query(1, ge=1, description="页码"),
        page_size: int = Query(10, ge=1, le=100, description="每页数量"),
        project_id: int = Query(None, description="项目ID精准筛选"),
        category: str = Query(None, description="需求类别筛选"),
        keyword: str = Query(None, description="关键词模糊搜索"),
        tapd_url: str = Query(None, description="TAPD URL精准匹配")
):
    """
    多条件分页查询需求
    - 支持项目ID精准筛选
    - 支持需求类别过滤
    - 支持关键词模糊匹配（名称/描述/备注）
    - 支持TAPD URL精准匹配
    """
    # 过滤模拟数据
    filtered_data = MOCK_DATA
    if project_id:
        filtered_data = [item for item in filtered_data if item["project_id"] == project_id]
    if category:
        filtered_data = [item for item in filtered_data if item["category"] == category]
    if keyword:
        filtered_data = [item for item in filtered_data if
                         keyword.lower() in item["name"].lower() or
                         keyword.lower() in item["description"].lower() or
                         (item["remark"] and keyword.lower() in item["remark"].lower())]
    if tapd_url:
        filtered_data = [item for item in filtered_data if
                         item["tapd_url"] and tapd_url.lower() in item["tapd_url"].lower()]

    # 分页
    total = len(filtered_data)
    start_idx = (page - 1) * page_size
    end_idx = min(start_idx + page_size, total)
    paged_data = filtered_data[start_idx:end_idx]

    # 打印分页信息，便于调试
    print(f"\n需求列表分页信息: 总数={total}, 页码={page}, 每页数量={page_size}\n")

    return SuccessExtra(
        data=paged_data,
        total=total,
        page=page,
        page_size=page_size
    )


@router.get("/get", summary="获取需求详情")
async def get_requirement(
        id: int = Query(..., description="需求ID"),
):
    """根据ID获取需求详情（包含关联项目基础信息）"""
    # 查找对应ID的需求
    req_obj = next((item for item in MOCK_DATA if item["id"] == id), None)
    if not req_obj:
        return {"code": 404, "msg": "需求不存在", "data": None}

    return Success(data=req_obj)


@router.put("/update", summary="全量更新需求")
async def update_requirement(requirement_in: dict):
    """全量更新需求字段（需传递所有必填字段）"""
    # 模拟更新操作，直接返回成功
    return Success(msg="更新成功")


@router.delete("/delete", summary="删除需求")
async def delete_requirement(
        id: int = Query(..., description="需求ID"),
        project_id: int = Query(..., description="项目ID"),
):
    """
    删除需求（需二次校验项目ID）
    - 防止越权删除
    """
    # 查找对应ID的需求
    global MOCK_DATA
    req_index = next((index for index, item in enumerate(MOCK_DATA) if item["id"] == id), None)

    if req_index is None:
        return {"code": 404, "msg": "需求不存在", "data": None}

    # 校验项目ID是否匹配，防止越权删除
    if MOCK_DATA[req_index]["project_id"] != project_id:
        return {"code": 403, "msg": "无权删除该需求", "data": None}

    # 从模拟数据中删除需求
    MOCK_DATA.pop(req_index)

    # 返回删除成功
    return Success(msg="删除成功")
