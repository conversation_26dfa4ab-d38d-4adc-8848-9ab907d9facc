from fastapi import APIRouter, Query, Depends, status, Path
from app.controllers.requirement import requirement_controller
from app.schemas.requirements import (
    RequirementCreate,
    RequirementUpdate,
    RequirementDelete,
    REQUIREMENT_CATEGORIES, RequirementBase, RequirementSelect
)
from app.schemas.base import Success, SuccessExtra
from tortoise.expressions import Q
from fastapi.exceptions import HTTPException
import logging

logger = logging.getLogger(__name__)

router = APIRouter(tags=["需求管理"])


@router.post("/create", summary="创建需求", status_code=status.HTTP_201_CREATED)
async def create_requirement(
        requirement_in: RequirementCreate,
):
    """
    创建新需求（需校验项目存在性）
    - **category**: 必须为预定义枚举值
    - **keywords**: 最多支持10个逗号分隔关键词
    """
    # 模拟创建操作，直接返回成功
    return Success(msg="创建成功", code=status.HTTP_201_CREATED)


@router.get("/list", summary="分页筛选需求列表")
async def list_requirements(
        page: int = Query(1, ge=1, description="页码"),
        page_size: int = Query(10, ge=1, le=100, description="每页数量"),
        limit: int = Query(100, ge=1, le=1000, description="最大数量"),
        project_id: int = Query(None, gt=0, description="项目ID精准筛选"),
        category: REQUIREMENT_CATEGORIES = Query(None, description="需求类别筛选"),
        keyword: str = Query(None, min_length=1, description="关键词模糊搜索"),
        tapd_url: str = Query(None, description="TAPD URL精准匹配"),
        latest_batch: bool = Query(False, description="是否只获取最近一次入库的需求")
):
    """
    多条件分页查询需求
    - 支持项目ID精准筛选
    - 支持需求类别过滤
    - 支持关键词模糊匹配（名称/描述/备注）
    - 支持TAPD URL精准匹配
    - 支持获取最近一次入库的需求（latest_batch=true）
    """
    try:
        # 构建查询条件
        query_filters = Q()

        # 添加过滤条件
        if project_id:
            query_filters &= Q(project_id=project_id)
        if category:
            query_filters &= Q(category=category)
        if keyword:
            query_filters &= (
                Q(name__icontains=keyword) |
                Q(description__icontains=keyword) |
                Q(remark__icontains=keyword)
            )
        if tapd_url:
            query_filters &= Q(tapd_url__icontains=tapd_url)

        # 如果需要获取最近一次入库的需求
        if latest_batch:
            # 获取最新的创建时间
            from app.models.admin import Requirement
            latest_requirement = await Requirement.all().order_by('-created_at').first()
            if latest_requirement:
                # 获取最新创建时间的年、月、日、时、分
                latest_date = latest_requirement.created_at
                # 查找同一批次的需求（创建时间在同一分钟内）
                from datetime import timedelta
                # 创建一个时间范围，比如同一分钟内或者前后5分钟
                time_range_start = latest_date - timedelta(minutes=5)
                time_range_end = latest_date + timedelta(minutes=5)
                query_filters &= Q(created_at__gte=time_range_start) & Q(created_at__lte=time_range_end)

                # 打印调试信息
                print(f"\n获取最近一次入库的需求，时间范围: {time_range_start} 到 {time_range_end}\n")

        # 查询数据库
        total, requirements = await requirement_controller.list(
            page=page,
            page_size=page_size,
            search=query_filters,
            order=["-id"]  # 按ID降序排序
        )

        # 转换为字典列表
        requirements_data = []
        for req in requirements:
            req_dict = {
                "id": req.id,
                "name": req.name,
                "description": req.description,
                "category": req.category,
                "parent": req.parent,
                "module": req.module,
                "level": req.level,
                "reviewer": req.reviewer,
                "keywords": req.keywords,
                "estimate": req.estimate,
                "criteria": req.criteria,
                "remark": req.remark,
                "project_id": req.project_id,
                "tapd_url": req.tapd_url,
                "created_at": req.created_at.isoformat() if req.created_at else None,
                "updated_at": req.updated_at.isoformat() if req.updated_at else None
            }
            requirements_data.append(req_dict)

        # 打印分页信息，便于调试
        print(f"\n需求列表分页信息: 总数={total}, 页码={page}, 每页数量={page_size}\n")

        return SuccessExtra(
            data=requirements_data,
            total=total,
            page=page,
            page_size=page_size
        )
    except Exception as e:
        # 如果数据库查询失败，使用模拟数据作为备份
        logger.error(f"从数据库获取需求列表失败: {str(e)}", exc_info=True)
        print(f"\n从数据库获取需求列表失败，使用模拟数据: {str(e)}\n")

        # 使用模拟数据
        mock_data = [
            {
                "id": 1,
                "name": "用户登录功能",
                "description": "实现用户登录功能，支持账号密码登录和第三方登录",
                "category": "功能",
                "parent": None,
                "module": "用户模块",
                "level": "高",
                "reviewer": "张三",
                "keywords": "登录,用户,认证",
                "estimate": 8,
                "criteria": "1. 用户可以使用账号密码登录\n2. 用户可以使用第三方账号登录\n3. 登录成功后跳转到首页",
                "remark": "需要支持微信、QQ、微博登录",
                "project_id": 1,
                "tapd_url": None,
                "created_at": "2025-04-25T10:00:00",
                "updated_at": "2025-04-25T10:00:00"
            },
            {
                "id": 2,
                "name": "用户注册功能",
                "description": "实现用户注册功能，支持邮箱注册和手机号注册",
                "category": "功能",
                "parent": None,
                "module": "用户模块",
                "level": "高",
                "reviewer": "张三",
                "keywords": "注册,用户,邮箱,手机",
                "estimate": 8,
                "criteria": "1. 用户可以使用邮箱注册\n2. 用户可以使用手机号注册\n3. 注册成功后跳转到登录页",
                "remark": "需要支持邮箱验证和手机验证码",
                "project_id": 1,
                "tapd_url": None,
                "created_at": "2025-04-25T10:30:00",
                "updated_at": "2025-04-25T10:30:00"
            },
            {
                "id": 3,
                "name": "用户信息修改",
                "description": "实现用户信息修改功能，支持修改用户名、头像、密码等",
                "category": "功能",
                "parent": None,
                "module": "用户模块",
                "level": "中",
                "reviewer": "李四",
                "keywords": "用户,信息,修改",
                "estimate": 5,
                "criteria": "1. 用户可以修改用户名\n2. 用户可以修改头像\n3. 用户可以修改密码",
                "remark": "需要支持头像上传和裁剪",
                "project_id": 1,
                "tapd_url": None,
                "created_at": "2025-04-25T11:00:00",
                "updated_at": "2025-04-25T11:00:00"
            }
        ]

        # 过滤模拟数据
        filtered_data = mock_data
        if project_id:
            filtered_data = [item for item in filtered_data if item["project_id"] == project_id]
        if category:
            filtered_data = [item for item in filtered_data if item["category"] == category]
        if keyword:
            filtered_data = [item for item in filtered_data if
                            keyword.lower() in item["name"].lower() or
                            keyword.lower() in item["description"].lower() or
                            (item["remark"] and keyword.lower() in item["remark"].lower())]
        if tapd_url:
            filtered_data = [item for item in filtered_data if
                            item["tapd_url"] and tapd_url.lower() in item["tapd_url"].lower()]

        # 如果需要获取最近一次入库的需求
        if latest_batch:
            # 对于模拟数据，我们假设最近一次入库的需求是创建时间最新的一批
            # 首先按创建时间排序
            from datetime import datetime
            sorted_data = sorted(filtered_data,
                                key=lambda x: datetime.fromisoformat(x["created_at"].replace('Z', '+00:00')),
                                reverse=True)

            if sorted_data:
                # 获取最新的创建时间
                latest_time = datetime.fromisoformat(sorted_data[0]["created_at"].replace('Z', '+00:00'))
                # 获取同一批次的需求（创建时间在同一分钟内）
                from datetime import timedelta
                time_range_start = latest_time - timedelta(minutes=5)
                time_range_end = latest_time + timedelta(minutes=5)

                # 过滤出在时间范围内的需求
                filtered_data = [item for item in sorted_data if
                                time_range_start <= datetime.fromisoformat(item["created_at"].replace('Z', '+00:00')) <= time_range_end]

                # 打印调试信息
                print(f"\n获取最近一次入库的需求（模拟数据），时间范围: {time_range_start} 到 {time_range_end}，共找到 {len(filtered_data)} 条需求\n")

        # 分页
        total = len(filtered_data)
        start_idx = (page - 1) * page_size
        end_idx = min(start_idx + page_size, total)
        paged_data = filtered_data[start_idx:end_idx]

        # 打印分页信息，便于调试
        print(f"\n需求列表分页信息(模拟数据): 总数={total}, 页码={page}, 每页数量={page_size}\n")

        return SuccessExtra(
            data=paged_data,
            total=total,
            page=page,
            page_size=page_size
        )


@router.get("/get", summary="获取需求详情")
async def get_requirement(
        id: int = Query(..., description="需求ID"),
):
    """根据ID获取需求详情（包含关联项目基础信息）"""
    try:
        # 从数据库获取需求
        requirement = await requirement_controller.get(id=id)

        # 获取关联的项目信息
        from app.models.admin import Project
        project = await Project.get_or_none(id=requirement.project_id)

        # 构建响应数据
        req_data = {
            "id": requirement.id,
            "name": requirement.name,
            "description": requirement.description,
            "category": requirement.category,
            "parent": requirement.parent,
            "module": requirement.module,
            "level": requirement.level,
            "reviewer": requirement.reviewer,
            "keywords": requirement.keywords,
            "estimate": requirement.estimate,
            "criteria": requirement.criteria,
            "remark": requirement.remark,
            "project_id": requirement.project_id,
            "tapd_url": requirement.tapd_url,
            "created_at": requirement.created_at.isoformat() if requirement.created_at else None,
            "updated_at": requirement.updated_at.isoformat() if requirement.updated_at else None
        }

        # 添加项目信息
        if project:
            req_data["project"] = {
                "id": project.id,
                "name": project.name,
                "desc": project.desc
            }

        return Success(data=req_data)

    except Exception as e:
        logger.error(f"获取需求详情失败: {str(e)}", exc_info=True)
        print(f"\n获取需求详情失败，使用模拟数据: {str(e)}\n")

        # 使用模拟数据作为备份
        mock_data = [
            {
                "id": 1,
                "name": "用户登录功能",
                "description": "实现用户登录功能，支持账号密码登录和第三方登录",
                "category": "功能",
                "parent": None,
                "module": "用户模块",
                "level": "高",
                "reviewer": "张三",
                "keywords": "登录,用户,认证",
                "estimate": 8,
                "criteria": "1. 用户可以使用账号密码登录\n2. 用户可以使用第三方账号登录\n3. 登录成功后跳转到首页",
                "remark": "需要支持微信、QQ、微博登录",
                "project_id": 1,
                "project": {"id": 1, "name": "用户中心项目", "desc": "用户中心相关功能"},
                "tapd_url": None,
                "created_at": "2025-04-25T10:00:00",
                "updated_at": "2025-04-25T10:00:00"
            },
            {
                "id": 2,
                "name": "用户注册功能",
                "description": "实现用户注册功能，支持邮箱注册和手机号注册",
                "category": "功能",
                "parent": None,
                "module": "用户模块",
                "level": "高",
                "reviewer": "张三",
                "keywords": "注册,用户,邮箱,手机",
                "estimate": 8,
                "criteria": "1. 用户可以使用邮箱注册\n2. 用户可以使用手机号注册\n3. 注册成功后跳转到登录页",
                "remark": "需要支持邮箱验证和手机验证码",
                "project_id": 1,
                "project": {"id": 1, "name": "用户中心项目", "desc": "用户中心相关功能"},
                "tapd_url": None,
                "created_at": "2025-04-25T10:30:00",
                "updated_at": "2025-04-25T10:30:00"
            },
            {
                "id": 3,
                "name": "用户信息修改",
                "description": "实现用户信息修改功能，支持修改用户名、头像、密码等",
                "category": "功能",
                "parent": None,
                "module": "用户模块",
                "level": "中",
                "reviewer": "李四",
                "keywords": "用户,信息,修改",
                "estimate": 5,
                "criteria": "1. 用户可以修改用户名\n2. 用户可以修改头像\n3. 用户可以修改密码",
                "remark": "需要支持头像上传和裁剪",
                "project_id": 1,
                "project": {"id": 1, "name": "用户中心项目", "desc": "用户中心相关功能"},
                "tapd_url": None,
                "created_at": "2025-04-25T11:00:00",
                "updated_at": "2025-04-25T11:00:00"
            }
        ]

        # 查找对应ID的需求
        req_obj = next((item for item in mock_data if item["id"] == id), None)
        if not req_obj:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="需求不存在"
            )

        return Success(data=req_obj)


@router.put("/update", summary="全量更新需求")
async def update_requirement(
        requirement_in: RequirementUpdate,
):
    """全量更新需求字段（需传递所有必填字段）"""
    try:
        # 调用控制器更新需求
        await requirement_controller.update(id=requirement_in.id, obj_in=requirement_in)
        return Success(msg="更新成功")
    except Exception as e:
        logger.error(f"更新需求失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新需求失败: {str(e)}"
        )


@router.delete("/delete", summary="删除需求")
async def delete_requirement(
        id: int = Query(..., description="需求ID"),
        project_id: int = Query(..., description="项目ID"),
):
    """
    删除需求（需二次校验项目ID）
    - 防止越权删除
    """
    try:
        # 先检查需求是否存在
        requirement = await requirement_controller.get(id=id)

        # 校验项目ID是否匹配，防止越权删除
        if requirement.project_id != project_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权删除该需求"
            )

        # 调用控制器删除需求
        await requirement_controller.delete(id=id)

        # 打印删除请求信息，便于调试
        print(f"\n删除需求成功: ID={id}, 项目ID={project_id}\n")

        # 返回删除成功
        return Success(msg="删除成功")
    except Exception as e:
        # 记录错误并返回失败
        logger.error(f"删除需求失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除需求失败: {str(e)}"
        )