"""
API依赖模块

提供API路由中常用的依赖函数。
"""

from typing import Optional

from fastapi import Depends, Header, HTTPException

from app.core.dependency import AuthControl
from app.models.admin import User


async def get_current_user(token: str = Header(..., description="认证令牌")) -> User:
    """
    获取当前用户

    这是一个FastAPI依赖函数，用于从请求头中获取token并验证用户身份。
    成功后返回用户对象，失败则抛出HTTP异常。

    参数:
        token (str): 请求头中的认证令牌

    返回:
        User: 当前用户对象

    异常:
        HTTPException: 认证失败时抛出
    """
    try:
        # 使用AuthControl.is_authed验证用户身份
        user = await AuthControl.is_authed(token)
        if not user:
            raise HTTPException(status_code=401, detail="认证失败")
        return user
    except HTTPException:
        # 直接重新抛出HTTPException
        raise
    except Exception as e:
        # 其他异常转换为HTTP 500错误
        raise HTTPException(status_code=500, detail=f"认证过程中发生错误: {str(e)}")
