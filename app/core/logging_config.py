import logging
import os
from logging.handlers import RotatingFileHandler

# 创建日志目录
os.makedirs("logs", exist_ok=True)

# 配置根日志记录器
def configure_logging():
    # 从环境变量获取日志级别，默认为INFO
    log_level_name = os.environ.get("LOG_LEVEL", "INFO").upper()
    log_level = getattr(logging, log_level_name, logging.INFO)

    # 创建日志格式
    log_format = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(log_format)
    console_handler.setLevel(log_level)

    # 创建文件处理器，使用RotatingFileHandler进行日志轮转
    file_handler = RotatingFileHandler(
        "logs/app.log",
        maxBytes=10*1024*1024,  # 10MB
        backupCount=3,
        encoding="utf-8"
    )
    file_handler.setFormatter(log_format)
    file_handler.setLevel(log_level)

    # 创建详细日志文件处理器，始终使用DEBUG级别
    debug_file_handler = RotatingFileHandler(
        "logs/debug.log",
        maxBytes=50*1024*1024,  # 50MB
        backupCount=2,
        encoding="utf-8"
    )
    debug_file_handler.setFormatter(log_format)
    debug_file_handler.setLevel(logging.DEBUG)

    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)  # 根记录器设置为DEBUG，让所有消息都能被捕获
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(debug_file_handler)

    # 设置第三方库的日志级别
    if log_level <= logging.DEBUG:
        # 在DEBUG模式下，允许第三方库的INFO级别日志
        logging.getLogger("uvicorn").setLevel(logging.INFO)
        logging.getLogger("fastapi").setLevel(logging.INFO)
    else:
        # 在非DEBUG模式下，限制第三方库的日志
        logging.getLogger("uvicorn").setLevel(logging.WARNING)
        logging.getLogger("fastapi").setLevel(logging.WARNING)

    # 记录当前日志级别
    root_logger.info(f"日志级别设置为: {log_level_name}")
    if log_level <= logging.DEBUG:
        root_logger.debug("调试日志已启用")

    return root_logger
