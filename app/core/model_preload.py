"""
模型预热模块

提供嵌入模型和其他模型的预热功能，以减少首次使用时的延迟。
"""

import logging
import threading
import time
from typing import Optional, List, Dict, Any

from sentence_transformers import SentenceTransformer

# 配置日志
logger = logging.getLogger(__name__)

# 全局变量，用于跟踪预热状态
_preload_status = {
    "embedding_model": {
        "status": "not_started",  # not_started, in_progress, completed, failed
        "start_time": None,
        "end_time": None,
        "elapsed_time": None,
        "error": None
    }
}


def preload_embedding_model(
    model_name: Optional[str] = None,
    device: Optional[str] = None,
    cache_folder: Optional[str] = None,
    test_texts: Optional[List[str]] = None,
    batch_size: int = 1,
    use_fp16: bool = False
) -> None:
    """
    预热嵌入模型，可以在后台线程中运行

    参数:
        model_name: 模型名称，如果为None则使用配置中的默认值
        device: 设备，如果为None则使用配置中的默认值
        cache_folder: 缓存目录，如果为None则使用配置中的默认值
        test_texts: 用于测试的文本列表，如果为None则使用默认测试文本
        batch_size: 批处理大小
        use_fp16: 是否使用半精度浮点数(FP16)加速
    """
    from app.settings.llm_config import get_embedding_model, EMBEDDING_MODEL, EMBEDDING_DEVICE, EMBEDDING_CACHE_DIR

    # 更新预热状态
    _preload_status["embedding_model"]["status"] = "in_progress"
    _preload_status["embedding_model"]["start_time"] = time.time()

    try:
        logger.info("开始预热嵌入模型...")
        
        # 使用提供的参数或默认值
        model_name = model_name or EMBEDDING_MODEL
        device = device or EMBEDDING_DEVICE
        cache_folder = cache_folder or EMBEDDING_CACHE_DIR
        
        if test_texts is None:
            test_texts = [
                "这是第一个测试文本，用于预热嵌入模型",
                "这是第二个测试文本，包含不同的内容",
                "这是第三个测试文本，用于确保模型已完全加载"
            ]
        
        # 获取嵌入模型实例（这将触发模型加载）
        model = get_embedding_model()
        
        if model:
            # 测试模型，确保它已完全加载
            start_encode_time = time.time()
            
            # 批量编码测试文本
            _ = model.encode(test_texts, batch_size=batch_size, show_progress_bar=False)
            
            encode_time = time.time() - start_encode_time
            total_time = time.time() - _preload_status["embedding_model"]["start_time"]
            
            # 更新预热状态
            _preload_status["embedding_model"]["status"] = "completed"
            _preload_status["embedding_model"]["end_time"] = time.time()
            _preload_status["embedding_model"]["elapsed_time"] = total_time
            
            logger.info(f"嵌入模型预热完成，总耗时: {total_time:.2f}秒，编码测试耗时: {encode_time:.2f}秒")
        else:
            raise Exception("嵌入模型加载失败")
            
    except Exception as e:
        logger.error(f"嵌入模型预热失败: {str(e)}")
        
        # 更新预热状态
        _preload_status["embedding_model"]["status"] = "failed"
        _preload_status["embedding_model"]["end_time"] = time.time()
        _preload_status["embedding_model"]["elapsed_time"] = time.time() - _preload_status["embedding_model"]["start_time"]
        _preload_status["embedding_model"]["error"] = str(e)


def start_model_preloading(in_background: bool = True) -> None:
    """
    启动所有模型的预热过程
    
    参数:
        in_background: 是否在后台线程中运行预热过程
    """
    if in_background:
        # 在后台线程中预热嵌入模型
        threading.Thread(target=preload_embedding_model, daemon=True).start()
        logger.info("已在后台线程启动嵌入模型预热")
    else:
        # 在当前线程中预热嵌入模型
        preload_embedding_model()


def get_preload_status() -> Dict[str, Any]:
    """
    获取模型预热状态
    
    返回:
        包含预热状态信息的字典
    """
    return _preload_status
