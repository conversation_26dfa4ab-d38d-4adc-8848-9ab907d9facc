import shutil  # 用于删除目录

from aerich import Command  # 数据库迁移工具
from fastapi import FastAPI  # FastAPI 框架
from fastapi.middleware import Middleware  # 中间件定义
from fastapi.middleware.cors import CORSMiddleware  # CORS 中间件
from tortoise.expressions import Q  # Tortoise ORM 查询条件构造工具
from fastapi.staticfiles import StaticFiles  # 静态文件服务

# 导入模块
from app.api import api_router  # API 路由
from app.controllers.api import api_controller  # API 控制器
from app.controllers.user import UserCreate, user_controller  # 用户控制器
from app.core.exceptions import (  # 异常处理相关
    DoesNotExist,
    DoesNotExistHandle,
    HTTPException,
    HttpExcHandle,
    IntegrityError,
    IntegrityHandle,
    RequestValidationError,
    RequestValidationHandle,
    ResponseValidationError,
    ResponseValidationHandle,
)
from app.log import logger  # 日志记录器
from app.models.admin import Api, Menu, Role  # 数据模型
from app.schemas.menus import MenuType  # 菜单类型枚举
from app.settings.config import settings  # 配置文件
from .middlewares import BackGroundTaskMiddleware, HttpAuditLogMiddleware  # 自定义中间件


def make_middlewares():
    """
    创建中间件列表。
    返回:
        list[Middleware]: 中间件列表
    """
    middleware = [
        Middleware(
            CORSMiddleware,  # 添加 CORS 中间件
            allow_origins=["*"],  # 允许所有来源，包括Chrome扩展
            allow_credentials=settings.CORS_ALLOW_CREDENTIALS,  # 是否允许凭据
            allow_methods=["*"],  # 允许所有方法
            allow_headers=["*", "token"],  # 允许所有头部，明确包括token头
            expose_headers=["Content-Disposition"],
            max_age=600
        ),
        Middleware(BackGroundTaskMiddleware),  # 后台任务中间件
        Middleware(
            HttpAuditLogMiddleware,  # HTTP 审计日志中间件
            methods=["GET", "POST", "PUT", "DELETE"],  # 需要记录的日志方法
            exclude_paths=[
                "/docs",
                "/openapi.json",
            ],  # 排除路径
        ),
    ]
    return middleware


def register_exceptions(app: FastAPI):
    """
    注册异常处理器。
    参数:
        app (FastAPI): FastAPI 应用实例
    """
    app.add_exception_handler(DoesNotExist, DoesNotExistHandle)  # 对象不存在异常
    app.add_exception_handler(HTTPException, HttpExcHandle)  # HTTP 异常
    app.add_exception_handler(IntegrityError, IntegrityHandle)  # 数据完整性异常
    app.add_exception_handler(RequestValidationError, RequestValidationHandle)  # 请求验证错误
    app.add_exception_handler(ResponseValidationError, ResponseValidationHandle)  # 响应验证错误


def register_routers(app: FastAPI, prefix: str = "/api"):
    """
    注册路由。
    参数:
        app (FastAPI): FastAPI 应用实例
        prefix (str): 路由前缀，默认为 /api
    """
    # 注册静态文件服务
    try:
        # 确保静态文件目录存在
        import os
        static_dir = os.path.join(os.getcwd(), "static")
        if not os.path.exists(static_dir):
            os.makedirs(static_dir)

        # 挂载静态文件目录
        app.mount("/static", StaticFiles(directory=static_dir), name="static")
        print(f"静态文件服务已挂载: {static_dir}")
    except Exception as e:
        print(f"挂载静态文件服务失败: {str(e)}")

    app.include_router(api_router, prefix=prefix)


async def init_superuser():
    """
    初始化超级用户。
    如果数据库中没有用户，则创建一个默认的超级用户。
    """
    user = await user_controller.model.exists()  # 检查是否存在用户
    if not user:
        await user_controller.create_user(
            UserCreate(
                username="admin",  # 默认用户名
                email="<EMAIL>",  # 默认邮箱
                password="123456",  # 默认密码
                is_active=True,  # 用户激活状态
                is_superuser=True,  # 超级用户
            )
        )


async def init_menus():
    """
    初始化菜单。
    如果数据库中没有菜单，则创建默认的系统管理菜单及其子菜单。
    """
    menus = await Menu.exists()  # 检查是否存在菜单
    if not menus:
        parent_menu = await Menu.create(  # 创建父菜单（系统管理）
            menu_type=MenuType.CATALOG,
            name="系统管理",
            path="/system",
            order=1,
            parent_id=0,
            icon="carbon:gui-management",
            is_hidden=False,
            component="Layout",
            keepalive=False,
            redirect="/system/user",
        )
        children_menu = [  # 创建子菜单
            Menu(
                menu_type=MenuType.MENU,
                name="用户管理",
                path="user",
                order=1,
                parent_id=parent_menu.id,
                icon="material-symbols:person-outline-rounded",
                is_hidden=False,
                component="/system/user",
                keepalive=False,
            ),
            # 其他子菜单...
        ]
        await Menu.bulk_create(children_menu)  # 批量创建子菜单
        await Menu.create(  # 创建一级菜单
            menu_type=MenuType.MENU,
            name="一级菜单",
            path="/top-menu",
            order=2,
            parent_id=0,
            icon="material-symbols:featured-play-list-outline",
            is_hidden=False,
            component="/top-menu",
            keepalive=False,
            redirect="",
        )


async def init_apis():
    """
    初始化 API。
    如果数据库中没有 API，则刷新 API 列表。
    """
    apis = await api_controller.model.exists()  # 检查是否存在 API
    if not apis:
        await api_controller.refresh_api()


async def init_db():
    """
    初始化数据库。
    包括创建数据库表结构、执行迁移和升级。
    """
    from tortoise import Tortoise
    from tortoise.exceptions import IntegrityError, OperationalError

    # 首先尝试连接数据库，确保数据库可用
    try:
        await Tortoise.init(config=settings.TORTOISE_ORM)
        logger.info("数据库连接成功")

        # 检查aerich表是否存在
        conn = Tortoise.get_connection("default")
        result = await conn.execute_query("SELECT to_regclass('aerich');")
        aerich_exists = result[0][0] is not None

        if aerich_exists:
            # 检查aerich表的id列是否有序列
            result = await conn.execute_query(
                "SELECT pg_get_serial_sequence('aerich', 'id');"
            )
            has_sequence = result[0][0] is not None

            if not has_sequence:
                logger.warning("aerich表的id列没有序列，正在创建序列...")
                try:
                    # 创建序列并设置为id列的默认值
                    await conn.execute_script("""
                    CREATE SEQUENCE IF NOT EXISTS aerich_id_seq;
                    ALTER TABLE aerich ALTER COLUMN id SET DEFAULT nextval('aerich_id_seq');
                    SELECT setval('aerich_id_seq', (SELECT COALESCE(MAX(id), 0) + 1 FROM aerich), false);
                    """)
                    logger.info("aerich表的id列序列创建成功")
                except Exception as e:
                    logger.error(f"创建aerich表序列失败: {str(e)}")

        await Tortoise.close_connections()
    except Exception as e:
        logger.error(f"数据库连接失败: {str(e)}")
        # 继续执行，让后续的初始化代码尝试处理

    command = Command(tortoise_config=settings.TORTOISE_ORM)  # 创建 Aerich 命令对象
    try:
        # 使用try-except包装每个可能失败的操作
        try:
            await command.init_db(safe=True)  # 初始化数据库
            logger.info("数据库表初始化成功")
        except FileExistsError:
            logger.info("migrations目录已存在，跳过init_db")
        except IntegrityError as e:
            logger.error(f"数据库完整性错误: {str(e)}")
            # 尝试修复aerich表
            try:
                await Tortoise.init(config=settings.TORTOISE_ORM)
                conn = Tortoise.get_connection("default")
                # 检查是否有空ID的记录
                result = await conn.execute_query("SELECT COUNT(*) FROM aerich WHERE id IS NULL;")
                if result[0][0] > 0:
                    # 删除空ID的记录
                    await conn.execute_script("DELETE FROM aerich WHERE id IS NULL;")
                    logger.info("已删除aerich表中ID为NULL的记录")
                await Tortoise.close_connections()
            except Exception as fix_err:
                logger.error(f"修复aerich表失败: {str(fix_err)}")
        except Exception as e:
            logger.error(f"初始化数据库失败: {str(e)}")

        try:
            await command.init()  # 初始化 Aerich
            logger.info("Aerich初始化成功")
        except Exception as e:
            logger.error(f"初始化Aerich失败: {str(e)}")
            # 继续执行，不要中断整个初始化过程

        # 暂时禁用自动迁移，因为我们已经手动创建了表
        logger.info("数据库迁移已禁用，使用手动创建的表")

    except Exception as e:
        logger.error(f"数据库初始化过程中发生错误: {str(e)}")
        # 即使出错，也继续执行，让应用能够启动

    # 暂时禁用数据库升级，因为我们已经手动创建了表
    logger.info("数据库升级已禁用，使用手动创建的表")


async def init_roles():
    """
    初始化角色。
    如果数据库中没有角色，则创建管理员和普通用户角色，并分配权限。
    """
    roles = await Role.exists()  # 检查是否存在角色
    if not roles:
        admin_role = await Role.create(  # 创建管理员角色
            name="管理员",
            desc="管理员角色",
        )
        user_role = await Role.create(  # 创建普通用户角色
            name="普通用户",
            desc="普通用户角色",
        )

        all_apis = await Api.all()  # 获取所有 API
        await admin_role.apis.add(*all_apis)  # 为管理员角色分配所有 API

        all_menus = await Menu.all()  # 获取所有菜单
        await admin_role.menus.add(*all_menus)  # 为管理员角色分配所有菜单
        await user_role.menus.add(*all_menus)  # 为普通用户角色分配所有菜单

        basic_apis = await Api.filter(Q(method__in=["GET"]) | Q(tags="基础模块"))  # 获取基础 API
        await user_role.apis.add(*basic_apis)  # 为普通用户角色分配基础 API


async def init_data():
    """
    初始化数据。
    包括数据库初始化、超级用户创建、菜单配置、API 配置和角色权限分配。
    """
    try:
        await init_db()  # 初始化数据库
        logger.info("数据库初始化完成")
    except Exception as e:
        logger.error(f"数据库初始化失败，但将继续执行其他初始化步骤: {str(e)}")

    try:
        await init_superuser()  # 初始化超级用户
        logger.info("超级用户初始化完成")
    except Exception as e:
        logger.error(f"超级用户初始化失败: {str(e)}")

    try:
        await init_menus()  # 初始化菜单
        logger.info("菜单初始化完成")
    except Exception as e:
        logger.error(f"菜单初始化失败: {str(e)}")

    try:
        await init_apis()  # 初始化 API
        logger.info("API初始化完成")
    except Exception as e:
        logger.error(f"API初始化失败: {str(e)}")

    try:
        await init_roles()  # 初始化角色
        logger.info("角色初始化完成")
    except Exception as e:
        logger.error(f"角色初始化失败: {str(e)}")

    logger.info("所有初始化步骤已完成，即使有错误也会继续启动应用")