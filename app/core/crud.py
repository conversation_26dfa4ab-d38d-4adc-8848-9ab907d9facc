from typing import Any, Dict, Generic, List, NewType, Tuple, Type, TypeVar, Union
from datetime import datetime

from pydantic import BaseModel  # Pydantic 的数据验证模型
from tortoise.expressions import Q  # Tortoise ORM 的查询条件构造工具
from tortoise.models import Model  # Tortoise ORM 的模型基类
import pytz  # 用于处理时区

# 获取带时区的当前时间
def get_now_with_timezone():
    """Get current datetime with UTC timezone information"""
    return datetime.now(pytz.UTC)

# 辅助函数，确保datetime对象有时区信息
def ensure_timezone(dt):
    """确保datetime对象有时区信息，如果没有则添加UTC时区"""
    if dt is None:
        return None
    if dt.tzinfo is None:
        return dt.replace(tzinfo=pytz.UTC)
    return dt


# 定义类型别名
Total = NewType("Total", int)  # 总数类型
ModelType = TypeVar("ModelType", bound=Model)  # 数据库模型类型
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)  # 创建时的 Pydantic 模型类型
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)  # 更新时的 Pydantic 模型类型


class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """
    基于 Tortoise ORM 和 Pydantic 的通用 CRUD 操作类。
    """

    def __init__(self, model: Type[ModelType]):
        """
        初始化 CRUD 类。
        参数:
            model (Type[ModelType]): 数据库模型类
        """
        self.model = model  # 绑定数据库模型

    async def get(self, id: int) -> ModelType:
        """
        根据 ID 获取单个对象。
        参数:
            id (int): 对象的主键 ID
        返回:
            ModelType: 查询到的对象
        """
        return await self.model.get(id=id)  # 使用 Tortoise ORM 的 get 方法查询对象

    async def list(
        self,
        page: int = 1,
        page_size: int = 10,
        search: Q = Q(),  # 默认空查询条件
        order: list = [],  # 默认无排序
    ) -> Tuple[Total, List[ModelType]]:
        """
        分页查询对象列表。
        参数:
            page (int): 当前页码
            page_size (int): 每页大小
            search (Q): 查询条件，默认为空
            order (list): 排序字段，默认无排序
        返回:
            Tuple[Total, List[ModelType]]: (总记录数, 查询结果列表)
        """
        print(type(self.model))  # 应该输出 <class 'tortoise.models.Model'>
        query = self.model.filter(search)  # 构造查询条件
        total = await query.count()  # 计算总记录数
        results = await query.offset((page - 1) * page_size).limit(page_size).order_by(*order)  # 分页查询
        return Total(total), results  # 返回总数和结果列表

    async def list_limit(
            self,
            page: int = 1,
            page_size: int = 10,
            search: Q = Q(),  # 默认空查询条件
            order: list = [],  # 默认无排序
            limit: int = 100
    ) -> Tuple[Total, List[ModelType]]:
        """
        分页查询对象列表。
        参数:
            page (int): 当前页码
            page_size (int): 每页大小
            search (Q): 查询条件，默认为空
            order (list): 排序字段，默认无排序
        返回:
            Tuple[Total, List[ModelType]]: (总记录数, 查询结果列表)
        """
        print(type(self.model))  # 应该输出 <class 'tortoise.models.Model'>
        query = self.model.filter(search)  # 构造查询条件
        total = await query.count()  # 计算总记录数
        results = await query.offset((page - 1) * page_size).limit(page_size).order_by(*order).limit(limit)  # 分页查询
        return Total(total), results  # 返回总数和结果列表

    async def create(self, obj_in: CreateSchemaType) -> ModelType:
        """
        创建新对象。
        参数:
            obj_in (CreateSchemaType): 创建时的数据模型
        返回:
            ModelType: 创建成功的对象
        """
        try:
            print("\n\n===== CRUDBase.create 开始 =====")
            print(f"模型类: {self.model.__name__}")

            if isinstance(obj_in, Dict):  # 如果输入是字典
                obj_dict = obj_in
                print("输入是字典类型")
            else:  # 如果输入是 Pydantic 模型
                obj_dict = obj_in.model_dump()  # 将模型转换为字典
                print("输入是模型类型")

            # 打印所有字段
            print("\nCRUDBase - 输入对象的所有字段:")
            for key, value in obj_dict.items():
                print(f"字段名: {key}, 值: {value}, 类型: {type(value)}")
                if isinstance(value, datetime):
                    print(f"  - 时区信息: {value.tzinfo}")

            # 确保所有datetime字段都有时区信息
            print("\nCRUDBase - 处理所有日期时间字段:")
            for key, value in list(obj_dict.items()):  # 使用list创建副本，避免在迭代过程中修改字典
                if isinstance(value, datetime):
                    old_value = value
                    if value.tzinfo is None:
                        obj_dict[key] = ensure_timezone(value)
                        print(f"字段 {key} 添加了时区信息:")
                        print(f"  - 原始值: {old_value}, 时区: {old_value.tzinfo}")
                        print(f"  - 处理后: {obj_dict[key]}, 时区: {obj_dict[key].tzinfo}")
                    else:
                        print(f"字段 {key} 已有时区信息: {value.tzinfo}")

            # 如果是Project模型，确保created_at和updated_at字段有值且有时区信息
            if self.model.__name__ == "Project":
                now = get_now_with_timezone()
                if "created_at" not in obj_dict or obj_dict["created_at"] is None:
                    obj_dict["created_at"] = now
                    print(f"为Project模型设置created_at: {now}, 时区: {now.tzinfo}")
                elif obj_dict["created_at"].tzinfo is None:
                    obj_dict["created_at"] = obj_dict["created_at"].replace(tzinfo=pytz.UTC)
                    print(f"为Project模型的created_at添加时区信息: {obj_dict['created_at'].tzinfo}")

                if "updated_at" not in obj_dict or obj_dict["updated_at"] is None:
                    obj_dict["updated_at"] = now
                    print(f"为Project模型设置updated_at: {now}, 时区: {now.tzinfo}")
                elif obj_dict["updated_at"].tzinfo is None:
                    obj_dict["updated_at"] = obj_dict["updated_at"].replace(tzinfo=pytz.UTC)
                    print(f"为Project模型的updated_at添加时区信息: {obj_dict['updated_at'].tzinfo}")

            # 强制设置category字段为"功能"，无论之前是什么值
            if self.model.__name__ == "Requirement":
                obj_dict["category"] = "功能"
                print("\nCRUDBase - 强制设置category字段为'功能'")

            # 获取下一个可用的ID
            print("\nCRUDBase - 获取下一个可用的ID...")
            try:
                # 获取当前最大ID
                max_id_obj = await self.model.all().order_by('-id').first()
                if max_id_obj:
                    next_id = max_id_obj.id + 1
                else:
                    next_id = 1
                print(f"下一个可用ID: {next_id}")

                # 将ID添加到obj_dict
                obj_dict['id'] = next_id
            except Exception as e:
                print(f"获取下一个可用ID失败: {str(e)}")
                print("将使用数据库自动生成的ID")

            print("\nCRUDBase - 创建模型实例...")
            obj = self.model(**obj_dict)  # 使用字典创建模型实例

            # 检查模型实例的日期时间字段
            print("\nCRUDBase - 检查模型实例的日期时间字段:")
            for field_name in dir(obj):
                if not field_name.startswith('_') and not callable(getattr(obj, field_name)):
                    field_value = getattr(obj, field_name)
                    if isinstance(field_value, datetime):
                        print(f"字段名: {field_name}, 值: {field_value}, 时区: {field_value.tzinfo}")
                        # 如果没有时区信息，添加时区信息
                        if field_value.tzinfo is None:
                            setattr(obj, field_name, ensure_timezone(field_value))
                            print(f"  - 已添加时区信息: {getattr(obj, field_name).tzinfo}")

            print("\nCRUDBase - 保存到数据库...")
            await obj.save()  # 保存到数据库
            print("CRUDBase - 保存成功!")
            print("===== CRUDBase.create 结束 =====\n\n")
            return obj  # 返回创建成功的对象
        except Exception as e:
            print(f"\nCRUDBase.create 出错: {str(e)}")
            print(f"错误类型: {type(e)}")
            import traceback
            print(f"错误堆栈: {traceback.format_exc()}")
            print("===== CRUDBase.create 出错 =====\n\n")
            raise

    async def update(self, id: int, obj_in: Union[UpdateSchemaType, Dict[str, Any]]) -> ModelType:
        """
        更新现有对象。
        参数:
            id (int): 对象的主键 ID
            obj_in (Union[UpdateSchemaType, Dict[str, Any]]): 更新时的数据模型或字典
        返回:
            ModelType: 更新后的对象
        """
        if isinstance(obj_in, Dict):  # 如果输入是字典
            obj_dict = obj_in
        else:  # 如果输入是 Pydantic 模型
            obj_dict = obj_in.model_dump(exclude_unset=True, exclude={"id"})  # 转换为字典，排除未设置的字段和主键

        # 确保所有datetime字段都有时区信息
        for key, value in obj_dict.items():
            if isinstance(value, datetime) and value.tzinfo is None:
                obj_dict[key] = ensure_timezone(value)

        obj = await self.get(id=id)  # 获取要更新的对象
        obj.update_from_dict(obj_dict)  # 使用字典更新对象属性
        await obj.save()  # 保存到数据库
        return obj  # 返回更新后的对象

    async def remove(self, id: int) -> None:
        """
        删除指定对象。
        参数:
            id (int): 对象的主键 ID
        """
        obj = await self.get(id=id)  # 获取要删除的对象
        await obj.delete()  # 删除对象