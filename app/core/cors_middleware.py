"""
自定义CORS中间件，专门处理Chrome扩展的跨域请求
"""
from fastapi import Request
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
import logging

logger = logging.getLogger(__name__)

class CustomCORSMiddleware(BaseHTTPMiddleware):
    """
    自定义CORS中间件，专门处理Chrome扩展的跨域请求
    """
    
    async def dispatch(self, request: Request, call_next):
        # 记录请求信息，用于调试
        logger.info(f"收到请求: {request.method} {request.url.path}")
        logger.info(f"请求头: {dict(request.headers)}")
        
        # 如果是OPTIONS请求（预检请求），直接返回带有CORS头的响应
        if request.method == "OPTIONS":
            logger.info(f"处理OPTIONS预检请求: {request.url.path}")
            
            # 创建响应
            response = JSONResponse(content={})
            
            # 添加CORS头
            response.headers["Access-Control-Allow-Origin"] = "*"
            response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
            response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization, token, X-Requested-With"
            response.headers["Access-Control-Max-Age"] = "86400"  # 24小时
            
            return response
        
        # 对于非OPTIONS请求，正常处理，但在响应中添加CORS头
        response = await call_next(request)
        
        # 添加CORS头到所有响应
        response.headers["Access-Control-Allow-Origin"] = "*"
        response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
        response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization, token, X-Requested-With"
        
        return response
