from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime
import pytz

class RequirementDocBase(BaseModel):
    """
    需求文档基础模型（所有操作的共用字段）
    """
    content: str = Field(..., description="需求文档内容")
    name: Optional[str] = Field(None, max_length=255, description="需求名称")
    url: Optional[str] = Field(None, max_length=500, description="需求链接")
    handler: Optional[str] = Field(None, max_length=100, description="处理人")
    developer: Optional[str] = Field(None, max_length=100, description="开发")
    tester: Optional[str] = Field(None, max_length=100, description="测试")
    project_id: Optional[int] = Field(None, description="关联项目")
    user_id: Optional[int] = Field(None, description="用户ID")
    source_type: Optional[str] = Field(None, max_length=50, description="来源类型")
    status: Optional[str] = Field(None, max_length=20, description="状态")

    class Config:
        from_attributes = True
        json_encoders = {
            # 确保datetime有时区信息，如果没有则添加UTC时区
            datetime: lambda v: v.replace(tzinfo=pytz.UTC).isoformat() if v and not v.tzinfo else v.isoformat() if v else None
        }

class RequirementDocCreate(RequirementDocBase):
    """
    创建需求文档专用模型
    """
    # 不包含created_at和updated_at字段，这些将由控制器自动添加
    pass

class RequirementDocUpdate(RequirementDocBase):
    """
    更新需求文档专用模型（所有字段可选 + 强制ID）
    """
    id: int = Field(..., description="需求文档ID")
    # 不包含updated_at字段，这将由控制器自动添加

    class Config:
        from_attributes = True

class RequirementDocInDB(RequirementDocBase):
    """
    数据库中的需求文档模型（包含ID和时间戳）
    """
    id: int
    # 确保时间戳字段有时区信息
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")

    class Config:
        from_attributes = True

    def __init__(self, **data):
        # 确保时间戳字段有时区信息
        if 'created_at' in data and data['created_at'] and not data['created_at'].tzinfo:
            data['created_at'] = data['created_at'].replace(tzinfo=pytz.UTC)
        if 'updated_at' in data and data['updated_at'] and not data['updated_at'].tzinfo:
            data['updated_at'] = data['updated_at'].replace(tzinfo=pytz.UTC)
        super().__init__(**data)
