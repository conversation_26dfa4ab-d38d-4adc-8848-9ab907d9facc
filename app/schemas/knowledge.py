from pydantic import BaseModel, Field
from typing import Optional, List
from enum import Enum
from datetime import datetime


class KnowledgeItemType(str, Enum):
    """知识条目类型枚举"""
    BUSINESS_RULE = "业务规则"
    CORE_FEATURE = "核心功能"
    TERMINOLOGY = "术语解释"
    TEST_POINT = "测试要点"
    DEFECT_SUMMARY = "历史缺陷总结"
    NON_FUNCTIONAL = "非功能要求"
    ENV_CONFIG = "环境配置"
    OTHER = "其他"


class KnowledgeSource(str, Enum):
    """知识条目来源枚举"""
    MANUAL = "手动添加"
    REQUIREMENT = "需求分析导入"
    TESTCASE = "测试用例导入"
    DEFECT = "缺陷导入"
    OTHER = "其他来源"


class KnowledgeItemBase(BaseModel):
    """知识条目基础模型"""
    title: str = Field(..., min_length=1, max_length=255, description="知识条目标题")
    content: str = Field(..., min_length=1, description="知识条目的详细内容")
    item_type: KnowledgeItemType = Field(default=KnowledgeItemType.OTHER, description="条目类型/分类")
    tags: Optional[str] = Field(None, max_length=255, description="标签或关键词，用逗号分隔")
    source: KnowledgeSource = Field(default=KnowledgeSource.MANUAL, description="来源")
    project_id: int = Field(..., gt=0, description="关联项目ID")
    source_url: Optional[str] = Field(None, max_length=500, description="源TAPD需求链接")
    source_reference_id: Optional[str] = Field(None, max_length=100, description="源需求引用ID")


class KnowledgeItemCreate(KnowledgeItemBase):
    """创建知识条目的请求模型"""
    pass


class KnowledgeItemUpdate(BaseModel):
    """更新知识条目的请求模型"""
    id: int = Field(..., description="知识条目ID")
    title: Optional[str] = Field(None, min_length=1, max_length=255, description="知识条目标题")
    content: Optional[str] = Field(None, min_length=1, description="知识条目的详细内容")
    item_type: Optional[KnowledgeItemType] = Field(None, description="条目类型/分类")
    tags: Optional[str] = Field(None, max_length=255, description="标签或关键词，用逗号分隔")
    source: Optional[KnowledgeSource] = Field(None, description="来源")
    project_id: Optional[int] = Field(None, gt=0, description="关联项目ID")
    source_url: Optional[str] = Field(None, max_length=500, description="源TAPD需求链接")
    source_reference_id: Optional[str] = Field(None, max_length=100, description="源需求引用ID")


class KnowledgeItemResponse(KnowledgeItemBase):
    """知识条目响应模型"""
    id: int
    creator_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class KnowledgeItemListResponse(BaseModel):
    """知识条目列表响应模型"""
    items: List[KnowledgeItemResponse]
    total: int
    page: int
    page_size: int
