from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime
from app.models.knowledge_relation import RelevanceLevel, RelationSource


class RequirementKnowledgeRelationBase(BaseModel):
    """需求与知识点关联关系基础模型"""
    requirement_id: int = Field(..., gt=0, description="关联需求ID")
    knowledge_id: int = Field(..., gt=0, description="关联知识点ID")
    relevance_level: RelevanceLevel = Field(default=RelevanceLevel.DIRECT, description="相关性级别")
    relevance_score: float = Field(default=0.0, ge=0.0, le=10.0, description="相关性分数(0-10)")
    source: RelationSource = Field(default=RelationSource.TESTCASE_GENERATION, description="关联关系来源")


class RequirementKnowledgeRelationCreate(RequirementKnowledgeRelationBase):
    """创建需求与知识点关联关系的请求模型"""
    pass


class RequirementKnowledgeRelationUpdate(BaseModel):
    """更新需求与知识点关联关系的请求模型"""
    relevance_level: Optional[RelevanceLevel] = None
    relevance_score: Optional[float] = Field(None, ge=0.0, le=10.0, description="相关性分数(0-10)")
    source: Optional[RelationSource] = None


class RequirementKnowledgeRelationResponse(RequirementKnowledgeRelationBase):
    """需求与知识点关联关系的响应模型"""
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class KnowledgePointWithRelation(BaseModel):
    """带有关联关系信息的知识点"""
    id: int
    title: str
    content: str
    item_type: str
    tags: Optional[str] = None
    source: str
    project_id: int = 1  # 默认项目ID为1
    relevance_level: RelevanceLevel
    relevance_score: float
    index: int  # 用于前端显示的序号
    content_preview: Optional[str] = None  # 内容预览，可选字段

    class Config:
        from_attributes = True

    @validator('item_type', pre=True)
    def validate_item_type(cls, v, values):
        """验证并转换 item_type 字段"""
        if v is None:
            return '其他'  # 默认类型
        return v

    @validator('relevance_level', pre=True)
    def validate_relevance_level(cls, v, values):
        """验证并转换 relevance_level 字段"""
        if isinstance(v, str):
            # 如果是字符串，尝试转换为枚举值
            try:
                return RelevanceLevel(v)
            except ValueError:
                # 如果转换失败，使用默认值
                return RelevanceLevel.DIRECT
        return v


class RequirementKnowledgeResponse(BaseModel):
    """需求相关知识点的响应模型"""
    requirement_id: int
    requirement_name: str
    direct_related: List[KnowledgePointWithRelation] = []
    indirect_related: List[KnowledgePointWithRelation] = []
    background_related: List[KnowledgePointWithRelation] = []
    total_points: int = 0

    class Config:
        from_attributes = True
