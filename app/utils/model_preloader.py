"""
模型预加载工具

这个模块负责在应用启动时预加载嵌入模型，
避免首次API调用时的延迟。
"""

import os
import time
import logging
import threading
from typing import Optional

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局变量，用于存储预加载的模型
_embedding_model = None

def preload_embedding_model_async():
    """
    异步预加载嵌入模型
    在后台线程中加载模型，不阻塞应用启动
    """
    thread = threading.Thread(target=_preload_embedding_model)
    thread.daemon = True
    thread.start()
    logger.info("嵌入模型预加载已在后台线程启动")
    return thread

def _preload_embedding_model():
    """
    实际执行模型预加载的函数
    """
    global _embedding_model
    
    try:
        logger.info("开始预加载嵌入模型...")
        start_time = time.time()
        
        # 确保使用国内镜像
        os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"
        
        # 导入必要的库
        from sentence_transformers import SentenceTransformer
        
        # 加载模型
        model_name = os.environ.get("EMBEDDING_MODEL", "BAAI/bge-large-zh")
        cache_folder = os.environ.get("TRANSFORMERS_CACHE", "/root/.cache/huggingface")
        
        logger.info(f"正在加载模型 {model_name}...")
        _embedding_model = SentenceTransformer(model_name, cache_folder=cache_folder)
        
        # 预热模型
        _embedding_model.encode("预热模型测试文本")
        
        elapsed_time = time.time() - start_time
        logger.info(f"嵌入模型预加载完成，耗时 {elapsed_time:.2f} 秒")
    
    except Exception as e:
        logger.error(f"嵌入模型预加载失败: {str(e)}")

def get_embedding_model():
    """
    获取预加载的嵌入模型
    如果模型尚未加载，会触发加载
    
    Returns:
        SentenceTransformer: 加载的嵌入模型
    """
    global _embedding_model
    
    if _embedding_model is None:
        # 如果模型尚未加载，同步加载
        logger.info("模型尚未预加载，正在同步加载...")
        _preload_embedding_model()
    
    return _embedding_model
