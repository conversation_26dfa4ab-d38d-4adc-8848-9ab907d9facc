import asyncio
import sys
import os
from datetime import datetime
from tortoise import <PERSON>toise

async def create_testcase():
    # Initialize Tortoise ORM
    await Tortoise.init(
        db_url="postgres://admin:admin123@localhost:5432/agent_testing",
        modules={"models": ["app.models.admin"]}
    )
    
    try:
        # Import models
        from app.models.admin import TestCase, TestStep
        
        # Get the next test_case_id
        max_id = await TestCase.all().order_by("-test_case_id").first()
        next_id = 1
        if max_id:
            next_id = max_id.test_case_id + 1
        
        print(f"Using test_case_id: {next_id}")
        
        # Create a test case
        testcase = TestCase(
            test_case_id=next_id,
            title="Test Case 1",
            desc="This is a test case",
            priority="高",
            status="未开始",
            preconditions="Test preconditions",
            postconditions="Test postconditions",
            tags="功能测试",
            requirement_id=1,
            project_id=1,
            creator="System",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        await testcase.save()
        print(f"Created test case with ID: {testcase.id}")
        
        # Create a test step
        test_step = TestStep(
            step_id=1,
            test_case_id=testcase.id,
            description="Step 1",
            expected_result="Result 1",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        await test_step.save()
        print(f"Created test step with ID: {test_step.id}")
        
        # Add the test step to the test case
        await testcase.steps.add(test_step)
        print("Added test step to test case")
        
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
    finally:
        # Close connections
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(create_testcase())
