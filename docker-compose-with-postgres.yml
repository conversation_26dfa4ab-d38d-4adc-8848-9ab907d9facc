version: '3'

services:
  # PostgreSQL数据库服务
  postgres:
    image: postgres:15
    container_name: postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: admin123
      POSTGRES_DB: agent_testing
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U admin"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - app_network

  # 应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: aitest-app
    ports:
      - "9999:80"
    restart: unless-stopped
    depends_on:
      - postgres
    environment:
      # 数据库配置 - 注意这里使用postgres服务名作为主机名
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=admin
      - DB_PASSWORD=admin123
      - DB_NAME=agent_testing
      # 这些变量保留用于兼容性
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_USER=admin
      - POSTGRES_PASSWORD=admin123
      - POSTGRES_DB=agent_testing
      # API配置
      - API_PORT=9999
      # 日志级别配置，可选值: DEBUG, INFO, WARNING, ERROR, CRITICAL
      - LOG_LEVEL=INFO
      # JWT配置
      - SECRET_KEY=3488a63e1765035d386f05409663f55c83bfae3b3c61a932744b20ad14244dcf
      - JWT_ALGORITHM=HS256
      - JWT_EXPIRE_MINUTES=10080
      # 应用名称
      - APP_NAME=agent_testing
      # 前端配置 - 使用相对路径，让前端自动使用当前访问的服务器地址
      - BACKEND_URL=""
      - API_PREFIX="/api/v1"
      # 大模型配置 - DeepSeek
      - DEFAULT_LLM_MODEL=deepseek-chat
      - LLM_API_BASE=https://api.deepseek.com/v1
      - LLM_API_KEY=***********************************
      - LLM_TIMEOUT_SECONDS=120
      - LLM_MAX_RETRIES=3
      # 大模型配置 - DashScope (阿里云)
      - DASHSCOPE_API_BASE=https://dashscope.aliyuncs.com/compatible-mode/v1
      - DASHSCOPE_API_KEY=""
      - DASHSCOPE_MODEL=deepseek-v3
      # 大模型配置 - Qwen VL (阿里云视觉模型)
      - QWEN_VL_MODEL=qwen-vl-plus-latest
      - QWEN_VL_API_BASE=https://dashscope.aliyuncs.com/compatible-mode/v1
      - QWEN_VL_API_KEY=sk-85477c3eb0424bb89d5421d2b28d2051
      # 嵌入模型配置
      - EMBEDDING_MODEL=BAAI/bge-large-zh
      - EMBEDDING_DEVICE=cpu
      - EMBEDDING_CACHE_DIR=/app/embedding_models
      - EMBEDDING_LOCAL_ONLY=true
    volumes:
      # 挂载日志目录到宿主机，方便查看
      - ./logs:/app/logs
      - ./logs:/var/log/app
      - ./logs/nginx:/var/log/nginx
      - ./logs/supervisor:/var/log/supervisor
      - ./embedding_models:/app/embedding_models
    networks:
      - app_network

  # PgAdmin管理工具（可选）
  pgadmin:
    image: dpage/pgadmin4
    container_name: pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - app_network

volumes:
  postgres_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  app_network:
    driver: bridge
