// 监听来自后台脚本的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.action === 'extractContent') {
    // 提取页面内容
    const content = extractPageContent();
    sendResponse(content);
  }
  return true;
});

// 提取页面内容的函数
function extractPageContent() {
  // 检查是否是需求详情页面
  if (window.location.href.includes('/story/detail/')) {
    // 提取标题
    const titleElement = document.querySelector('.story-title');
    const title = titleElement ? titleElement.textContent.trim() : '无标题';
    
    // 提取内容
    const contentElement = document.querySelector('.story-content');
    const content = contentElement ? contentElement.innerHTML : '无内容';
    
    // 提取需求ID
    const idMatch = window.location.href.match(/\/(\d+)$/);
    const id = idMatch ? idMatch[1] : '未知ID';
    
    return {
      type: '需求',
      id: id,
      title: title,
      content: content
    };
  }
  
  // 检查是否是任务详情页面
  if (window.location.href.includes('/task/detail/')) {
    // 提取标题
    const titleElement = document.querySelector('.task-title');
    const title = titleElement ? titleElement.textContent.trim() : '无标题';
    
    // 提取内容
    const contentElement = document.querySelector('.task-content');
    const content = contentElement ? contentElement.innerHTML : '无内容';
    
    // 提取任务ID
    const idMatch = window.location.href.match(/\/(\d+)$/);
    const id = idMatch ? idMatch[1] : '未知ID';
    
    return {
      type: '任务',
      id: id,
      title: title,
      content: content
    };
  }
  
  // 如果不是支持的页面类型
  return {
    type: '未知',
    id: '未知ID',
    title: document.title,
    content: '不支持的TAPD页面类型'
  };
}
