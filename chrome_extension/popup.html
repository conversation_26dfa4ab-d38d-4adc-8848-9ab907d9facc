<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>TAPD Content Extractor</title>
  <style>
    body {
      width: 300px;
      padding: 10px;
      font-family: Arial, sans-serif;
    }
    .header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
    }
    .header img {
      width: 24px;
      height: 24px;
      margin-right: 10px;
    }
    .header h1 {
      font-size: 16px;
      margin: 0;
    }
    .status {
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 15px;
      background-color: #f5f5f5;
    }
    .status.connected {
      background-color: #e6f4ea;
      color: #137333;
    }
    .status.disconnected {
      background-color: #fce8e6;
      color: #c5221f;
    }
    button {
      background-color: #4285f4;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
      width: 100%;
      margin-bottom: 10px;
    }
    button:hover {
      background-color: #3367d6;
    }
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    .settings {
      margin-top: 15px;
    }
    .settings label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    .settings input {
      width: 100%;
      padding: 8px;
      box-sizing: border-box;
      margin-bottom: 10px;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    .footer {
      font-size: 12px;
      color: #666;
      margin-top: 15px;
      text-align: center;
    }
  </style>
</head>
<body>
  <div class="header">
    <img src="images/icon48.png" alt="Logo">
    <h1>TAPD Content Extractor</h1>
  </div>
  
  <div id="statusContainer" class="status">
    <div id="statusMessage">检查连接状态...</div>
  </div>
  
  <button id="extractButton" disabled>提取当前页面内容</button>
  <button id="configureButton">配置应用连接</button>
  
  <div id="settingsContainer" class="settings" style="display: none;">
    <label for="appUrl">应用接收URL:</label>
    <input type="text" id="appUrl" placeholder="http://localhost:3100/api/tapd/content">
    <button id="saveSettings">保存设置</button>
  </div>
  
  <div class="footer">
    <p>使用此插件可以提取TAPD页面内容并发送到您的应用</p>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
