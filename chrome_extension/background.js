// 监听插件安装事件
chrome.runtime.onInstalled.addListener(function() {
  console.log('TAPD Content Extractor 已安装');
});

// 监听来自弹出窗口的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.action === 'getTapdCookies') {
    // 获取TAPD域的所有Cookie
    chrome.cookies.getAll({domain: 'tapd.cn'}, function(cookies) {
      sendResponse({cookies: cookies});
    });
    return true; // 保持消息通道开放，以便异步响应
  }
  
  if (request.action === 'extractContent') {
    // 获取当前活动标签页
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      const currentTab = tabs[0];
      
      // 向内容脚本发送消息，提取内容
      chrome.tabs.sendMessage(currentTab.id, {action: 'extractContent'}, function(response) {
        sendResponse(response);
      });
    });
    return true; // 保持消息通道开放，以便异步响应
  }
});

// 监听标签页更新事件，检测TAPD页面
chrome.tabs.onUpdated.addListener(function(tabId, changeInfo, tab) {
  if (changeInfo.status === 'complete' && tab.url && tab.url.includes('tapd.cn')) {
    // 更新插件图标状态，表示当前页面是TAPD
    chrome.action.setIcon({
      tabId: tabId,
      path: {
        16: 'images/icon16.png',
        48: 'images/icon48.png',
        128: 'images/icon128.png'
      }
    });
  }
});
