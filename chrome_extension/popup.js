document.addEventListener('DOMContentLoaded', function() {
  const statusContainer = document.getElementById('statusContainer');
  const statusMessage = document.getElementById('statusMessage');
  const extractButton = document.getElementById('extractButton');
  const configureButton = document.getElementById('configureButton');
  const settingsContainer = document.getElementById('settingsContainer');
  const appUrlInput = document.getElementById('appUrl');
  const saveSettingsButton = document.getElementById('saveSettings');
  
  // 加载保存的设置
  chrome.storage.sync.get(['appUrl'], function(result) {
    if (result.appUrl) {
      appUrlInput.value = result.appUrl;
      checkConnection(result.appUrl);
    } else {
      updateStatus('disconnected', '未配置应用连接');
    }
  });
  
  // 检查是否在TAPD页面
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    const currentUrl = tabs[0].url;
    if (currentUrl.includes('tapd.cn')) {
      extractButton.disabled = false;
    } else {
      updateStatus('disconnected', '当前不是TAPD页面');
      extractButton.disabled = true;
    }
  });
  
  // 提取内容按钮点击事件
  extractButton.addEventListener('click', function() {
    chrome.storage.sync.get(['appUrl'], function(result) {
      if (!result.appUrl) {
        updateStatus('disconnected', '请先配置应用连接');
        return;
      }
      
      chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        const currentTab = tabs[0];
        
        // 获取TAPD的Cookie
        chrome.cookies.getAll({domain: 'tapd.cn'}, function(cookies) {
          const cookieString = cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');
          
          // 执行内容脚本提取内容
          chrome.scripting.executeScript({
            target: {tabId: currentTab.id},
            function: extractTapdContent
          }, function(results) {
            if (chrome.runtime.lastError) {
              updateStatus('disconnected', '内容提取失败: ' + chrome.runtime.lastError.message);
              return;
            }
            
            const content = results[0].result;
            if (!content) {
              updateStatus('disconnected', '无法提取内容');
              return;
            }
            
            // 发送内容到应用
            sendToApp(result.appUrl, {
              url: currentTab.url,
              title: content.title,
              content: content.content,
              cookies: cookieString
            });
          });
        });
      });
    });
  });
  
  // 配置按钮点击事件
  configureButton.addEventListener('click', function() {
    settingsContainer.style.display = settingsContainer.style.display === 'none' ? 'block' : 'none';
  });
  
  // 保存设置按钮点击事件
  saveSettingsButton.addEventListener('click', function() {
    const appUrl = appUrlInput.value.trim();
    if (!appUrl) {
      updateStatus('disconnected', '请输入有效的应用URL');
      return;
    }
    
    chrome.storage.sync.set({appUrl: appUrl}, function() {
      settingsContainer.style.display = 'none';
      checkConnection(appUrl);
    });
  });
  
  // 检查与应用的连接
  function checkConnection(url) {
    updateStatus('', '正在检查连接...');
    
    fetch(url, {
      method: 'OPTIONS',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    .then(response => {
      if (response.ok) {
        updateStatus('connected', '已连接到应用');
        extractButton.disabled = false;
      } else {
        updateStatus('disconnected', '连接失败: ' + response.status);
      }
    })
    .catch(error => {
      updateStatus('disconnected', '连接错误: ' + error.message);
    });
  }
  
  // 发送内容到应用
  function sendToApp(url, data) {
    updateStatus('', '正在发送内容...');
    
    fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    })
    .then(response => {
      if (response.ok) {
        updateStatus('connected', '内容已成功发送到应用');
      } else {
        updateStatus('disconnected', '发送失败: ' + response.status);
      }
    })
    .catch(error => {
      updateStatus('disconnected', '发送错误: ' + error.message);
    });
  }
  
  // 更新状态显示
  function updateStatus(type, message) {
    statusContainer.className = 'status ' + type;
    statusMessage.textContent = message;
  }
});

// 提取TAPD内容的函数
function extractTapdContent() {
  // 检查是否是需求详情页面
  if (window.location.href.includes('/story/detail/')) {
    // 提取标题
    const titleElement = document.querySelector('.story-title');
    const title = titleElement ? titleElement.textContent.trim() : '无标题';
    
    // 提取内容
    const contentElement = document.querySelector('.story-content');
    const content = contentElement ? contentElement.innerHTML : '无内容';
    
    return {
      title: title,
      content: content
    };
  }
  
  // 检查是否是任务详情页面
  if (window.location.href.includes('/task/detail/')) {
    // 提取标题
    const titleElement = document.querySelector('.task-title');
    const title = titleElement ? titleElement.textContent.trim() : '无标题';
    
    // 提取内容
    const contentElement = document.querySelector('.task-content');
    const content = contentElement ? contentElement.innerHTML : '无内容';
    
    return {
      title: title,
      content: content
    };
  }
  
  // 如果不是支持的页面类型
  return {
    title: document.title,
    content: '不支持的TAPD页面类型'
  };
}
