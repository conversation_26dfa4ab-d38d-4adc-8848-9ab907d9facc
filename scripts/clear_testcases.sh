#!/bin/bash

# 清除数据库中的所有测试用例数据的Shell脚本

# 默认数据库连接参数
DB_USER="postgres"
DB_PASSWORD="postgres"
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="agent_testing"

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -u, --user USER        数据库用户名 (默认: $DB_USER)"
    echo "  -p, --password PASS    数据库密码 (默认: $DB_PASSWORD)"
    echo "  -h, --host HOST        数据库主机地址 (默认: $DB_HOST)"
    echo "  -P, --port PORT        数据库端口 (默认: $DB_PORT)"
    echo "  -d, --dbname DBNAME    数据库名称 (默认: $DB_NAME)"
    echo "  -f, --force            强制执行，不提示确认"
    echo "  --help                 显示此帮助信息"
    exit 0
}

# 解析命令行参数
FORCE=false
while [[ $# -gt 0 ]]; do
    case "$1" in
        -u|--user)
            DB_USER="$2"
            shift 2
            ;;
        -p|--password)
            DB_PASSWORD="$2"
            shift 2
            ;;
        -h|--host)
            DB_HOST="$2"
            shift 2
            ;;
        -P|--port)
            DB_PORT="$2"
            shift 2
            ;;
        -d|--dbname)
            DB_NAME="$2"
            shift 2
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        --help)
            show_help
            ;;
        *)
            echo "未知选项: $1"
            show_help
            ;;
    esac
done

# 确认操作
if [ "$FORCE" = false ]; then
    echo "警告：此操作将删除所有测试用例数据，且无法恢复！"
    read -p "确定要继续吗？(y/n): " CONFIRM
    if [ "$CONFIRM" != "y" ] && [ "$CONFIRM" != "Y" ]; then
        echo "操作已取消"
        exit 0
    fi
fi

# 执行SQL脚本
echo "正在连接到数据库: $DB_HOST:$DB_PORT/$DB_NAME..."
export PGPASSWORD="$DB_PASSWORD"
psql -U "$DB_USER" -h "$DB_HOST" -p "$DB_PORT" -d "$DB_NAME" -f "$(dirname "$0")/clear_testcases_direct.sql"

# 检查执行结果
if [ $? -eq 0 ]; then
    echo "所有测试用例数据已清除！"
else
    echo "清除数据时出错，请检查数据库连接参数和权限。"
fi
