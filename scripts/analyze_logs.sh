#!/bin/bash

# 日志分析脚本
# 使用方法: ./analyze_logs.sh [errors|warnings|summary|size]

# 确保脚本可执行
# chmod +x analyze_logs.sh

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 获取容器ID
get_container_id() {
    echo $(docker ps --filter "name=agent_testing" --format "{{.ID}}")
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}日志分析工具${NC}"
    echo "使用方法: $0 [errors|warnings|summary|size]"
    echo ""
    echo "选项:"
    echo "  errors   - 分析所有日志中的错误"
    echo "  warnings - 分析所有日志中的警告"
    echo "  summary  - 显示日志摘要信息"
    echo "  size     - 显示日志文件大小"
    echo ""
}

# 检查容器是否运行
check_container() {
    local container_id=$(get_container_id)
    if [ -z "$container_id" ]; then
        echo -e "${RED}错误: 找不到运行中的容器${NC}"
        exit 1
    fi
    echo $container_id
}

# 分析错误
analyze_errors() {
    local container_id=$(check_container)
    
    echo -e "${RED}分析应用日志中的错误${NC}"
    docker exec $container_id grep -i "error" /var/log/app/app.log | tail -n 50
    
    echo -e "\n${RED}分析Nginx错误日志${NC}"
    docker exec $container_id grep -i "error" /var/log/nginx/error.log | tail -n 20
    
    echo -e "\n${RED}分析Supervisor日志中的错误${NC}"
    docker exec $container_id grep -i "error" /var/log/supervisor/supervisord.log | tail -n 20
    
    echo -e "\n${RED}分析调试日志中的错误${NC}"
    docker exec $container_id grep -i "error" /app/logs/debug.log | tail -n 50
}

# 分析警告
analyze_warnings() {
    local container_id=$(check_container)
    
    echo -e "${YELLOW}分析应用日志中的警告${NC}"
    docker exec $container_id grep -i "warn" /var/log/app/app.log | tail -n 50
    
    echo -e "\n${YELLOW}分析Nginx错误日志中的警告${NC}"
    docker exec $container_id grep -i "warn" /var/log/nginx/error.log | tail -n 20
    
    echo -e "\n${YELLOW}分析Supervisor日志中的警告${NC}"
    docker exec $container_id grep -i "warn" /var/log/supervisor/supervisord.log | tail -n 20
    
    echo -e "\n${YELLOW}分析调试日志中的警告${NC}"
    docker exec $container_id grep -i "warn" /app/logs/debug.log | tail -n 50
}

# 显示日志摘要
show_summary() {
    local container_id=$(check_container)
    
    echo -e "${BLUE}应用日志摘要${NC}"
    docker exec $container_id bash -c "cat /var/log/app/app.log | wc -l | xargs echo '总行数:'"
    docker exec $container_id bash -c "grep -i 'error' /var/log/app/app.log | wc -l | xargs echo '错误数:'"
    docker exec $container_id bash -c "grep -i 'warn' /var/log/app/app.log | wc -l | xargs echo '警告数:'"
    
    echo -e "\n${BLUE}Nginx访问日志摘要${NC}"
    docker exec $container_id bash -c "cat /var/log/nginx/access.log | wc -l | xargs echo '总请求数:'"
    docker exec $container_id bash -c "grep ' 404 ' /var/log/nginx/access.log | wc -l | xargs echo '404错误数:'"
    docker exec $container_id bash -c "grep ' 500 ' /var/log/nginx/access.log | wc -l | xargs echo '500错误数:'"
    
    echo -e "\n${BLUE}Nginx错误日志摘要${NC}"
    docker exec $container_id bash -c "cat /var/log/nginx/error.log | wc -l | xargs echo '总行数:'"
    
    echo -e "\n${BLUE}调试日志摘要${NC}"
    docker exec $container_id bash -c "cat /app/logs/debug.log | wc -l | xargs echo '总行数:'"
    docker exec $container_id bash -c "grep -i 'error' /app/logs/debug.log | wc -l | xargs echo '错误数:'"
    docker exec $container_id bash -c "grep -i 'warn' /app/logs/debug.log | wc -l | xargs echo '警告数:'"
}

# 显示日志文件大小
show_size() {
    local container_id=$(check_container)
    
    echo -e "${GREEN}日志文件大小${NC}"
    docker exec $container_id bash -c "du -sh /var/log/app/app.log | cut -f1"
    docker exec $container_id bash -c "du -sh /var/log/nginx/access.log | cut -f1"
    docker exec $container_id bash -c "du -sh /var/log/nginx/error.log | cut -f1"
    docker exec $container_id bash -c "du -sh /var/log/supervisor/supervisord.log | cut -f1"
    docker exec $container_id bash -c "du -sh /app/logs/debug.log | cut -f1"
    
    echo -e "\n${GREEN}日志目录总大小${NC}"
    docker exec $container_id bash -c "du -sh /var/log/app | cut -f1"
    docker exec $container_id bash -c "du -sh /var/log/nginx | cut -f1"
    docker exec $container_id bash -c "du -sh /var/log/supervisor | cut -f1"
    docker exec $container_id bash -c "du -sh /app/logs | cut -f1"
}

# 主函数
main() {
    if [ $# -lt 1 ]; then
        show_help
        exit 1
    fi
    
    local action=$1
    
    case $action in
        errors)
            analyze_errors
            ;;
        warnings)
            analyze_warnings
            ;;
        summary)
            show_summary
            ;;
        size)
            show_size
            ;;
        help|-h|--help)
            show_help
            ;;
        *)
            echo -e "${RED}错误: 未知操作 '$action'${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main $@
