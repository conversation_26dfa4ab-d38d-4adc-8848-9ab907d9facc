#!/bin/bash

# 测试容器中嵌入模型预加载的脚本
# 使用方法: ./test_embedding_in_container.sh [pod_name]

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 获取Pod名称
if [ -z "$1" ]; then
    POD_NAME=$(kubectl get pods -l app=agent-testing -o jsonpath="{.items[0].metadata.name}")
    if [ -z "$POD_NAME" ]; then
        echo -e "${RED}错误: 找不到agent-testing的Pod，请提供Pod名称作为参数${NC}"
        exit 1
    fi
else
    POD_NAME=$1
fi

echo -e "${BLUE}在Pod ${POD_NAME} 中测试嵌入模型预加载${NC}"

# 检查测试脚本是否存在
echo -e "${YELLOW}检查测试脚本是否存在...${NC}"
kubectl exec $POD_NAME -- ls -la /app/test_embedding_preload.py 2>/dev/null
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}测试脚本不存在，正在复制到容器中...${NC}"
    # 创建临时测试脚本
    cat > /tmp/test_embedding_preload.py << 'EOF'
"""
测试嵌入模型预加载脚本

这个脚本用于测试嵌入模型是否已经被正确预加载到容器中。
它会尝试加载嵌入模型并测量加载时间，以验证模型是否已经被缓存。
"""

import os
import time
import logging
from sentence_transformers import SentenceTransformer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 模型配置
EMBEDDING_MODEL = os.environ.get("EMBEDDING_MODEL", "BAAI/bge-large-zh")
EMBEDDING_DEVICE = os.environ.get("EMBEDDING_DEVICE", "cpu")
EMBEDDING_CACHE_DIR = os.environ.get("EMBEDDING_CACHE_DIR", "/root/.cache/huggingface")

# 测试文本
TEST_TEXTS = [
    "这是第一个测试文本，用于验证嵌入模型是否已预加载",
    "这是第二个测试文本，包含不同的内容",
    "这是第三个测试文本，用于确保模型已完全加载"
]

def test_embedding_preload():
    """测试嵌入模型预加载"""
    logger.info(f"开始测试嵌入模型预加载: {EMBEDDING_MODEL}")
    logger.info(f"设备: {EMBEDDING_DEVICE}")
    logger.info(f"缓存目录: {EMBEDDING_CACHE_DIR}")
    
    # 检查缓存目录是否存在
    if os.path.exists(EMBEDDING_CACHE_DIR):
        logger.info(f"缓存目录存在: {EMBEDDING_CACHE_DIR}")
        # 列出缓存目录中的文件
        files = os.listdir(EMBEDDING_CACHE_DIR)
        logger.info(f"缓存目录中的文件数量: {len(files)}")
        if len(files) > 0:
            logger.info(f"缓存目录中的部分文件: {files[:5]}")
    else:
        logger.warning(f"缓存目录不存在: {EMBEDDING_CACHE_DIR}")
    
    # 测量模型加载时间
    start_time = time.time()
    logger.info("开始加载模型...")
    
    try:
        # 创建模型
        model = SentenceTransformer(
            EMBEDDING_MODEL, 
            device=EMBEDDING_DEVICE,
            cache_folder=EMBEDDING_CACHE_DIR
        )
        
        load_time = time.time() - start_time
        logger.info(f"模型加载完成，耗时: {load_time:.2f}秒")
        
        # 获取模型信息
        model_size_mb = sum(p.numel() * 4 / (1024 * 1024) for p in model.parameters())
        logger.info(f"模型大小: {model_size_mb:.2f} MB")
        logger.info(f"模型维度: {model.get_sentence_embedding_dimension()}")
        
        # 测试编码
        start_encode_time = time.time()
        logger.info("开始编码测试文本...")
        
        embeddings = model.encode(TEST_TEXTS)
        
        encode_time = time.time() - start_encode_time
        logger.info(f"编码完成，耗时: {encode_time:.2f}秒")
        logger.info(f"嵌入维度: {embeddings.shape}")
        
        # 测试相似度计算
        logger.info("测试相似度计算...")
        similarities = model.similarity(embeddings, embeddings)
        logger.info(f"相似度矩阵形状: {similarities.shape}")
        
        logger.info("测试成功完成！")
        return True
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    test_embedding_preload()
EOF
    
    # 复制测试脚本到容器
    kubectl cp /tmp/test_embedding_preload.py $POD_NAME:/app/test_embedding_preload.py
    
    # 清理临时文件
    rm /tmp/test_embedding_preload.py
    
    echo -e "${GREEN}测试脚本已复制到容器中${NC}"
else
    echo -e "${GREEN}测试脚本已存在${NC}"
fi

# 运行测试脚本
echo -e "${BLUE}运行测试脚本...${NC}"
kubectl exec $POD_NAME -- python /app/test_embedding_preload.py

# 检查缓存目录
echo -e "${BLUE}检查缓存目录...${NC}"
kubectl exec $POD_NAME -- ls -la /root/.cache/huggingface

# 检查模型文件
echo -e "${BLUE}检查模型文件...${NC}"
kubectl exec $POD_NAME -- find /root/.cache -name "*.bin" | head -5

echo -e "${GREEN}测试完成${NC}"
