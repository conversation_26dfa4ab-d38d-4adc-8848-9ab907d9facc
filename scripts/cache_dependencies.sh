#!/bin/bash
# 依赖缓存脚本 - 用于预先下载和缓存项目依赖

set -e

# 创建缓存目录
CACHE_DIR="./dependency_cache"
mkdir -p $CACHE_DIR
mkdir -p $CACHE_DIR/npm
mkdir -p $CACHE_DIR/pip

echo "===== 开始缓存依赖 ====="

# 缓存npm依赖
echo "正在缓存npm依赖..."
if [ ! -d "$CACHE_DIR/npm/node_modules" ]; then
  cd web
  npm config set registry https://registry.npmmirror.com/
  npm install -g pnpm@latest
  pnpm config set registry https://registry.npmmirror.com/
  pnpm config set store-dir $CACHE_DIR/npm/store
  pnpm install --frozen-lockfile
  cd ..
  echo "npm依赖缓存完成"
else
  echo "使用已缓存的npm依赖"
fi

# 缓存pip依赖
echo "正在缓存pip依赖..."
if [ ! -d "$CACHE_DIR/pip/packages" ]; then
  mkdir -p $CACHE_DIR/pip/packages
  pip download -d $CACHE_DIR/pip/packages -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
  echo "pip依赖缓存完成"
else
  echo "使用已缓存的pip依赖"
fi

echo "===== 依赖缓存完成 ====="
