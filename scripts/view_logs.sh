#!/bin/bash

# 日志查看脚本
# 使用方法: ./view_logs.sh [app|nginx|supervisor|all] [tail|cat|grep] [参数]

# 确保脚本可执行
# chmod +x view_logs.sh

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 获取容器ID
get_container_id() {
    echo $(docker ps --filter "name=agent_testing" --format "{{.ID}}")
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}日志查看工具${NC}"
    echo "使用方法: $0 [app|nginx|supervisor|all] [tail|cat|grep] [参数]"
    echo ""
    echo "选项:"
    echo "  app        - 查看应用日志"
    echo "  nginx      - 查看Nginx日志"
    echo "  supervisor - 查看Supervisor日志"
    echo "  all        - 查看所有日志"
    echo ""
    echo "命令:"
    echo "  tail       - 实时查看日志 (默认显示最后10行)"
    echo "  cat        - 显示整个日志文件"
    echo "  grep       - 搜索日志内容"
    echo ""
    echo "示例:"
    echo "  $0 app tail -f        # 实时查看应用日志"
    echo "  $0 nginx cat          # 显示Nginx访问日志"
    echo "  $0 app grep \"error\"   # 在应用日志中搜索\"error\""
    echo "  $0 all grep \"error\"   # 在所有日志中搜索\"error\""
    echo "  $0 app debug          # 查看应用调试日志"
    echo ""
}

# 检查容器是否运行
check_container() {
    local container_id=$(get_container_id)
    if [ -z "$container_id" ]; then
        echo -e "${RED}错误: 找不到运行中的容器${NC}"
        exit 1
    fi
    echo $container_id
}

# 查看应用日志
view_app_logs() {
    local container_id=$(check_container)
    local command=$1
    shift
    
    case $command in
        tail)
            echo -e "${GREEN}查看应用日志 (tail)${NC}"
            docker exec $container_id tail $@ /var/log/app/app.log
            ;;
        cat)
            echo -e "${GREEN}查看应用日志 (cat)${NC}"
            docker exec $container_id cat /var/log/app/app.log
            ;;
        grep)
            echo -e "${GREEN}搜索应用日志 (grep $@)${NC}"
            docker exec $container_id grep $@ /var/log/app/app.log
            ;;
        debug)
            echo -e "${GREEN}查看应用调试日志${NC}"
            docker exec $container_id cat /app/logs/debug.log | tail -n 100
            ;;
        *)
            echo -e "${RED}错误: 未知命令 '$command'${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 查看Nginx日志
view_nginx_logs() {
    local container_id=$(check_container)
    local command=$1
    shift
    
    case $command in
        tail)
            echo -e "${GREEN}查看Nginx访问日志 (tail)${NC}"
            docker exec $container_id tail $@ /var/log/nginx/access.log
            echo -e "\n${GREEN}查看Nginx错误日志 (tail)${NC}"
            docker exec $container_id tail $@ /var/log/nginx/error.log
            ;;
        cat)
            echo -e "${GREEN}查看Nginx访问日志 (cat)${NC}"
            docker exec $container_id cat /var/log/nginx/access.log
            echo -e "\n${GREEN}查看Nginx错误日志 (cat)${NC}"
            docker exec $container_id cat /var/log/nginx/error.log
            ;;
        grep)
            echo -e "${GREEN}搜索Nginx访问日志 (grep $@)${NC}"
            docker exec $container_id grep $@ /var/log/nginx/access.log
            echo -e "\n${GREEN}搜索Nginx错误日志 (grep $@)${NC}"
            docker exec $container_id grep $@ /var/log/nginx/error.log
            ;;
        *)
            echo -e "${RED}错误: 未知命令 '$command'${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 查看Supervisor日志
view_supervisor_logs() {
    local container_id=$(check_container)
    local command=$1
    shift
    
    case $command in
        tail)
            echo -e "${GREEN}查看Supervisor日志 (tail)${NC}"
            docker exec $container_id tail $@ /var/log/supervisor/supervisord.log
            ;;
        cat)
            echo -e "${GREEN}查看Supervisor日志 (cat)${NC}"
            docker exec $container_id cat /var/log/supervisor/supervisord.log
            ;;
        grep)
            echo -e "${GREEN}搜索Supervisor日志 (grep $@)${NC}"
            docker exec $container_id grep $@ /var/log/supervisor/supervisord.log
            ;;
        *)
            echo -e "${RED}错误: 未知命令 '$command'${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 查看所有日志
view_all_logs() {
    local command=$1
    shift
    
    case $command in
        grep)
            echo -e "${YELLOW}在所有日志中搜索 '$@'${NC}"
            view_app_logs grep $@
            echo ""
            view_nginx_logs grep $@
            echo ""
            view_supervisor_logs grep $@
            ;;
        *)
            echo -e "${RED}错误: 'all' 选项只支持 'grep' 命令${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 主函数
main() {
    if [ $# -lt 1 ]; then
        show_help
        exit 1
    fi
    
    local log_type=$1
    shift
    
    case $log_type in
        app)
            if [ $# -lt 1 ]; then
                view_app_logs tail -n 20
            else
                view_app_logs $@
            fi
            ;;
        nginx)
            if [ $# -lt 1 ]; then
                view_nginx_logs tail -n 20
            else
                view_nginx_logs $@
            fi
            ;;
        supervisor)
            if [ $# -lt 1 ]; then
                view_supervisor_logs tail -n 20
            else
                view_supervisor_logs $@
            fi
            ;;
        all)
            if [ $# -lt 1 ]; then
                echo -e "${RED}错误: 'all' 选项需要指定命令${NC}"
                show_help
                exit 1
            else
                view_all_logs $@
            fi
            ;;
        help|-h|--help)
            show_help
            ;;
        *)
            echo -e "${RED}错误: 未知日志类型 '$log_type'${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main $@
