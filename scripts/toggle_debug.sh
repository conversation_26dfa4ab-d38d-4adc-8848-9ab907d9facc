#!/bin/bash

# 日志级别切换脚本
# 使用方法: ./toggle_debug.sh [on|off]

# 确保脚本可执行
# chmod +x toggle_debug.sh

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 获取容器ID
get_container_id() {
    echo $(docker ps --filter "name=agent_testing" --format "{{.ID}}")
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}日志级别切换工具${NC}"
    echo "使用方法: $0 [on|off|status]"
    echo ""
    echo "选项:"
    echo "  on     - 开启调试日志 (设置LOG_LEVEL=DEBUG)"
    echo "  off    - 关闭调试日志 (设置LOG_LEVEL=INFO)"
    echo "  status - 显示当前日志级别"
    echo ""
}

# 检查容器是否运行
check_container() {
    local container_id=$(get_container_id)
    if [ -z "$container_id" ]; then
        echo -e "${RED}错误: 找不到运行中的容器${NC}"
        exit 1
    fi
    echo $container_id
}

# 获取当前日志级别
get_log_level() {
    local container_id=$(check_container)
    local log_level=$(docker exec $container_id bash -c 'echo $LOG_LEVEL')
    if [ -z "$log_level" ]; then
        log_level="INFO (默认)"
    fi
    echo $log_level
}

# 设置日志级别
set_log_level() {
    local container_id=$(check_container)
    local level=$1
    
    docker exec $container_id bash -c "export LOG_LEVEL=$level"
    
    # 重启应用以应用新的日志级别
    docker exec $container_id supervisorctl restart fastapi
    
    echo -e "${GREEN}日志级别已设置为 $level${NC}"
    echo -e "${YELLOW}注意: 这只是临时更改，容器重启后将恢复为默认设置${NC}"
    echo -e "${YELLOW}要永久更改，请修改docker-compose.yml文件中的LOG_LEVEL环境变量${NC}"
}

# 主函数
main() {
    if [ $# -lt 1 ]; then
        show_help
        exit 1
    fi
    
    local action=$1
    
    case $action in
        on)
            set_log_level "DEBUG"
            ;;
        off)
            set_log_level "INFO"
            ;;
        status)
            local level=$(get_log_level)
            echo -e "${GREEN}当前日志级别: $level${NC}"
            ;;
        help|-h|--help)
            show_help
            ;;
        *)
            echo -e "${RED}错误: 未知操作 '$action'${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main $@
