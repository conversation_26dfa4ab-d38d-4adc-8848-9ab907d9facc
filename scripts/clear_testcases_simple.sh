#!/bin/bash

# 简单的清除测试用例数据脚本

# 默认数据库连接参数
DB_USER="postgres"
DB_PASSWORD="postgres"
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="agent_testing"

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -u, --user USER        数据库用户名 (默认: $DB_USER)"
    echo "  -p, --password PASS    数据库密码 (默认: $DB_PASSWORD)"
    echo "  -h, --host HOST        数据库主机地址 (默认: $DB_HOST)"
    echo "  -P, --port PORT        数据库端口 (默认: $DB_PORT)"
    echo "  -d, --dbname DBNAME    数据库名称 (默认: $DB_NAME)"
    echo "  -f, --force            强制执行，不提示确认"
    echo "  --help                 显示此帮助信息"
    exit 0
}

# 解析命令行参数
FORCE=false
while [[ $# -gt 0 ]]; do
    case "$1" in
        -u|--user)
            DB_USER="$2"
            shift 2
            ;;
        -p|--password)
            DB_PASSWORD="$2"
            shift 2
            ;;
        -h|--host)
            DB_HOST="$2"
            shift 2
            ;;
        -P|--port)
            DB_PORT="$2"
            shift 2
            ;;
        -d|--dbname)
            DB_NAME="$2"
            shift 2
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        --help)
            show_help
            ;;
        *)
            echo "未知选项: $1"
            show_help
            ;;
    esac
done

# 确认操作
if [ "$FORCE" = false ]; then
    echo "警告：此操作将删除所有测试用例数据，且无法恢复！"
    read -p "确定要继续吗？(y/n): " CONFIRM
    if [ "$CONFIRM" != "y" ] && [ "$CONFIRM" != "Y" ]; then
        echo "操作已取消"
        exit 0
    fi
fi

# 创建临时SQL文件
TMP_SQL=$(mktemp)
cat > "$TMP_SQL" << EOF
-- 开始事务
BEGIN;

-- 1. 首先删除测试用例和测试步骤的关联关系
DELETE FROM test_cases_test_steps;

-- 2. 删除测试步骤
DELETE FROM test_steps;

-- 3. 最后删除测试用例
DELETE FROM test_cases;

-- 4. 重置自增ID（可选）
ALTER SEQUENCE test_cases_id_seq RESTART WITH 1;
ALTER SEQUENCE test_steps_id_seq RESTART WITH 1;

-- 提交事务
COMMIT;

-- 确认删除结果
\echo '删除操作完成，当前数据统计：'
\echo '测试用例数量：'
SELECT COUNT(*) FROM test_cases;
\echo '测试步骤数量：'
SELECT COUNT(*) FROM test_steps;
\echo '关联关系数量：'
SELECT COUNT(*) FROM test_cases_test_steps;
EOF

# 执行SQL脚本
echo "正在连接到数据库: $DB_HOST:$DB_PORT/$DB_NAME..."
export PGPASSWORD="$DB_PASSWORD"
psql -U "$DB_USER" -h "$DB_HOST" -p "$DB_PORT" -d "$DB_NAME" -f "$TMP_SQL"

# 检查执行结果
if [ $? -eq 0 ]; then
    echo "所有测试用例数据已清除！"
else
    echo "清除数据时出错，请检查数据库连接参数和权限。"
fi

# 删除临时SQL文件
rm -f "$TMP_SQL"
