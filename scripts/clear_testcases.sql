-- 清除所有测试用例数据的SQL脚本

-- 开始事务
BEGIN;

-- 1. 首先删除测试用例和测试步骤的关联关系
DELETE FROM test_cases_test_steps;

-- 2. 删除测试步骤
DELETE FROM test_steps;

-- 3. 最后删除测试用例
DELETE FROM test_cases;

-- 4. 重置自增ID（可选）
ALTER SEQUENCE test_cases_id_seq RESTART WITH 1;
ALTER SEQUENCE test_steps_id_seq RESTART WITH 1;

-- 提交事务
COMMIT;

-- 确认删除结果
SELECT COUNT(*) FROM test_cases;
SELECT COUNT(*) FROM test_steps;
SELECT COUNT(*) FROM test_cases_test_steps;
