"""
使用 psycopg2 清除数据库中的所有测试用例数据
"""
import sys
import os
import logging
import argparse
import psycopg2
from psycopg2 import sql

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clear_testcases(db_user, db_password, db_host, db_port, db_name):
    """清除所有测试用例数据"""
    # 构建数据库连接参数
    conn_params = {
        "user": db_user,
        "password": db_password,
        "host": db_host,
        "port": db_port,
        "dbname": db_name
    }
    
    logger.info(f"正在连接到数据库: {db_host}:{db_port}/{db_name}...")
    
    # 连接到数据库
    try:
        conn = psycopg2.connect(**conn_params)
        conn.autocommit = False  # 使用事务
        cursor = conn.cursor()
        logger.info("数据库连接成功！")
        
        try:
            # 1. 首先删除测试用例和测试步骤的关联关系
            logger.info("开始删除测试用例和测试步骤的关联关系...")
            cursor.execute("DELETE FROM test_cases_test_steps")
            logger.info(f"删除测试用例和测试步骤的关联关系完成，影响行数：{cursor.rowcount}")
            
            # 2. 删除测试步骤
            logger.info("开始删除测试步骤...")
            cursor.execute("DELETE FROM test_steps")
            logger.info(f"删除测试步骤完成，影响行数：{cursor.rowcount}")
            
            # 3. 最后删除测试用例
            logger.info("开始删除测试用例...")
            cursor.execute("DELETE FROM test_cases")
            logger.info(f"删除测试用例完成，影响行数：{cursor.rowcount}")
            
            # 4. 重置自增ID（可选）
            logger.info("开始重置自增ID...")
            cursor.execute("ALTER SEQUENCE test_cases_id_seq RESTART WITH 1")
            cursor.execute("ALTER SEQUENCE test_steps_id_seq RESTART WITH 1")
            logger.info("重置自增ID完成")
            
            # 提交事务
            conn.commit()
            logger.info("所有测试用例数据已清除！")
            
            # 统计数据
            cursor.execute("SELECT COUNT(*) FROM test_cases")
            testcase_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM test_steps")
            teststep_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM test_cases_test_steps")
            relation_count = cursor.fetchone()[0]
            
            logger.info(f"数据库中现有 {testcase_count} 个测试用例，{teststep_count} 个测试步骤，{relation_count} 个关联关系")
            
        except Exception as e:
            # 回滚事务
            conn.rollback()
            logger.error(f"清除数据时出错，已回滚事务：{str(e)}")
            raise
        finally:
            # 关闭游标
            cursor.close()
    except Exception as e:
        logger.error(f"连接数据库时出错：{str(e)}")
        raise
    finally:
        # 关闭数据库连接
        if 'conn' in locals() and conn:
            conn.close()
            logger.info("数据库连接已关闭")

def main():
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description="清除数据库中的所有测试用例数据")
    parser.add_argument("--user", default="postgres", help="数据库用户名")
    parser.add_argument("--password", default="postgres", help="数据库密码")
    parser.add_argument("--host", default="localhost", help="数据库主机地址")
    parser.add_argument("--port", default="5432", help="数据库端口")
    parser.add_argument("--dbname", default="agent_testing", help="数据库名称")
    parser.add_argument("--force", action="store_true", help="强制执行，不提示确认")
    
    args = parser.parse_args()
    
    # 添加确认提示
    if args.force or input("警告：此操作将删除所有测试用例数据，且无法恢复！确定要继续吗？(y/n): ").lower() == 'y':
        clear_testcases(args.user, args.password, args.host, args.port, args.dbname)
    else:
        print("操作已取消")

if __name__ == "__main__":
    main()
