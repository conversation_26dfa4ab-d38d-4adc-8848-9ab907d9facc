"""
清除数据库中的所有测试用例数据
"""
import sys
import os
import asyncio
import logging
import argparse

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tortoise import Tortoise

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def clear_testcases(db_user, db_password, db_host, db_port, db_name):
    """清除所有测试用例数据"""
    # 构建数据库连接URL
    db_url = f"postgres://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
    logger.info(f"正在连接到数据库: {db_host}:{db_port}/{db_name}...")

    # 连接到数据库
    try:
        await Tortoise.init(
            db_url=db_url,
            modules={"models": ["app.models.admin"]}
        )

        conn = Tortoise.get_connection("default")
        logger.info("数据库连接成功！")

        # 1. 首先删除测试用例和测试步骤的关联关系
        logger.info("开始删除测试用例和测试步骤的关联关系...")
        sql = "DELETE FROM test_cases_test_steps"
        result = await conn.execute_query(sql)
        logger.info(f"删除测试用例和测试步骤的关联关系完成，影响行数：{result[0] if result and result[0] else 0}")

        # 2. 删除测试步骤
        logger.info("开始删除测试步骤...")
        sql = "DELETE FROM test_steps"
        result = await conn.execute_query(sql)
        logger.info(f"删除测试步骤完成，影响行数：{result[0] if result and result[0] else 0}")

        # 3. 最后删除测试用例
        logger.info("开始删除测试用例...")
        sql = "DELETE FROM test_cases"
        result = await conn.execute_query(sql)
        logger.info(f"删除测试用例完成，影响行数：{result[0] if result and result[0] else 0}")

        # 4. 重置自增ID（可选）
        logger.info("开始重置自增ID...")
        sql = "ALTER SEQUENCE test_cases_id_seq RESTART WITH 1"
        await conn.execute_query(sql)
        sql = "ALTER SEQUENCE test_steps_id_seq RESTART WITH 1"
        await conn.execute_query(sql)
        logger.info("重置自增ID完成")

        logger.info("所有测试用例数据已清除！")
    except Exception as e:
        logger.error(f"清除数据时出错：{str(e)}")
        raise
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()

def main():
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description="清除数据库中的所有测试用例数据")
    parser.add_argument("--user", default="postgres", help="数据库用户名")
    parser.add_argument("--password", default="postgres", help="数据库密码")
    parser.add_argument("--host", default="localhost", help="数据库主机地址")
    parser.add_argument("--port", default="5432", help="数据库端口")
    parser.add_argument("--dbname", default="agent_testing", help="数据库名称")
    parser.add_argument("--force", action="store_true", help="强制执行，不提示确认")

    args = parser.parse_args()

    # 添加确认提示
    if args.force or input("警告：此操作将删除所有测试用例数据，且无法恢复！确定要继续吗？(y/n): ").lower() == 'y':
        asyncio.run(clear_testcases(args.user, args.password, args.host, args.port, args.dbname))
    else:
        print("操作已取消")

if __name__ == "__main__":
    main()
