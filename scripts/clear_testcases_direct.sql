-- 清除所有测试用例数据的SQL脚本（直接执行版本）

-- 开始事务
BEGIN;

-- 1. 首先删除测试用例和测试步骤的关联关系
DELETE FROM test_cases_test_steps;

-- 2. 删除测试步骤
DELETE FROM test_steps;

-- 3. 最后删除测试用例
DELETE FROM test_cases;

-- 4. 重置自增ID（可选）
ALTER SEQUENCE test_cases_id_seq RESTART WITH 1;
ALTER SEQUENCE test_steps_id_seq RESTART WITH 1;

-- 提交事务
COMMIT;

-- 确认删除结果
\echo '删除操作完成，当前数据统计：'
\echo '测试用例数量：'
SELECT COUNT(*) FROM test_cases;
\echo '测试步骤数量：'
SELECT COUNT(*) FROM test_steps;
\echo '关联关系数量：'
SELECT COUNT(*) FROM test_cases_test_steps;
