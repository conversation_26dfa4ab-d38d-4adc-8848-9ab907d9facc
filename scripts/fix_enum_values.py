"""
修复脚本，用于修复数据库中的枚举值。
"""
import sys
import os
import asyncio
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tortoise import Tortoise

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def fix_enum_values():
    """修复枚举值"""
    # 连接到数据库
    await Tortoise.init(
        db_url="postgres://admin:admin@localhost:5432/agent_testing",
        modules={"models": ["app.models.admin"]}
    )
    
    conn = Tortoise.get_connection("default")
    
    try:
        # 1. 修复tags字段
        logger.info("开始修复tags字段...")
        sql = """
        UPDATE test_cases
        SET tags = 
            CASE 
                WHEN tags = '单元测试' THEN '单元'
                WHEN tags = '功能测试' THEN '功能'
                WHEN tags = '集成测试' THEN '集成'
                WHEN tags = '系统测试' THEN '系统'
                WHEN tags = '冒烟测试' THEN '冒烟'
                WHEN tags = '版本验证' THEN '版本'
                WHEN tags = '性能测试' THEN '性能'
                WHEN tags = '压力测试' THEN '压力'
                WHEN tags = '异常测试' THEN '异常'
                WHEN tags = '并发测试' THEN '并发'
                WHEN tags = '边界测试' THEN '边界'
                WHEN tags = '兼容性测试' THEN '兼容'
                WHEN tags = '安全测试' THEN '安全'
                WHEN tags = 'UI测试' THEN 'UI'
                WHEN tags = '配置测试' THEN '配置'
                ELSE '功能'
            END
        WHERE tags IN ('单元测试', '功能测试', '集成测试', '系统测试', '冒烟测试', '版本验证', '性能测试', '压力测试', '异常测试', '并发测试', '边界测试', '兼容性测试', '安全测试', 'UI测试', '配置测试')
        """
        result = await conn.execute_query(sql)
        logger.info(f"修复tags字段完成，影响行数：{result[0] if result and result[0] else 0}")
        
        # 2. 修复status字段
        logger.info("开始修复status字段...")
        sql = """
        UPDATE test_cases
        SET status = 
            CASE 
                WHEN status = '未开始' THEN '待测'
                WHEN status = '进行中' THEN '进行'
                WHEN status = '通过' THEN '通过'
                WHEN status = '失败' THEN '失败'
                WHEN status = '阻塞' THEN '阻塞'
                ELSE '待测'
            END
        WHERE status IN ('未开始', '进行中', '通过', '失败', '阻塞')
        """
        result = await conn.execute_query(sql)
        logger.info(f"修复status字段完成，影响行数：{result[0] if result and result[0] else 0}")
        
        # 3. 修复priority字段
        logger.info("开始修复priority字段...")
        sql = """
        UPDATE test_cases
        SET priority = 
            CASE 
                WHEN priority = '高' THEN '高'
                WHEN priority = '中' THEN '中'
                WHEN priority = '低' THEN '低'
                ELSE '中'
            END
        WHERE priority IN ('高', '中', '低')
        """
        result = await conn.execute_query(sql)
        logger.info(f"修复priority字段完成，影响行数：{result[0] if result and result[0] else 0}")
        
        logger.info("枚举值修复完成！")
    except Exception as e:
        logger.error(f"修复过程中出错：{str(e)}")
        raise
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(fix_enum_values())
