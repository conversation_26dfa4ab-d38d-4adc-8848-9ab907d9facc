#!/usr/bin/env python
import sys
import os
import subprocess
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_migration(sql_file_path):
    """
    执行SQL迁移脚本
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(sql_file_path):
            logger.error(f"SQL文件不存在: {sql_file_path}")
            return False
        
        # 从环境变量或配置文件获取数据库连接信息
        db_host = os.environ.get("DB_HOST", "localhost")
        db_user = os.environ.get("DB_USER", "admin")
        db_password = os.environ.get("DB_PASSWORD", "admin123")
        db_name = os.environ.get("DB_NAME", "agent_testing")
        
        # 构建psql命令
        cmd = f"PGPASSWORD={db_password} psql -h {db_host} -U {db_user} -d {db_name} -f {sql_file_path}"
        
        # 执行命令
        logger.info(f"执行SQL迁移: {sql_file_path}")
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        # 检查执行结果
        if result.returncode == 0:
            logger.info(f"SQL迁移成功: {sql_file_path}")
            logger.info(f"输出: {result.stdout}")
            return True
        else:
            logger.error(f"SQL迁移失败: {sql_file_path}")
            logger.error(f"错误: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"执行SQL迁移时发生错误: {str(e)}")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        logger.error("请提供SQL迁移文件路径")
        sys.exit(1)
    
    sql_file_path = sys.argv[1]
    success = run_migration(sql_file_path)
    
    if success:
        logger.info("迁移成功完成")
        sys.exit(0)
    else:
        logger.error("迁移失败")
        sys.exit(1)
