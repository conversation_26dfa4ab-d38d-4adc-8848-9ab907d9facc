"""
数据库管理工具 - 提供测试用例数据管理功能
"""
import sys
import os
import asyncio
import logging
import argparse

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tortoise import Tortoise

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库连接参数
DB_CONFIG = {
    "user": "postgres",
    "password": "postgres",
    "host": "localhost",
    "port": "5432",
    "dbname": "agent_testing"
}

async def connect_db(db_user=None, db_password=None, db_host=None, db_port=None, db_name=None):
    """连接到数据库"""
    # 使用提供的参数或默认值
    user = db_user or DB_CONFIG["user"]
    password = db_password or DB_CONFIG["password"]
    host = db_host or DB_CONFIG["host"]
    port = db_port or DB_CONFIG["port"]
    dbname = db_name or DB_CONFIG["dbname"]

    # 构建数据库连接URL
    db_url = f"postgres://{user}:{password}@{host}:{port}/{dbname}"
    logger.info(f"正在连接到数据库: {host}:{port}/{dbname}...")

    await Tortoise.init(
        db_url=db_url,
        modules={"models": ["app.models.admin"]}
    )

    logger.info("数据库连接成功！")
    return Tortoise.get_connection("default")

async def close_db():
    """关闭数据库连接"""
    await Tortoise.close_connections()

async def clear_testcases(db_user=None, db_password=None, db_host=None, db_port=None, db_name=None):
    """清除所有测试用例数据"""
    conn = await connect_db(db_user, db_password, db_host, db_port, db_name)

    try:
        # 1. 首先删除测试用例和测试步骤的关联关系
        logger.info("开始删除测试用例和测试步骤的关联关系...")
        sql = "DELETE FROM test_cases_test_steps"
        result = await conn.execute_query(sql)
        logger.info(f"删除测试用例和测试步骤的关联关系完成，影响行数：{result[0] if result and result[0] else 0}")

        # 2. 删除测试步骤
        logger.info("开始删除测试步骤...")
        sql = "DELETE FROM test_steps"
        result = await conn.execute_query(sql)
        logger.info(f"删除测试步骤完成，影响行数：{result[0] if result and result[0] else 0}")

        # 3. 最后删除测试用例
        logger.info("开始删除测试用例...")
        sql = "DELETE FROM test_cases"
        result = await conn.execute_query(sql)
        logger.info(f"删除测试用例完成，影响行数：{result[0] if result and result[0] else 0}")

        # 4. 重置自增ID（可选）
        logger.info("开始重置自增ID...")
        sql = "ALTER SEQUENCE test_cases_id_seq RESTART WITH 1"
        await conn.execute_query(sql)
        sql = "ALTER SEQUENCE test_steps_id_seq RESTART WITH 1"
        await conn.execute_query(sql)
        logger.info("重置自增ID完成")

        logger.info("所有测试用例数据已清除！")
    except Exception as e:
        logger.error(f"清除数据时出错：{str(e)}")
        raise
    finally:
        await close_db()

async def count_testcases(db_user=None, db_password=None, db_host=None, db_port=None, db_name=None):
    """统计测试用例数量"""
    conn = await connect_db(db_user, db_password, db_host, db_port, db_name)

    try:
        # 统计测试用例数量
        sql = "SELECT COUNT(*) FROM test_cases"
        result = await conn.execute_query(sql)
        testcase_count = result[1][0][0] if result and result[1] and result[1][0] else 0

        # 统计测试步骤数量
        sql = "SELECT COUNT(*) FROM test_steps"
        result = await conn.execute_query(sql)
        teststep_count = result[1][0][0] if result and result[1] and result[1][0] else 0

        # 统计关联关系数量
        sql = "SELECT COUNT(*) FROM test_cases_test_steps"
        result = await conn.execute_query(sql)
        relation_count = result[1][0][0] if result and result[1] and result[1][0] else 0

        logger.info(f"数据库中共有 {testcase_count} 个测试用例，{teststep_count} 个测试步骤，{relation_count} 个关联关系")

        return {
            "testcase_count": testcase_count,
            "teststep_count": teststep_count,
            "relation_count": relation_count
        }
    except Exception as e:
        logger.error(f"统计数据时出错：{str(e)}")
        raise
    finally:
        await close_db()

async def delete_testcase_by_id(testcase_id, db_user=None, db_password=None, db_host=None, db_port=None, db_name=None):
    """根据ID删除测试用例"""
    conn = await connect_db(db_user, db_password, db_host, db_port, db_name)

    try:
        # 1. 首先删除测试用例和测试步骤的关联关系
        logger.info(f"开始删除测试用例 ID={testcase_id} 的关联关系...")
        sql = "DELETE FROM test_cases_test_steps WHERE test_cases_id = $1"
        result = await conn.execute_query(sql, [testcase_id])
        logger.info(f"删除关联关系完成，影响行数：{result[0] if result and result[0] else 0}")

        # 2. 删除测试用例
        logger.info(f"开始删除测试用例 ID={testcase_id}...")
        sql = "DELETE FROM test_cases WHERE id = $1"
        result = await conn.execute_query(sql, [testcase_id])
        logger.info(f"删除测试用例完成，影响行数：{result[0] if result and result[0] else 0}")

        if result and result[0] and result[0] > 0:
            logger.info(f"测试用例 ID={testcase_id} 已成功删除！")
            return True
        else:
            logger.warning(f"未找到 ID={testcase_id} 的测试用例")
            return False
    except Exception as e:
        logger.error(f"删除测试用例时出错：{str(e)}")
        raise
    finally:
        await close_db()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="测试用例数据管理工具")

    # 添加数据库连接参数
    parser.add_argument("--user", default=DB_CONFIG["user"], help=f"数据库用户名 (默认: {DB_CONFIG['user']})")
    parser.add_argument("--password", default=DB_CONFIG["password"], help=f"数据库密码 (默认: {DB_CONFIG['password']})")
    parser.add_argument("--host", default=DB_CONFIG["host"], help=f"数据库主机地址 (默认: {DB_CONFIG['host']})")
    parser.add_argument("--port", default=DB_CONFIG["port"], help=f"数据库端口 (默认: {DB_CONFIG['port']})")
    parser.add_argument("--dbname", default=DB_CONFIG["dbname"], help=f"数据库名称 (默认: {DB_CONFIG['dbname']})")

    subparsers = parser.add_subparsers(dest="command", help="命令")

    # 清除所有测试用例命令
    clear_parser = subparsers.add_parser("clear", help="清除所有测试用例数据")
    clear_parser.add_argument("--force", action="store_true", help="强制执行，不提示确认")

    # 统计测试用例数量命令
    count_parser = subparsers.add_parser("count", help="统计测试用例数量")

    # 删除指定ID的测试用例命令
    delete_parser = subparsers.add_parser("delete", help="删除指定ID的测试用例")
    delete_parser.add_argument("id", type=int, help="要删除的测试用例ID")

    args = parser.parse_args()

    # 提取数据库连接参数
    db_params = {
        "db_user": args.user,
        "db_password": args.password,
        "db_host": args.host,
        "db_port": args.port,
        "db_name": args.dbname
    }

    if args.command == "clear":
        if args.force or input("警告：此操作将删除所有测试用例数据，且无法恢复！确定要继续吗？(y/n): ").lower() == 'y':
            asyncio.run(clear_testcases(**db_params))
        else:
            print("操作已取消")
    elif args.command == "count":
        result = asyncio.run(count_testcases(**db_params))
        print(f"测试用例数量: {result['testcase_count']}")
        print(f"测试步骤数量: {result['teststep_count']}")
        print(f"关联关系数量: {result['relation_count']}")
    elif args.command == "delete":
        if input(f"确定要删除ID为 {args.id} 的测试用例吗？(y/n): ").lower() == 'y':
            success = asyncio.run(delete_testcase_by_id(args.id, **db_params))
            if success:
                print(f"测试用例 ID={args.id} 已成功删除！")
            else:
                print(f"未找到 ID={args.id} 的测试用例")
        else:
            print("操作已取消")
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
