"""
数据迁移脚本，用于将数据库中的旧枚举值更新为新的枚举值。
"""
import sys
import os
import asyncio
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tortoise import Tortoise

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 枚举值映射
TAG_MAPPING = {
    "单元测试": "单元",
    "功能测试": "功能",
    "集成测试": "集成",
    "系统测试": "系统",
    "冒烟测试": "冒烟",
    "版本验证": "版本",
    "性能测试": "性能",
    "压力测试": "压力",
    "异常测试": "异常",
    "并发测试": "并发",
    "边界测试": "边界",
    "兼容性测试": "兼容",
    "安全测试": "安全",
    "UI测试": "UI",
    "配置测试": "配置"
}

STATUS_MAPPING = {
    "未开始": "待测",
    "进行中": "进行",
    "通过": "通过",
    "失败": "失败",
    "阻塞": "阻塞"
}

PRIORITY_MAPPING = {
    "高": "高",
    "中": "中",
    "低": "低"
}

async def migrate_enum_values():
    """迁移枚举值"""
    # 连接到数据库
    await Tortoise.init(
        db_url="postgres://admin:admin@localhost:5432/agent_testing",
        modules={"models": ["app.models.admin"]}
    )
    
    conn = Tortoise.get_connection("default")
    
    try:
        # 1. 迁移tags字段
        logger.info("开始迁移tags字段...")
        for old_tag, new_tag in TAG_MAPPING.items():
            sql = """
            UPDATE test_cases
            SET tags = $1
            WHERE tags = $2
            """
            result = await conn.execute_query(sql, [new_tag, old_tag])
            logger.info(f"将tags从'{old_tag}'更新为'{new_tag}'，影响行数：{result[0] if result and result[0] else 0}")
        
        # 2. 迁移status字段
        logger.info("开始迁移status字段...")
        for old_status, new_status in STATUS_MAPPING.items():
            sql = """
            UPDATE test_cases
            SET status = $1
            WHERE status = $2
            """
            result = await conn.execute_query(sql, [new_status, old_status])
            logger.info(f"将status从'{old_status}'更新为'{new_status}'，影响行数：{result[0] if result and result[0] else 0}")
        
        # 3. 迁移priority字段
        logger.info("开始迁移priority字段...")
        for old_priority, new_priority in PRIORITY_MAPPING.items():
            sql = """
            UPDATE test_cases
            SET priority = $1
            WHERE priority = $2
            """
            result = await conn.execute_query(sql, [new_priority, old_priority])
            logger.info(f"将priority从'{old_priority}'更新为'{new_priority}'，影响行数：{result[0] if result and result[0] else 0}")
        
        logger.info("枚举值迁移完成！")
    except Exception as e:
        logger.error(f"迁移过程中出错：{str(e)}")
        raise
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(migrate_enum_values())
