#!/usr/bin/env python
import asyncio
import sys
from tortoise import Tortoise, connections
from app.settings.config import settings

async def init():
    # 初始化Tortoise ORM
    await Tortoise.init(config=settings.TORTOISE_ORM)
    print("数据库连接初始化完成")

async def fix_timezone():
    # 读取SQL脚本
    with open("migrations/fix_timezone.sql", "r") as f:
        sql_script = f.read()
    
    # 分割SQL语句
    sql_statements = [stmt.strip() for stmt in sql_script.split(';') if stmt.strip()]
    
    # 获取数据库连接
    conn = connections.get("default")
    
    # 执行每条SQL语句
    for stmt in sql_statements:
        if stmt:
            print(f"执行SQL: {stmt}")
            try:
                result = await conn.execute_query(stmt)
                print(f"执行结果: {result}")
            except Exception as e:
                print(f"执行出错: {e}")
    
    print("时区修复完成")

async def main():
    try:
        await init()
        await fix_timezone()
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(main())
