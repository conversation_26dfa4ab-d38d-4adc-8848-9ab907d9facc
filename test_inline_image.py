#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import logging
from bs4 import BeautifulSoup
from app.api.v1.reqAgent.image_markdown_converter import ImageMarkdownConverter

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_inline_image")

def main():
    """测试内联图片处理功能"""
    # 检查是否提供了HTML文件路径
    if len(sys.argv) < 2:
        print("用法: python test_inline_image.py <html_file_path>")
        print("例如: python test_inline_image.py tapd_content.txt")
        return
    
    # 获取HTML文件路径
    html_file_path = sys.argv[1]
    
    # 检查文件是否存在
    if not os.path.exists(html_file_path):
        print(f"错误: 文件 {html_file_path} 不存在")
        return
    
    # 读取HTML文件内容
    try:
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
    except Exception as e:
        print(f"读取文件失败: {str(e)}")
        return
    
    print(f"成功读取HTML文件，内容长度: {len(html_content)}")
    
    # 使用BeautifulSoup解析HTML
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 查找所有图片元素
    images = soup.find_all('img')
    print(f"找到 {len(images)} 张图片")
    
    # 创建ImageMarkdownConverter实例
    converter = ImageMarkdownConverter(
        image_analysis_enabled=True,
        strip=['script', 'style'],
        convert=['a', 'b', 'i', 'strong', 'em', 'code', 'pre', 'p', 'br', 'hr', 'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'img', 'table', 'thead', 'tbody', 'tr', 'th', 'td'],
        keep_inline_images_in=['a']  # 允许在<a>标签中保留内联图片
    )
    
    # 处理每张图片
    for i, img in enumerate(images):
        print(f"\n处理图片 {i+1}/{len(images)}")
        
        # 获取图片属性
        src = img.get('src', '')
        alt = img.get('alt', '')
        original_src = img.get('original_src', '')
        href = img.get('href', '')
        
        print(f"图片属性:")
        print(f"  - src: {src[:50]}...")
        print(f"  - alt: {alt}")
        print(f"  - original_src: {original_src[:50]}...")
        print(f"  - href: {href[:50]}...")
        
        # 创建一个模拟的parent_tags列表，包含'_inline'
        parent_tags = ['_inline']
        
        # 使用convert_img方法处理图片
        result = converter.convert_img(img, '', parent_tags)
        print(f"处理结果: {result[:100]}...")
        
        # 如果结果只是alt文本，说明图片被跳过了
        if result == alt:
            print("警告: 图片被跳过，只返回了alt文本")
            
            # 尝试使用original_src或href作为图片源
            if original_src:
                print(f"尝试使用original_src作为图片源: {original_src}")
                img['src'] = original_src
                result = converter.convert_img(img, '', [])  # 不包含'_inline'
                print(f"使用original_src的处理结果: {result[:100]}...")
            elif href:
                print(f"尝试使用href作为图片源: {href}")
                img['src'] = href
                result = converter.convert_img(img, '', [])  # 不包含'_inline'
                print(f"使用href的处理结果: {result[:100]}...")
    
    print("\n所有图片处理完成")

if __name__ == "__main__":
    main()
