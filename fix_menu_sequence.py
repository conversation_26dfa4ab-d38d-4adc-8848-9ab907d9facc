#!/usr/bin/env python
import asyncio
import sys
import os
from tortoise import Tortoise, connections
from app.settings.config import settings

async def init():
    # 初始化Tortoise ORM
    await Tortoise.init(config=settings.TORTOISE_ORM)
    print("数据库连接初始化完成")

async def fix_menu_sequence():
    """修复菜单表的序列问题"""
    try:
        # 获取数据库连接
        conn = connections.get("default")
        
        # 读取SQL文件内容
        script_dir = os.path.dirname(os.path.abspath(__file__))
        sql_file_path = os.path.join(script_dir, "migrations", "fix_menu_sequence.sql")
        
        with open(sql_file_path, 'r') as f:
            sql_content = f.read()
        
        # 按分号分割SQL语句
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        # 执行每条SQL语句
        for stmt in sql_statements:
            if stmt:
                print(f"执行SQL: {stmt}")
                result = await conn.execute_query(stmt)
                if result and result[1]:
                    print(f"结果: {result[1]}")
        
        print("\n菜单表序列修复完成")
    except Exception as e:
        print(f"修复菜单表序列时出错: {e}")
        import traceback
        print(traceback.format_exc())

async def main():
    try:
        await init()
        await fix_menu_sequence()
    finally:
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(main())
