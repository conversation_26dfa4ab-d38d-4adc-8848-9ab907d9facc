#!/usr/bin/env python
import asyncio
import sys
import pytz
from datetime import datetime
from tortoise import Tortoise, connections
from app.settings.config import settings

# 获取带时区的当前时间
def get_now_with_timezone():
    """获取带UTC时区的当前时间"""
    return datetime.now(pytz.UTC)

async def init():
    # 初始化Tortoise ORM
    await Tortoise.init(config=settings.TORTOISE_ORM)
    print("数据库连接初始化完成")

async def fix_menu_timezone():
    """直接修复菜单表中的时区问题"""
    # 获取数据库连接
    conn = connections.get("default")

    # 1. 修改PostgreSQL会话时区设置
    await conn.execute_query("SET timezone TO 'UTC';")
    print("已将当前会话时区设置为UTC")

    # 2. 获取所有菜单记录
    result = await conn.execute_query("SELECT id, created_at, updated_at FROM menu;")
    menus = result[1]
    print(f"获取到 {len(menus)} 条菜单记录")

    # 3. 更新每个菜单的时间字段
    now = get_now_with_timezone()
    for menu in menus:
        menu_id = menu[0]
        print(f"\n处理菜单ID: {menu_id}")
        print(f"原始created_at: {menu[1]}")
        print(f"原始updated_at: {menu[2]}")
        
        # 更新updated_at字段为当前时间（带时区）
        query = "UPDATE menu SET updated_at = $1 WHERE id = $2"
        await conn.execute_query(query, [now, menu_id])
        print(f"已更新菜单ID {menu_id} 的updated_at为 {now}")
    
    print("\n所有菜单时区问题修复完成")

async def main():
    try:
        await init()
        await fix_menu_timezone()
    finally:
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(main())
