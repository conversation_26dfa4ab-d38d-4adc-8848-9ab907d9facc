import asyncio
from tortoise import Tortoise, connections
from app.settings.config import TORTOISE_ORM

async def check_test_cases_schema():
    # 初始化Tortoise ORM
    await Tortoise.init(config=TORTOISE_ORM)
    
    # 获取数据库连接
    conn = connections.get("default")
    
    # 查询test_cases表的requirement_id字段类型
    try:
        result = await conn.execute_query("""
            SELECT column_name, data_type, character_maximum_length 
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'test_cases'
            AND column_name = 'requirement_id';
        """)
        print("test_cases表的requirement_id字段信息:")
        for row in result[1]:
            print(f"列名: {row[0]}, 数据类型: {row[1]}, 最大长度: {row[2]}")
    except Exception as e:
        print(f"查询失败: {e}")
    
    # 关闭连接
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(check_test_cases_schema())
