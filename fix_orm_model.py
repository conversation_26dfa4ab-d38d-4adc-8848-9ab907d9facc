"""
修复ORM模型中的关系定义。
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.models.admin import TestCase, TestStep
from tortoise import Tortoise, run_async

async def main():
    # 连接到数据库
    await Tortoise.init(
        db_url="postgres://admin:admin@localhost:5432/agent_testing",
        modules={"models": ["app.models.admin"]}
    )
    
    # 打印连接信息
    print("已连接到数据库")
    
    # 打印TestCase模型的steps字段定义
    print("TestCase.steps字段定义:")
    steps_field = TestCase._meta.fields_map["steps"]
    print(f"  模型: {steps_field.model_name}")
    print(f"  关联名称: {steps_field.related_name}")
    print(f"  关联表: {steps_field.through}")
    print(f"  前向键: {steps_field.forward_key}")
    print(f"  后向键: {steps_field.backward_key}")
    
    # 修改TestCase模型的steps字段定义
    steps_field.through = "test_cases_test_steps"
    steps_field.forward_key = "test_cases_id"
    steps_field.backward_key = "test_step_id"
    
    print("\n修改后的TestCase.steps字段定义:")
    print(f"  模型: {steps_field.model_name}")
    print(f"  关联名称: {steps_field.related_name}")
    print(f"  关联表: {steps_field.through}")
    print(f"  前向键: {steps_field.forward_key}")
    print(f"  后向键: {steps_field.backward_key}")
    
    # 关闭连接
    await Tortoise.close_connections()
    print("\n数据库连接已关闭")

if __name__ == "__main__":
    run_async(main())
