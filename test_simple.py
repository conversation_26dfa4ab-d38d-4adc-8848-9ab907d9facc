"""
简单测试图片解析功能
"""
import os
import sys
import traceback

# 添加当前目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

try:
    # 导入图片解析功能
    from app.api.v1.reqAgent.image_markdown_converter import html_to_markdown_with_image_analysis
    
    # 设置环境变量，启用图片分析
    os.environ["ENABLE_IMAGE_ANALYSIS"] = "true"
    
    # 设置 OpenAI API 密钥 - 请替换为您自己的API密钥
    # os.environ["LLM_API_KEY"] = "sk-your-openai-api-key-here"
    
    # 简单的HTML内容，包含图片
    html = '<img src="https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7" alt="用户界面示例">'
    
    print("\n" + "="*50)
    print("简单图片解析测试")
    print("="*50)
    
    # 转换HTML并分析图片
    result = html_to_markdown_with_image_analysis(html)
    
    # 打印结果
    print("HTML输入:")
    print(html)
    print("\nMarkdown输出:")
    print(result)
    
    print("="*50)
    print("测试完成")
    print("="*50)
    
except Exception as e:
    print(f"测试失败: {str(e)}")
    print("错误详情:")
    traceback.print_exc()
