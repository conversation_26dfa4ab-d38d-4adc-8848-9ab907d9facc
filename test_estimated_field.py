import asyncio
import json
from tortoise import Tortoise, connections
from app.settings.config import TORTOISE_ORM
import uuid

# 模拟需求分析智能体的输出
json_output = '''
{
  "requirements": [
    {
      "name": "测试estimated字段",
      "description": "测试estimated字段与remark字段的处理",
      "category": "功能",
      "parent": "",
      "module": "测试模块",
      "level": "高",
      "reviewer": "测试人员",
      "estimated": 8,
      "criteria": "测试标准",
      "remark": "[功能模块: 测试模块]",
      "keywords": "测试,estimated,remark",
      "project_id": 1
    }
  ]
}
'''

async def test_estimated_field():
    # 初始化Tortoise ORM
    await Tortoise.init(config=TORTOISE_ORM)
    
    try:
        # 解析JSON
        data = json.loads(json_output)
        requirement_dict = data["requirements"][0]
        
        # 生成唯一名称，避免冲突
        requirement_dict["name"] = f"测试estimated字段-{uuid.uuid4().hex[:8]}"
        
        print(f"原始需求字典: {requirement_dict}")
        
        # 创建最小化字典
        minimal_dict = {
            "name": requirement_dict.get("name", f"需求-{uuid.uuid4().hex[:8]}"),
            "description": requirement_dict.get("description", "需求描述"),
            "category": "功能",
            "level": requirement_dict.get("level", "高"),
            "project_id": requirement_dict.get("project_id", 1)
        }
        
        # 处理estimate/estimated字段
        if "estimate" in requirement_dict:
            minimal_dict["estimate"] = requirement_dict["estimate"]
        elif "estimated" in requirement_dict:
            minimal_dict["estimate"] = requirement_dict["estimated"]
            print(f"\n将estimated字段值({requirement_dict['estimated']})转换为estimate字段\n")
        else:
            minimal_dict["estimate"] = 8
            
        # 处理remark字段
        if "remark" in requirement_dict:
            minimal_dict["remark"] = requirement_dict["remark"]
            print(f"\n添加remark字段: {requirement_dict['remark']}\n")
        
        print(f"最小化字典: {minimal_dict}")
        
        # 获取数据库连接
        conn = connections.get("default")
        
        # 构建SQL插入语句
        fields = []
        values = []
        params = []
        
        for field_name, field_value in minimal_dict.items():
            if field_value is not None:
                fields.append(field_name)
                values.append(f"${len(params) + 1}")
                params.append(field_value)
        
        # 添加created_at和updated_at字段
        fields.extend(["created_at", "updated_at"])
        values.extend(["NOW()", "NOW()"])
        
        # 构建完整的SQL语句
        sql = f"INSERT INTO requirement ({', '.join(fields)}) VALUES ({', '.join(values)}) RETURNING id"
        
        # 执行SQL语句
        print(f"执行SQL: {sql}")
        print(f"参数: {params}")
        result = await conn.execute_query(sql, params)
        requirement_id = result[1][0][0]
        print(f"需求保存成功! ID: {requirement_id}")
        
        # 查询刚刚创建的记录
        result = await conn.execute_query(
            "SELECT id, name, remark, estimate FROM requirement WHERE id = $1",
            [requirement_id]
        )
        
        print(f"查询结果: {result}")
        
    except Exception as e:
        print(f"错误: {str(e)}")
    
    # 关闭连接
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(test_estimated_field())
