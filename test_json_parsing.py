import json

# 模拟需求分析智能体的输出
json_output = '''
{
  "requirements": [
    {
      "name": "商品多选机制实现",
      "description": "作为一名照护师，我希望能够批量选择商品，这样可以提高工作效率。",
      "category": "功能",
      "parent": "",
      "module": "照护商品",
      "level": "BR",
      "reviewer": "朱海华;",
      "estimated": 8,
      "criteria": "1.每个商品前有可操作的checkbox 2.支持批量选择",
      "remark": "[功能模块: 照护商品]",
      "keywords": "商品,多选,checkbox",
      "project_id": 6,
      "tapd_url": "https://www.tapd.cn/tapd_fe/22012671/story/detail/1122012671001004719"
    },
    {
      "name": "购物车汇总信息显示",
      "description": "作为一名照护师，我希望购物车能正确显示汇总信息，这样可以准确了解订单详情。",
      "category": "功能",
      "parent": "",
      "module": "照护商品",
      "level": "BR",
      "reviewer": "朱海华;",
      "estimated": 8,
      "criteria": "正确显示:商品数量/总价/减免金额/赠品信息",
      "remark": "[功能模块: 照护商品]",
      "keywords": "购物车,汇总信息,商品数量,总价,减免金额,赠品",
      "project_id": 6,
      "tapd_url": "https://www.tapd.cn/tapd_fe/22012671/story/detail/1122012671001004719"
    }
  ]
}
'''

# 解析JSON
try:
    data = json.loads(json_output)
    print("JSON解析成功!")
    
    # 检查requirements数组
    if "requirements" in data and isinstance(data["requirements"], list):
        print(f"找到{len(data['requirements'])}个需求项")
        
        # 检查每个需求项
        for i, req in enumerate(data["requirements"]):
            print(f"\n需求项 #{i+1}:")
            
            # 检查字段名
            print(f"字段名: {', '.join(req.keys())}")
            
            # 特别检查remark字段
            if "remark" in req:
                print(f"remark字段: '{req['remark']}'")
            else:
                print("警告: 没有找到remark字段!")
                
            # 检查estimate/estimated字段
            if "estimate" in req:
                print(f"estimate字段: {req['estimate']}, 类型: {type(req['estimate'])}")
            elif "estimated" in req:
                print(f"estimated字段: {req['estimated']}, 类型: {type(req['estimated'])}")
            else:
                print("警告: 没有找到estimate或estimated字段!")
    else:
        print("错误: 没有找到requirements数组或格式不正确")
        
except json.JSONDecodeError as e:
    print(f"JSON解析错误: {str(e)}")
except Exception as e:
    print(f"其他错误: {str(e)}")
