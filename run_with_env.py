import os
import sys

# 直接设置环境变量
os.environ["OPENAI_API_KEY"] = "sk-2cdc85223c2f4f9a976a57e3ae9ba4b9"
os.environ["LLM_API_KEY"] = "sk-2cdc85223c2f4f9a976a57e3ae9ba4b9"
os.environ["LLM_API_BASE"] = "https://api.deepseek.com/v1"
os.environ["DEFAULT_LLM_MODEL"] = "deepseek-chat"

print("已设置环境变量:")
print(f"OPENAI_API_KEY = {os.environ.get('OPENAI_API_KEY')}")
print(f"LLM_API_KEY = {os.environ.get('LLM_API_KEY')}")
print(f"LLM_API_BASE = {os.environ.get('LLM_API_BASE')}")
print(f"DEFAULT_LLM_MODEL = {os.environ.get('DEFAULT_LLM_MODEL')}")

# 运行应用程序
print("正在启动应用程序...")
import run
