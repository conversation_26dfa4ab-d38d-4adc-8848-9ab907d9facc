#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import logging
from app.api.v1.reqAgent.image_markdown_converter import html_to_markdown_with_image_analysis

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_image_processing")

def main():
    """测试图片处理功能"""
    # 检查是否提供了HTML文件路径
    if len(sys.argv) < 2:
        print("用法: python test_image_processing.py <html_file_path>")
        print("例如: python test_image_processing.py tapd_content.txt")
        return
    
    # 获取HTML文件路径
    html_file_path = sys.argv[1]
    
    # 检查文件是否存在
    if not os.path.exists(html_file_path):
        print(f"错误: 文件 {html_file_path} 不存在")
        return
    
    # 读取HTML文件内容
    try:
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
    except Exception as e:
        print(f"读取文件失败: {str(e)}")
        return
    
    print(f"成功读取HTML文件，内容长度: {len(html_content)}")
    
    # 转换HTML为Markdown，并处理图片
    try:
        markdown_content = html_to_markdown_with_image_analysis(
            html_content=html_content,
            image_analysis_enabled=True
        )
        
        # 将结果保存到文件
        output_file = f"{os.path.splitext(html_file_path)[0]}_processed.md"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        print(f"处理完成，结果已保存到 {output_file}")
    except Exception as e:
        print(f"处理HTML内容失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
