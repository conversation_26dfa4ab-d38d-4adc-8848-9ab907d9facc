#!/usr/bin/env python
import asyncio
import sys
from tortoise import Tortoise
from app.settings.config import settings
from app.models.admin import Menu
from app.schemas.menus import MenuType

async def init():
    # 初始化Tortoise ORM
    await Tortoise.init(config=settings.TORTOISE_ORM)
    print("数据库连接初始化完成")

async def test_menu_create():
    """测试菜单创建功能"""
    try:
        # 创建一个测试菜单
        test_menu = await Menu.create(
            menu_type=MenuType.MENU,
            name="知识库",
            path="/library",
            order=1,
            parent_id=0,
            icon="mdi-adjust",
            is_hidden=False,
            component="Layout",
            keepalive=True,
        )
        
        print(f"菜单创建成功! ID: {test_menu.id}")
        print(f"菜单详情: {await test_menu.to_dict()}")
        
        # 删除测试菜单（清理测试数据）
        await test_menu.delete()
        print("测试菜单已删除")
        
    except Exception as e:
        print(f"测试菜单创建时出错: {e}")
        import traceback
        print(traceback.format_exc())

async def main():
    try:
        await init()
        await test_menu_create()
    finally:
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(main())
