import asyncio
from tortoise import Tortoise
from app.settings.config import TORTOISE_ORM

async def delete_all_data():
    # Initialize Tortoise ORM
    await Tortoise.init(config=TORTOISE_ORM)
    
    # Get connection
    conn = Tortoise.get_connection("default")
    
    try:
        # 1. 删除测试用例步骤关联表数据
        print("删除测试用例步骤关联表数据...")
        result = await conn.execute_query("DELETE FROM test_cases_test_steps;")
        print(f"已删除 test_cases_test_steps 表中的所有数据")
        
        # 2. 删除测试步骤数据
        print("\n删除测试步骤数据...")
        result = await conn.execute_query("DELETE FROM test_steps;")
        print(f"已删除 test_steps 表中的所有数据")
        
        # 3. 删除测试用例数据
        print("\n删除测试用例数据...")
        result = await conn.execute_query("DELETE FROM test_cases;")
        print(f"已删除 test_cases 表中的所有数据")
        
        # 4. 删除需求文档数据
        print("\n删除需求文档数据...")
        result = await conn.execute_query("DELETE FROM requirement_doc;")
        print(f"已删除 requirement_doc 表中的所有数据")
        
        # 5. 删除需求数据
        print("\n删除需求数据...")
        result = await conn.execute_query("DELETE FROM requirement;")
        print(f"已删除 requirement 表中的所有数据")
        
        print("\n所有数据已成功删除！")
    except Exception as e:
        print(f"删除数据时出错: {e}")
    
    # Close connections
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(delete_all_data())
