#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import logging
import base64
from bs4 import BeautifulSoup
from app.api.v1.reqAgent.image_markdown_converter import ImageMarkdownConverter

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_html_base64")

def main():
    """测试HTML Base64编码图片处理功能"""
    # 检查是否提供了HTML文件路径
    if len(sys.argv) < 2:
        print("用法: python test_html_base64.py <html_file_path>")
        print("例如: python test_html_base64.py tapd_content.txt")
        return
    
    # 获取HTML文件路径
    html_file_path = sys.argv[1]
    
    # 检查文件是否存在
    if not os.path.exists(html_file_path):
        print(f"错误: 文件 {html_file_path} 不存在")
        return
    
    # 读取HTML文件内容
    try:
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
    except Exception as e:
        print(f"读取文件失败: {str(e)}")
        return
    
    print(f"成功读取HTML文件，内容长度: {len(html_content)}")
    
    # 使用BeautifulSoup解析HTML
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 查找所有图片元素
    images = soup.find_all('img')
    print(f"找到 {len(images)} 张图片")
    
    # 创建ImageMarkdownConverter实例
    converter = ImageMarkdownConverter(
        image_analysis_enabled=True,
        strip=['script', 'style'],
        convert=['a', 'b', 'i', 'strong', 'em', 'code', 'pre', 'p', 'br', 'hr', 'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'img', 'table', 'thead', 'tbody', 'tr', 'th', 'td']
    )
    
    # 处理每张图片
    for i, img in enumerate(images):
        print(f"\n处理图片 {i+1}/{len(images)}")
        
        # 获取图片属性
        src = img.get('src', '')
        alt = img.get('alt', '')
        original_src = img.get('original_src', '')
        href = img.get('href', '')
        
        print(f"图片属性:")
        print(f"  - src: {src[:50]}...")
        print(f"  - alt: {alt}")
        print(f"  - original_src: {original_src[:50]}...")
        print(f"  - href: {href[:50]}...")
        
        # 检查是否是HTML Base64编码
        is_html_base64 = src.startswith('data:text/html;base64')
        print(f"  - 是否HTML Base64编码: {is_html_base64}")
        
        if is_html_base64:
            print("\n这是HTML Base64编码图片，尝试提取真实图片URL:")
            
            # 尝试从其他属性获取真实图片URL
            real_src = None
            if original_src and (original_src.endswith('.png') or original_src.endswith('.jpg') or 
                               original_src.endswith('.jpeg') or original_src.endswith('.gif')):
                print(f"  - 使用original_src属性作为图片源: {original_src}")
                real_src = original_src
            elif href and (href.endswith('.png') or href.endswith('.jpg') or 
                         href.endswith('.jpeg') or href.endswith('.gif')):
                print(f"  - 使用href属性作为图片源: {href}")
                real_src = href
            
            if real_src:
                # 使用真实图片URL进行分析
                print("\n使用真实图片URL进行分析:")
                result = converter.analyze_image(real_src, alt)
                if result:
                    print("-" * 50)
                    print(result)
                    print("-" * 50)
                else:
                    print("图片分析失败")
                
                # 将结果保存到文件
                output_file = f"image_{i+1}_analysis.txt"
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(result if result else "图片分析失败")
                
                print(f"分析结果已保存到 {output_file}")
            else:
                print("无法获取真实图片URL")
        else:
            # 使用convert_img方法处理图片
            print("\n使用convert_img方法处理图片:")
            result = converter.convert_img(img, '', [])
            if result:
                print("-" * 50)
                print(result)
                print("-" * 50)
            else:
                print("图片处理失败")
            
            # 将结果保存到文件
            output_file = f"image_{i+1}_analysis.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(result if result else "图片处理失败")
            
            print(f"分析结果已保存到 {output_file}")
    
    print("\n所有图片处理完成")

if __name__ == "__main__":
    main()
