2025-05-20 00:47:41,400 - app - INFO - 创建FastAPI应用...
2025-05-20 00:47:41,445 - app - INFO - FastAPI应用创建完成
2025-05-20 00:47:41,446 - app.utils.model_preloader - INFO - 开始预加载嵌入模型...
2025-05-20 00:47:41,446 - app.utils.model_preloader - INFO - 嵌入模型预加载已在后台线程启动
已加载.env文件中的环境变量
OPENAI_API_KEY = sk-your-openai-api-key
LLM_API_KEY = sk-your-openai-api-key
已设置环境变量:
OPENAI_API_KEY = sk-2cdc85223c2f4f9a976a57e3ae9ba4b9
LLM_API_KEY = sk-2cdc85223c2f4f9a976a57e3ae9ba4b9
LLM_API_BASE = https://api.deepseek.com/v1
DEFAULT_LLM_MODEL = deepseek-chat
静态文件服务已挂载: /Users/<USER>/test/danwen/testing2/AItestPlatform/static
模型预加载已在后台启动
2025-05-20 00:47:41,446 - app.utils.model_preloader - INFO - 正在加载模型 BAAI/bge-large-zh...
2025-05-20 00:47:41,449 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: BAAI/bge-large-zh
2025-05-20 00:47:41 - uvicorn.error - INFO - Will watch for changes in these directories: ['/Users/<USER>/test/danwen/testing2/AItestPlatform']
2025-05-20 00:47:41,449 - uvicorn.error - INFO - Will watch for changes in these directories: ['/Users/<USER>/test/danwen/testing2/AItestPlatform']
2025-05-20 00:47:41 - uvicorn.error - INFO - Uvicorn running on http://0.0.0.0:9999 (Press CTRL+C to quit)
2025-05-20 00:47:41,450 - uvicorn.error - INFO - Uvicorn running on http://0.0.0.0:9999 (Press CTRL+C to quit)
2025-05-20 00:47:41 - uvicorn.error - INFO - Started reloader process [62843] using WatchFiles
2025-05-20 00:47:41,450 - uvicorn.error - INFO - Started reloader process [62843] using WatchFiles
2025-05-20 00:47:51,305 - app - INFO - 创建FastAPI应用...
2025-05-20 00:47:51,343 - app - INFO - FastAPI应用创建完成
2025-05-20 00:47:51,344 - app.utils.model_preloader - INFO - 开始预加载嵌入模型...
2025-05-20 00:47:51,344 - app.utils.model_preloader - INFO - 正在加载模型 BAAI/bge-large-zh...
2025-05-20 00:47:51,344 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: BAAI/bge-large-zh
2025-05-20 00:47:51,344 - app.utils.model_preloader - INFO - 嵌入模型预加载已在后台线程启动
已加载.env文件中的环境变量
OPENAI_API_KEY = sk-2cdc85223c2f4f9a976a57e3ae9ba4b9
LLM_API_KEY = sk-2cdc85223c2f4f9a976a57e3ae9ba4b9
已设置环境变量:
OPENAI_API_KEY = sk-2cdc85223c2f4f9a976a57e3ae9ba4b9
LLM_API_KEY = sk-2cdc85223c2f4f9a976a57e3ae9ba4b9
LLM_API_BASE = https://api.deepseek.com/v1
DEFAULT_LLM_MODEL = deepseek-chat
静态文件服务已挂载: /Users/<USER>/test/danwen/testing2/AItestPlatform/static
模型预加载已在后台启动
2025-05-20 00:47:51,513 - sentence_transformers.SentenceTransformer - WARNING - No sentence-transformers model found with name BAAI/bge-large-zh. Creating a new one with MEAN pooling.
2025-05-20 00:47:51 - uvicorn.error - INFO - Started server process [62959]
2025-05-20 00:47:51,567 - uvicorn.error - INFO - Started server process [62959]
2025-05-20 00:47:51 - uvicorn.error - INFO - Waiting for application startup.
2025-05-20 00:47:51,567 - uvicorn.error - INFO - Waiting for application startup.
2025-05-20 00:47:51,568 - app - INFO - 应用启动中...
2025-05-20 00:47:51.596 | INFO     | app.core.init_app:init_db:186 - 数据库连接成功
2025-05-20 00:47:51.728 | ERROR    | app.core.init_app:init_db:215 - 数据库连接失败: 'int' object is not subscriptable
2025-05-20 00:47:51.728 | INFO     | app.core.init_app:init_db:225 - migrations目录已存在，跳过init_db
2025-05-20 00:47:51.784 | INFO     | app.core.init_app:init_db:246 - Aerich初始化成功
2025-05-20 00:47:51.784 | INFO     | app.core.init_app:init_db:252 - 数据库迁移已禁用，使用手动创建的表
2025-05-20 00:47:51.784 | INFO     | app.core.init_app:init_db:259 - 数据库升级已禁用，使用手动创建的表
2025-05-20 00:47:51.784 | INFO     | app.core.init_app:init_data:296 - 数据库初始化完成
2025-05-20 00:47:51.790 | INFO     | app.core.init_app:init_data:302 - 超级用户初始化完成
2025-05-20 00:47:51.795 | INFO     | app.core.init_app:init_data:308 - 菜单初始化完成
2025-05-20 00:47:51.800 | INFO     | app.core.init_app:init_data:314 - API初始化完成
2025-05-20 00:47:51.803 | INFO     | app.core.init_app:init_data:320 - 角色初始化完成
2025-05-20 00:47:51.803 | INFO     | app.core.init_app:init_data:324 - 所有初始化步骤已完成，即使有错误也会继续启动应用
2025-05-20 00:47:51,803 - app.core.model_preload - INFO - 已在后台线程启动嵌入模型预热
2025-05-20 00:47:51,803 - app - INFO - 应用初始化完成
2025-05-20 00:47:51 - uvicorn.error - INFO - Application startup complete.
2025-05-20 00:47:51,805 - uvicorn.error - INFO - Application startup complete.
2025-05-20 00:47:51,815 - llm_config - INFO - 初始化模型客户端成功: deepseek-chat, API Base: https://api.deepseek.com/v1
2025-05-20 00:47:51,823 - llm_config - INFO - 初始化DashScope客户端成功: deepseek-v3, API Base: https://dashscope.aliyuncs.com/compatible-mode/v1
2025-05-20 00:47:51,823 - app.core.model_preload - INFO - 开始预热嵌入模型...
2025-05-20 00:47:51,823 - llm_config - INFO - 开始加载嵌入模型: BAAI/bge-large-zh, 这可能需要一些时间...
2025-05-20 00:47:51,823 - llm_config - INFO - 模型将从缓存目录加载: ./embedding_models
2025-05-20 00:47:51,823 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: BAAI/bge-large-zh
2025-05-20 00:47:51,942 - sentence_transformers.SentenceTransformer - WARNING - No sentence-transformers model found with name BAAI/bge-large-zh. Creating a new one with MEAN pooling.
2025-05-20 00:47:52,208 - app.utils.model_preloader - ERROR - 嵌入模型预加载失败: [Errno 30] Read-only file system: '/root'
2025-05-20 00:47:57 - uvicorn.access - INFO - 127.0.0.1:53655 - "OPTIONS /api/v1/auth/login HTTP/1.1" 200
2025-05-20 00:47:57,277 - app.core.cors_middleware - INFO - 收到请求: POST /api/v1/auth/login
2025-05-20 00:47:57,277 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'content-length': '40', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:47:57 - uvicorn.access - INFO - 127.0.0.1:53655 - "POST /api/v1/auth/login HTTP/1.1" 200
2025-05-20 00:47:57 - uvicorn.access - INFO - 127.0.0.1:53655 - "OPTIONS /api/v1/base/userinfo HTTP/1.1" 200
2025-05-20 00:47:57,498 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/base/userinfo
2025-05-20 00:47:57,498 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwiaXNfc3VwZXJ1c2VyIjpmYWxzZX0.TxCU67MEoNpJHfcoveZnxwP8Q15iP97YC4jJlUBQgvM', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:47:57 - uvicorn.access - INFO - 127.0.0.1:53655 - "GET /api/v1/base/userinfo HTTP/1.1" 200
2025-05-20 00:47:57 - uvicorn.access - INFO - 127.0.0.1:53655 - "OPTIONS /api/v1/base/usermenu HTTP/1.1" 200
2025-05-20 00:47:57,523 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/base/usermenu
2025-05-20 00:47:57,523 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwiaXNfc3VwZXJ1c2VyIjpmYWxzZX0.TxCU67MEoNpJHfcoveZnxwP8Q15iP97YC4jJlUBQgvM', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:47:57 - uvicorn.access - INFO - 127.0.0.1:53655 - "GET /api/v1/base/usermenu HTTP/1.1" 200
2025-05-20 00:47:57 - uvicorn.access - INFO - 127.0.0.1:53655 - "OPTIONS /api/v1/base/userapi HTTP/1.1" 200
2025-05-20 00:47:57,551 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/base/userapi
2025-05-20 00:47:57,551 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwiaXNfc3VwZXJ1c2VyIjpmYWxzZX0.TxCU67MEoNpJHfcoveZnxwP8Q15iP97YC4jJlUBQgvM', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:47:57 - uvicorn.access - INFO - 127.0.0.1:53655 - "GET /api/v1/base/userapi HTTP/1.1" 200
2025-05-20 00:47:58,262 - llm_config - INFO - 嵌入模型加载成功: BAAI/bge-large-zh
2025-05-20 00:47:58,262 - llm_config - INFO - 模型大小: 1241.77 MB, 设备: cpu
2025-05-20 00:47:58,262 - llm_config - INFO - 模型维度: 1024

Batches:   0%|          | 0/1 [00:00<?, ?it/s]2025-05-20 00:47:58,340 - watchfiles.main - INFO - 1 change detected

Batches: 100%|██████████| 1/1 [00:01<00:00,  1.57s/it]
Batches: 100%|██████████| 1/1 [00:01<00:00,  1.57s/it]
2025-05-20 00:47:59,839 - llm_config - INFO - 模型测试成功，嵌入维度: 1024
2025-05-20 00:48:00,852 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/base/userinfo
2025-05-20 00:48:00,852 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwiaXNfc3VwZXJ1c2VyIjpmYWxzZX0.TxCU67MEoNpJHfcoveZnxwP8Q15iP97YC4jJlUBQgvM', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:48:00 - uvicorn.access - INFO - 127.0.0.1:53699 - "GET /api/v1/base/userinfo HTTP/1.1" 200
2025-05-20 00:48:01,040 - app.core.model_preload - INFO - 嵌入模型预热完成，总耗时: 9.22秒，编码测试耗时: 1.20秒
2025-05-20 00:48:01,057 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/base/usermenu
2025-05-20 00:48:01,057 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwiaXNfc3VwZXJ1c2VyIjpmYWxzZX0.TxCU67MEoNpJHfcoveZnxwP8Q15iP97YC4jJlUBQgvM', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:48:01 - uvicorn.access - INFO - 127.0.0.1:53699 - "GET /api/v1/base/usermenu HTTP/1.1" 200
2025-05-20 00:48:01,086 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/base/userapi
2025-05-20 00:48:01,087 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwiaXNfc3VwZXJ1c2VyIjpmYWxzZX0.TxCU67MEoNpJHfcoveZnxwP8Q15iP97YC4jJlUBQgvM', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:48:01 - uvicorn.access - INFO - 127.0.0.1:53699 - "GET /api/v1/base/userapi HTTP/1.1" 200
2025-05-20 00:48:01,524 - app.utils.model_preloader - ERROR - 嵌入模型预加载失败: We couldn't connect to 'https://huggingface.co' to load this file, couldn't find it in the cached files and it looks like BAAI/bge-large-zh is not the path to a directory containing a file named config.json.
Checkout your internet connection or see how to run the library in offline mode at 'https://huggingface.co/docs/transformers/installation#offline-mode'.
2025-05-20 00:48:02 - uvicorn.access - INFO - 127.0.0.1:53699 - "OPTIONS /api/v1/project/list HTTP/1.1" 200
2025-05-20 00:48:02,076 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/project/list
2025-05-20 00:48:02,076 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwiaXNfc3VwZXJ1c2VyIjpmYWxzZX0.TxCU67MEoNpJHfcoveZnxwP8Q15iP97YC4jJlUBQgvM', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:48:02 - uvicorn.access - INFO - 127.0.0.1:53699 - "GET /api/v1/project/list HTTP/1.1" 200
2025-05-20 00:48:02,152 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:48:02,152 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:48:02,157 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 00:48:02,161 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 00:48:02 - uvicorn.access - INFO - 127.0.0.1:53699 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:48:02,166 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:48:02,166 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:48:02,167 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 00:48:02,167 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 00:48:02 - uvicorn.access - INFO - 127.0.0.1:53699 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:48:12,088 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:48:12,093 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:48:12,098 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 00:48:12,098 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 00:48:12 - uvicorn.access - INFO - 127.0.0.1:53780 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:48:12,101 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:48:12,101 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:48:12,102 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 00:48:12,102 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 00:48:12 - uvicorn.access - INFO - 127.0.0.1:53780 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:48:22,082 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:48:22,082 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:48:22,083 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 00:48:22,083 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 00:48:22 - uvicorn.access - INFO - 127.0.0.1:53834 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:48:22,086 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:48:22,086 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:48:22,086 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 00:48:22,086 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 00:48:22 - uvicorn.access - INFO - 127.0.0.1:53834 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:48:25,758 - app.core.cors_middleware - INFO - 收到请求: POST /api/v1/reqAgent/tapd/parse
2025-05-20 00:48:25,758 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'content-length': '122409', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'content-type': 'application/json', 'token': 'dev', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'chrome-extension://meppjiibionboabjneejamdfdjceeklc', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'sec-fetch-storage-access': 'active', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:48:25,758 - app.api.v1.reqAgent.tapdAgent - INFO - 接收到TAPD内容解析请求，请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'content-length': '122409', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'content-type': 'application/json', 'token': 'dev', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'chrome-extension://meppjiibionboabjneejamdfdjceeklc', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'sec-fetch-storage-access': 'active', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:48:25,760 - app.api.v1.reqAgent.tapdAgent - INFO - 从请求体中获取token: dev
2025-05-20 00:48:25,760 - app.api.v1.reqAgent.tapdAgent - INFO - 接收到TAPD内容，长度: 113723
2025-05-20 00:48:25,820 - app.api.v1.reqAgent.tapdAgent - INFO - 找到 0 张需求文档图片
2025-05-20 00:48:25,835 - app.api.v1.reqAgent.tapdAgent - INFO - 开始解析需求内容，标题: 【P0】万寿路血压项目病历优化-精诚合作（Web）-TAPD平台, URL: https://www.tapd.cn/tapd_fe/22012671/story/detail/1122012671001005124?menu_workitem_type_id=0
2025-05-20 00:48:25,835 - app.api.v1.reqAgent.tapdAgent - INFO - 非JSON格式，将使用HTML解析
2025-05-20 00:48:25,864 - app.api.v1.reqAgent.tapdAgent - INFO - 使用BeautifulSoup解析HTML内容
2025-05-20 00:48:25,870 - app.api.v1.reqAgent.tapdAgent - INFO - 开始进入html_to_markdown_with_image_analysis
2025-05-20 00:48:25,871 - image_markdown_converter - INFO - HTML内容长度: 6046
2025-05-20 00:48:25,871 - image_markdown_converter - INFO - HTML内容中不包含Base64编码内容
2025-05-20 00:48:25,871 - image_markdown_converter - INFO - HTML内容中不包含HTML Base64编码
2025-05-20 00:48:25,871 - image_markdown_converter - INFO - HTML内容中不包含图片Base64编码
2025-05-20 00:48:25,875 - image_markdown_converter - INFO - 找到 3 张需求文档图片
2025-05-20 00:48:25,877 - image_markdown_converter - INFO - 成功保存Base64图片到: tapd_images2/req_doc_img_1_20250520_004825.png
2025-05-20 00:48:25,877 - image_markdown_converter - INFO - 已保存第1张需求文档图片到: tapd_images2/req_doc_img_1_20250520_004825.png
2025-05-20 00:48:25,877 - image_markdown_converter - INFO - 已将第1张需求文档图片的src替换为本地路径: tapd_images2/req_doc_img_1_20250520_004825.png
2025-05-20 00:48:25,878 - image_markdown_converter - INFO - 成功保存Base64图片到: tapd_images2/req_doc_img_2_20250520_004825.png
2025-05-20 00:48:25,878 - image_markdown_converter - INFO - 已保存第2张需求文档图片到: tapd_images2/req_doc_img_2_20250520_004825.png
2025-05-20 00:48:25,878 - image_markdown_converter - INFO - 已将第2张需求文档图片的src替换为本地路径: tapd_images2/req_doc_img_2_20250520_004825.png
2025-05-20 00:48:25,880 - image_markdown_converter - INFO - 成功保存Base64图片到: tapd_images2/req_doc_img_3_20250520_004825.png
2025-05-20 00:48:25,880 - image_markdown_converter - INFO - 已保存第3张需求文档图片到: tapd_images2/req_doc_img_3_20250520_004825.png
2025-05-20 00:48:25,880 - image_markdown_converter - INFO - 已将第3张需求文档图片的src替换为本地路径: tapd_images2/req_doc_img_3_20250520_004825.png
2025-05-20 00:48:25,882 - image_markdown_converter - INFO - HTML内容中包含 3 张图片，其中 3 张是需求文档中的实际图片
2025-05-20 00:48:25,882 - image_markdown_converter - INFO - ImageMarkdownConverter初始化完成，api_key=sk-85477c3eb0424bb89d5421d2b28d2051, api_base=https://dashscope.aliyuncs.com/compatible-mode/v1, model=qwen-vl-plus-latest
2025-05-20 00:48:25,886 - image_markdown_converter - INFO - 图片属性 - alt: 需求文档图片, src: tapd_images2/req_doc_img_1_20250520_004825.png..., original_src: https://file.tapd.cn//tfl/captures/2025-04/tapd_22..., href: https://file.tapd.cn/tfl/captures/2025-04/tapd_220...
2025-05-20 00:48:25,886 - image_markdown_converter - INFO - 开始分析图片: tapd_images2/req_doc_img_1_20250520_004825.png
2025-05-20 00:48:25,886 - image_markdown_converter - INFO - 开始使用Autogen处理图片分析，alt_text: 需求文档图片
2025-05-20 00:48:25,889 - image_markdown_converter - INFO - 开始分析图片，使用模型: qwen-vl-plus-latest
2025-05-20 00:48:25,890 - image_markdown_converter - INFO - 检测到本地图片文件路径: tapd_images2/req_doc_img_1_20250520_004825.png
2025-05-20 00:48:25,890 - image_markdown_converter - INFO - 原始图片大小: 0.10KB
2025-05-20 00:48:25,890 - image_markdown_converter - INFO - 成功读取本地图片并转换为URL，长度: 158
2025-05-20 00:48:25,890 - image_markdown_converter - INFO - 发送API请求到: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions
2025-05-20 00:48:25,890 - image_markdown_converter - INFO - 尝试发送请求 (尝试 1/3)，超时时间: 120秒
2025-05-20 00:48:25,985 - watchfiles.main - INFO - 15 changes detected
2025-05-20 00:48:26,168 - image_markdown_converter - ERROR - API请求失败，状态码: 400
2025-05-20 00:48:26,168 - image_markdown_converter - ERROR - 响应内容: {"error":{"code":"InvalidParameter.DataInspection","param":null,"message":"The media format is not supported or incorrect for the data inspection.","type":"InvalidParameter.DataInspection"},"id":"chatcmpl-a09a2466-20fa-9101-97b0-a7d3150f196d","request_id":"a09a2466-20fa-9101-97b0-a7d3150f196d"}
2025-05-20 00:48:26,168 - image_markdown_converter - INFO - Autogen分析成功，响应长度: 19
2025-05-20 00:48:26,168 - image_markdown_converter - INFO - 图片分析成功，分析结果长度: 37
2025-05-20 00:48:26,168 - image_markdown_converter - WARNING - 图片分析返回错误消息，使用模拟分析结果
2025-05-20 00:48:26,168 - image_markdown_converter - INFO - -----------------开始进入_get_mock_analysis-------------------
2025-05-20 00:48:26,170 - image_markdown_converter - INFO - 图片属性 - alt: 需求文档图片, src: tapd_images2/req_doc_img_2_20250520_004825.png..., original_src: https://file.tapd.cn//tfl/captures/2025-04/tapd_22..., href: https://file.tapd.cn/tfl/captures/2025-04/tapd_220...
2025-05-20 00:48:26,170 - image_markdown_converter - INFO - 开始分析图片: tapd_images2/req_doc_img_2_20250520_004825.png
2025-05-20 00:48:26,170 - image_markdown_converter - INFO - 开始使用Autogen处理图片分析，alt_text: 需求文档图片
2025-05-20 00:48:26,170 - image_markdown_converter - INFO - 开始分析图片，使用模型: qwen-vl-plus-latest
2025-05-20 00:48:26,170 - image_markdown_converter - INFO - 检测到本地图片文件路径: tapd_images2/req_doc_img_2_20250520_004825.png
2025-05-20 00:48:26,171 - image_markdown_converter - INFO - 原始图片大小: 0.10KB
2025-05-20 00:48:26,171 - image_markdown_converter - INFO - 成功读取本地图片并转换为URL，长度: 158
2025-05-20 00:48:26,171 - image_markdown_converter - INFO - 发送API请求到: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions
2025-05-20 00:48:26,171 - image_markdown_converter - INFO - 尝试发送请求 (尝试 1/3)，超时时间: 120秒
2025-05-20 00:48:26,454 - image_markdown_converter - ERROR - API请求失败，状态码: 400
2025-05-20 00:48:26,454 - image_markdown_converter - ERROR - 响应内容: {"error":{"code":"InvalidParameter.DataInspection","param":null,"message":"The media format is not supported or incorrect for the data inspection.","type":"InvalidParameter.DataInspection"},"id":"chatcmpl-0742e9d8-a5b9-9d0f-a56f-f0be2723fd33","request_id":"0742e9d8-a5b9-9d0f-a56f-f0be2723fd33"}
2025-05-20 00:48:26,454 - image_markdown_converter - INFO - Autogen分析成功，响应长度: 19
2025-05-20 00:48:26,454 - image_markdown_converter - INFO - 图片分析成功，分析结果长度: 37
2025-05-20 00:48:26,454 - image_markdown_converter - WARNING - 图片分析返回错误消息，使用模拟分析结果
2025-05-20 00:48:26,454 - image_markdown_converter - INFO - -----------------开始进入_get_mock_analysis-------------------
2025-05-20 00:48:26,455 - image_markdown_converter - INFO - 图片属性 - alt: 需求文档图片, src: tapd_images2/req_doc_img_3_20250520_004825.png..., original_src: https://file.tapd.cn//tfl/captures/2025-04/tapd_22..., href: https://file.tapd.cn/tfl/captures/2025-04/tapd_220...
2025-05-20 00:48:26,455 - image_markdown_converter - INFO - 开始分析图片: tapd_images2/req_doc_img_3_20250520_004825.png
2025-05-20 00:48:26,455 - image_markdown_converter - INFO - 开始使用Autogen处理图片分析，alt_text: 需求文档图片
2025-05-20 00:48:26,455 - image_markdown_converter - INFO - 开始分析图片，使用模型: qwen-vl-plus-latest
2025-05-20 00:48:26,455 - image_markdown_converter - INFO - 检测到本地图片文件路径: tapd_images2/req_doc_img_3_20250520_004825.png
2025-05-20 00:48:26,455 - image_markdown_converter - INFO - 原始图片大小: 0.10KB
2025-05-20 00:48:26,455 - image_markdown_converter - INFO - 成功读取本地图片并转换为URL，长度: 158
2025-05-20 00:48:26,455 - image_markdown_converter - INFO - 发送API请求到: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions
2025-05-20 00:48:26,455 - image_markdown_converter - INFO - 尝试发送请求 (尝试 1/3)，超时时间: 120秒
2025-05-20 00:48:26,844 - image_markdown_converter - ERROR - API请求失败，状态码: 400
2025-05-20 00:48:26,844 - image_markdown_converter - ERROR - 响应内容: {"error":{"code":"InvalidParameter.DataInspection","param":null,"message":"The media format is not supported or incorrect for the data inspection.","type":"InvalidParameter.DataInspection"},"id":"chatcmpl-8aee82e3-0300-976a-a4f2-3d3aa092b222","request_id":"8aee82e3-0300-976a-a4f2-3d3aa092b222"}
2025-05-20 00:48:26,844 - image_markdown_converter - INFO - Autogen分析成功，响应长度: 19
2025-05-20 00:48:26,844 - image_markdown_converter - INFO - 图片分析成功，分析结果长度: 37
2025-05-20 00:48:26,844 - image_markdown_converter - WARNING - 图片分析返回错误消息，使用模拟分析结果
2025-05-20 00:48:26,844 - image_markdown_converter - INFO - -----------------开始进入_get_mock_analysis-------------------
2025-05-20 00:48:26,845 - image_markdown_converter - INFO - 需求背景：

万寿路的血压项目，针对血压的病历，需要修改对应的现病史和既往史的选项。

需求详情：

入口：病历填写。

![需求文档图片](tapd_images2/req_doc_img_1_20250520_004825.png "点击看原图")

<!-- 图片分析(模拟) -->
该图片展示了一个与需求相关的参考图示。图片的标题或描述是"需求文档图片"。图片内容可能包含界面设计、功能流程、数据结构或其他与需求相关的视觉信息。该图片用于辅助理解需求文档中描述的功能或概念。

需求一：高血压病种入组时，修改现病史的下拉选项。

当患者入组病种为高血压时，下方的现病史选项为：。

|  |
| --- |
| 肾病 |
| 远端为主的对称性的多发神经病（DSPN） |
| 视网膜病变 |
| 神经病变 |
| 下肢动脉病变 |
| 高血压 |
| 冠心病 |
| 脑卒中 |
| 心律失常 |
| 左心室肥厚 |
| 糖尿病足 |
| 慢性肾功能不全 |
| 蛋白尿 |
| 脑血管病 |
| 心功能不全 |
| 其他 |

需求二：高血压病种入组时，修改既往史的下拉选项。

|  |
| --- |
| 糖尿病 |
| 肾病 |
| 远端为主的对称性的多发神经病（DSPN） |
| 视网膜病变 |
| 神经病变 |
| 下肢动脉病变 |
| 高血压 |
| 冠心病 |
| 脑卒中 |
| 心律失常 |
| 左心室肥厚 |
| 糖尿病足 |
| 慢性肾功能不全 |
| 蛋白尿 |
| 脑血管病 |
| 心功能不全 |
| 其他 |

需求三：糖尿病或高血压单一病种入组时，通过既往史和现病史判断出现另一个病种的控制目标和测量方案的填写。

1. 糖尿病入组的患者，当现病史或者既往史选择了高血压时，在健康目标填写时，在血糖模组下方出现高血压的控制目标与测量模组。

![需求文档图片](tapd_images2/req_doc_img_2_20250520_004825.png "点击看原图")

<!-- 图片分析(模拟) -->
该图片展示了一个与需求相关的参考图示。图片的标题或描述是"需求文档图片"。图片内容可能包含界面设计、功能流程、数据结构或其他与需求相关的视觉信息。该图片用于辅助理解需求文档中描述的功能或概念。

2. 高血压入组的患者，当现现病史选择了糖尿病足，既往史选择了糖尿病足或者糖尿病，在健康目标填写时，在血压模组下方出现血糖的控制目标与测量模组。

![需求文档图片](tapd_images2/req_doc_img_3_20250520_004825.png "点击看原图")

<!-- 图片分析(模拟) -->
该图片展示了一个与需求相关的参考图示。图片的标题或描述是"需求文档图片"。图片内容可能包含界面设计、功能流程、数据结构或其他与需求相关的视觉信息。该图片用于辅助理解需求文档中描述的功能或概念。

### 翻译

  

### 标题:-------------------------转换后的Markdown文本长度
2025-05-20 00:48:26,845 - image_markdown_converter - INFO - 1253-------------------------转换后的Markdown文本长度
2025-05-20 00:48:26,845 - app.api.v1.reqAgent.tapdAgent - INFO - 从 div[class="detail-item__content-wrapper"] 提取到内容并转换为Markdown，长度: 1284
2025-05-20 00:48:26,847 - app.api.v1.reqAgent.tapdAgent - INFO - 从 span[field="developer"] 提取到开发人员: 董曰凯;
2025-05-20 00:48:26,848 - app.api.v1.reqAgent.tapdAgent - INFO - 从 span[field="creator"] 提取到处理人: 杜曌770386961
2025-05-20 00:48:26,848 - app.api.v1.reqAgent.tapdAgent - INFO - 从 span[field="custom_field_three"] 提取到测试人员: 朱海华;
2025-05-20 00:48:26 - uvicorn.access - INFO - 127.0.0.1:53859 - "POST /api/v1/reqAgent/tapd/parse HTTP/1.1" 200
2025-05-20 00:48:30,111 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:48:30,123 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:48:30,139 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:48:30 - uvicorn.access - INFO - 127.0.0.1:53882 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:48:32,000 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:48:32,000 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:48:32,001 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:48:32 - uvicorn.access - INFO - 127.0.0.1:53882 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:48:32,003 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:48:32,003 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:48:32,004 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:48:32 - uvicorn.access - INFO - 127.0.0.1:53900 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:48:42,002 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:48:42,002 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:48:42,003 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:48:42 - uvicorn.access - INFO - 127.0.0.1:53958 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:48:42,006 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:48:42,006 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:48:42,007 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:48:42 - uvicorn.access - INFO - 127.0.0.1:53958 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:48:52,008 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:48:52,009 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:48:52,009 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:48:52 - uvicorn.access - INFO - 127.0.0.1:54012 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:48:52,011 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:48:52,012 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:48:52,012 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:48:52 - uvicorn.access - INFO - 127.0.0.1:54012 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:49:02,013 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:49:02,013 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:49:02,014 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:49:02 - uvicorn.access - INFO - 127.0.0.1:54065 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:49:02,016 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:49:02,016 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:49:02,016 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:49:02 - uvicorn.access - INFO - 127.0.0.1:54065 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:49:12,012 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:49:12,012 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:49:12,012 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:49:12 - uvicorn.access - INFO - 127.0.0.1:54130 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:49:12,016 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:49:12,016 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:49:12,017 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:49:12 - uvicorn.access - INFO - 127.0.0.1:54132 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:49:22,001 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:49:22,001 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:49:22,002 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:49:22 - uvicorn.access - INFO - 127.0.0.1:54189 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:49:22,004 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:49:22,004 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:49:22,005 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:49:22 - uvicorn.access - INFO - 127.0.0.1:54191 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:49:32,012 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:49:32,012 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:49:32,013 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:49:32 - uvicorn.access - INFO - 127.0.0.1:54242 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:49:32,016 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:49:32,017 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:49:32,017 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:49:32 - uvicorn.access - INFO - 127.0.0.1:54242 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:49:42,010 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:49:42,011 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:49:42,011 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:49:42 - uvicorn.access - INFO - 127.0.0.1:54297 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:49:42,014 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:49:42,014 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:49:42,014 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:49:42 - uvicorn.access - INFO - 127.0.0.1:54297 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:49:52,013 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:49:52,013 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:49:52,014 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:49:52 - uvicorn.access - INFO - 127.0.0.1:54351 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:49:52,017 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:49:52,017 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:49:52,018 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:49:52 - uvicorn.access - INFO - 127.0.0.1:54353 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:50:02,008 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:50:02,009 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:50:02,012 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:50:02 - uvicorn.access - INFO - 127.0.0.1:54403 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:50:02,018 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:50:02,018 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:50:02,020 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:50:02 - uvicorn.access - INFO - 127.0.0.1:54405 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:50:12,010 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:50:12,012 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:50:12,014 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:50:12 - uvicorn.access - INFO - 127.0.0.1:54462 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:50:12,017 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:50:12,017 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:50:12,018 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:50:12 - uvicorn.access - INFO - 127.0.0.1:54464 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:50:22,008 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:50:22,009 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:50:22,009 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:50:22 - uvicorn.access - INFO - 127.0.0.1:54515 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:50:22,015 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:50:22,015 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:50:22,015 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:50:22 - uvicorn.access - INFO - 127.0.0.1:54515 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:50:32,006 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:50:32,006 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:50:32,007 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:50:32 - uvicorn.access - INFO - 127.0.0.1:54570 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:50:32,008 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:50:32,008 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:50:32,008 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:50:32 - uvicorn.access - INFO - 127.0.0.1:54570 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:50:42,009 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:50:42,010 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:50:42,010 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:50:42 - uvicorn.access - INFO - 127.0.0.1:54624 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:50:42,012 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:50:42,012 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:50:42,012 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:50:42 - uvicorn.access - INFO - 127.0.0.1:54624 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:50:52,001 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:50:52,001 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:50:52,003 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:50:52 - uvicorn.access - INFO - 127.0.0.1:54681 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:50:52,007 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:50:52,007 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:50:52,008 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:50:52 - uvicorn.access - INFO - 127.0.0.1:54681 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:51:02,013 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:51:02,013 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:02,013 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:51:02 - uvicorn.access - INFO - 127.0.0.1:54736 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:51:02,015 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:51:02,015 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:02,015 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:51:02 - uvicorn.access - INFO - 127.0.0.1:54736 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:51:12,008 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:51:12,009 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:12,011 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:51:12 - uvicorn.access - INFO - 127.0.0.1:54798 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:51:12,014 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:51:12,014 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:12,014 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:51:12 - uvicorn.access - INFO - 127.0.0.1:54798 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:51:22,012 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:51:22,013 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:22,013 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:51:22 - uvicorn.access - INFO - 127.0.0.1:54851 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:51:22,014 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:51:22,014 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:22,014 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:51:22 - uvicorn.access - INFO - 127.0.0.1:54851 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:51:23,176 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/base/userinfo
2025-05-20 00:51:23,176 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwiaXNfc3VwZXJ1c2VyIjpmYWxzZX0.TxCU67MEoNpJHfcoveZnxwP8Q15iP97YC4jJlUBQgvM', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:23 - uvicorn.access - INFO - 127.0.0.1:54876 - "GET /api/v1/base/userinfo HTTP/1.1" 200
2025-05-20 00:51:23,525 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/base/usermenu
2025-05-20 00:51:23,525 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwiaXNfc3VwZXJ1c2VyIjpmYWxzZX0.TxCU67MEoNpJHfcoveZnxwP8Q15iP97YC4jJlUBQgvM', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:23 - uvicorn.access - INFO - 127.0.0.1:54876 - "GET /api/v1/base/usermenu HTTP/1.1" 200
2025-05-20 00:51:23,563 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/base/userapi
2025-05-20 00:51:23,563 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwiaXNfc3VwZXJ1c2VyIjpmYWxzZX0.TxCU67MEoNpJHfcoveZnxwP8Q15iP97YC4jJlUBQgvM', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:23 - uvicorn.access - INFO - 127.0.0.1:54876 - "GET /api/v1/base/userapi HTTP/1.1" 200
2025-05-20 00:51:23,857 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/project/list
2025-05-20 00:51:23,858 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwiaXNfc3VwZXJ1c2VyIjpmYWxzZX0.TxCU67MEoNpJHfcoveZnxwP8Q15iP97YC4jJlUBQgvM', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:23 - uvicorn.access - INFO - 127.0.0.1:54876 - "GET /api/v1/project/list HTTP/1.1" 200
2025-05-20 00:51:23,884 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:51:23,885 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:23,888 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:51:23 - uvicorn.access - INFO - 127.0.0.1:54876 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:51:23,891 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:51:23,891 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:23,893 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:51:23 - uvicorn.access - INFO - 127.0.0.1:54896 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:51:33,855 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:51:33,856 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:33,858 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:51:33 - uvicorn.access - INFO - 127.0.0.1:54954 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:51:33,863 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:51:33,864 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:33,865 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:51:33 - uvicorn.access - INFO - 127.0.0.1:54954 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:51:43,856 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:51:43,857 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:43,858 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:51:43 - uvicorn.access - INFO - 127.0.0.1:55010 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:51:43,863 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:51:43,863 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:43,865 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:51:43 - uvicorn.access - INFO - 127.0.0.1:55012 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:51:53,858 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:51:53,859 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:53,862 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:51:53 - uvicorn.access - INFO - 127.0.0.1:55071 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:51:53,868 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:51:53,868 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:53,870 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:51:53 - uvicorn.access - INFO - 127.0.0.1:55073 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:51:56,780 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:51:56,798 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:56,821 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:51:56 - uvicorn.access - INFO - 127.0.0.1:55073 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:51:57,361 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/base/userinfo
2025-05-20 00:51:57,361 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwiaXNfc3VwZXJ1c2VyIjpmYWxzZX0.TxCU67MEoNpJHfcoveZnxwP8Q15iP97YC4jJlUBQgvM', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:57 - uvicorn.access - INFO - 127.0.0.1:55073 - "GET /api/v1/base/userinfo HTTP/1.1" 200
2025-05-20 00:51:57,520 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/base/usermenu
2025-05-20 00:51:57,525 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwiaXNfc3VwZXJ1c2VyIjpmYWxzZX0.TxCU67MEoNpJHfcoveZnxwP8Q15iP97YC4jJlUBQgvM', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:57 - uvicorn.access - INFO - 127.0.0.1:55073 - "GET /api/v1/base/usermenu HTTP/1.1" 200
2025-05-20 00:51:57,567 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/base/userapi
2025-05-20 00:51:57,568 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwiaXNfc3VwZXJ1c2VyIjpmYWxzZX0.TxCU67MEoNpJHfcoveZnxwP8Q15iP97YC4jJlUBQgvM', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:57 - uvicorn.access - INFO - 127.0.0.1:55073 - "GET /api/v1/base/userapi HTTP/1.1" 200
2025-05-20 00:51:57,824 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/project/list
2025-05-20 00:51:57,824 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwiaXNfc3VwZXJ1c2VyIjpmYWxzZX0.TxCU67MEoNpJHfcoveZnxwP8Q15iP97YC4jJlUBQgvM', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:57,832 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:51:57,833 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:57,834 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:51:57 - uvicorn.access - INFO - 127.0.0.1:55134 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:51:57,841 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:51:57,841 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:51:57,842 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:51:57 - uvicorn.access - INFO - 127.0.0.1:55136 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:51:57 - uvicorn.access - INFO - 127.0.0.1:55131 - "GET /api/v1/project/list HTTP/1.1" 200
2025-05-20 00:52:07,834 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:52:07,835 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:52:07,837 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:52:07 - uvicorn.access - INFO - 127.0.0.1:55196 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:52:07,844 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:52:07,846 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:52:07,848 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:52:07 - uvicorn.access - INFO - 127.0.0.1:55196 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:52:17,831 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:52:17,832 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:52:17,832 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:52:17 - uvicorn.access - INFO - 127.0.0.1:55249 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:52:17,834 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:52:17,834 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:52:17,835 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:52:17 - uvicorn.access - INFO - 127.0.0.1:55249 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:52:27,827 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:52:27,829 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:52:27,831 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:52:27 - uvicorn.access - INFO - 127.0.0.1:55299 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:52:27,839 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:52:27,841 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:52:27,842 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:52:27 - uvicorn.access - INFO - 127.0.0.1:55301 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:52:37,832 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:52:37,832 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:52:37,834 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:52:37 - uvicorn.access - INFO - 127.0.0.1:55356 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:52:37,838 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:52:37,838 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:52:37,839 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:52:37 - uvicorn.access - INFO - 127.0.0.1:55358 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:52:44,113 - watchfiles.main - INFO - 44 changes detected
2025-05-20 00:52:47,827 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:52:47,828 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:52:47,829 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:52:47 - uvicorn.access - INFO - 127.0.0.1:55412 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:52:47,837 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:52:47,837 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:52:47,841 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:52:47 - uvicorn.access - INFO - 127.0.0.1:55412 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:52:57,830 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:52:57,836 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:52:57,853 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:52:57 - uvicorn.access - INFO - 127.0.0.1:55463 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:52:57,965 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:52:57,965 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:52:57,967 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:52:57 - uvicorn.access - INFO - 127.0.0.1:55465 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:53:07,129 - watchfiles.main - INFO - 56 changes detected
2025-05-20 00:53:07,826 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:53:07,826 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:53:07,831 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:53:07 - uvicorn.access - INFO - 127.0.0.1:55527 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:53:07,846 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:53:07,847 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:53:07,850 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:53:07 - uvicorn.access - INFO - 127.0.0.1:55529 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:53:14,594 - watchfiles.main - INFO - 4 changes detected
2025-05-20 00:53:17,827 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:53:17,827 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:53:17,829 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:53:17 - uvicorn.access - INFO - 127.0.0.1:55584 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:53:17,845 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:53:17,845 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:53:17,846 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:53:17 - uvicorn.access - INFO - 127.0.0.1:55584 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:53:27,831 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:53:27,832 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:53:27,835 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:53:27 - uvicorn.access - INFO - 127.0.0.1:55636 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:53:27,842 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:53:27,843 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:53:27,848 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:53:27 - uvicorn.access - INFO - 127.0.0.1:55636 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:53:37,845 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:53:37,847 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:53:37,852 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:53:37 - uvicorn.access - INFO - 127.0.0.1:55689 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:53:37,861 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:53:37,861 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:53:37,862 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:53:37 - uvicorn.access - INFO - 127.0.0.1:55691 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:53:47,828 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:53:47,833 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:53:47,837 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:53:47 - uvicorn.access - INFO - 127.0.0.1:55742 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:53:47,845 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:53:47,846 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:53:47,848 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:53:47 - uvicorn.access - INFO - 127.0.0.1:55744 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:53:57,823 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:53:57,824 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:53:57,825 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:53:57 - uvicorn.access - INFO - 127.0.0.1:55797 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:53:57,832 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:53:57,832 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:53:57,837 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:53:57 - uvicorn.access - INFO - 127.0.0.1:55797 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:54:02,243 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:54:02,248 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:54:02,272 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:54:02 - uvicorn.access - INFO - 127.0.0.1:55797 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:54:08,563 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:54:08,563 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:54:08,564 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:54:08 - uvicorn.access - INFO - 127.0.0.1:55882 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:54:08,565 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:54:08,565 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:54:08,565 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:54:08 - uvicorn.access - INFO - 127.0.0.1:55882 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:54:18,567 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:54:18,568 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:54:18,568 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:54:18 - uvicorn.access - INFO - 127.0.0.1:55952 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:54:18,570 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:54:18,570 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:54:18,571 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:54:18 - uvicorn.access - INFO - 127.0.0.1:55954 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:54:28,575 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:54:28,576 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:54:28,577 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:54:28 - uvicorn.access - INFO - 127.0.0.1:56017 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:54:28,584 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:54:28,584 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:54:28,586 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:54:28 - uvicorn.access - INFO - 127.0.0.1:56019 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:54:38,561 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:54:38,562 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:54:38,562 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:54:38 - uvicorn.access - INFO - 127.0.0.1:56076 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:54:38,564 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:54:38,565 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:54:38,565 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:54:38 - uvicorn.access - INFO - 127.0.0.1:56076 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:54:48,570 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:54:48,570 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:54:48,571 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:54:48 - uvicorn.access - INFO - 127.0.0.1:56130 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:54:48,572 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:54:48,572 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:54:48,572 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:54:48 - uvicorn.access - INFO - 127.0.0.1:56130 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:54:58,561 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:54:58,562 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:54:58,563 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:54:58 - uvicorn.access - INFO - 127.0.0.1:56185 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:54:58,566 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:54:58,566 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:54:58,567 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:54:58 - uvicorn.access - INFO - 127.0.0.1:56187 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:55:51,587 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:55:51,588 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:55:51,591 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:55:51 - uvicorn.access - INFO - 127.0.0.1:56528 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:55:51,596 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:55:51,596 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:55:51,597 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:55:51 - uvicorn.access - INFO - 127.0.0.1:56528 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:56:51,723 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:56:51,725 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:56:51,743 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:56:51 - uvicorn.access - INFO - 127.0.0.1:56856 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:56:51,792 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:56:51,793 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:56:51,900 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:56:51 - uvicorn.access - INFO - 127.0.0.1:56858 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:57:51,584 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:57:51,585 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:57:51,586 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:57:51 - uvicorn.access - INFO - 127.0.0.1:57153 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:57:51,592 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:57:51,592 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:57:51,594 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:57:51 - uvicorn.access - INFO - 127.0.0.1:57155 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:58:51,580 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:58:51,580 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:58:51,582 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:58:51 - uvicorn.access - INFO - 127.0.0.1:57504 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:58:51,584 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:58:51,584 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:58:51,585 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:58:51 - uvicorn.access - INFO - 127.0.0.1:57504 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:59:51,584 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:59:51,585 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:59:51,588 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:59:51 - uvicorn.access - INFO - 127.0.0.1:57836 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 00:59:51,593 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 00:59:51,593 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 00:59:51,594 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 00:59:51 - uvicorn.access - INFO - 127.0.0.1:57836 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:00:51,585 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:00:51,586 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:00:51,589 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 01:00:51 - uvicorn.access - INFO - 127.0.0.1:58159 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:00:51,593 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:00:51,593 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:00:51,595 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 01:00:51 - uvicorn.access - INFO - 127.0.0.1:58159 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:01:51,592 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:01:51,594 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:01:51,597 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 01:01:51 - uvicorn.access - INFO - 127.0.0.1:58480 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:01:51,603 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:01:51,603 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:01:51,607 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有1个需求
2025-05-20 01:01:51 - uvicorn.access - INFO - 127.0.0.1:58482 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:02:19,579 - watchfiles.main - INFO - 1 change detected
2025-05-20 01:02:19 - uvicorn.error - WARNING - WatchFiles detected changes in 'app/api/v1/agent/testcase_agents.py'. Reloading...
2025-05-20 01:02:19,596 - uvicorn.error - WARNING - WatchFiles detected changes in 'app/api/v1/agent/testcase_agents.py'. Reloading...
2025-05-20 01:02:19 - uvicorn.error - INFO - Shutting down
2025-05-20 01:02:19,637 - uvicorn.error - INFO - Shutting down
2025-05-20 01:02:19 - uvicorn.error - INFO - Waiting for application shutdown.
2025-05-20 01:02:19,743 - uvicorn.error - INFO - Waiting for application shutdown.
2025-05-20 01:02:19,764 - app - INFO - 应用关闭中...
2025-05-20 01:02:19,768 - tortoise - INFO - Tortoise-ORM shutdown
2025-05-20 01:02:19,769 - app - INFO - 数据库连接已关闭
2025-05-20 01:02:19 - uvicorn.error - INFO - Application shutdown complete.
2025-05-20 01:02:19,772 - uvicorn.error - INFO - Application shutdown complete.
2025-05-20 01:02:19 - uvicorn.error - INFO - Finished server process [62959]
2025-05-20 01:02:19,777 - uvicorn.error - INFO - Finished server process [62959]
2025-05-20 01:02:20,293 - watchfiles.main - INFO - 1 change detected
2025-05-20 01:02:24,058 - watchfiles.main - INFO - 3 changes detected
2025-05-20 01:02:28,958 - app - INFO - 创建FastAPI应用...
2025-05-20 01:02:29,022 - app - INFO - FastAPI应用创建完成
2025-05-20 01:02:29,024 - app.utils.model_preloader - INFO - 开始预加载嵌入模型...
2025-05-20 01:02:29,024 - app.utils.model_preloader - INFO - 正在加载模型 BAAI/bge-large-zh...
2025-05-20 01:02:29,024 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: BAAI/bge-large-zh
2025-05-20 01:02:29,025 - app.utils.model_preloader - INFO - 嵌入模型预加载已在后台线程启动
已加载.env文件中的环境变量
OPENAI_API_KEY = sk-2cdc85223c2f4f9a976a57e3ae9ba4b9
LLM_API_KEY = sk-2cdc85223c2f4f9a976a57e3ae9ba4b9
已设置环境变量:
OPENAI_API_KEY = sk-2cdc85223c2f4f9a976a57e3ae9ba4b9
LLM_API_KEY = sk-2cdc85223c2f4f9a976a57e3ae9ba4b9
LLM_API_BASE = https://api.deepseek.com/v1
DEFAULT_LLM_MODEL = deepseek-chat
静态文件服务已挂载: /Users/<USER>/test/danwen/testing2/AItestPlatform/static
模型预加载已在后台启动
2025-05-20 01:02:29 - uvicorn.error - INFO - Started server process [67879]
2025-05-20 01:02:29,212 - uvicorn.error - INFO - Started server process [67879]
2025-05-20 01:02:29 - uvicorn.error - INFO - Waiting for application startup.
2025-05-20 01:02:29,213 - uvicorn.error - INFO - Waiting for application startup.
2025-05-20 01:02:29,214 - app - INFO - 应用启动中...
2025-05-20 01:02:29.243 | INFO     | app.core.init_app:init_db:186 - 数据库连接成功
2025-05-20 01:02:29.299 | ERROR    | app.core.init_app:init_db:215 - 数据库连接失败: 'int' object is not subscriptable
2025-05-20 01:02:29.300 | INFO     | app.core.init_app:init_db:225 - migrations目录已存在，跳过init_db
2025-05-20 01:02:29.359 | INFO     | app.core.init_app:init_db:246 - Aerich初始化成功
2025-05-20 01:02:29.359 | INFO     | app.core.init_app:init_db:252 - 数据库迁移已禁用，使用手动创建的表
2025-05-20 01:02:29.359 | INFO     | app.core.init_app:init_db:259 - 数据库升级已禁用，使用手动创建的表
2025-05-20 01:02:29.359 | INFO     | app.core.init_app:init_data:296 - 数据库初始化完成
2025-05-20 01:02:29.362 | INFO     | app.core.init_app:init_data:302 - 超级用户初始化完成
2025-05-20 01:02:29.363 | INFO     | app.core.init_app:init_data:308 - 菜单初始化完成
2025-05-20 01:02:29.365 | INFO     | app.core.init_app:init_data:314 - API初始化完成
2025-05-20 01:02:29.366 | INFO     | app.core.init_app:init_data:320 - 角色初始化完成
2025-05-20 01:02:29.366 | INFO     | app.core.init_app:init_data:324 - 所有初始化步骤已完成，即使有错误也会继续启动应用
2025-05-20 01:02:29,366 - app.core.model_preload - INFO - 已在后台线程启动嵌入模型预热
2025-05-20 01:02:29,366 - app - INFO - 应用初始化完成
2025-05-20 01:02:29 - uvicorn.error - INFO - Application startup complete.
2025-05-20 01:02:29,367 - uvicorn.error - INFO - Application startup complete.
2025-05-20 01:02:29,375 - llm_config - INFO - 初始化模型客户端成功: deepseek-chat, API Base: https://api.deepseek.com/v1
2025-05-20 01:02:29,382 - llm_config - INFO - 初始化DashScope客户端成功: deepseek-v3, API Base: https://dashscope.aliyuncs.com/compatible-mode/v1
2025-05-20 01:02:29,382 - app.core.model_preload - INFO - 开始预热嵌入模型...
2025-05-20 01:02:29,383 - llm_config - INFO - 开始加载嵌入模型: BAAI/bge-large-zh, 这可能需要一些时间...
2025-05-20 01:02:29,383 - llm_config - INFO - 模型将从缓存目录加载: ./embedding_models
2025-05-20 01:02:29,383 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: BAAI/bge-large-zh
2025-05-20 01:02:29,670 - sentence_transformers.SentenceTransformer - WARNING - No sentence-transformers model found with name BAAI/bge-large-zh. Creating a new one with MEAN pooling.
2025-05-20 01:02:30,349 - app.utils.model_preloader - ERROR - 嵌入模型预加载失败: [Errno 30] Read-only file system: '/root'
2025-05-20 01:02:36,242 - watchfiles.main - INFO - 1 change detected
2025-05-20 01:02:36,261 - llm_config - INFO - 嵌入模型加载成功: BAAI/bge-large-zh
2025-05-20 01:02:36,263 - llm_config - INFO - 模型大小: 1241.77 MB, 设备: cpu
2025-05-20 01:02:36,263 - llm_config - INFO - 模型维度: 1024

Batches:   0%|          | 0/1 [00:00<?, ?it/s]
Batches: 100%|██████████| 1/1 [00:01<00:00,  1.29s/it]
Batches: 100%|██████████| 1/1 [00:01<00:00,  1.29s/it]
2025-05-20 01:02:37,589 - llm_config - INFO - 模型测试成功，嵌入维度: 1024
2025-05-20 01:02:38,155 - app.core.model_preload - INFO - 嵌入模型预热完成，总耗时: 8.77秒，编码测试耗时: 0.57秒
2025-05-20 01:02:44,738 - watchfiles.main - INFO - 1 change detected
2025-05-20 01:02:44 - uvicorn.error - WARNING - WatchFiles detected changes in 'app/api/v1/agent/testcase_agents.py'. Reloading...
2025-05-20 01:02:44,740 - uvicorn.error - WARNING - WatchFiles detected changes in 'app/api/v1/agent/testcase_agents.py'. Reloading...
2025-05-20 01:02:44 - uvicorn.error - INFO - Shutting down
2025-05-20 01:02:44,780 - uvicorn.error - INFO - Shutting down
2025-05-20 01:02:44 - uvicorn.error - INFO - Waiting for application shutdown.
2025-05-20 01:02:44,884 - uvicorn.error - INFO - Waiting for application shutdown.
2025-05-20 01:02:44,886 - app - INFO - 应用关闭中...
2025-05-20 01:02:44,903 - tortoise - INFO - Tortoise-ORM shutdown
2025-05-20 01:02:44,903 - app - INFO - 数据库连接已关闭
2025-05-20 01:02:44 - uvicorn.error - INFO - Application shutdown complete.
2025-05-20 01:02:44,903 - uvicorn.error - INFO - Application shutdown complete.
2025-05-20 01:02:44 - uvicorn.error - INFO - Finished server process [67879]
2025-05-20 01:02:44,903 - uvicorn.error - INFO - Finished server process [67879]
2025-05-20 01:02:45,360 - watchfiles.main - INFO - 1 change detected
2025-05-20 01:02:48,496 - watchfiles.main - INFO - 3 changes detected
2025-05-20 01:02:53,022 - app - INFO - 创建FastAPI应用...
2025-05-20 01:02:53,072 - app - INFO - FastAPI应用创建完成
2025-05-20 01:02:53,073 - app.utils.model_preloader - INFO - 开始预加载嵌入模型...
2025-05-20 01:02:53,073 - app.utils.model_preloader - INFO - 嵌入模型预加载已在后台线程启动
2025-05-20 01:02:53,073 - app.utils.model_preloader - INFO - 正在加载模型 BAAI/bge-large-zh...
2025-05-20 01:02:53,074 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: BAAI/bge-large-zh
已加载.env文件中的环境变量
OPENAI_API_KEY = sk-2cdc85223c2f4f9a976a57e3ae9ba4b9
LLM_API_KEY = sk-2cdc85223c2f4f9a976a57e3ae9ba4b9
已设置环境变量:
OPENAI_API_KEY = sk-2cdc85223c2f4f9a976a57e3ae9ba4b9
LLM_API_KEY = sk-2cdc85223c2f4f9a976a57e3ae9ba4b9
LLM_API_BASE = https://api.deepseek.com/v1
DEFAULT_LLM_MODEL = deepseek-chat
静态文件服务已挂载: /Users/<USER>/test/danwen/testing2/AItestPlatform/static
模型预加载已在后台启动
2025-05-20 01:02:53 - uvicorn.error - INFO - Started server process [68042]
2025-05-20 01:02:53,257 - uvicorn.error - INFO - Started server process [68042]
2025-05-20 01:02:53 - uvicorn.error - INFO - Waiting for application startup.
2025-05-20 01:02:53,258 - uvicorn.error - INFO - Waiting for application startup.
2025-05-20 01:02:53,258 - app - INFO - 应用启动中...
2025-05-20 01:02:53.294 | INFO     | app.core.init_app:init_db:186 - 数据库连接成功
2025-05-20 01:02:53.348 | ERROR    | app.core.init_app:init_db:215 - 数据库连接失败: 'int' object is not subscriptable
2025-05-20 01:02:53.351 | INFO     | app.core.init_app:init_db:225 - migrations目录已存在，跳过init_db
2025-05-20 01:02:53.404 | INFO     | app.core.init_app:init_db:246 - Aerich初始化成功
2025-05-20 01:02:53.404 | INFO     | app.core.init_app:init_db:252 - 数据库迁移已禁用，使用手动创建的表
2025-05-20 01:02:53.405 | INFO     | app.core.init_app:init_db:259 - 数据库升级已禁用，使用手动创建的表
2025-05-20 01:02:53.405 | INFO     | app.core.init_app:init_data:296 - 数据库初始化完成
2025-05-20 01:02:53.407 | INFO     | app.core.init_app:init_data:302 - 超级用户初始化完成
2025-05-20 01:02:53.408 | INFO     | app.core.init_app:init_data:308 - 菜单初始化完成
2025-05-20 01:02:53.410 | INFO     | app.core.init_app:init_data:314 - API初始化完成
2025-05-20 01:02:53.411 | INFO     | app.core.init_app:init_data:320 - 角色初始化完成
2025-05-20 01:02:53.411 | INFO     | app.core.init_app:init_data:324 - 所有初始化步骤已完成，即使有错误也会继续启动应用
2025-05-20 01:02:53,411 - app.core.model_preload - INFO - 已在后台线程启动嵌入模型预热
2025-05-20 01:02:53,411 - app - INFO - 应用初始化完成
2025-05-20 01:02:53 - uvicorn.error - INFO - Application startup complete.
2025-05-20 01:02:53,411 - uvicorn.error - INFO - Application startup complete.
2025-05-20 01:02:53,417 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:02:53,417 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:02:53,417 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:02:53,417 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:02:53 - uvicorn.access - INFO - 127.0.0.1:58811 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:02:53,420 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:02:53,420 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:02:53,420 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:02:53,420 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:02:53 - uvicorn.access - INFO - 127.0.0.1:58811 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:02:53,422 - llm_config - INFO - 初始化模型客户端成功: deepseek-chat, API Base: https://api.deepseek.com/v1
2025-05-20 01:02:53,430 - llm_config - INFO - 初始化DashScope客户端成功: deepseek-v3, API Base: https://dashscope.aliyuncs.com/compatible-mode/v1
2025-05-20 01:02:53,430 - app.core.model_preload - INFO - 开始预热嵌入模型...
2025-05-20 01:02:53,430 - llm_config - INFO - 开始加载嵌入模型: BAAI/bge-large-zh, 这可能需要一些时间...
2025-05-20 01:02:53,430 - llm_config - INFO - 模型将从缓存目录加载: ./embedding_models
2025-05-20 01:02:53,430 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: BAAI/bge-large-zh
2025-05-20 01:02:54,634 - sentence_transformers.SentenceTransformer - WARNING - No sentence-transformers model found with name BAAI/bge-large-zh. Creating a new one with MEAN pooling.
2025-05-20 01:02:54,889 - app.utils.model_preloader - ERROR - 嵌入模型预加载失败: [Errno 30] Read-only file system: '/root'
2025-05-20 01:03:01,264 - llm_config - INFO - 嵌入模型加载成功: BAAI/bge-large-zh
2025-05-20 01:03:01,267 - llm_config - INFO - 模型大小: 1241.77 MB, 设备: cpu
2025-05-20 01:03:01,267 - llm_config - INFO - 模型维度: 1024

Batches:   0%|          | 0/1 [00:00<?, ?it/s]2025-05-20 01:03:01,327 - watchfiles.main - INFO - 1 change detected

Batches: 100%|██████████| 1/1 [00:01<00:00,  1.01s/it]
Batches: 100%|██████████| 1/1 [00:01<00:00,  1.01s/it]
2025-05-20 01:03:02,276 - llm_config - INFO - 模型测试成功，嵌入维度: 1024
2025-05-20 01:03:02,932 - app.core.model_preload - INFO - 嵌入模型预热完成，总耗时: 9.50秒，编码测试耗时: 0.66秒
2025-05-20 01:03:51,590 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:03:51,591 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:03:51,594 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:03:51,594 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:03:51 - uvicorn.access - INFO - 127.0.0.1:59122 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:03:51,598 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:03:51,598 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:03:51,598 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:03:51,598 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:03:51 - uvicorn.access - INFO - 127.0.0.1:59122 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:04:51,586 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:04:51,588 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:04:51,591 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:04:51,591 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:04:51 - uvicorn.access - INFO - 127.0.0.1:59436 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:04:51,597 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:04:51,597 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:04:51,598 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:04:51,598 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:04:51 - uvicorn.access - INFO - 127.0.0.1:59436 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:05:17,256 - watchfiles.main - INFO - 19 changes detected
2025-05-20 01:05:19,963 - watchfiles.main - INFO - 45 changes detected
2025-05-20 01:05:23,655 - watchfiles.main - INFO - 4 changes detected
2025-05-20 01:05:51,611 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:05:51,619 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:05:51,623 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:05:51,624 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:05:51 - uvicorn.access - INFO - 127.0.0.1:59787 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:05:51,630 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:05:51,630 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:05:51,631 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:05:51,631 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:05:51 - uvicorn.access - INFO - 127.0.0.1:59787 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:06:51,601 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:06:51,603 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:06:51,607 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:06:51,608 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:06:51 - uvicorn.access - INFO - 127.0.0.1:60148 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:06:51,616 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:06:51,617 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:06:51,618 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:06:51,618 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:06:51 - uvicorn.access - INFO - 127.0.0.1:60150 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:07:51,591 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:07:51,592 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:07:51,595 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:07:51,595 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:07:51 - uvicorn.access - INFO - 127.0.0.1:60444 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:07:51,597 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:07:51,597 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:07:51,597 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:07:51,598 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:07:51 - uvicorn.access - INFO - 127.0.0.1:60444 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:08:51,572 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:08:51,573 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:08:51,574 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:08:51,574 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:08:51 - uvicorn.access - INFO - 127.0.0.1:60745 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:08:51,575 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:08:51,575 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:08:51,576 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:08:51,576 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:08:51 - uvicorn.access - INFO - 127.0.0.1:60745 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:09:42,606 - watchfiles.main - INFO - 1 change detected
2025-05-20 01:09:42 - uvicorn.error - WARNING - WatchFiles detected changes in 'app/api/v1/agent/testcase_agents.py'. Reloading...
2025-05-20 01:09:42,610 - uvicorn.error - WARNING - WatchFiles detected changes in 'app/api/v1/agent/testcase_agents.py'. Reloading...
2025-05-20 01:09:42 - uvicorn.error - INFO - Shutting down
2025-05-20 01:09:42,700 - uvicorn.error - INFO - Shutting down
2025-05-20 01:09:42 - uvicorn.error - INFO - Waiting for application shutdown.
2025-05-20 01:09:42,803 - uvicorn.error - INFO - Waiting for application shutdown.
2025-05-20 01:09:42,814 - app - INFO - 应用关闭中...
2025-05-20 01:09:42,818 - tortoise - INFO - Tortoise-ORM shutdown
2025-05-20 01:09:42,819 - app - INFO - 数据库连接已关闭
2025-05-20 01:09:42 - uvicorn.error - INFO - Application shutdown complete.
2025-05-20 01:09:42,819 - uvicorn.error - INFO - Application shutdown complete.
2025-05-20 01:09:42 - uvicorn.error - INFO - Finished server process [68042]
2025-05-20 01:09:42,820 - uvicorn.error - INFO - Finished server process [68042]
2025-05-20 01:09:43,334 - watchfiles.main - INFO - 1 change detected
2025-05-20 01:09:47,237 - watchfiles.main - INFO - 3 changes detected
2025-05-20 01:09:52,303 - app - INFO - 创建FastAPI应用...
2025-05-20 01:09:52,375 - app - INFO - FastAPI应用创建完成
2025-05-20 01:09:52,375 - app.utils.model_preloader - INFO - 开始预加载嵌入模型...
2025-05-20 01:09:52,376 - app.utils.model_preloader - INFO - 嵌入模型预加载已在后台线程启动
2025-05-20 01:09:52,376 - app.utils.model_preloader - INFO - 正在加载模型 BAAI/bge-large-zh...
2025-05-20 01:09:52,376 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: BAAI/bge-large-zh
已加载.env文件中的环境变量
OPENAI_API_KEY = sk-2cdc85223c2f4f9a976a57e3ae9ba4b9
LLM_API_KEY = sk-2cdc85223c2f4f9a976a57e3ae9ba4b9
已设置环境变量:
OPENAI_API_KEY = sk-2cdc85223c2f4f9a976a57e3ae9ba4b9
LLM_API_KEY = sk-2cdc85223c2f4f9a976a57e3ae9ba4b9
LLM_API_BASE = https://api.deepseek.com/v1
DEFAULT_LLM_MODEL = deepseek-chat
静态文件服务已挂载: /Users/<USER>/test/danwen/testing2/AItestPlatform/static
模型预加载已在后台启动
2025-05-20 01:09:52 - uvicorn.error - INFO - Started server process [70406]
2025-05-20 01:09:52,660 - uvicorn.error - INFO - Started server process [70406]
2025-05-20 01:09:52 - uvicorn.error - INFO - Waiting for application startup.
2025-05-20 01:09:52,661 - uvicorn.error - INFO - Waiting for application startup.
2025-05-20 01:09:52,662 - app - INFO - 应用启动中...
2025-05-20 01:09:52.690 | INFO     | app.core.init_app:init_db:186 - 数据库连接成功
2025-05-20 01:09:52.770 | ERROR    | app.core.init_app:init_db:215 - 数据库连接失败: 'int' object is not subscriptable
2025-05-20 01:09:52.770 | INFO     | app.core.init_app:init_db:225 - migrations目录已存在，跳过init_db
2025-05-20 01:09:52.822 | INFO     | app.core.init_app:init_db:246 - Aerich初始化成功
2025-05-20 01:09:52.822 | INFO     | app.core.init_app:init_db:252 - 数据库迁移已禁用，使用手动创建的表
2025-05-20 01:09:52.823 | INFO     | app.core.init_app:init_db:259 - 数据库升级已禁用，使用手动创建的表
2025-05-20 01:09:52.823 | INFO     | app.core.init_app:init_data:296 - 数据库初始化完成
2025-05-20 01:09:52.826 | INFO     | app.core.init_app:init_data:302 - 超级用户初始化完成
2025-05-20 01:09:52.833 | INFO     | app.core.init_app:init_data:308 - 菜单初始化完成
2025-05-20 01:09:52.842 | INFO     | app.core.init_app:init_data:314 - API初始化完成
2025-05-20 01:09:52.847 | INFO     | app.core.init_app:init_data:320 - 角色初始化完成
2025-05-20 01:09:52.847 | INFO     | app.core.init_app:init_data:324 - 所有初始化步骤已完成，即使有错误也会继续启动应用
2025-05-20 01:09:52,848 - app.core.model_preload - INFO - 已在后台线程启动嵌入模型预热
2025-05-20 01:09:52,848 - app - INFO - 应用初始化完成
2025-05-20 01:09:52 - uvicorn.error - INFO - Application startup complete.
2025-05-20 01:09:52,849 - uvicorn.error - INFO - Application startup complete.
2025-05-20 01:09:52,860 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:09:52,861 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:09:52,861 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:09:52,862 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:09:52 - uvicorn.access - INFO - 127.0.0.1:61100 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:09:52,865 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:09:52,865 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:09:52,866 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:09:52,866 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:09:52 - uvicorn.access - INFO - 127.0.0.1:61114 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:09:52,869 - llm_config - INFO - 初始化模型客户端成功: deepseek-chat, API Base: https://api.deepseek.com/v1
2025-05-20 01:09:52,879 - llm_config - INFO - 初始化DashScope客户端成功: deepseek-v3, API Base: https://dashscope.aliyuncs.com/compatible-mode/v1
2025-05-20 01:09:52,879 - app.core.model_preload - INFO - 开始预热嵌入模型...
2025-05-20 01:09:52,879 - llm_config - INFO - 开始加载嵌入模型: BAAI/bge-large-zh, 这可能需要一些时间...
2025-05-20 01:09:52,879 - llm_config - INFO - 模型将从缓存目录加载: ./embedding_models
2025-05-20 01:09:52,879 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: BAAI/bge-large-zh
2025-05-20 01:09:53,117 - sentence_transformers.SentenceTransformer - WARNING - No sentence-transformers model found with name BAAI/bge-large-zh. Creating a new one with MEAN pooling.
2025-05-20 01:09:53,783 - app.utils.model_preloader - ERROR - 嵌入模型预加载失败: [Errno 30] Read-only file system: '/root'
2025-05-20 01:10:00,659 - llm_config - INFO - 嵌入模型加载成功: BAAI/bge-large-zh
2025-05-20 01:10:00,659 - llm_config - INFO - 模型大小: 1241.77 MB, 设备: cpu
2025-05-20 01:10:00,659 - llm_config - INFO - 模型维度: 1024

Batches:   0%|          | 0/1 [00:00<?, ?it/s]2025-05-20 01:10:00,760 - watchfiles.main - INFO - 1 change detected

Batches: 100%|██████████| 1/1 [00:01<00:00,  1.55s/it]
Batches: 100%|██████████| 1/1 [00:01<00:00,  1.55s/it]
2025-05-20 01:10:02,218 - llm_config - INFO - 模型测试成功，嵌入维度: 1024
2025-05-20 01:10:02,762 - app.core.model_preload - INFO - 嵌入模型预热完成，总耗时: 9.88秒，编码测试耗时: 0.54秒
2025-05-20 01:10:51,592 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:10:51,593 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:10:51,597 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:10:51,598 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:10:51 - uvicorn.access - INFO - 127.0.0.1:61415 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:10:51,604 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:10:51,604 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:10:51,605 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:10:51,605 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:10:51 - uvicorn.access - INFO - 127.0.0.1:61415 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:11:51,597 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:11:51,598 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:11:51,599 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:11:51,599 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:11:51 - uvicorn.access - INFO - 127.0.0.1:61702 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:11:51,602 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:11:51,602 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:11:51,602 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:11:51,602 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:11:51 - uvicorn.access - INFO - 127.0.0.1:61702 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:12:51,598 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:12:51,600 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:12:51,603 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:12:51,604 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:12:51 - uvicorn.access - INFO - 127.0.0.1:62016 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:12:51,609 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:12:51,610 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:12:51,613 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:12:51,613 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:12:51 - uvicorn.access - INFO - 127.0.0.1:62018 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:13:51,585 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:13:51,586 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:13:51,587 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:13:51,588 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:13:51 - uvicorn.access - INFO - 127.0.0.1:62327 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:13:51,594 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:13:51,594 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:13:51,596 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:13:51,597 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:13:51 - uvicorn.access - INFO - 127.0.0.1:62327 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:14:51,618 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:14:51,619 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:14:51,623 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:14:51,623 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:14:51 - uvicorn.access - INFO - 127.0.0.1:62641 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:14:51,628 - app.core.cors_middleware - INFO - 收到请求: GET /api/v1/reqAgent/tapd/list
2025-05-20 01:14:51,628 - app.core.cors_middleware - INFO - 请求头: {'host': 'localhost:9999', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:3100', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:3100/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-05-20 01:14:51,630 - app.api.v1.reqAgent.tapdAgent - INFO - 获取需求列表，缓存中共有0个需求
2025-05-20 01:14:51,630 - app.api.v1.reqAgent.tapdAgent - INFO - 添加了测试需求数据
2025-05-20 01:14:51 - uvicorn.access - INFO - 127.0.0.1:62641 - "GET /api/v1/reqAgent/tapd/list HTTP/1.1" 200
2025-05-20 01:15:12,775 - watchfiles.main - INFO - 1 change detected
2025-05-20 01:15:12 - uvicorn.error - WARNING - WatchFiles detected changes in 'app/api/v1/agent/testcase_agents.py'. Reloading...
2025-05-20 01:15:12,784 - uvicorn.error - WARNING - WatchFiles detected changes in 'app/api/v1/agent/testcase_agents.py'. Reloading...
2025-05-20 01:15:12 - uvicorn.error - INFO - Shutting down
2025-05-20 01:15:12,852 - uvicorn.error - INFO - Shutting down
2025-05-20 01:15:12 - uvicorn.error - INFO - Waiting for application shutdown.
2025-05-20 01:15:12,958 - uvicorn.error - INFO - Waiting for application shutdown.
2025-05-20 01:15:12,966 - app - INFO - 应用关闭中...
2025-05-20 01:15:12,984 - tortoise - INFO - Tortoise-ORM shutdown
2025-05-20 01:15:12,984 - app - INFO - 数据库连接已关闭
2025-05-20 01:15:12 - uvicorn.error - INFO - Application shutdown complete.
2025-05-20 01:15:12,986 - uvicorn.error - INFO - Application shutdown complete.
2025-05-20 01:15:12 - uvicorn.error - INFO - Finished server process [70406]
2025-05-20 01:15:12,990 - uvicorn.error - INFO - Finished server process [70406]
2025-05-20 01:15:13,570 - watchfiles.main - INFO - 1 change detected
2025-05-20 01:15:14,634 - watchfiles.main - INFO - 1 change detected
2025-05-20 01:15:14 - uvicorn.error - WARNING - WatchFiles detected changes in 'app/api/v1/agent/testcase_agents.py'. Reloading...
2025-05-20 01:15:14,634 - uvicorn.error - WARNING - WatchFiles detected changes in 'app/api/v1/agent/testcase_agents.py'. Reloading...
2025-05-20 01:15:17,438 - watchfiles.main - INFO - 3 changes detected
