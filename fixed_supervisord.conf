[supervisord]
nodaemon=true
logfile=/var/log/supervisor/supervisord.log
logfile_maxbytes=10MB
logfile_backups=3
pidfile=/tmp/supervisord.pid
user=root

[program:nginx]
command=/usr/sbin/nginx -g "daemon off;"
autostart=true
autorestart=true
stdout_logfile=/var/log/nginx/access.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=3
stderr_logfile=/var/log/nginx/error.log
stderr_logfile_maxbytes=10MB
stderr_logfile_backups=3

[program:fastapi]
command=python run.py
directory=/app
autostart=true
autorestart=true
stdout_logfile=/var/log/app/app.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=3
stderr_logfile=/var/log/app/error.log
stderr_logfile_maxbytes=10MB
stderr_logfile_backups=3
environment=PYTHONUNBUFFERED=1,PORT=9999,DB_HOST=host.docker.internal,DB_PORT=5432,DB_USER=admin,DB_PASSWORD=admin123,DB_NAME=agent_testing,POSTGRES_HOST=host.docker.internal,POSTGRES_PORT=5432,POSTGRES_USER=admin,POSTGRES_PASSWORD=admin123,POSTGRES_DB=agent_testing,API_PORT=9999,LOG_LEVEL=info
