[tool.poetry]
name = "vue-fastapi-admin"
version = "0.1.0"
description = "Vue Fastapi admin"
authors = ["mizhexia<PERSON>ia<PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "0.111.0"
tortoise-orm = "^0.20.1"
pydantic = "^2.7.1"
email-validator = "^2.0.0.post2"
passlib = "^1.7.4"
pyjwt = "^2.7.0"
black = "^23.7.0"
isort = "^5.12.0"
ruff = "^0.0.281"
loguru = "^0.7.0"
pydantic-settings = "^2.0.3"
argon2-cffi = "^23.1.0"
pydantic-core = "^2.18.2"
annotated-types = "^0.6.0"
setuptools = "^70.0.0"
uvicorn = "^0.30.1"
h11 = "^0.14.0"
aerich = "^0.7.2"

[tool.black]
line-length = 120
target-version = ["py310", "py311"]

[[tool.poetry.source]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple/"
priority = "primary"

[tool.ruff]
line-length = 120
extend-select = [
  # "I",    # isort
#   "B",    # flake8-bugbear
#   "C4",   # flake8-comprehensions
#   "PGH",  # pygrep-hooks
  # "RUF",  # ruff
#   "W",    # pycodestyle
#   "YTT",  # flake8-2020
]
ignore = [
    "F403",
    "F405",
]
[tool.aerich]
tortoise_orm = "app.settings.config.TORTOISE_ORM"
location = "./migrations"
src_folder = "./."
