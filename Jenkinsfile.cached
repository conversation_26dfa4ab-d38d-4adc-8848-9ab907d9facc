pipeline {
    agent any

    environment {
        REPO = 'AItestPlatform'
        DOCKERFILE = 'Dockerfile.cached'
        NPM_TOKEN = credentials('npm_token')
    }

    parameters {
        string(name: 'BRANCH', defaultValue: 'main', description: '要构建的分支')
        choice(name: 'BUILD_ENV', choices: ['dev', 'test', 'prod'], description: '构建环境')
        booleanParam(name: 'PUSH_IMAGE', defaultValue: true, description: '是否推送镜像')
        booleanParam(name: 'UPDATE_CACHE', defaultValue: false, description: '是否更新依赖缓存')
    }

    stages {
        stage('拉取代码') {
            steps {
                // 简化的git步骤
                git url: "*************:iHealthTestGroup/aitest.git", branch: "${params.BRANCH}", credentialsId: 'gitee-8835'

                // 简单显示构建信息
                echo "构建分支: ${params.BRANCH}"
                echo "构建环境: ${params.BUILD_ENV}"
            }
        }

        stage('缓存依赖') {
            when {
                expression { return params.UPDATE_CACHE == true }
            }
            steps {
                sh '''#!/bin/bash
                echo "正在缓存项目依赖..."
                chmod +x scripts/cache_dependencies.sh
                ./scripts/cache_dependencies.sh
                '''
            }
        }

        stage('准备环境配置') {
            steps {
                // 准备环境配置文件
                sh '''#!/bin/bash
                echo "准备环境配置文件..."
                
                # 根据环境设置配置文件
                if [ "${BUILD_ENV}" = "prod" ]; then
                    cp config/prod.env .env
                elif [ "${BUILD_ENV}" = "test" ]; then
                    cp config/test.env .env
                else
                    cp config/dev.env .env
                fi
                '''
            }
        }

        stage('生成镜像') {
            steps {
                // 准备环境
                sh 'echo ${WORKSPACE}'
                sh 'echo ${BRANCH}'
                sh 'echo registry=https://registry.ihealthcn.com/ > .npmrc'
                sh 'echo //registry.ihealthcn.com/:_authToken=${NPM_TOKEN} >> .npmrc'

                // 调用build-image作业
                build job: "Utils/build-image", parameters: [
                    string(name: 'ws', value: "${WORKSPACE}"),
                    string(name: 'dockerfilepath', value: "${DOCKERFILE}"),
                    string(name: 'reponame', value: "${REPO}"),
                    string(name: 'branch', value: "${params.BRANCH}"),
                    string(name: 'buildargs', value: "ENV=${params.BUILD_ENV}")
                ]
            }
        }

        stage('推送到华为镜像仓库') {
            when {
                expression { return params.PUSH_IMAGE == true }
            }
            steps {
                // 调用push-image作业
                build job: "Utils/push-image", parameters: [
                    string(name: 'imagename', value: "${REPO.toLowerCase()}:${params.BRANCH.toLowerCase()}")
                ]
            }
        }
    }

    post {
        always {
            cleanWs(patterns: [[pattern: 'web/node_modules', type: 'EXCLUDE']], 
                    deleteDirs: true)
        }
        success {
            echo "构建成功!"
        }
        failure {
            echo "构建失败!"
        }
    }
}
