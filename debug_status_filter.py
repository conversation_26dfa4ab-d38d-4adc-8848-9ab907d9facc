#!/usr/bin/env python3
"""
调试多选状态筛选功能的脚本
"""

import requests
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_single_status():
    """测试单个状态筛选"""
    print("=== 测试单个状态筛选 ===")
    
    url = "http://localhost:9999/api/v1/testcase/list"
    params = {
        "page": 1,
        "page_size": 10,
        "status": ["通过"]  # 单个状态
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"请求URL: {response.url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据总数: {data.get('total', 0)}")
            print(f"返回的测试用例数量: {len(data.get('data', []))}")
            
            # 检查返回的测试用例状态
            if data.get('data'):
                statuses = [item.get('status') for item in data['data']]
                status_count = {}
                for status in statuses:
                    status_count[status] = status_count.get(status, 0) + 1
                print(f"状态分布: {status_count}")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")

def test_multiple_status():
    """测试多个状态筛选"""
    print("\n=== 测试多个状态筛选 ===")
    
    url = "http://localhost:9999/api/v1/testcase/list"
    params = {
        "page": 1,
        "page_size": 10,
        "status": ["通过", "失败"]  # 多个状态
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"请求URL: {response.url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据总数: {data.get('total', 0)}")
            print(f"返回的测试用例数量: {len(data.get('data', []))}")
            
            # 检查返回的测试用例状态
            if data.get('data'):
                statuses = [item.get('status') for item in data['data']]
                status_count = {}
                for status in statuses:
                    status_count[status] = status_count.get(status, 0) + 1
                print(f"状态分布: {status_count}")
                
                # 验证是否只返回了指定状态的测试用例
                expected_statuses = {"通过", "失败"}
                actual_statuses = set(statuses)
                if actual_statuses.issubset(expected_statuses):
                    print("✅ 筛选正确：只返回了指定状态的测试用例")
                else:
                    print(f"❌ 筛选错误：返回了非指定状态的测试用例 {actual_statuses - expected_statuses}")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")

def test_no_status():
    """测试不指定状态筛选"""
    print("\n=== 测试不指定状态筛选 ===")
    
    url = "http://localhost:9999/api/v1/testcase/list"
    params = {
        "page": 1,
        "page_size": 10
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"请求URL: {response.url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据总数: {data.get('total', 0)}")
            print(f"返回的测试用例数量: {len(data.get('data', []))}")
            
            # 检查返回的测试用例状态
            if data.get('data'):
                statuses = [item.get('status') for item in data['data']]
                status_count = {}
                for status in statuses:
                    status_count[status] = status_count.get(status, 0) + 1
                print(f"状态分布: {status_count}")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")

def test_empty_status():
    """测试空状态数组"""
    print("\n=== 测试空状态数组 ===")
    
    url = "http://localhost:9999/api/v1/testcase/list"
    params = {
        "page": 1,
        "page_size": 10,
        "status": []  # 空数组
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"请求URL: {response.url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据总数: {data.get('total', 0)}")
            print(f"返回的测试用例数量: {len(data.get('data', []))}")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")

def test_url_encoding():
    """测试URL编码方式"""
    print("\n=== 测试URL编码方式 ===")
    
    # 模拟前端发送的请求方式
    url = "http://localhost:9999/api/v1/testcase/list?page=1&page_size=10&status=通过&status=失败"
    
    try:
        response = requests.get(url)
        print(f"请求URL: {response.url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据总数: {data.get('total', 0)}")
            print(f"返回的测试用例数量: {len(data.get('data', []))}")
            
            # 检查返回的测试用例状态
            if data.get('data'):
                statuses = [item.get('status') for item in data['data']]
                status_count = {}
                for status in statuses:
                    status_count[status] = status_count.get(status, 0) + 1
                print(f"状态分布: {status_count}")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")

if __name__ == "__main__":
    print("开始测试多选状态筛选功能...")
    
    test_no_status()
    test_single_status()
    test_multiple_status()
    test_empty_status()
    test_url_encoding()
    
    print("\n测试完成！")
