import asyncio
from tortoise import Tortoise
from app.settings.config import TORTOISE_ORM

async def check_requirement_docs():
    # Initialize Tortoise ORM
    await Tortoise.init(config=TORTOISE_ORM)

    # Import RequirementDoc model after Tortoise is initialized
    from app.models.admin import RequirementDoc

    # 查询特定URL的需求文档
    url_to_check = "1122012671001005018"
    docs = await RequirementDoc.filter(url__contains=url_to_check).values()
    print(f"找到 {len(docs)} 条包含 '{url_to_check}' 的记录:")

    for doc in docs:
        print(f"ID: {doc.get('id')}, URL: {doc.get('url')}, 名称: {doc.get('name')}")
        print(f"创建时间: {doc.get('created_at')}")
        print("-" * 50)

    # Close connections
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(check_requirement_docs())
