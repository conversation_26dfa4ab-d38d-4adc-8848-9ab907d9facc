from markdownify import markdownify

# 测试基本的图片标签
html = '<img src="test.jpg" alt="Test Image">'
result = markdownify(html)
print("基本图片标签:")
print(result)

# 测试带有其他属性的图片标签
html2 = '<img src="test.jpg" alt="Test Image" width="100" height="100">'
result2 = markdownify(html2)
print("\n带属性的图片标签:")
print(result2)

# 测试带有样式的图片标签
html3 = '<img src="test.jpg" alt="Test Image" style="width:100px;height:100px;">'
result3 = markdownify(html3)
print("\n带样式的图片标签:")
print(result3)
