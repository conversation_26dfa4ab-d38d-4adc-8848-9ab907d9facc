-- 创建requirement表
CREATE TABLE IF NOT EXISTS "requirement" (
    "id" SERIAL PRIMARY KEY,
    "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "name" VARCHAR(255) NOT NULL UNIQUE,
    "description" TEXT NOT NULL,
    "category" VARCHAR(20) NOT NULL,
    "parent" VARCHAR(50) NULL,
    "module" VARCHAR(50) NULL,
    "level" VARCHAR(20) NOT NULL,
    "reviewer" VARCHAR(50) NULL,
    "keywords" VARCHAR(100) NULL,
    "estimate" INTEGER CHECK (estimate > 0),
    "criteria" TEXT NULL,
    "remark" TEXT NULL,
    "project_id" INTEGER NOT NULL,
    "tapd_url" VARCHAR(500) NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS "idx_requirement_updated" ON "requirement" ("updated_at");
CREATE INDEX IF NOT EXISTS "idx_requirement_project_id" ON "requirement" ("project_id");
