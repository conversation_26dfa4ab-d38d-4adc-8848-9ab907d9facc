import asyncio
import os
import sys

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from tortoise import Tortoise
from app.settings.config import TORTOISE_ORM
from app.models.admin import Requirement, TestCase, TestStep
from tortoise.exceptions import OperationalError

async def delete_all_data():
    # Initialize Tortoise ORM
    await Tortoise.init(config=TORTOISE_ORM)

    # Delete all test steps
    print("Deleting all test steps...")
    try:
        test_steps_count = await TestStep.all().count()
        if test_steps_count > 0:
            await TestStep.all().delete()
            print(f"Deleted {test_steps_count} test steps")
        else:
            print("No test steps found")
    except OperationalError as e:
        print(f"Error deleting test steps: {e}")

    # Delete all test cases
    print("Deleting all test cases...")
    try:
        test_cases_count = await TestCase.all().count()
        if test_cases_count > 0:
            await TestCase.all().delete()
            print(f"Deleted {test_cases_count} test cases")
        else:
            print("No test cases found")
    except OperationalError as e:
        print(f"Error deleting test cases: {e}")

    # Delete all requirements
    print("Deleting all requirements...")
    try:
        requirements_count = await Requirement.all().count()
        if requirements_count > 0:
            await Requirement.all().delete()
            print(f"Deleted {requirements_count} requirements")
        else:
            print("No requirements found")
    except OperationalError as e:
        print(f"Error deleting requirements: {e}")

    # Close connections
    await Tortoise.close_connections()

    print("Data deletion process completed!")

if __name__ == "__main__":
    asyncio.run(delete_all_data())
