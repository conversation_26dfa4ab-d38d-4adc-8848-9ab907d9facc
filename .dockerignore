# Git
.git
.gitignore

# Docker
.docker
docker-compose.yml
Dockerfile
Dockerfile.optimized

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
venv/
.venv/
.pytest_cache/
.coverage
htmlcov/

# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Project specific
logs/
uploads/
*.log
*.sqlite3
*.db
*.pid
*.sock
.env
.env.local
.env.development
.env.test
.env.production

# Documentation
docs/
README.md
LICENSE
*.md
