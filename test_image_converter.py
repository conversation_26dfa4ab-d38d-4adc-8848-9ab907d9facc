#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import logging
from app.api.v1.reqAgent.image_markdown_converter import ImageMarkdownConverter

# 配置日志
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_image_converter")

def test_local_image_analysis():
    """测试本地图片分析功能"""
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # 获取测试图片路径
    test_image_dir = "tapd_images2"

    # 检查目录是否存在
    if not os.path.exists(test_image_dir):
        print(f"错误: 目录 {test_image_dir} 不存在")
        return False

    # 获取目录中的第一个图片
    image_files = [f for f in os.listdir(test_image_dir) if f.endswith(('.png', '.jpg', '.jpeg', '.gif'))]

    if not image_files:
        print(f"错误: 目录 {test_image_dir} 中没有图片文件")
        return False

    test_image_path = os.path.join(test_image_dir, image_files[0])
    print(f"开始测试本地图片分析功能，图片路径: {test_image_path}")

    # 创建转换器实例
    print("创建ImageMarkdownConverter实例...")
    converter = ImageMarkdownConverter(image_analysis_enabled=True)
    print("ImageMarkdownConverter实例创建成功")

    # 检查autogen_image_analyzer模块是否可用
    try:
        print("尝试导入autogen_image_analyzer模块...")
        from app.api.v1.reqAgent.autogen_image_analyzer import analyze_image_with_autogen
        print("成功导入autogen_image_analyzer模块")
    except ImportError as e:
        print(f"导入autogen_image_analyzer模块失败: {str(e)}")
        print("尝试使用相对导入...")
        try:
            import sys
            sys.path.append(os.path.abspath('.'))
            from app.api.v1.reqAgent.autogen_image_analyzer import analyze_image_with_autogen
            print("使用相对导入成功")
        except ImportError as e2:
            print(f"相对导入也失败了: {str(e2)}")
            print(f"当前工作目录: {os.getcwd()}")
            print(f"Python路径: {sys.path}")

    # 分析图片
    print("开始分析图片...")
    analysis_result = converter.analyze_image(test_image_path, "测试图片")

    # 检查结果
    if analysis_result:
        print(f"图片分析成功，结果长度: {len(analysis_result)}")
        print(f"分析结果: {analysis_result[:200]}...")
        return True
    else:
        print("图片分析失败")
        return False

def main():
    """测试图片转换功能"""
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--local":
        # 测试本地图片分析
        return test_local_image_analysis()

    # 检查是否提供了HTML文件路径
    if len(sys.argv) < 2:
        print("用法: python test_image_converter.py <html_file_path>")
        print("例如: python test_image_converter.py tapd_content.txt")
        print("或者: python test_image_converter.py --local (测试本地图片分析)")
        return False

    # 获取HTML文件路径
    html_file_path = sys.argv[1]

    # 检查文件是否存在
    if not os.path.exists(html_file_path):
        print(f"错误: 文件 {html_file_path} 不存在")
        return False

    # 读取HTML文件内容
    try:
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
    except Exception as e:
        print(f"读取文件失败: {str(e)}")
        return False

    print(f"成功读取HTML文件，内容长度: {len(html_content)}")

    # 创建ImageMarkdownConverter实例
    converter = ImageMarkdownConverter(
        image_analysis_enabled=True,
        convert=['a', 'b', 'i', 'strong', 'em', 'code', 'pre', 'p', 'br', 'hr', 'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'img', 'table', 'thead', 'tbody', 'tr', 'th', 'td']
    )

    # 使用BeautifulSoup解析HTML
    from bs4 import BeautifulSoup
    soup = BeautifulSoup(html_content, 'html.parser')

    # 查找所有图片元素
    images = soup.find_all('img')
    print(f"找到 {len(images)} 张图片")

    # 处理每张图片
    for i, img in enumerate(images):
        src = img.get('src', '')
        alt = img.get('alt', '')
        print(f"\n处理图片 {i+1}/{len(images)}: {src[:50]}...")

        # 使用convert_img方法处理图片
        result = converter.convert_img(img, '', [])
        print(f"处理结果: {result[:100]}...")

    print("\n所有图片处理完成")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
