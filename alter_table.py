import asyncio
from tortoise import Tortoise, connections
from app.settings.config import TORTOISE_ORM

async def alter_test_cases_table():
    # 初始化Tortoise ORM
    await Tortoise.init(config=TORTOISE_ORM)
    
    # 获取数据库连接
    conn = connections.get("default")
    
    # 修改test_cases表的requirement_id字段类型为bigint
    try:
        # 先查询当前类型
        result = await conn.execute_query("""
            SELECT column_name, data_type
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'test_cases'
            AND column_name = 'requirement_id';
        """)
        
        if result[1]:
            current_type = result[1][0][1]
            print(f"当前requirement_id字段类型: {current_type}")
            
            if current_type == 'integer':
                # 执行ALTER TABLE语句修改字段类型
                print("开始修改字段类型为bigint...")
                await conn.execute_query("""
                    ALTER TABLE test_cases 
                    ALTER COLUMN requirement_id TYPE bigint;
                """)
                print("字段类型修改完成")
                
                # 验证修改结果
                result = await conn.execute_query("""
                    SELECT column_name, data_type
                    FROM information_schema.columns 
                    WHERE table_schema = 'public' 
                    AND table_name = 'test_cases'
                    AND column_name = 'requirement_id';
                """)
                
                if result[1]:
                    new_type = result[1][0][1]
                    print(f"修改后的requirement_id字段类型: {new_type}")
            else:
                print(f"字段类型已经是{current_type}，无需修改")
        else:
            print("未找到requirement_id字段")
    except Exception as e:
        print(f"修改表结构失败: {e}")
    
    # 关闭连接
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(alter_test_cases_table())
