# 华为云CCE部署指南

本文档提供了将应用部署到华为云CCE（Cloud Container Engine）的详细步骤。

## 前提条件

1. 已有华为云账号并创建了CCE集群
2. 已配置kubectl工具并能够连接到CCE集群
3. 已在华为云上创建RDS（关系型数据库服务）PostgreSQL实例
4. 已配置华为云容器镜像服务（SWR）

## 部署步骤

### 1. 准备配置文件

#### 创建ConfigMap和Secret

创建一个`config.yaml`文件，用于存储应用配置：

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: agent-testing-config
data:
  API_PORT: "9999"
  LOG_LEVEL: "INFO"
  APP_NAME: "agent_testing"
  BACKEND_URL: "http://<您的服务域名>:9999"
  API_PREFIX: ""
  DEFAULT_LLM_MODEL: "deepseek-chat"
  LLM_API_BASE: "https://api.deepseek.com/v1"
  LLM_TIMEOUT_SECONDS: "120"
  LLM_MAX_RETRIES: "3"
  DASHSCOPE_API_BASE: "https://dashscope.aliyuncs.com/compatible-mode/v1"
  DASHSCOPE_MODEL: "deepseek-v3"
  QWEN_VL_MODEL: "qwen-vl-plus-latest"
  QWEN_VL_API_BASE: "https://dashscope.aliyuncs.com/compatible-mode/v1"
---
apiVersion: v1
kind: Secret
metadata:
  name: agent-testing-secrets
type: Opaque
stringData:
  DB_HOST: "<PostgreSQL数据库地址>"
  DB_PORT: "5432"
  DB_USER: "<数据库用户名>"
  DB_PASSWORD: "<数据库密码>"
  DB_NAME: "agent_testing"
  SECRET_KEY: "<生成一个新的密钥，可使用 openssl rand -hex 32>"
  JWT_ALGORITHM: "HS256"
  JWT_EXPIRE_MINUTES: "10080"
  LLM_API_KEY: "<您的DeepSeek API密钥>"
  DASHSCOPE_API_KEY: "<您的阿里云DashScope API密钥>"
  QWEN_VL_API_KEY: "<您的阿里云通义千问视觉模型API密钥>"
```

应用配置：

```bash
kubectl apply -f config.yaml
```

### 2. 准备镜像

#### 构建并推送Docker镜像到华为云SWR

```bash
# 登录华为云SWR
docker login -u cn-north-4@<华为云账号> -p <临时登录指令密码> swr.cn-north-4.myhuaweicloud.com

# 构建镜像
cd agent_testing_20250326
docker build -t swr.cn-north-4.myhuaweicloud.com/<组织名>/agent-testing:latest .

# 推送镜像
docker push swr.cn-north-4.myhuaweicloud.com/<组织名>/agent-testing:latest
```

### 3. 创建Kubernetes部署文件

创建一个`deployment.yaml`文件：

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-testing
  labels:
    app: agent-testing
spec:
  replicas: 1
  selector:
    matchLabels:
      app: agent-testing
  template:
    metadata:
      labels:
        app: agent-testing
    spec:
      containers:
      - name: agent-testing
        image: swr.cn-north-4.myhuaweicloud.com/<组织名>/agent-testing:latest
        ports:
        - containerPort: 80
        - containerPort: 9999
        envFrom:
        - configMapRef:
            name: agent-testing-config
        - secretRef:
            name: agent-testing-secrets
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        volumeMounts:
        - name: logs-volume
          mountPath: /var/log/app
        - name: nginx-logs-volume
          mountPath: /var/log/nginx
      volumes:
      - name: logs-volume
        emptyDir: {}
      - name: nginx-logs-volume
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: agent-testing
spec:
  selector:
    app: agent-testing
  ports:
  - name: http
    port: 80
    targetPort: 80
  - name: api
    port: 9999
    targetPort: 9999
  type: ClusterIP
```

### 4. 创建Ingress资源（用于外部访问）

创建一个`ingress.yaml`文件：

```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: agent-testing-ingress
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
spec:
  rules:
  - host: <您的域名>
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: agent-testing
            port:
              number: 80
```

### 5. 应用Kubernetes配置

```bash
# 应用部署和服务
kubectl apply -f deployment.yaml

# 应用Ingress
kubectl apply -f ingress.yaml
```

### 6. 验证部署

```bash
# 检查Pod状态
kubectl get pods -l app=agent-testing

# 检查服务状态
kubectl get svc agent-testing

# 检查Ingress状态
kubectl get ingress agent-testing-ingress
```

### 7. 数据库初始化

应用首次启动时会自动初始化数据库。如果需要手动初始化或重置数据库，可以执行：

```bash
# 获取Pod名称
POD_NAME=$(kubectl get pods -l app=agent-testing -o jsonpath="{.items[0].metadata.name}")

# 执行数据库初始化命令
kubectl exec -it $POD_NAME -- python /app/scripts/init_db.py
```

### 8. 监控和日志

在Kubernetes环境中查看日志：

```bash
# 获取Pod名称
POD_NAME=$(kubectl get pods -l app=agent-testing -o jsonpath="{.items[0].metadata.name}")

# 查看应用日志
kubectl logs $POD_NAME -c agent-testing

# 查看容器内的特定日志文件
kubectl exec $POD_NAME -- cat /var/log/app/app.log
kubectl exec $POD_NAME -- cat /var/log/app/error.log
kubectl exec $POD_NAME -- cat /var/log/nginx/access.log
kubectl exec $POD_NAME -- cat /var/log/nginx/error.log
```

### 9. 备份和恢复

定期备份PostgreSQL数据库是很重要的。在CCE环境中，可以使用以下方法：

```bash
# 创建一个临时Pod来执行备份
kubectl run pg-backup --image=postgres:13 --restart=Never --rm -i -- \
  pg_dump -h <DB_HOST> -U <DB_USER> -d <DB_NAME> > backup_$(date +%Y%m%d).sql

# 恢复数据库
kubectl run pg-restore --image=postgres:13 --restart=Never --rm -i -- \
  psql -h <DB_HOST> -U <DB_USER> -d <DB_NAME> < backup_file.sql
```

## 故障排除

### 1. Pod无法启动

检查Pod状态和日志：

```bash
# 查看Pod状态
kubectl get pods -l app=agent-testing

# 查看Pod详细信息
kubectl describe pod <pod-name>

# 查看Pod日志
kubectl logs <pod-name>
```

### 2. 数据库连接问题

确保数据库连接信息正确，并且CCE集群可以访问RDS实例：

```bash
# 创建一个临时Pod来测试数据库连接
kubectl run pg-test --image=postgres:13 --restart=Never --rm -i -- \
  psql -h <DB_HOST> -U <DB_USER> -d <DB_NAME> -c "SELECT 1;"
```

### 3. 502 Bad Gateway错误

检查Ingress和Service配置：

```bash
# 检查Ingress状态
kubectl describe ingress agent-testing-ingress

# 检查Service状态
kubectl describe service agent-testing

# 检查Pod中的Nginx日志
POD_NAME=$(kubectl get pods -l app=agent-testing -o jsonpath="{.items[0].metadata.name}")
kubectl exec $POD_NAME -- cat /var/log/nginx/error.log
```

### 4. 大模型API调用问题

如果大模型相关功能无法正常工作，请检查：

1. 确保您提供了有效的API密钥
2. 检查API调用日志
3. 确认网络连接是否正常（CCE集群是否能访问DeepSeek或阿里云API）

```bash
# 获取Pod名称
POD_NAME=$(kubectl get pods -l app=agent-testing -o jsonpath="{.items[0].metadata.name}")

# 查看应用日志中的大模型相关错误
kubectl exec $POD_NAME -- cat /var/log/app/error.log | grep -i "model\|llm\|api\|qwen"

# 测试网络连接
kubectl exec $POD_NAME -- curl -I https://api.deepseek.com/v1
kubectl exec $POD_NAME -- curl -I https://dashscope.aliyuncs.com/compatible-mode/v1
```

### 5. 图像分析问题

如果TAPD需求中的图像分析功能无法正常工作，请检查：

1. 确保您提供了有效的阿里云通义千问视觉模型API密钥
2. 检查图像分析相关日志
3. 确认网络连接是否正常（CCE集群是否能访问阿里云API）

```bash
# 获取Pod名称
POD_NAME=$(kubectl get pods -l app=agent-testing -o jsonpath="{.items[0].metadata.name}")

# 查看应用日志中的图像分析相关错误
kubectl exec $POD_NAME -- cat /var/log/app/error.log | grep -i "image\|qwen\|vision"

# 查看图像分析模块的日志
kubectl exec $POD_NAME -- cat /var/log/app/app.log | grep -i "image_markdown_converter"

# 测试网络连接
kubectl exec $POD_NAME -- curl -I https://dashscope.aliyuncs.com/compatible-mode/v1
```

## 更新应用

当需要更新应用时，执行以下步骤：

```bash
# 构建新版本镜像
docker build -t swr.cn-north-4.myhuaweicloud.com/<组织名>/agent-testing:latest .

# 推送新版本镜像
docker push swr.cn-north-4.myhuaweicloud.com/<组织名>/agent-testing:latest

# 重启部署以使用新镜像
kubectl rollout restart deployment agent-testing

# 监控更新状态
kubectl rollout status deployment agent-testing
```

## 性能优化

1. 配置HPA（Horizontal Pod Autoscaler）实现自动扩缩容：

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-testing-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-testing
  minReplicas: 1
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80
```

2. 使用华为云CDN加速静态资源
3. 根据需求调整PostgreSQL数据库参数
4. 配置CCE集群的Ingress Controller以优化网络性能
